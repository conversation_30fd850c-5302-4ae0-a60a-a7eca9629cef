<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tamm 스푼 연동</title>
    <style>
        body {
            width: 350px;
            min-height: 200px;
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            font-size: 32px;
            margin-bottom: 8px;
        }
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
        }
        .user-details h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            color: #333;
        }
        .user-details p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #7c3aed;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 8px;
        }
        .btn:hover {
            background: #6d28d9;
            transform: translateY(-1px);
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            text-align: center;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-weight: 500;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
        }
        .hidden {
            display: none;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
            font-size: 14px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🥄</div>
            <div class="title">Tamm 스푼 연동</div>
        </div>

        <!-- 잘못된 페이지 -->
        <div id="wrong-page" class="status error hidden">
            스푼캐스트 페이지에서 사용해주세요!<br>
            <button class="btn" onclick="window.open('https://www.spooncast.net/kr', '_blank')">
                스푼캐스트로 이동
            </button>
        </div>

        <!-- 로그인 안됨 -->
        <div id="not-login" class="status error hidden">
            스푼캐스트에 로그인해주세요!<br>
            <button class="btn" onclick="window.open('https://www.spooncast.net/kr/login', '_blank')">
                로그인하러 가기
            </button>
        </div>

        <!-- 로그인됨 -->
        <div id="login-page" class="hidden">
            <div class="user-info">
                <img id="user-avatar" class="user-avatar" src="/favicon.ico" alt="프로필">
                <div class="user-details">
                    <h3 id="user-name">사용자</h3>
                    <p id="user-tag">@username</p>
                </div>
            </div>
            
            <button id="login-btn" class="btn">
                🔗 TAMM에 연동하기
            </button>
            
            <div class="warning">
                <strong>📋 디버깅 모드</strong><br>
                F12를 눌러 개발자 도구를 열고<br>
                Console 탭에서 상세 로그를 확인하세요
            </div>
        </div>

        <!-- 성공 메시지 -->
        <div id="success-message" class="status success hidden">
            ✅ 연동이 완료되었습니다!<br>
            이제 Tamm에서 스푼 기능을 사용할 수 있습니다.
        </div>
    </div>

    <!-- Firebase SDK 로드 -->
    <script src="firebase-app.js"></script>
    <script src="firebase-firestore.js"></script>
    
    <!-- 메인 스크립트 -->
    <script src="extension-popup.js"></script>
</body>
</html> 