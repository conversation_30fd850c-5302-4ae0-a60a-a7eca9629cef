<!--
 * Index.vue
 * Created on Wed Oct 14 2020
 *
 * Copyright (c) Tree Some. Licensed under the MIT License.
-->
<template>
	<v-main class="custom white pa-0">
		<v-container fluid class="pa-0">
			<v-row class="ma-0">
				<v-col cols="12" class="pa-0">
					<v-card class="pa-0" flat color="white">
						<v-card-title class="text-center justify-center">Setting</v-card-title>
						<v-card-text>
							<!-- 여기에 설정 내용이 추가될 예정입니다 -->
							<div class="text-center mt-4">
								<v-btn rounded color="primary" elevation="2" class="px-4" @click="openUserScoreManager">
									유저 점수 및 퀴즈
								</v-btn>
								<v-btn rounded color="primary" elevation="2" class="px-4 ml-3" @click="openFishingSettings">
									낚시 설정
								</v-btn>
							</div>

							<!-- 인증 관련 버튼들 -->
							<v-divider class="my-6"></v-divider>
							<div class="text-center">
								<h3 class="mb-4">계정 관리</h3>
								
								<!-- 현재 사용자 정보 표시 -->
								<div v-if="currentUser" class="mb-4">
									<v-chip :color="currentUser.role === 'admin' ? 'red' : 'blue'" text-color="white" class="mb-2">
										<v-icon left>{{ currentUser.role === 'admin' ? 'mdi-crown' : 'mdi-account' }}</v-icon>
										{{ currentUser.role === 'admin' ? '관리자' : '사용자' }}
									</v-chip>
									<p class="mb-0">{{ currentUser.name }} ({{ currentUser.email }})</p>
								</div>

								<!-- 편지 쓰기 버튼 (일반 사용자용) -->
								<div v-if="!isAdmin" class="mb-3">
									<v-btn rounded color="purple" elevation="2" class="px-4" @click="messageDialog = true">
										<v-icon left>mdi-email-outline</v-icon>
										관리자에게 편지 쓰기
									</v-btn>
								</div>

								<!-- 관리자 전용 버튼 -->
								<div v-if="isAdmin" class="mb-3">
									<v-btn rounded color="orange" elevation="2" class="px-4 mr-3" @click="adminPanelDialog = true">
										<v-icon left>mdi-account-supervisor</v-icon>
										관리자 패널
									</v-btn>
									<v-btn rounded color="purple" elevation="2" class="px-4" @click="mailboxDialog = true">
										<v-icon left>mdi-email</v-icon>
										편지함
										<v-chip v-if="unreadMessagesCount > 0" small color="red" text-color="white" class="ml-2">
											{{ unreadMessagesCount }}
										</v-chip>
									</v-btn>
								</div>

								<!-- 로그아웃 버튼 -->
								<div>
									<v-btn rounded color="red" elevation="2" class="px-4" @click="handleLogout">
										<v-icon left>mdi-logout</v-icon>
										로그아웃
									</v-btn>
								</div>
							</div>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>
		</v-container>
		
		<!-- 유저 점수 관리 다이얼로그 -->
		<v-dialog v-model="userScoreDialog" max-width="800" persistent class="custom-dialog">
			<v-card class="no-scroll-card">
				<v-card-title class="headline pa-3 position-relative">
					<div class="w-100 text-center">⭑</div>
					<v-btn icon @click="userScoreDialog = false" class="close-btn">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text class="pa-0">
					<div v-if="loadError" class="error-message pa-0">
						<p>{{ loadError }}</p>
					</div>
					<div v-else-if="isLoading" class="d-flex align-center justify-center pa-5">
						<v-progress-circular indeterminate color="primary"></v-progress-circular>
						<span class="ml-2">파일을 불러오는 중...</span>
					</div>
					<div v-else class="component-container">
						<!-- 동적으로 로드된 번들 컴포넌트 렌더링 -->
						<component v-if="bundleComponent" :is="bundleComponent"></component>
					</div>
				</v-card-text>
			</v-card>
		</v-dialog>

		<!-- 낚시 설정 다이얼로그 -->
		<v-dialog v-model="fishingSettingsDialog" max-width="800" persistent class="custom-dialog">
			<v-card class="no-scroll-card">
				<v-card-title class="headline pa-3 position-relative">
					<div class="w-100 text-center">⭑</div>
					<v-btn icon @click="fishingSettingsDialog = false" class="close-btn">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text class="pa-0">
					<div v-if="fishingLoadError" class="error-message pa-0">
						<p>{{ fishingLoadError }}</p>
					</div>
					<div v-else-if="isFishingLoading" class="d-flex align-center justify-center pa-5">
						<v-progress-circular indeterminate color="primary"></v-progress-circular>
						<span class="ml-2">설정을 불러오는 중...</span>
					</div>
					<div v-else class="component-container">
						<component v-if="fishingComponent" :is="fishingComponent"></component>
						<div v-else class="text-center pa-4">
							<p>낚시 설정을 준비 중입니다.</p>
						</div>
					</div>
				</v-card-text>
			</v-card>
		</v-dialog>

		<!-- 로그아웃 확인 다이얼로그 -->
		<v-dialog v-model="logoutDialog" max-width="400" persistent>
			<v-card>
				<v-card-title class="headline">
					<v-icon left color="red">mdi-logout</v-icon>
					로그아웃 확인
				</v-card-title>
				<v-card-text>
					정말로 로그아웃 하시겠습니까?
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn text @click="logoutDialog = false">취소</v-btn>
					<v-btn color="red" text @click="confirmLogout">로그아웃</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<!-- 편지함 및 관리자 패널 컴포넌트들 -->
		<MessageCompose 
			v-model="messageDialog" 
			@message-sent="onMessageSent"
			@error="showError"
		/>
		
		<AdminMailbox 
			v-if="isAdmin"
			v-model="mailboxDialog" 
			@open-message="openMessage"
		/>
		
		<MessageRead 
			v-model="readMessageDialog" 
			:message="selectedMessage"
			@message-read="onMessageRead"
		/>
		
		<AdminPanel
			v-if="isAdmin"
			v-model="adminPanelDialog"
			@success="showSuccess"
			@error="showError"
		/>
	</v-main>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import * as VuetifyComponents from 'vuetify/lib/components';
import { authService } from '@/plugins/auth-service';
import { mailboxService, MailboxMessage } from '@/plugins/mailbox-service';
import AdminMailbox from '@/views/Components/AdminMailbox.vue';
import MessageCompose from '@/views/Components/MessageCompose.vue';
import MessageRead from '@/views/Components/MessageRead.vue';
import AdminPanel from '@/views/Components/AdminPanel.vue';

const { ipcRenderer } = window.require('electron');
const path = window.require('path');
const fs = window.require('fs');
const vm = window.require('vm');

@Component({
	components: {
		AdminMailbox,
		MessageCompose,
		MessageRead,
		AdminPanel
	}
})
export default class Setting extends Mixins(GlobalMixins) {
	// 다이얼로그 상태
	public userScoreDialog: boolean = false;
	public isLoading: boolean = false;
	public loadError: string | null = null;
	public filePath: string = '';
	public bundleComponent: any = null;
	
	// 낚시 설정 관련 상태
	public fishingSettingsDialog: boolean = false;
	public isFishingLoading: boolean = false;
	public fishingLoadError: string | null = null;
	public fishingComponent: any = null;

	// 관리자 패널 관련 상태
	public adminPanelDialog: boolean = false;

	// 로그아웃 관련 상태
	public logoutDialog: boolean = false;

	// 편지함 다이얼로그 상태
	public messageDialog: boolean = false;
	public mailboxDialog: boolean = false;
	public readMessageDialog: boolean = false;
	public selectedMessage: MailboxMessage | null = null;

	// 현재 사용자 정보
	public get currentUser() {
		return authService.getCurrentUser();
	}

	// 관리자 여부 확인
	public get isAdmin() {
		const user = this.currentUser;
		return user && user.role === 'admin';
	}

	// 읽지 않은 편지 개수 계산
	public get unreadMessagesCount() {
		// AdminMailbox 컴포넌트에서 관리하는 unreadCount를 사용하거나
		// mailboxService를 통해 직접 계산
		return 0; // 실제 구현 시 mailboxService.getUnreadCount() 등으로 구현
	}

	// 컴포넌트 마운트 시 데이터 로드
	public mounted() {
		// 기본 설정 초기화
	}

	// 유저 점수 관리 창 열기
	public async openUserScoreManager() {
		this.userScoreDialog = true;
		this.isLoading = true;
		this.loadError = null;
		this.bundleComponent = null;
		
		try {
			// Windows 환경에 맞게 AppData 경로 직접 설정
			const appDataPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\tamm-v1\\bundles\\fan_level\\page.vue';
			this.filePath = appDataPath;
			
			// 파일 존재 확인
			if (fs.existsSync(appDataPath)) {
				console.log('파일 존재함, Vue 컴포넌트 로드 시도 중...');
				
				// Vue 컴포넌트 로드
				this.loadVueComponent(appDataPath);
			} else {
				console.log('파일이 존재하지 않음');
				this.loadError = '파일을 찾을 수 없습니다: ' + appDataPath;
				this.isLoading = false;
			}
		} catch (error: any) {
			console.error('파일 로드 오류:', error);
			this.loadError = '파일 로드 중 오류가 발생했습니다: ' + error.message;
			this.isLoading = false;
		}
	}
	
	// BundleRenderer.vue의 방식을 참고하여 Vue 컴포넌트 동적 로드
	private loadVueComponent(filePath: string) {
		try {
			// Vue 파일 읽기
			const vueFileContent = fs.readFileSync(filePath, 'utf8');
			
			// template 및 script 부분 추출
			const template = this.getInnerString(vueFileContent, 'template');
			let script = this.getInnerString(vueFileContent, 'script');
			// style 추출 시도
			const style = this.getInnerString(vueFileContent, 'style');
			
			if (!template || !script) {
				throw new Error('Vue 파일의 template 또는 script 태그를 찾을 수 없습니다.');
			}
			
			// script 내용 수정: export default { ... } -> module = { ... }
			script = script.replace(/export\s+default\s+{/, 'module = {');
			
			// VM 컨텍스트 생성 및 스크립트 실행
			const vmScript = new vm.Script(script);
			const context: any = {};
			context.module = {};
			// 필수 컨텍스트만 추가하여 성능 최적화
			context.window = window;
			context.require = window.require;
			context.__dirname = path.dirname(filePath);
			context.console = console;
			
			// 스크립트 실행
			vmScript.runInNewContext(context);
			
			// 컴포넌트 생성
			const component: any = {
				template,
				...context.module,
				mixins: [Mixins(GlobalMixins)],
			};
			
			// 필요한 메서드 및 컴포넌트 추가
			if (!component.methods) {
				component.methods = {};
			}
			
			// 리로드 메서드 추가
			component.methods.reload = () => {
				console.log('컴포넌트 리로드');
				this.openUserScoreManager();
			};
			
			// Vuetify 컴포넌트 추가
			component.components = { ...VuetifyComponents };
			
			// 스타일 적용 (beforeDestroy 무한 재귀 수정)
			if (style) {
				// 스타일 ID 생성 (고유한 ID 생성)
				const styleId = 'user-score-manager-' + Date.now();
				
				// 스타일 태그 생성 및 추가
				const styleEl = document.createElement('style');
				styleEl.textContent = style;
				styleEl.setAttribute('id', styleId);
				document.head.appendChild(styleEl);
				
				// 저장된 원래 beforeDestroy 함수
				const originalBeforeDestroy = component.beforeDestroy;
				
				// 새로운 beforeDestroy 함수 설정
				component.beforeDestroy = function() {
					// 스타일 태그 제거
					const styleTag = document.getElementById(styleId);
					if (styleTag) {
						styleTag.remove();
					}
					
					// 원래 beforeDestroy가 있었다면 호출
					if (originalBeforeDestroy && typeof originalBeforeDestroy === 'function') {
						originalBeforeDestroy.call(this);
					}
				};
			}
			
			// 컴포넌트 설정 완료
			this.bundleComponent = component;
			this.isLoading = false;
			
			console.log('Vue 컴포넌트가 성공적으로 로드되었습니다.');
		} catch (error: any) {
			console.error('Vue 컴포넌트 로드 오류:', error);
			this.loadError = '컴포넌트 로드 중 오류가 발생했습니다: ' + error.message;
			this.isLoading = false;
		}
	}
	
	// Template 내부 문자열 추출 헬퍼 함수
	private getInnerString(txt: string, tag: string) {
		const regx = new RegExp(`<${tag}>((?:.|\r|\n)*)?</${tag}>`);
		const m: any = txt.match(regx);
		if (m) {
			return m[1];
		}
		return null;
	}

	// 낚시 설정 창 열기
	public async openFishingSettings() {
		this.fishingSettingsDialog = true;
		this.isFishingLoading = true;
		this.fishingLoadError = null;
		this.fishingComponent = null;
		
		try {
			// 여기서 낚시 설정 관련 컴포넌트를 로드합니다
			// Windows 환경에 맞게 AppData 경로 직접 설정
			const appDataPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\tamm-v1\\bundles\\fishing\\page.vue';
			
			// 파일 존재 확인
			if (fs.existsSync(appDataPath)) {
				console.log('낚시 설정 파일 존재함, Vue 컴포넌트 로드 시도 중...');
				
				// Vue 컴포넌트 로드
				this.loadFishingComponent(appDataPath);
			} else {
				console.log('낚시 설정 파일이 존재하지 않음');
				this.fishingLoadError = '파일을 찾을 수 없습니다: ' + appDataPath;
				this.isFishingLoading = false;
			}
		} catch (error: any) {
			console.error('낚시 설정 파일 로드 오류:', error);
			this.fishingLoadError = '파일 로드 중 오류가 발생했습니다: ' + error.message;
			this.isFishingLoading = false;
		}
	}
	
	// 낚시 설정 컴포넌트 로드
	private loadFishingComponent(filePath: string) {
		try {
			// Vue 파일 읽기
			const vueFileContent = fs.readFileSync(filePath, 'utf8');
			
			// template 및 script 부분 추출
			const template = this.getInnerString(vueFileContent, 'template');
			let script = this.getInnerString(vueFileContent, 'script');
			// style 추출 시도
			const style = this.getInnerString(vueFileContent, 'style');
			
			if (!template || !script) {
				throw new Error('Vue 파일의 template 또는 script 태그를 찾을 수 없습니다.');
			}
			
			// script 내용 수정: export default { ... } -> module = { ... }
			script = script.replace(/export\s+default\s+{/, 'module = {');
			
			// VM 컨텍스트 생성 및 스크립트 실행
			const vmScript = new vm.Script(script);
			const context: any = {};
			context.module = {};
			// 필수 컨텍스트만 추가하여 성능 최적화
			context.window = window;
			context.require = window.require;
			context.__dirname = path.dirname(filePath);
			context.console = console;
			
			// 스크립트 실행
			vmScript.runInNewContext(context);
			
			// 컴포넌트 생성
			const component: any = {
				template,
				...context.module,
				mixins: [Mixins(GlobalMixins)],
			};
			
			// 필요한 메서드 및 컴포넌트 추가
			if (!component.methods) {
				component.methods = {};
			}
			
			// 리로드 메서드 추가
			component.methods.reload = () => {
				console.log('낚시 설정 컴포넌트 리로드');
				this.openFishingSettings();
			};
			
			// Vuetify 컴포넌트 추가
			component.components = { ...VuetifyComponents };
			
			// 스타일 적용 (beforeDestroy 무한 재귀 수정)
			if (style) {
				// 스타일 ID 생성 (고유한 ID 생성)
				const styleId = 'fishing-settings-' + Date.now();
				
				// 스타일 태그 생성 및 추가
				const styleEl = document.createElement('style');
				styleEl.textContent = style;
				styleEl.setAttribute('id', styleId);
				document.head.appendChild(styleEl);
				
				// 저장된 원래 beforeDestroy 함수
				const originalBeforeDestroy = component.beforeDestroy;
				
				// 새로운 beforeDestroy 함수 설정
				component.beforeDestroy = function() {
					// 스타일 태그 제거
					const styleTag = document.getElementById(styleId);
					if (styleTag) {
						styleTag.remove();
					}
					
					// 원래 beforeDestroy가 있었다면 호출
					if (originalBeforeDestroy && typeof originalBeforeDestroy === 'function') {
						originalBeforeDestroy.call(this);
					}
				};
			}
			
			// 컴포넌트 설정 완료
			this.fishingComponent = component;
			this.isFishingLoading = false;
			
			console.log('낚시 설정 컴포넌트가 성공적으로 로드되었습니다.');
		} catch (error: any) {
			console.error('낚시 설정 컴포넌트 로드 오류:', error);
			this.fishingLoadError = '컴포넌트 로드 중 오류가 발생했습니다: ' + error.message;
			this.isFishingLoading = false;
		}
	}

	// 로그아웃 처리
	public handleLogout() {
		this.logoutDialog = true;
	}

	// 로그아웃 확인
	public async confirmLogout() {
		try {
			// 로그아웃 처리
			authService.logout();
			
			this.logoutDialog = false;
			
			// 성공 메시지
			this.$swal.fire({
				icon: 'success',
				title: '로그아웃 완료',
				text: '성공적으로 로그아웃되었습니다.',
				timer: 1500,
				showConfirmButton: false
			});
			
			// 인증 페이지로 리다이렉트
			setTimeout(() => {
				this.$router.push('/login');
			}, 1500);
			
		} catch (error) {
			console.error('로그아웃 오류:', error);
			this.$swal.fire({
				icon: 'error',
				title: '오류',
				text: '로그아웃 중 오류가 발생했습니다.'
			});
		}
	}

	// 편지함 관련 이벤트 핸들러
	public onMessageSent() {
		this.showSuccess('편지가 전송되었습니다.');
	}

	public openMessage(message: MailboxMessage) {
		this.selectedMessage = message;
		this.readMessageDialog = true;
	}

	public onMessageRead(message: MailboxMessage) {
		// 편지 읽음 처리 완료 후 필요한 작업
		console.log('편지 읽음 처리 완료:', message.subject);
	}

	// 성공 메시지 표시
	private showSuccess(message: string) {
		// Vuetify Snackbar 또는 다른 알림 방식 사용
		console.log(`✅ ${message}`);
	}

	// 오류 메시지 표시
	private showError(message: string) {
		// Vuetify Snackbar 또는 다른 알림 방식 사용
		console.error(`❌ ${message}`);
	}
}
</script>
<style scoped>
.error-message {
	color: #d32f2f;
	background-color: #ffebee;
	border-radius: 4px;
}

.component-container {
	overflow: hidden;
}

/* 다이얼로그 스크롤바 제거 */
.custom-dialog {
	overflow: hidden !important;
}

.no-scroll-card {
	overflow: hidden !important;
	max-height: none !important;
}

/* 전역 스크롤바 스타일 수정 - 필요한 경우에만 사용 */
::v-deep ::-webkit-scrollbar {
	display: none !important;
}

/* 다이얼로그 제목 중앙 정렬용 스타일 */
.position-relative {
	position: relative;
}

.close-btn {
	position: absolute;
	right: 8px;
	top: 50%;
	transform: translateY(-50%);
}
</style>
