import React, { useState } from 'react';
import styled from 'styled-components';
import { AuthProvider } from './contexts/AuthContext';
import { SpoonAuthProvider } from './contexts/SpoonAuthContext';
import AuthModal from './components/AuthModal';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import BroadcastRoom from './components/BroadcastRoom';
import ApprovalRequests from './components/ApprovalRequests';
import AccountManagement from './components/AccountManagement';
import SpoonGuide from './components/SpoonGuide';
import './App.css';

const AppContainer = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
`;

const MainContainer = styled.div`
  flex: 1;
  display: flex;
  position: relative;
`;

const ContentArea = styled.div<{ $sidebarOpen: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  margin-left: ${props => props.$sidebarOpen ? '240px' : '0'};
`;

const SidebarOverlay = styled.div<{ $show: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: ${props => props.$show ? 'block' : 'none'};
`;

function App() {
  const [selectedBroadcast, setSelectedBroadcast] = useState<any>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState('home');

  const handleBroadcastSelect = (broadcast: any) => {
    setSelectedBroadcast(broadcast);
  };

  const handleBackToMain = () => {
    setSelectedBroadcast(null);
    setCurrentPage('home');
  };

  const handleAuthModalOpen = () => {
    setIsAuthModalOpen(true);
  };

  const handleAuthModalClose = () => {
    setIsAuthModalOpen(false);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleSidebarClose = () => {
    setIsSidebarOpen(false);
  };

  const handleMenuSelect = (menu: string) => {
    setCurrentPage(menu);
    setSelectedBroadcast(null);
  };

  const handleNavigateToGuide = () => {
    setCurrentPage('spoon-guide');
    setSelectedBroadcast(null);
  };

  const renderContent = () => {
    if (selectedBroadcast) {
      return (
        <BroadcastRoom
          broadcast={selectedBroadcast}
          onBack={handleBackToMain}
        />
      );
    }

    switch (currentPage) {
      case 'approval-requests':
        return <ApprovalRequests />;
      case 'account-management':
        return <AccountManagement />;
      case 'spoon-guide':
        return <SpoonGuide />;
      case 'home':
      default:
        return (
          <MainContent
            onBroadcastSelect={handleBroadcastSelect}
            searchQuery={searchQuery}
          />
        );
    }
  };

  return (
    <AuthProvider>
      <SpoonAuthProvider onNavigateToGuide={handleNavigateToGuide}>
        <AppContainer>
          <Header 
            onSearch={handleSearch}
            onAuthClick={handleAuthModalOpen}
            onSidebarToggle={handleSidebarToggle}
          />
          <MainContainer>
            <Sidebar 
              isOpen={isSidebarOpen}
              onClose={handleSidebarClose}
              onMenuSelect={handleMenuSelect}
            />
            <SidebarOverlay $show={isSidebarOpen} onClick={handleSidebarClose} />
            <ContentArea $sidebarOpen={isSidebarOpen}>
              {renderContent()}
            </ContentArea>
          </MainContainer>
          
          <AuthModal
            isOpen={isAuthModalOpen}
            onClose={handleAuthModalClose}
            onAuthSuccess={handleAuthModalClose}
          />
        </AppContainer>
      </SpoonAuthProvider>
    </AuthProvider>
  );
}

export default App;
