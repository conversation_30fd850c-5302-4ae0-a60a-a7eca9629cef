<template>
  <div class="post-detail-container">
    <!-- 헤더 -->
    <div class="post-detail-header">
      <v-btn icon @click="$emit('close')" class="mr-2">
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
      <span class="text-h6 font-weight-medium">게시물 상세</span>
    </div>

    <!-- 로딩 상태 -->
    <div v-if="loading" class="text-center py-4">
      <v-progress-circular indeterminate color="purple" size="24"></v-progress-circular>
      <div class="mt-2 text-body-2 grey--text">게시물을 불러오는 중...</div>
    </div>

    <!-- 에러 상태 -->
    <div v-else-if="error" class="text-center py-4">
      <v-icon color="red" size="32">mdi-alert-circle</v-icon>
      <div class="mt-2 text-body-2 red--text">{{ error }}</div>
      <v-btn small text color="purple" @click="loadPostDetail" class="mt-2">
        다시 시도
      </v-btn>
    </div>

    <!-- 게시물 상세 내용 및 댓글 목록 -->
    <div v-else-if="postDetail" class="post-detail-content">
      <!-- 게시물 정보 -->
      <div class="post-info mb-4">
        <div class="d-flex">
          <!-- 작성자 프로필 -->
          <v-avatar size="40" class="mr-3 flex-shrink-0">
            <v-img :src="getAuthorProfileUrl()"></v-img>
          </v-avatar>
          
          <!-- 게시물 내용 -->
          <div class="flex-grow-1">
            <div class="d-flex align-center mb-1">
              <span class="font-weight-medium text-body-2">{{ getAuthorNickname() }}</span>
              <span class="ml-2 text-caption grey--text">{{ formatDate(postDetail.created) }}</span>
              
              <!-- 공개범위 표시 -->
              <v-chip
                x-small
                :color="getVisibilityColor(postDetail.visibleOption)"
                text-color="white"
                class="ml-2"
              >
                {{ getVisibilityText(postDetail.visibleOption) }}
              </v-chip>
            </div>
            
            <!-- 게시물 텍스트 -->
            <div class="post-content text-body-2 mb-2" v-html="formatContent(postDetail.contents)"></div>
            
            <!-- 미디어 (이미지) -->
            <div v-if="postDetail.media && postDetail.media.length > 0" class="post-media mb-2">
              <div v-for="(media, index) in postDetail.media" :key="index" class="media-item">
                <v-img 
                  :src="media.url" 
                  max-width="200" 
                  max-height="200" 
                  class="rounded"
                  @click="openImageDialog(media.url)">
                </v-img>
              </div>
            </div>
            
            <!-- 좋아요/댓글 수 -->
            <div class="post-stats d-flex align-center">
              <div class="d-flex align-center mr-4 like-button" @click="toggleLike">
                <v-icon 
                  size="16" 
                  :color="postDetail.likeStatus ? 'red' : 'grey'"
                  class="like-icon"
                >
                  {{ postDetail.likeStatus ? 'mdi-heart' : 'mdi-heart-outline' }}
                </v-icon>
                <span class="ml-1 text-caption" :class="postDetail.likeStatus ? 'red--text' : 'grey--text'">
                  {{ postDetail.likeCount || 0 }}
                </span>
              </div>
              <div class="d-flex align-center">
                <v-icon size="16" color="grey">mdi-comment-outline</v-icon>
                <span class="ml-1 text-caption grey--text">{{ postDetail.commentCount || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 댓글 섹션 -->
      <div class="comments-section">
        <div class="comments-header d-flex align-center mb-3">
          <span class="text-subtitle-2 font-weight-medium">댓글 {{ postDetail.commentCount || 0 }}</span>
        </div>

        <!-- 댓글 목록 -->
        <div v-if="loadingComments" class="text-center py-4">
          <v-progress-circular indeterminate color="purple" size="20"></v-progress-circular>
          <div class="mt-1 text-caption grey--text">댓글을 불러오는 중...</div>
        </div>

        <div v-else-if="comments.length > 0" class="comments-list" ref="commentsContainer" @scroll="throttledScroll">
          <div v-for="comment in comments" :key="comment.commentId" class="comment-item mb-3">
            <div class="d-flex">
              <v-avatar size="32" class="mr-3 flex-shrink-0">
                <v-img :src="getCommentAuthorProfileUrl(comment)"></v-img>
              </v-avatar>
              
              <div class="flex-grow-1">
                <div class="comment-header mb-1">
                  <span class="font-weight-medium text-body-2">{{ getCommentAuthorNickname(comment) }}</span>
                  <span class="ml-2 text-caption grey--text">{{ formatDate(comment.created) }}</span>
                </div>
                <div class="comment-content text-body-2" v-html="formatContent(comment.contents)"></div>
                
                <!-- 답글 수 및 답글 보기 버튼 -->
                <div v-if="comment.replyCount > 0" class="reply-actions mt-2">
                  <v-btn 
                    x-small 
                    text 
                    color="purple" 
                    @click="toggleReplies(comment)"
                    :loading="repliesLoading[comment.commentId]"
                  >
                    <v-icon small class="mr-1">
                      {{ repliesExpanded[comment.commentId] ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                    </v-icon>
                    {{ repliesExpanded[comment.commentId] ? '답글 접기' : `답글 ${comment.replyCount}개 보기` }}
                  </v-btn>
                </div>

                <!-- 답글 목록 -->
                <div v-if="repliesExpanded[comment.commentId] && replies[comment.commentId]" class="replies-container mt-3">
                  <div v-for="reply in replies[comment.commentId]" :key="reply.commentId" class="reply-item mb-2">
                    <div class="d-flex">
                      <v-avatar size="28" class="mr-2 flex-shrink-0">
                        <v-img :src="getCommentAuthorProfileUrl(reply)"></v-img>
                      </v-avatar>
                      
                      <div class="flex-grow-1">
                        <div class="reply-header mb-1">
                          <span class="font-weight-medium text-caption">{{ getCommentAuthorNickname(reply) }}</span>
                          <span class="ml-2 text-caption grey--text">{{ formatDate(reply.created) }}</span>
                        </div>
                        <div class="reply-content text-caption" v-html="formatContent(reply.contents)"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 더 많은 댓글 로딩 -->
          <div v-if="loadingMoreComments" class="text-center py-3">
            <v-progress-circular indeterminate color="purple" size="20"></v-progress-circular>
            <div class="mt-1 text-caption grey--text">더 많은 댓글을 불러오는 중...</div>
          </div>

          <!-- 모든 댓글 로드 완료 -->
          <div v-else-if="!hasMoreComments && comments.length > 0" class="text-center py-3">
            <div class="text-caption grey--text">모든 댓글을 불러왔습니다</div>
          </div>
        </div>

        <!-- 댓글이 없는 경우 -->
        <div v-else class="text-center py-4">
          <v-icon color="grey" size="32">mdi-comment-outline</v-icon>
          <div class="mt-2 text-caption grey--text">첫 번째 댓글을 작성해보세요</div>
        </div>
      </div>
    </div>

    <!-- 댓글 작성 (하단 고정) -->
    <div v-if="postDetail" class="comment-input-fixed">
      <div class="comment-input-container">
        <v-avatar size="32" class="mr-3 flex-shrink-0">
          <v-img :src="getCurrentUserProfileUrl()"></v-img>
        </v-avatar>
        <div class="comment-input-wrapper">
          <v-text-field
            v-model="newComment"
            placeholder="댓글을 입력하세요..."
            outlined
            dense
            hide-details
            class="comment-input-field"
            @keydown.enter="submitComment"
          ></v-text-field>
          <v-btn
            class="comment-submit-btn"
            color="purple"
            :disabled="!newComment.trim()"
            :loading="submittingComment"
            @click="submitComment"
          >
            등록
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 이미지 다이얼로그 -->
    <v-dialog v-model="imageDialog.show" max-width="80%">
      <v-card>
        <v-img :src="imageDialog.src" contain></v-img>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

interface PostDetailData {
  id: number;
  targetUserId: number;
  userId: number;
  contents: string;
  visibleOption: string;
  type: string;
  likeCount: number;
  commentCount: number;
  updated: string;
  created: string;
  media: Array<{ url: string; type: string }>;
  likeStatus: boolean;
  plan: any;
}

interface Comment {
  commentId: number;
  userId: number;
  replyCount: number;
  contents: string;
  created: string;
  updated: string;
  mentionedUserList: any[];
}

interface CommentsResponse {
  postId: number;
  comments: Comment[];
  offset: number;
}

@Component
export default class PostDetail extends Vue {
  @Prop({ required: true }) readonly postId!: number;
  @Prop({ required: true }) readonly currentUserId!: number;

  // 상태 관리
  public loading: boolean = false;
  public loadingComments: boolean = false;
  public loadingMoreComments: boolean = false;
  public error: string = '';
  public postDetail: PostDetailData | null = null;
  public comments: Comment[] = [];
  public hasMoreComments: boolean = true;
  public commentsOffset: number = 0;
  public submittingComment: boolean = false;
  public newComment: string = '';
  public repliesLoading: { [commentId: number]: boolean } = {};
  public repliesExpanded: { [commentId: number]: boolean } = {};
  public replies: { [commentId: number]: Comment[] } = {};

  // 사용자 정보 캐시
  private userCache: Map<number, { nickname: string; profile_url: string }> = new Map();

  // 이미지 다이얼로그
  public imageDialog = {
    show: false,
    src: ''
  };

  // 컴포넌트가 마운트되면 데이터 로드
  mounted() {
    this.loadPostDetail();
  }

  // 게시물 상세 정보 로드
  public async loadPostDetail() {
    if (!this.postId) return;

    this.loading = true;
    this.error = '';

    try {
      const url = `https://kr-gw.spooncast.net/posts/${this.postId}`;
      console.log('게시물 상세 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json() as PostDetailData;
      console.log('게시물 상세 응답:', data);
      
      this.postDetail = data;

      // 댓글 로드
      await this.loadComments();
      
      // 작성자 정보 로드
      await this.loadUserInfo(data.userId);
    } catch (error) {
      console.error('게시물 상세 로드 중 오류:', error);
      this.error = '게시물을 불러오는 중 오류가 발생했습니다';
    } finally {
      this.loading = false;
    }
  }

  // 댓글 목록 로드
  public async loadComments() {
    if (!this.postId) return;

    this.loadingComments = true;

    try {
      const url = `https://kr-gw.spooncast.net/posts/${this.postId}/comments`;
      console.log('댓글 목록 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json() as CommentsResponse;
      console.log('댓글 목록 응답:', data);
      
      this.comments = data.comments || [];
      this.commentsOffset = data.offset || 0;
      this.hasMoreComments = this.comments.length > 0;

      // 댓글 작성자 정보 로드
      await this.loadCommentsUsersInfo(this.comments);
    } catch (error) {
      console.error('댓글 목록 로드 중 오류:', error);
    } finally {
      this.loadingComments = false;
    }
  }

  // 더 많은 댓글 로드
  public async loadMoreComments() {
    if (!this.postId || !this.hasMoreComments || this.loadingMoreComments) return;

    this.loadingMoreComments = true;

    try {
      const url = `https://kr-gw.spooncast.net/posts/${this.postId}/comments?offset=${this.commentsOffset}`;
      console.log('더 많은 댓글 로드 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json() as CommentsResponse;
      const newComments = data.comments || [];
      
      if (newComments.length > 0) {
        this.comments.push(...newComments);
        this.commentsOffset = data.offset || 0;
        
        // 새 댓글 작성자 정보 로드
        await this.loadCommentsUsersInfo(newComments);
      } else {
        this.hasMoreComments = false;
      }
    } catch (error) {
      console.error('더 많은 댓글 로드 중 오류:', error);
    } finally {
      this.loadingMoreComments = false;
    }
  }

  // 댓글 작성
  public async submitComment() {
    if (!this.newComment.trim() || !this.postId) return;

    this.submittingComment = true;

    try {
      const url = `https://kr-gw.spooncast.net/posts/${this.postId}/comments`;
      console.log('댓글 작성 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Content-Type': 'application/json',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
        return;
      }

      const response = await fetch(url, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify({
          contents: this.newComment.trim()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log('댓글 작성 성공');
      
      // 댓글 입력창 초기화
      this.newComment = '';
      
      // 댓글 목록 새로고침
      this.comments = [];
      this.commentsOffset = 0;
      this.hasMoreComments = true;
      await this.loadComments();
      
      // 게시물 댓글 수 업데이트
      if (this.postDetail) {
        this.postDetail.commentCount++;
      }
    } catch (error) {
      console.error('댓글 작성 중 오류:', error);
    } finally {
      this.submittingComment = false;
    }
  }

  // 사용자 정보 로드
  private async loadUserInfo(userId: number) {
    if (this.userCache.has(userId)) return;

    try {
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      }

      const url = `https://kr-api.spooncast.net/users/meta/?user_ids=${userId}&include_current_live=false`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) return;

      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        const userInfo = data.results[0];
        this.userCache.set(userId, {
          nickname: userInfo.nickname || `User${userId}`,
          profile_url: userInfo.profile_url || 'https://via.placeholder.com/40x40?text=User'
        });
      }
    } catch (error) {
      console.error('사용자 정보 로드 중 오류:', error);
    }
  }

  // 댓글 작성자들 정보 일괄 로드
  private async loadCommentsUsersInfo(comments: Comment[]) {
    const userIds = Array.from(new Set(comments.map(comment => comment.userId)));
    
    if (userIds.length === 0) return;

    try {
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      }

      const userIdsParam = userIds.join(',');
      const url = `https://kr-api.spooncast.net/users/meta/?user_ids=${userIdsParam}&include_current_live=false`;

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) return;

      const data = await response.json();

      if (data.results && Array.isArray(data.results)) {
        data.results.forEach((userInfo: any) => {
          this.userCache.set(userInfo.id, {
            nickname: userInfo.nickname || `User${userInfo.id}`,
            profile_url: userInfo.profile_url || 'https://via.placeholder.com/40x40?text=User'
          });
        });
      }
    } catch (error) {
      console.error('댓글 작성자 정보 로드 중 오류:', error);
    }
  }

  // 좋아요 토글
  private async toggleLike() {
    if (!this.postDetail) return;

    // 현재 상태 저장 (실패시 복원용)
    const originalLikeStatus = this.postDetail.likeStatus;
    const originalLikeCount = this.postDetail.likeCount;
    
    // UI 즉시 업데이트
    this.postDetail.likeStatus = !this.postDetail.likeStatus;
    if (this.postDetail.likeStatus) {
      this.postDetail.likeCount++;
    } else {
      this.postDetail.likeCount--;
    }

    try {
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Content-Length': '0',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
        // 원래 상태로 복원
        this.postDetail.likeStatus = originalLikeStatus;
        this.postDetail.likeCount = originalLikeCount;
        return;
      }

      const url = `https://kr-gw.spooncast.net/posts/${this.postDetail.id}/like`;
      const method = this.postDetail.likeStatus ? 'POST' : 'DELETE';

      const response = await fetch(url, {
        method,
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log(`좋아요 ${this.postDetail.likeStatus ? '추가' : '취소'} 성공`);
    } catch (error) {
      console.error('좋아요 토글 중 오류:', error);
      
      // 실패시 원래 상태로 복원
      this.postDetail.likeStatus = originalLikeStatus;
      this.postDetail.likeCount = originalLikeCount;
    }
  }

  // 프로필 URL 및 닉네임 가져오기
  private getAuthorProfileUrl(): string {
    if (!this.postDetail) return 'https://via.placeholder.com/40x40?text=User';
    
    const cached = this.userCache.get(this.postDetail.userId);
    if (cached) {
      return cached.profile_url;
    }
    
    return 'https://via.placeholder.com/40x40?text=User';
  }

  private getAuthorNickname(): string {
    if (!this.postDetail) return 'Unknown';
    
    const cached = this.userCache.get(this.postDetail.userId);
    if (cached) {
      return cached.nickname;
    }
    
    return `User${this.postDetail.userId}`;
  }

  private getCommentAuthorProfileUrl(comment: Comment): string {
    const cached = this.userCache.get(comment.userId);
    if (cached) {
      return cached.profile_url;
    }
    
    return 'https://via.placeholder.com/32x32?text=User';
  }

  private getCommentAuthorNickname(comment: Comment): string {
    const cached = this.userCache.get(comment.userId);
    if (cached) {
      return cached.nickname;
    }
    
    return `User${comment.userId}`;
  }

  private getCurrentUserProfileUrl(): string {
    const cached = this.userCache.get(this.currentUserId);
    if (cached) {
      return cached.profile_url;
    }
    
    return 'https://via.placeholder.com/32x32?text=User';
  }

  // 공개범위 관련 메서드
  private getVisibilityText(visibleOption: string): string {
    switch (visibleOption) {
      case 'ALL':
        return '모두볼수있어요';
      case 'ONLYFAN':
        return '팬만볼수있어요';
      case 'ONLYME':
        return '나만볼수있어요';
      default:
        return '모두볼수있어요';
    }
  }

  private getVisibilityColor(visibleOption: string): string {
    switch (visibleOption) {
      case 'ALL':
        return 'green';
      case 'ONLYFAN':
        return 'purple';
      case 'ONLYME':
        return 'orange';
      default:
        return 'green';
    }
  }

  // 유틸리티 메서드들
  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        if (diffHours === 0) {
          const diffMinutes = Math.floor(diffMs / (1000 * 60));
          return `${diffMinutes}분 전`;
        }
        return `${diffHours}시간 전`;
      } else if (diffDays < 7) {
        return `${diffDays}일 전`;
      } else {
        return date.toLocaleDateString('ko-KR', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return dateString;
    }
  }

  private formatContent(content: string): string {
    if (!content) return '';
    return content.replace(/\n/g, '<br>');
  }

  private openImageDialog(src: string) {
    this.imageDialog.src = src;
    this.imageDialog.show = true;
  }

  // 스크롤 이벤트 핸들러
  private onScroll() {
    const container = this.$refs.commentsContainer as HTMLElement;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const scrollThreshold = scrollHeight - (scrollTop + clientHeight);

    if (scrollThreshold <= 100 && this.hasMoreComments && !this.loadingMoreComments) {
      this.loadMoreComments();
    }
  }

  // 스크롤 throttling
  private scrollThrottleTimer: number | null = null;

  private throttledScroll() {
    if (this.scrollThrottleTimer) return;
    
    this.scrollThrottleTimer = window.setTimeout(() => {
      this.onScroll();
      this.scrollThrottleTimer = null;
    }, 200);
  }

  // 컴포넌트 파괴 시 타이머 정리
  beforeDestroy() {
    if (this.scrollThrottleTimer) {
      clearTimeout(this.scrollThrottleTimer);
    }
  }

  // 답글 토글
  private async toggleReplies(comment: Comment) {
    if (!this.postId || !comment.commentId) return;

    // 이미 답글이 로드되어 있고 펼쳐져 있으면 접기
    if (this.repliesExpanded[comment.commentId] && this.replies[comment.commentId]) {
      this.$set(this.repliesExpanded, comment.commentId, false);
      return;
    }

    // 답글이 로드되어 있지 않으면 로드
    if (!this.replies[comment.commentId]) {
      this.$set(this.repliesLoading, comment.commentId, true);

      try {
        const url = `https://kr-gw.spooncast.net/posts/${this.postId}/comments/${comment.commentId}/replies`;
        console.log('답글 목록 API 요청 URL:', url);
        
        // 인증 헤더 설정
        const headers: any = {
          'Accept': 'application/json',
          'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
          'Origin': 'https://www.spooncast.net',
          'Priority': 'u=1, i',
          'Referer': 'https://www.spooncast.net/',
          'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-site',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        };

        // 스푼캐스트 인증 토큰 추가
        const auth = (this as any).$cfg?.get('auth');
        if (auth && auth.spoon && auth.spoon.token) {
          headers['Authorization'] = `Bearer ${auth.spoon.token}`;
        } else {
          console.warn('스푼캐스트 인증 토큰이 없습니다.');
          return;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json() as CommentsResponse;
        console.log('답글 목록 응답:', data);
        
        // 답글 데이터 저장 (응답 구조는 댓글과 동일)
        this.$set(this.replies, comment.commentId, data.comments || []);
        
        // 답글 작성자 정보 로드
        await this.loadCommentsUsersInfo(data.comments || []);
      } catch (error) {
        console.error('답글 목록 로드 중 오류:', error);
      } finally {
        this.$set(this.repliesLoading, comment.commentId, false);
      }
    }

    // 답글 펼치기
    this.$set(this.repliesExpanded, comment.commentId, true);
  }
}
</script>

<style scoped>
.post-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative; /* 절대 위치 지정을 위한 상대 위치 설정 */
}

.post-detail-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
}

.post-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 80px; /* 하단 댓글 입력창 공간 확보 */
}

.post-info {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
}

.post-content {
  word-break: break-word;
  line-height: 1.4;
}

.post-media {
  margin-top: 8px;
}

.media-item {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.post-stats {
  margin-top: 8px;
}

.like-button {
  cursor: pointer;
  transition: transform 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
}

.like-button:hover {
  transform: scale(1.05);
  background-color: rgba(255, 0, 0, 0.1);
}

.like-icon {
  transition: color 0.2s ease;
}

.comments-section {
  margin-top: 16px;
}

.comments-list {
  max-height: 400px;
  overflow-y: auto;
}

.comment-item {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
}

.comment-content {
  word-break: break-word;
  line-height: 1.4;
}

.reply-count {
  cursor: pointer;
}

.reply-count:hover {
  text-decoration: underline;
}

.reply-actions {
  cursor: pointer;
}

.replies-container {
  margin-left: 20px;
}

.reply-item {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
}

.reply-header {
  font-weight: bold;
}

.reply-content {
  word-break: break-word;
  line-height: 1.4;
}

/* 하단 고정 댓글 입력창 - 다이얼로그 내부에 위치 */
.comment-input-fixed {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: white;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.comment-input-container {
  display: flex;
  align-items: center;
  max-width: 100%;
}

.comment-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 25px; /* 완전한 캡슐 모양 */
  padding: 6px 12px 6px 16px;
  margin-left: 8px;
  min-height: 44px;
}

/* 댓글 입력 필드 캡슐 모양 */
.comment-input-field {
  background-color: transparent !important;
  flex: 1;
}

.comment-input-field >>> .v-input__control {
  min-height: 32px !important;
}

.comment-input-field >>> .v-input__slot {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding-left: 0 !important;
  padding-right: 8px !important;
  margin-bottom: 0 !important;
}

.comment-input-field >>> .v-text-field__slot {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.comment-input-field >>> .v-text-field__slot input {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
  font-size: 14px;
  padding: 8px 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.comment-input-field >>> fieldset {
  display: none !important;
}

.comment-input-field >>> .v-input__append-inner {
  margin-top: 0 !important;
  padding-left: 0 !important;
}

/* 등록 버튼 캡슐 모양 */
.comment-submit-btn {
  border-radius: 18px !important; /* 더 둥근 캡슐 모양 */
  min-width: 50px !important;
  height: 32px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  margin-left: 6px !important;
  box-shadow: none !important;
  letter-spacing: -0.2px;
}

.comment-submit-btn.v-btn {
  padding: 0 16px !important;
}

.comment-submit-btn.v-btn--disabled {
  background-color: #e0e0e0 !important;
  color: #9e9e9e !important;
}

.comment-submit-btn:not(.v-btn--disabled) {
  background-color: #9c27b0 !important;
  color: white !important;
}

.comment-submit-btn:not(.v-btn--disabled):hover {
  background-color: #7b1fa2 !important;
}

/* 스크롤바 스타일 */
.post-detail-content::-webkit-scrollbar,
.comments-list::-webkit-scrollbar {
  width: 6px;
}

.post-detail-content::-webkit-scrollbar-track,
.comments-list::-webkit-scrollbar-track {
  background: transparent;
}

.post-detail-content::-webkit-scrollbar-thumb,
.comments-list::-webkit-scrollbar-thumb {
  background-color: rgba(156, 39, 176, 0.3);
  border-radius: 10px;
}

.post-detail-content::-webkit-scrollbar-thumb:hover,
.comments-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 39, 176, 0.5);
}
</style> 