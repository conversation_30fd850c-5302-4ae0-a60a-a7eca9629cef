/*
 * cfg-lite-ipc.ts
 * Created on Sat Nov 28 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
 */

const { ipc<PERSON><PERSON><PERSON> } = window.require('electron');
const path = window.require('path');

export default class CfgLite {
	private readonly evtName: string = 'cfg-lite';
	private cache: Map<string, {value: any, timestamp: number}> = new Map();
	private readonly CACHE_TTL = 10000; // 일반 캐시 유효 시간 (10초)
	private readonly BUNDLE_CACHE_TTL = Number.MAX_SAFE_INTEGER; // 번들 캐시는 사실상 영구적 (앱 실행 중 유지)
	private isLogging: boolean = false; // 로깅 제어 변수

	constructor(private cfgFile: string, private privKey: string = '') {
		const ret = ipcRenderer.sendSync(this.evtName, 'new', this.cfgFile, this.privKey);
		if ( !ret ) {
			throw new Error(`Cannot open config file ${this.cfgFile},${this.privKey}`);
		}
		// 개발 환경에서만 로깅 활성화
		this.isLogging = process.env.NODE_ENV === 'development' && !this.isBundleConfig();
	}

	/**
	 * 파일 경로가 번들 폴더에 있는지 확인
	 */
	private isBundleConfig(): boolean {
		const normPath = this.cfgFile.replace(/\\/g, '/');
		return normPath.includes('/bundles/') || normPath.includes('\\bundles\\');
	}

	/**
	 * 캐시 TTL 선택 (번들 설정은 영구 캐시, 일반 설정은 임시 캐시)
	 */
	private getCacheTTL(): number {
		return this.isBundleConfig() ? this.BUNDLE_CACHE_TTL : this.CACHE_TTL;
	}

	public save(file?: string, removeBefore: boolean = false) {
		// 저장 시 캐시 무효화
		this.invalidateCache();
		return ipcRenderer.sendSync(this.evtName, 'save', this.cfgFile, file, removeBefore);
	}

	public get(key?: string) {
		// 캐시 키 (키가 없으면 파일 전체를 의미)
		const cacheKey = key || '_entire_file_';
		
		// 캐시에서 확인
		const now = Date.now();
		const cachedItem = this.cache.get(cacheKey);
		const cacheTTL = this.getCacheTTL();
		
		if (cachedItem && (now - cachedItem.timestamp < cacheTTL)) {
			// 로깅 제거 (캐시 히트 시 로그 출력하지 않음)
			return cachedItem.value;
		}
		
		// 캐시 미스: 실제 값 가져오기
		if (this.isLogging) {
			console.log(`[cfg-cache] Loading config: ${this.cfgFile}, key=${key || 'all'}`);
		}
		const value = ipcRenderer.sendSync(this.evtName, 'get', this.cfgFile, key);
		
		// 캐시 업데이트
		this.cache.set(cacheKey, {
			value,
			timestamp: now
		});
		
		return value;
	}

	public set(key: string, value: any) {
		if ( !key ) {
			throw Error('key is not valid');
		}
		// 설정 변경 시 캐시 무효화
		this.invalidateCache();
		return ipcRenderer.sendSync(this.evtName, 'set', this.cfgFile, key, value);
	}

	public merge(key: string, value: any) {
		if ( !key ) {
			throw Error('key is not valid');
		}
		// 설정 변경 시 캐시 무효화
		this.invalidateCache();
		return ipcRenderer.sendSync(this.evtName, 'merge', this.cfgFile, key, value);
	}

	public delete(key: string) {
		if ( !key ) {
			throw Error('key is not valid');
		}
		// 설정 변경 시 캐시 무효화
		this.invalidateCache();
		return ipcRenderer.sendSync(this.evtName, 'delete', this.cfgFile, key);
	}

	public deleteAll() {
		// 설정 변경 시 캐시 무효화
		this.invalidateCache();
		return ipcRenderer.sendSync(this.evtName, 'deleteAll', this.cfgFile);
	}
	
	// 캐시 무효화 메소드
	private invalidateCache() {
		// 번들 설정 파일인 경우 캐시 유지 (변경 사항이 있더라도)
		if (this.isBundleConfig()) {
			if (this.isLogging) {
				console.log(`[cfg-cache] Keeping bundle cache for: ${this.cfgFile}`);
			}
			return;
		}
		
		// 일반 설정 파일은 캐시 초기화
		this.cache.clear();
	}
}
