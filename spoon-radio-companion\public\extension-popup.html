<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tamm 스푼 연동</title>
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            background: #fafafa;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #7c3aed;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            font-size: 14px;
        }
        .content {
            text-align: center;
        }
        .hidden {
            display: none;
        }
        .btn {
            background: #7c3aed;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #6d28d9;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn.secondary {
            background: #e5e7eb;
            color: #374151;
        }
        .btn.secondary:hover {
            background: #d1d5db;
        }
        .user-info {
            background: white;
            padding: 16px;
            border-radius: 12px;
            margin: 16px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin: 0 auto 12px;
            display: block;
            border: 2px solid #7c3aed;
        }
        .user-name {
            font-weight: 600;
            margin-bottom: 4px;
            color: #1f2937;
        }
        .user-tag {
            color: #666;
            font-size: 14px;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
        }
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
            border-left: 4px solid #7c3aed;
        }
        .step-guide {
            background: white;
            padding: 16px;
            border-radius: 12px;
            margin: 16px 0;
            text-align: left;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .step-guide h4 {
            margin: 0 0 12px 0;
            color: #7c3aed;
            font-size: 16px;
        }
        .step-guide ol {
            margin: 0;
            padding-left: 20px;
        }
        .step-guide li {
            margin: 8px 0;
            color: #374151;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🥄 Tamm</div>
        <div class="subtitle">스푼캐스트 연동</div>
    </div>

    <div class="content">
        <!-- 잘못된 페이지 -->
        <div id="wrong-page" class="hidden">
            <div class="error">
                스푼캐스트 페이지에서만 사용할 수 있습니다.
            </div>
            <div class="step-guide">
                <h4>📋 사용 방법</h4>
                <ol>
                    <li>스푼캐스트에 접속하세요</li>
                    <li>로그인을 완료하세요</li>
                    <li>다시 이 확장프로그램을 클릭하세요</li>
                </ol>
            </div>
            <button class="btn" onclick="window.open('https://www.spooncast.net/kr', '_blank')">
                스푼캐스트로 이동
            </button>
        </div>

        <!-- 로그인 안됨 -->
        <div id="not-login" class="hidden">
            <div class="error">
                스푼캐스트에 로그인이 필요합니다.
            </div>
            <div class="info">
                로그인 후 다시 이 확장프로그램을 클릭해주세요.
            </div>
            <button class="btn" onclick="window.open('https://www.spooncast.net/kr/signin', '_blank')">
                스푼캐스트 로그인
            </button>
        </div>

        <!-- 로그인됨 -->
        <div id="login-page" class="hidden">
            <div class="user-info">
                <img id="user-avatar" class="user-avatar" src="" alt="프로필">
                <div id="user-name" class="user-name"></div>
                <div id="user-tag" class="user-tag"></div>
            </div>
            
            <div class="info">
                아래 버튼을 클릭하면 Tamm 앱에 자동으로 연동됩니다.
            </div>
            
            <button id="login-btn" class="btn">
                🔗 TAMM에 연동하기
            </button>
            
            <div id="success-message" class="success hidden">
                ✅ 연동이 완료되었습니다!<br>
                잠시 후 자동으로 닫힙니다.
            </div>
        </div>
    </div>

    <script src="extension-popup.js"></script>
</body>
</html> 