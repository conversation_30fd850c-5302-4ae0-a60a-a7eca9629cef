@charset "UTF-8";
.slick-loading .slick-list {
    background: #fff url(/node_modules/slick-carousel/slick/ajax-loader.c5cd7f53.gif) 50% no-repeat
}

@font-face {
    font-family: slick;
    font-weight: 400;
    font-style: normal;
    src: url(/src/images/slick.ced611da.eot);
    src: url(/src/images/slick.ced611da.eot?#iefix) format("embedded-opentype"), url(/src/images/slick.b7c9e1e4.woff) format("woff"), url(/src/images/slick.d41f55a7.ttf) format("truetype"), url(/node_modules/slick-carousel/slick/fonts/slick.f97e3bbf.svg#slick) format("svg")
}

.slick-next,
.slick-prev {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 50%;
    display: block;
    width: 20px;
    height: 20px;
    padding: 0;
    transform: translateY(-50%);
    cursor: pointer;
    border: none
}

.slick-next,
.slick-next:focus,
.slick-next:hover,
.slick-prev,
.slick-prev:focus,
.slick-prev:hover {
    color: transparent;
    outline: none;
    background: transparent
}

.slick-next:focus:before,
.slick-next:hover:before,
.slick-prev:focus:before,
.slick-prev:hover:before {
    opacity: 1
}

.slick-next.slick-disabled:before,
.slick-prev.slick-disabled:before {
    opacity: .25
}

.slick-next:before,
.slick-prev:before {
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: .75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.slick-prev {
    left: -25px
}

[dir=rtl] .slick-prev {
    right: -25px;
    left: auto
}

.slick-prev:before {
    content: "←"
}

[dir=rtl] .slick-prev:before {
    content: "→"
}

.slick-next {
    right: -25px
}

[dir=rtl] .slick-next {
    right: auto;
    left: -25px
}

.slick-next:before {
    content: "→"
}

[dir=rtl] .slick-next:before {
    content: "←"
}

.slick-dotted.slick-slider {
    margin-bottom: 30px
}

.slick-dots {
    position: absolute;
    bottom: -25px;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: center
}

.slick-dots li {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    padding: 0
}

.slick-dots li,
.slick-dots li button {
    width: 20px;
    height: 20px;
    cursor: pointer
}

.slick-dots li button {
    font-size: 0;
    line-height: 0;
    display: block;
    padding: 5px;
    color: transparent;
    border: 0;
    outline: none;
    background: transparent
}

.slick-dots li button:focus,
.slick-dots li button:hover {
    outline: none
}

.slick-dots li button:focus:before,
.slick-dots li button:hover:before {
    opacity: 1
}

.slick-dots li button:before {
    font-family: slick;
    font-size: 6px;
    line-height: 20px;
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    content: "•";
    text-align: center;
    opacity: .25;
    color: #000;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.slick-dots li.slick-active button:before {
    opacity: .75;
    color: #000
}

.slick-slider {
    box-sizing: border-box;
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -khtml-user-select: none;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent
}

.slick-list,
.slick-slider {
    position: relative;
    display: block
}

.slick-list {
    overflow: hidden;
    margin: 0;
    padding: 0
}

.slick-list:focus {
    outline: none
}

.slick-list.dragging {
    cursor: pointer;
    cursor: hand
}

.slick-slider .slick-list,
.slick-slider .slick-track {
    transform: translateZ(0)
}

.slick-track {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.slick-track:after,
.slick-track:before {
    display: table;
    content: ""
}

.slick-track:after {
    clear: both
}

.slick-loading .slick-track {
    visibility: hidden
}

.slick-slide {
    display: none;
    float: left;
    height: 100%;
    min-height: 1px
}

[dir=rtl] .slick-slide {
    float: right
}

.slick-slide img {
    display: block
}

.slick-slide.slick-loading img {
    display: none
}

.slick-slide.dragging img {
    pointer-events: none
}

.slick-initialized .slick-slide {
    display: block
}

.slick-loading .slick-slide {
    visibility: hidden
}

.slick-vertical .slick-slide {
    display: block;
    height: auto;
    border: 1px solid transparent
}

.slick-arrow.slick-hidden {
    display: none
}

.cast-list-item-container {
    position: relative
}

.cast-list-item-container .thumbnail {
    position: relative;
    overflow: hidden;
    height: 112px;
    border: 1px solid #e6e6e6;
    border-radius: 16px;
    box-sizing: border-box
}

.cast-list-item-container .thumbnail .bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    transform: scale(1.1);
    transition: opacity .35s, transform .35s
}

.cast-list-item-container .thumbnail .live-badge {
    display: inline-block;
    position: absolute;
    width: 60px;
    height: 18px;
    bottom: 6px;
    left: 6px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    border-radius: 9px;
    background-color: #b3b3b3;
    color: #fff
}

.cast-list-item-container .thumbnail .time-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background-color: rgba(0, 0, 0, .5);
    padding: 3px 4px;
    border-radius: 6px;
    font-size: 10px;
    line-height: 10px;
    color: #fff
}

.cast-list-item-container .thumbnail a {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .35s;
    z-index: 1
}

.cast-list-item-container .thumbnail a .icon {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    background: url(/src/images/list/list_item_headphone.a102c176.png) 50% 50% no-repeat;
    background-size: 34px 34px;
    transform: scale(1.8);
    transition: opacity .35s, transform .35s
}

.cast-list-item-container .thumbnail:hover .bg {
    transform: scale(1)
}

.cast-list-item-container .thumbnail:hover a {
    opacity: 1
}

.cast-list-item-container .thumbnail:hover a .icon {
    opacity: 1;
    transform: scale(1)
}

.cast-list-item-container .thumbnail.loaded {
    background: none
}

.cast-list-item-container .cast-info {
    position: relative
}

.cast-list-item-container .cast-info .rank {
    margin-top: 6px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #ff4100
}

.cast-list-item-container .cast-info .title {
    margin-top: 6px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #4d4d4d;
    -webkit-line-clamp: 2
}

.cast-list-item-container .cast-info .name {
    display: flex;
    align-items: center;
    margin-top: 6px;
    font-size: 14px;
    line-height: 18px;
    color: grey
}

.cast-list-item-container .cast-info .name .verified {
    width: 10px;
    margin: 0 4px
}

.cast-list-item-container .cast-info .name a:hover,
.cast-list-item-container .cast-info .title a:hover {
    text-decoration: underline
}

.cast-list-item-container .cast-info .count-info {
    display: flex;
    margin-top: 6px
}

.cast-list-item-container .cast-info .count-info .count-info-item {
    display: flex;
    align-items: center;
    margin-left: 12px
}

.cast-list-item-container .cast-info .count-info .count-info-item p {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 16px;
    color: #b3b3b3;
    cursor: default
}

.cast-list-item-container .cast-info .count-info .count-info-item p img {
    height: 16px;
    margin-right: 4px
}

.cast-list-item-container .cast-info .count-info .count-info-item:first-child {
    margin-left: 0
}

.cast-list-item-container .cast-info .tags {
    overflow: hidden;
    max-height: 24px;
    margin-top: 8px
}

.cast-list-item-container .cast-info .tags ul {
    display: flex;
    flex-wrap: wrap
}

.cast-list-item-container .cast-info .tags ul li {
    display: flex;
    flex: none;
    margin-left: 6px
}

.cast-list-item-container .cast-info .tags ul li a {
    position: relative;
    padding: 4px 6px;
    font-size: 12px;
    line-height: 16px;
    border-radius: 12px;
    color: #333;
    background-color: #f2f2f2
}

.cast-list-item-container .cast-info .tags ul li a:hover {
    background-color: #e8e8e8
}

.cast-list-item-container .cast-info .tags ul li a:active {
    background-color: #d5d5d5
}

.cast-list-item-container .cast-info .tags ul li a.change-color {
    background-color: #e6e6e6
}

.cast-list-item-container .cast-info .tags ul li a.change-color:hover {
    background-color: #dcdcdc
}

.cast-list-item-container .cast-info .tags ul li:first-child {
    margin-left: 0
}

.cast-list-item-container .created-date p {
    position: absolute;
    bottom: 4px;
    right: 16px;
    font-size: 12px;
    line-height: 16px;
    color: #b3b3b3
}

.cast-list-item-container.detail,
.cast-list-item-container.profile,
.cast-list-item-container.search,
.cast-list-item-container.storage {
    display: flex
}

.cast-list-item-container.detail .cast-info,
.cast-list-item-container.profile .cast-info,
.cast-list-item-container.search .cast-info,
.cast-list-item-container.storage .cast-info {
    flex: 1 1;
    padding: 0 12px
}

.cast-list-item-container.detail.storage .thumbnail,
.cast-list-item-container.detail .thumbnail,
.cast-list-item-container.profile .thumbnail,
.cast-list-item-container.search .thumbnail {
    flex: none;
    width: 166px;
    height: 94px
}

.cast-list-item-container.storage .thumbnail {
    width: 144px;
    height: 80px
}

.cast-list-item-container.square .thumbnail {
    height: 194px;
    border: none;
    box-shadow: 0 6px 8px 0 rgba(0, 0, 0, .2);
    border-radius: 16px
}

.cast-list-item-container.square .shadow-cover {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: transparent
}

.cast-list-item-container.square .cast-info .title {
    margin-top: 12px
}

.cast-list-item-container.square .cast-info .count-info {
    display: none
}

.app-container.rtl .cast-list-item-container .thumbnail .live-badge {
    left: auto;
    right: 6px
}

.app-container.rtl .cast-list-item-container .thumbnail .time-badge {
    left: auto;
    right: 8px
}

.app-container.rtl .cast-list-item-container .cast-info .count-info .count-info-item {
    margin-left: 0;
    margin-right: 12px
}

.app-container.rtl .cast-list-item-container .cast-info .count-info .count-info-item p img {
    margin-left: 4px;
    margin-right: 0
}

.app-container.rtl .cast-list-item-container .cast-info .count-info .count-info-item:first-child {
    margin-right: 0
}

.app-container.rtl .cast-list-item-container .tags ul li {
    margin-left: 0;
    margin-right: 6px
}

.app-container.rtl .cast-list-item-container .tags ul li:first-child {
    margin-right: 0
}

.app-container.rtl .cast-list-item-container .created-date p {
    left: 16px;
    right: auto
}

@media screen and (max-width:767px) {
    .cast-list-item-container {
        display: flex
    }
    .cast-list-item-container.detail .thumbnail,
    .cast-list-item-container.profile .thumbnail,
    .cast-list-item-container.storage .thumbnail,
    .cast-list-item-container .thumbnail {
        flex: none;
        width: 88px;
        height: 88px;
        border: 0;
        border-radius: 12px
    }
    .cast-list-item-container .thumbnail .live-badge {
        width: 45px;
        height: 16px;
        font-size: 10px;
        line-height: 16px;
        border-radius: 2px
    }
    .cast-list-item-container .thumbnail .time-badge {
        top: 6px;
        left: 6px
    }
    .cast-list-item-container .cast-info,
    .cast-list-item-container.detail .cast-info,
    .cast-list-item-container.profile .cast-info,
    .cast-list-item-container.storage .cast-info {
        padding: 0 10px;
        flex: 1 1
    }
    .cast-list-item-container .cast-info .rank {
        margin-top: 4px;
        font-size: 12px;
        line-height: 16px
    }
    .cast-list-item-container .cast-info .title {
        margin-top: 14px;
        -webkit-line-clamp: 1
    }
    .cast-list-item-container .cast-info .name {
        margin-top: 2px;
        font-size: 12px;
        line-height: 16px
    }
    .cast-list-item-container .cast-info .count-info {
        margin-top: 8px
    }
    .cast-list-item-container .cast-info .tags {
        display: none
    }
    .cast-list-item-container .created-date {
        display: flex;
        align-items: center
    }
    .cast-list-item-container .created-date p {
        position: relative;
        left: auto;
        right: 4px;
        bottom: auto;
        font-size: 10px
    }
    .cast-list-item-container.top .cast-info .title {
        margin-top: 4px
    }
    .cast-list-item-container.square {
        height: 100%
    }
    .cast-list-item-container.square .thumbnail {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        border: 0;
        border-radius: 16px;
        box-shadow: none
    }
    .cast-list-item-container.square .thumbnail .shadow-cover {
        background: rgba(0, 0, 0, .3)
    }
    .cast-list-item-container.square .thumbnail .time-badge {
        top: 8px;
        left: 8px
    }
    .cast-list-item-container.square .thumbnail a {
        z-index: 2
    }
    .cast-list-item-container.square .cast-info {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: absolute;
        width: calc(100% - 3px);
        height: 100%;
        top: 0;
        left: 3px;
        padding: 0;
        margin: 0
    }
    .cast-list-item-container.square .cast-info p {
        color: #fff
    }
    .cast-list-item-container.square .cast-info .title {
        margin: 0 8px;
        -webkit-line-clamp: 2;
        z-index: 1
    }
    .cast-list-item-container.square .cast-info .name {
        margin: 6px 8px 12px;
        z-index: 1
    }
    .cast-list-item-container.square .cast-info .count-info {
        display: none
    }
    .cast-list-item-container.search .thumbnail {
        background-color: rgba(0, 0, 0, .3);
        width: 100%;
        min-height: calc(50vw - 12px)
    }
    .cast-list-item-container.search .cast-info {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        padding: 0;
        margin: 0
    }
    .cast-list-item-container.search .cast-info p {
        color: #fff
    }
    .cast-list-item-container.search .cast-info .title {
        margin: 0 8px;
        -webkit-line-clamp: 2;
        z-index: 1
    }
    .cast-list-item-container.search .cast-info .name {
        color: #fff;
        margin: 4px 8px 12px;
        z-index: 1
    }
    .cast-list-item-container.search .cast-info .count-info {
        display: none
    }
    .app-container.rtl .cast-list-item-container .thumbnail .time-badge {
        left: auto;
        right: 6px
    }
    .app-container.rtl .cast-list-item-container .created-date p {
        left: 4px;
        right: auto
    }
}

.live-call-list-item-container {
    display: flex;
    flex: 1 1
}

.live-call-list-item-container .thumbnail {
    display: flex;
    flex: none;
    position: relative;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-size: 50px 50px
}

.live-call-list-item-container .thumbnail div {
    display: block;
    flex: 1 1;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover
}

.live-call-list-item-container .thumbnail.loaded {
    background: none
}

.live-call-list-item-container .thumbnail.live-list {
    width: 36px;
    height: 36px;
    background-size: 36px 36px
}

.live-call-list-item-container .thumbnail.live-list div {
    border: 1px solid #fff
}

@media screen and (max-width:767px) {
    .live-call-list-item-container .thumbnail {
        width: 36px;
        height: 36px
    }
    .live-call-list-item-container .thumbnail.live-list {
        width: 28px;
        height: 28px;
        padding: 0;
        background-size: 28px 28px
    }
}

.live-list-item-container {
    position: relative;
    overflow: hidden;
    z-index: 0
}

.live-list-item-container>.thumbnail {
    position: relative;
    overflow: hidden;
    height: 112px;
    border: 1px solid #e6e6e6;
    border-radius: 16px;
    box-sizing: border-box
}

.live-list-item-container>.thumbnail .bg {
    width: 100%;
    height: 100%;
    transform: scale(1.1);
    transition: opacity .35s, transform .35s
}

.live-list-item-container>.thumbnail .live-badge {
    position: absolute;
    top: 8px;
    left: 8px
}

.live-list-item-container>.thumbnail .badge.live-call {
    display: flex;
    position: absolute;
    bottom: 8px;
    right: 8px
}

.live-list-item-container>.thumbnail .badge.live-call>ul {
    display: flex;
    flex-wrap: nowrap
}

.live-list-item-container>.thumbnail .badge.live-call>ul>li {
    position: relative;
    margin-left: -8px
}

.live-list-item-container>.thumbnail .badge.live-call>ul>li>.count {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-size: 12px;
    border-radius: 50%;
    color: #fff;
    background-color: rgba(0, 0, 0, .5)
}

.live-list-item-container>.thumbnail .badge.live-call>ul>li:first-child {
    margin-left: 0
}

.live-list-item-container>.thumbnail a {
    border-radius: 4px;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, .3);
    transition: opacity .35s;
    z-index: 3
}

.live-list-item-container>.thumbnail a .icon {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    background: url(/src/images/list/list_item_headphone.a102c176.png) 50% 50% no-repeat;
    background-size: 34px 34px;
    transform: scale(1.8);
    transition: opacity .35s, transform .35s
}

.live-list-item-container>.thumbnail.loaded {
    background: none
}

.live-list-item-container>.thumbnail:hover .bg {
    transform: scale(1)
}

.live-list-item-container>.thumbnail:hover a {
    opacity: 1
}

.live-list-item-container>.thumbnail:hover a .icon {
    opacity: 1;
    transform: scale(1)
}

.live-list-item-container .rank {
    width: 28px;
    padding: 4px 4px 8px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px
}

.live-list-item-container .live-info .live-badge {
    display: none;
    margin-top: 4px
}

.live-list-item-container .live-info .title {
    position: relative;
    margin-top: 6px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    -webkit-line-clamp: 2
}

.live-list-item-container .live-info .title a:hover {
    text-decoration: underline
}

.live-list-item-container .live-info .name {
    display: flex;
    align-items: center;
    margin-top: 6px;
    font-size: 14px;
    line-height: 18px;
    color: grey
}

.live-list-item-container .live-info .name a:hover {
    text-decoration: underline
}

.live-list-item-container .live-info .name .verified {
    width: 10px;
    margin: 0 4px
}

.live-list-item-container .live-info .count-info {
    display: flex;
    margin-top: 6px
}

.live-list-item-container .live-info .count-info .count-info-item {
    display: flex;
    align-items: center
}

.live-list-item-container .live-info .count-info .count-info-item p {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    font-size: 12px;
    line-height: 16px;
    color: #b3b3b3;
    cursor: default
}

.live-list-item-container .live-info .count-info .count-info-item p img {
    height: 16px;
    margin-right: 4px
}

.live-list-item-container .live-info .count-info .count-info-item:first-child {
    margin-right: 12px
}

.live-list-item-container .tags {
    overflow: hidden;
    max-height: 24px;
    margin-top: 8px
}

.live-list-item-container .tags ul {
    display: flex;
    flex-wrap: wrap
}

.live-list-item-container .tags ul li {
    display: flex;
    flex: none;
    max-width: 100%;
    margin-left: 6px
}

.live-list-item-container .tags ul li a {
    position: relative;
    padding: 0 6px;
    font-size: 12px;
    line-height: 24px;
    border-radius: 12px;
    color: #333;
    background-color: #f2f2f2
}

.live-list-item-container .tags ul li a:hover {
    background-color: #e8e8e8
}

.live-list-item-container .tags ul li a:active {
    background-color: #d5d5d5
}

.live-list-item-container .tags ul li:first-child {
    margin-left: 0
}

.live-list-item-container.search {
    display: flex
}

.live-list-item-container.search .live-info {
    flex: 1 1;
    padding: 0 12px
}

.live-list-item-container.search .live-info .title {
    margin-top: 0
}

.live-list-item-container.search>.thumbnail {
    flex: none;
    width: 166px;
    height: 94px
}

.live-list-item-container.side {
    display: flex
}

.live-list-item-container.side>.thumbnail {
    flex: none;
    width: 168px;
    height: 96px
}

.live-list-item-container.side .live-info {
    margin-left: 4px
}

.live-list-item-container.side .live-info .title {
    margin-top: 4px
}

.live-list-item-container.share {
    display: flex
}

.live-list-item-container.share>.thumbnail {
    flex: none;
    width: 144px;
    height: 88px;
    border: none;
    border-radius: 4px
}

.live-list-item-container.share .live-info {
    padding: 0 12px
}

.live-list-item-container.share .live-info .title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #fff
}

.live-list-item-container.share .live-info .name {
    margin-top: 4px;
    font-size: 13px;
    line-height: 19px;
    color: #b3b3b3
}

.live-list-item-container.event {
    display: flex
}

.live-list-item-container.event>.thumbnail {
    flex: none;
    width: 88px;
    height: 88px
}

.live-list-item-container.event>.thumbnail .live-badge {
    display: none
}

.live-list-item-container.event>.thumbnail .badge.live-call {
    bottom: 6px;
    right: 6px
}

.live-list-item-container.event>.thumbnail .badge.live-call>ul>li>.count {
    font-size: 10px
}

.live-list-item-container.event .live-info {
    flex: 1 1;
    position: relative;
    padding: 0 10px
}

.live-list-item-container.event .live-info .title {
    margin-top: 14px;
    font-size: 14px;
    line-height: 18px;
    -webkit-line-clamp: 1
}

.live-list-item-container.event .live-info .name {
    margin-top: 2px;
    font-size: 12px;
    line-height: 16px
}

.live-list-item-container.event .live-info .count-info {
    margin-top: 8px
}

.live-list-item-container.event .live-info .live-badge {
    display: flex
}

.live-list-item-container.event .live-info .live-badge+.title {
    margin-top: 4px
}

.live-list-item-container.event .tags {
    display: none
}

.live-list-item-container .list-block {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 5
}

.app-container.rtl .live-list-item-container>.thumbnail .live-badge {
    left: auto;
    right: 8px
}

.app-container.rtl .live-list-item-container>.thumbnail .badge.live-call {
    left: 8px;
    right: auto
}

.app-container.rtl .live-list-item-container>.thumbnail .badge.live-call>ul>li {
    margin-left: 0;
    margin-right: -8px
}

.app-container.rtl .live-list-item-container>.thumbnail .badge.live-call>ul>li:first-child {
    margin-right: 0
}

.app-container.rtl .live-list-item-container .live-info .count-info .count-info-item p img {
    margin-left: 4px;
    margin-right: 0
}

.app-container.rtl .live-list-item-container .live-info .count-info .count-info-item:first-child {
    margin-left: 12px;
    margin-right: 0
}

.app-container.rtl .live-list-item-container .tags ul li {
    margin-left: 0;
    margin-right: 6px
}

.app-container.rtl .live-list-item-container .tags ul li:first-child {
    margin-right: 0
}

.app-container.rtl .live-list-item-container.side .live-info {
    margin-left: 0;
    margin-right: 4px
}

@media screen and (max-width:767px) {
    .live-list-item-container {
        display: flex;
        overflow: visible
    }
    .live-list-item-container>.thumbnail {
        flex: none;
        width: 88px;
        height: 88px
    }
    .live-list-item-container>.thumbnail .live-badge {
        display: none
    }
    .live-list-item-container>.thumbnail .badge.live-call {
        bottom: 6px;
        right: 6px
    }
    .live-list-item-container>.thumbnail .badge.live-call>ul>li>.count {
        font-size: 10px
    }
    .live-list-item-container .live-info {
        flex: 1 1;
        position: relative;
        padding: 0 10px
    }
    .live-list-item-container .live-info .title {
        margin-top: 14px;
        font-size: 14px;
        line-height: 18px;
        -webkit-line-clamp: 1
    }
    .live-list-item-container .live-info .name {
        margin-top: 2px;
        font-size: 12px;
        line-height: 16px
    }
    .live-list-item-container .live-info .count-info {
        margin-top: 8px
    }
    .live-list-item-container .live-info .live-badge {
        display: flex
    }
    .live-list-item-container .live-info .live-badge+.title {
        margin-top: 4px
    }
    .live-list-item-container .tags {
        display: none
    }
    .live-list-item-container.search>.thumbnail {
        width: 100%;
        min-height: calc(50vw - 12px)
    }
    .live-list-item-container.search>.thumbnail a {
        border-radius: 6px;
        opacity: 1;
        z-index: 1
    }
    .live-list-item-container.search>.thumbnail .badge.live-call {
        display: none
    }
    .live-list-item-container.search a {
        opacity: 1
    }
    .live-list-item-container.search .live-info {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        padding: 0;
        margin: 0;
        z-index: 1
    }
    .live-list-item-container.search .live-info .live-badge {
        margin: 0 8px;
        pointer-events: none
    }
    .live-list-item-container.search .live-info .title {
        margin: 4px 8px 0;
        color: #fff
    }
    .live-list-item-container.search .live-info .name {
        margin: 4px 8px 12px;
        font-size: 12px;
        color: #fff
    }
    .live-list-item-container.search .live-info .count-info {
        display: none
    }
    .app-container.rtl .live-list-item-container>.thumbnail .badge.live-call {
        left: 5px;
        right: auto
    }
}

/*# sourceMappingURL=46.2bec12f3.chunk.css.map */