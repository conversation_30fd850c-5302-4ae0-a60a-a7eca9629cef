﻿// Firebase 인증 관련 Vuex 모듈
import { Module } from 'vuex';
import { auth } from '@/main';
import { signOut } from 'firebase/auth';

// 인터페이스 정의
export interface TammUser {
  uid: string;
  email: string | null;
  name?: string;
  role?: 'user' | 'admin';
  isApproved?: boolean;
  createdAt?: Date;
  [key: string]: any;
}

export interface AuthState {
  tammUser: TammUser | null;
  loading: boolean;
  error: string | null;
}

// 초기 상태
const initialState: AuthState = {
  tammUser: null,
  loading: false,
  error: null,
};

const authModule: Module<AuthState, any> = {
  namespaced: true,
  state: { ...initialState },
  
  getters: {
    // 사용자가 로그인되어 있는지 확인
    isAuthenticated: (state) => !!state.tammUser,
    
    // 사용자가 관리자인지 확인
    isAdmin: (state) => state.tammUser?.role === 'admin',
    
    // 현재 사용자 정보 반환
    currentUser: (state) => state.tammUser,
  },
  
  mutations: {
    // 로딩 상태 설정
    setLoading(state, loading: boolean) {
      state.loading = loading;
    },
    
    // 오류 설정
    setError(state, error: string | null) {
      state.error = error;
    },
    
    // Firebase 사용자 설정
    setTammUser(state, user: TammUser | null) {
      state.tammUser = user;
      state.loading = false;
      state.error = null;
    },
    
    // 사용자 정보 초기화
    clearTammUser(state) {
      state.tammUser = null;
    },
    
    // 인증 상태 초기화
    resetState(state) {
      Object.assign(state, { ...initialState });
    },
  },
  
  actions: {
    // 로그아웃 액션
    async logout({ commit }) {
      try {
        commit('setLoading', true);
        await signOut(auth);
        commit('clearTammUser');
        return { success: true };
      } catch (error: any) {
        commit('setError', error.message);
        return { success: false, error: error.message };
      } finally {
        commit('setLoading', false);
      }
    },
  },
};

export default authModule;