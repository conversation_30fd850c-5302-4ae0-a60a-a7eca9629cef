<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>스푼캐스트 연동</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        .logo {
            font-size: 48px;
            margin-bottom: 16px;
        }
        .title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 32px;
            line-height: 1.5;
        }
        .status {
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-weight: 500;
        }
        .status.loading {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
        }
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 8px 0;
            transition: all 0.2s ease;
        }
        .btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        .user-avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            margin: 0 auto 12px;
            display: block;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .user-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        .user-tag {
            color: #666;
            font-size: 14px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #ff6b35;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🥄</div>
        <div class="title">스푼캐스트 연동</div>
        <div class="subtitle">
            스푼캐스트에 로그인하여<br>
            Tamm 앱과 연동해보세요
        </div>

        <!-- 로딩 상태 -->
        <div id="loading-status" class="status loading">
            <div class="spinner"></div>
            <div style="margin-top: 12px;">토큰 확인 중...</div>
        </div>

        <!-- 로그인 필요 -->
        <div id="login-required" class="hidden">
            <div class="status error">
                스푼캐스트 로그인이 필요합니다
            </div>
            <button class="btn" onclick="loginToSpoon()">
                스푼캐스트 로그인하기
            </button>
        </div>

        <!-- 사용자 정보 표시 -->
        <div id="user-found" class="hidden">
            <div class="user-info">
                <img id="user-avatar" class="user-avatar" src="" alt="프로필">
                <div id="user-name" class="user-name"></div>
                <div id="user-tag" class="user-tag"></div>
            </div>
            <button id="connect-btn" class="btn" onclick="connectToTamm()">
                Tamm에 연동하기
            </button>
        </div>

        <!-- 연동 완료 -->
        <div id="success-status" class="status success hidden">
            <div>✅ 연동이 완료되었습니다!</div>
            <div style="font-size: 14px; margin-top: 8px;">
                이 창을 닫고 Tamm 앱을 확인해주세요
            </div>
        </div>
    </div>

    <script>
        // URL에서 세션 ID 추출
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session');
        
        if (!sessionId) {
            document.getElementById('loading-status').innerHTML = 
                '<div class="status error">잘못된 접근입니다</div>';
        }

        let currentUserInfo = null;

        // 페이지 로드 시 토큰 확인
        window.addEventListener('load', checkSpoonLogin);

        function checkSpoonLogin() {
            console.log('🔍 스푼캐스트 로그인 상태 확인');
            
            try {
                // 국가 코드 확인
                const countryCode = sessionStorage.getItem('SPOONCAST_countryCode');
                
                if (!countryCode) {
                    showLoginRequired();
                    return;
                }
                
                const crCode = countryCode.toUpperCase();
                const userInfoKey = `SPOONCAST_${crCode}_userInfo`;
                const refreshTokenKey = `SPOONCAST_${crCode}_refreshToken`;
                
                // 사용자 정보 가져오기
                const userInfoStr = localStorage.getItem(userInfoKey);
                if (!userInfoStr || userInfoStr.trim() === '') {
                    showLoginRequired();
                    return;
                }
                
                // 리프레시 토큰 가져오기
                const refreshToken = localStorage.getItem(refreshTokenKey) || '';
                
                // 사용자 정보 파싱
                const userInfo = JSON.parse(userInfoStr);
                userInfo.refresh_token = refreshToken;
                
                if (!userInfo.token) {
                    showLoginRequired();
                    return;
                }
                
                currentUserInfo = userInfo;
                showUserInfo(userInfo);
                
            } catch (error) {
                console.error('❌ 토큰 확인 실패:', error);
                showLoginRequired();
            }
        }

        function showLoginRequired() {
            document.getElementById('loading-status').classList.add('hidden');
            document.getElementById('login-required').classList.remove('hidden');
        }

        function showUserInfo(userInfo) {
            document.getElementById('loading-status').classList.add('hidden');
            document.getElementById('user-avatar').src = userInfo.profile_url || '/favicon.ico';
            document.getElementById('user-name').textContent = userInfo.nickname || '사용자';
            document.getElementById('user-tag').textContent = userInfo.tag ? `@${userInfo.tag}` : '';
            document.getElementById('user-found').classList.remove('hidden');
        }

        function loginToSpoon() {
            window.location.href = 'https://www.spooncast.net/kr/signin';
        }

        async function connectToTamm() {
            if (!currentUserInfo || !sessionId) {
                alert('연동에 필요한 정보가 없습니다.');
                return;
            }

            const connectBtn = document.getElementById('connect-btn');
            connectBtn.disabled = true;
            connectBtn.textContent = '연동 중...';

            try {
                console.log('📡 Firebase로 토큰 전송');
                
                const response = await fetch('/spoonTokenProxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        action: 'store',
                        tokenData: {
                            id: currentUserInfo.id,
                            nickname: currentUserInfo.nickname,
                            profile_url: currentUserInfo.profile_url,
                            tag: currentUserInfo.tag,
                            token: currentUserInfo.token,
                            refresh_token: currentUserInfo.refresh_token
                        }
                    })
                });

                if (response.ok) {
                    console.log('✅ 토큰 저장 성공');
                    document.getElementById('user-found').classList.add('hidden');
                    document.getElementById('success-status').classList.remove('hidden');
                    
                    // 부모 창에 성공 메시지 전달
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'SPOON_AUTH_SUCCESS',
                            sessionId: sessionId
                        }, '*');
                    }
                    
                    // 3초 후 창 닫기
                    setTimeout(() => {
                        window.close();
                    }, 3000);
                    
                } else {
                    throw new Error(`서버 오류: ${response.status}`);
                }

            } catch (error) {
                console.error('❌ 연동 실패:', error);
                alert('연동에 실패했습니다: ' + error.message);
                connectBtn.disabled = false;
                connectBtn.textContent = 'Tamm에 연동하기';
            }
        }

        // 스토리지 변경 감지 (로그인 상태 변경)
        window.addEventListener('storage', (e) => {
            if (e.key && e.key.includes('SPOONCAST') && e.key.includes('userInfo')) {
                console.log('💾 로그인 상태 변경 감지');
                setTimeout(checkSpoonLogin, 1000);
            }
        });
    </script>
</body>
</html> 