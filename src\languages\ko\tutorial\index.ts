export default {
	'author': '연하영',
	'step-hello': {
		0: '스푸너님! 오셨군요!',
		1: '안녕하세요. 소피아 사용 안내를 맡은 {{ tutorial.author }}이라고 합니다.',
		2: '헤헤. 아무래도 스푸너님은 소피아가 처음이신 것 같은데 제가 알려드릴게요.',
		3: '먼저 소피아는 스푼 DJ분들의 방송을 관리해 주고, 더 나아가 컨텐츠를 제공해 주는 AI예요.',
		4: '대표적인 기능으로는 청취자들과의 소통, 룰렛, 도네이션이 있어요. 지금부터 하나씩 알려드리도록 할게요.',
		whoru: '(경계한다.)',
		nice2meetu: '(반갑게 맞이한다.)',
		sub1: {
			0: '그렇게 경계하지 않으셔도 돼요.',
			1: '아이 참! 안 잡아먹는다니까요.',
		},
		sub2: {
			0: '악수요? 자! 반가운 의미로 악수!',
		},
	},
	'step-partner-live': {
		0: '방송을 관리하려면 방송에 들어가는 법부터 알아야겠죠?',
		1: '마침 지금 소피아의 파트너 DJ들이 방송하고 있네요. [{{ home.join-live }}]를 눌러 방송에 입장해 볼까요?',
	},
	'step-normal-live': {
		0: '방송을 관리하려면 방송에 들어가는 법부터 알아야겠죠?',
		1: '상단에 DJ이름을 검색해서 들어갈 방송을 찾아봐요.',
		2: '이렇게, 프로필에 빨간 점이 표시되어 있으면 방송중이란 뜻이에요.\n프로필 사진을 눌러 방송에 입장해 볼까요?',
	},
	'step-live': {
		0: '방송 창에서는 청취자들의 입장, 좋아요, 스푼 선물, 채팅을 관리할 수 있어요.',
		1: '방송 창 아래는 메뉴와 직접 입력할 수 있는 채팅창입니다. 한 번 [안녕하세요.] 라고 보내볼까요?',
		2: '좋아요! 분명 디제이도 인사를 받으면 방송에 힘이 날거예요.\n이제 왼쪽 하단의 + 버튼을 눌러서 메뉴를 살펴봐요.',
		3: '소리를 설정해 볼게요. DJ가 직접 소피아를 사용하는 경우 소리는 불필요하니 볼륨을 [5]로 지정해보죠!',
		4: '또는 스피커 버튼을 누를 경우 음소거할 수 있답니다.\n소리 설정은 소피아가 다시 켜져도 유지돼요.',
	},
};
