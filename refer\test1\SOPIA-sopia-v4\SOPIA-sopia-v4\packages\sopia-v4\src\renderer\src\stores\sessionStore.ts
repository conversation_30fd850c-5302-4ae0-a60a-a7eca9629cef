import { create } from 'zustand'

interface Session {
  id: string
  token: string
  // 필요한 다른 세션 정보 추가
}

interface SessionStore {
  session: Session | null
  setSession: (session: Session | null) => void
  clearSession: () => void
}

export const useSessionStore = create<SessionStore>((set) => ({
  session: null,
  setSession: (session) => set({ session }),
  clearSession: () => set({ session: null })
}))
