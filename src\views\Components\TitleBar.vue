<template>
	<v-app-bar
		color="purple lighten-5"
		dense
		:elevation="0"
		style="max-height: 48px; z-index: 10;"
		class="sopia-title-bar">

		<div v-if="!isMacOS" style="display:flex;align-items:center;">
			<div class="no-drag cursor-pointer" @click="toggleSidebar">
				<img src="@/assets/icon128.png" width="32px" class="mr-4">
			</div>
			<span class="text-caption">TAMM</span>
		</div>
		<v-spacer></v-spacer>
		<template v-if="shouldShowTitlebarElements">
			<search-box></search-box>
			<v-spacer></v-spacer>
			<v-menu
				v-model="avatarMenu"
				:close-on-content-click="false"
				offset-y
				left
				transition="slide-y-transition"
				:nudge-width="250"
				:nudge-bottom="10">
				<template v-slot:activator="{ on, attrs }">
					<v-avatar size="32" class="no-drag" v-bind="attrs" v-on="on">
						<img :src="$store.getters.user.profile_url">
					</v-avatar>
				</template>
				<v-card color="white" elevation="3" class="profile-popup">
					<v-list-item class="px-2">
						<v-list-item-avatar size="56" color="grey lighten-3">
							<v-img :src="$store.getters.user.profile_url"></v-img>
						</v-list-item-avatar>

						<v-list-item-content class="ml-2">
							<v-list-item-title class="title font-weight-bold" style="font-size: 1.1rem !important;">
								{{ $store.getters.user.nickname }}
							</v-list-item-title>
							<v-list-item-subtitle class="mt-1" style="font-size: 0.8rem !important; color: #666;">
								@{{ $store.getters.user.tag }}
							</v-list-item-subtitle>
						</v-list-item-content>
					</v-list-item>
					
					<v-divider class="mx-4 my-2"></v-divider>
					
					<v-list dense class="profile-action-list">
						<v-list-item link @click="$assign(userLink)" class="profile-action-item">
							<v-list-item-icon class="mr-3">
								<v-icon size="20" color="primary">mdi-account</v-icon>
							</v-list-item-icon>
							<v-list-item-content>
								<v-list-item-title>프로필 보기</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
						
						<v-list-item link @click="openFollowingDialog" class="profile-action-item">
							<v-list-item-icon class="mr-3">
								<v-icon size="20" color="purple">mdi-account-multiple</v-icon>
							</v-list-item-icon>
							<v-list-item-content>
								<v-list-item-title>내 팔로잉</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
						
						<v-list-item link @click="spoonLogout" class="profile-action-item">
							<v-list-item-icon class="mr-3">
								<v-icon size="20" color="red darken-1">mdi-logout</v-icon>
							</v-list-item-icon>
							<v-list-item-content>
								<v-list-item-title>{{ $t('spoon-logout') }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list>
				</v-card>
			</v-menu>
		</template>

		<v-spacer></v-spacer>

		<div v-if="!isMacOS" class="d-flex align-center">
			<!-- 사용자 정보 표시 (홈버튼 좌측) - 로그인된 상태에서만 표시 -->
			<div v-if="isLogin && isAuthenticated && currentUser" class="user-info-container no-drag mr-3">
				<v-chip 
					:color="currentUser.role === 'admin' ? 'red' : 'blue'" 
					text-color="white" 
					small
					class="user-info-chip"
				>
					<v-icon left small>{{ currentUser.role === 'admin' ? 'mdi-crown' : 'mdi-account' }}</v-icon>
					{{ currentUser.name }}
				</v-chip>
			</div>

			<v-btn 
				class="no-drag mr-2" 
				plain 
				small 
				icon 
				@click.stop="goHome" 
				title="홈으로 이동"
				:disabled="!isAuthenticated"
			>
				<v-icon style="font-size: 15px;">mdi-home</v-icon>
			</v-btn>
			<v-btn class="no-drag mr-2" plain small icon @click.stop="goBack">
				<v-icon style="font-size: 15px;">mdi-arrow-left</v-icon>
			</v-btn>
			<v-btn class="no-drag mr-2" plain small icon @click.stop="refresh">
				<v-icon style="font-size: 15px;">mdi-refresh</v-icon>
			</v-btn>
			<v-btn class="no-drag mr-2" plain small icon @click.stop="minimize">
				<v-icon style="font-size: 15px;">mdi-window-minimize</v-icon>
			</v-btn>
			<v-btn color="red" class="no-drag" plain small icon @click.stop="quit">
				<v-icon style="font-size: 15px;">mdi-close</v-icon>
			</v-btn>
		</div>
		<div v-else style="display:flex;align-items:center;">
			<img src="@/assets/icon128.png" width="32px" class="mr-4 no-drag" @click="upEGG">
			<span class="text-caption">TAMM - {{ version }}</span>
		</div>
	</v-app-bar>
</template>
<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import pkg from '../../../package.json';
import SearchBox from '../Search/SearchBox.vue';
import { authService } from '@/plugins/auth-service';
const { ipcRenderer } = window.require('electron');
const os = window.require('os');

@Component({
	components: {
		SearchBox,
	},
})
export default class TitleBar extends Mixins(GlobalMixins) {
	public avatarMenu: boolean = false;
	public countEGG: number = 0;
	public hideTitlebarElements: boolean = false;
	public currentUserData: any = null; // 반응형 사용자 데이터

	@Prop(Boolean) public isLogin!: boolean;
	@Prop({ type: Function, default: null }) public onToggleSidebar!: Function;

	created() {
		// 타이틀바 요소 표시/숨김 이벤트 리스너
		this.$evt.$on('hide-titlebar-elements', (hide: boolean) => {
			this.hideTitlebarElements = hide;
		});

		// 사용자 정보 업데이트를 위한 타이머 설정
		this.updateCurrentUser();
		this.startUserUpdateTimer();
	}

	mounted() {
		// 라우터 변경 시 사용자 정보 업데이트
		this.$watch('$route', () => {
			this.updateCurrentUser();
		});

		// isLogin prop 변경 시 사용자 정보 업데이트
		this.$watch('isLogin', () => {
			this.updateCurrentUser();
		});
	}

	beforeDestroy() {
		// 이벤트 리스너 정리
		this.$evt.$off('hide-titlebar-elements');
		
		// 타이머 정리
		if (this.userUpdateTimer) {
			clearInterval(this.userUpdateTimer);
		}
	}

	private userUpdateTimer: any = null;

	private startUserUpdateTimer() {
		// 1초마다 사용자 정보 확인 (실시간 업데이트)
		this.userUpdateTimer = setInterval(() => {
			this.updateCurrentUser();
		}, 1000);
	}

	private updateCurrentUser() {
		const newUser = authService.getCurrentUser();
		// 사용자 정보가 변경된 경우에만 업데이트
		if (JSON.stringify(this.currentUserData) !== JSON.stringify(newUser)) {
			this.currentUserData = newUser;
		}
	}

	public get version() {
		console.log(this.$route);
		return pkg.version;
	}

	public get isMacOS() {
		return os.platform() === 'darwin';
	}

	public get userLink() {
		return `/user/${this.$store.getters.user.id}`;
	}

	public avatarClick() {
		console.log('click');
	}

	public upEGG() {
		this.countEGG += 1;
		if ( this.countEGG > 5 ) {
			this.countEGG = 0;
			ipcRenderer.send('open-dev-tools');
		}
	}

	public toggleSidebar() {
		console.log('사이드바 토글 요청');
		this.$evt.$emit('toggle-sidebar');
	}

	public spoonLogout() {
		window.logout();
		/*
		this.$cfg.delete('auth.spoon');
		this.$cfg.save();
		this.$store.state.loginDialog = true;
		*/
	}

	public goBack() {
		this.$router.go(-1);
	}

	public refresh() {
		location.reload();
	}

	public minimize() {
		ipcRenderer.send('app:minimize');
	}

	public quit() {
		ipcRenderer.send('app:quit');
	}

	public goHome() {
		if (this.$route.path !== '/') {
			this.$router.push('/').catch(err => {
				if (err.name !== 'NavigationDuplicated') {
					throw err;
				}
			});
		}
	}

	public openFollowingDialog() {
		this.$store.commit('setFollowingDialogOpen', true);
		this.avatarMenu = false;
	}

	public get isAuthenticated(): boolean {
		return authService.isAuthenticated();
	}

	public get currentUser() {
		return this.currentUserData;
	}

	public get isOnLoginPage(): boolean {
		return this.$route.name === 'Login' || this.$route.path === '/login' || this.$route.path.includes('/login');
	}

	public get shouldShowTitlebarElements(): boolean {
		return this.isLogin && 
			   !this.isOnLoginPage &&
			   this.$route.name !== 'Home' && 
			   this.$route.name !== 'Search' && 
			   !this.$route.path.includes('/cmd/') && 
			   !this.$route.path.includes('/code/') && 
			   !this.$route.path.includes('/bundle/') && 
			   this.$route.path !== '/usertab' && 
			   !this.$route.path.includes('/user') && 
			   this.$route.path !== '/lotterytab' && 
			   this.$route.path !== '/musictab' && 
			   this.$route.path !== '/roulette' && 
			   this.$route.path !== '/imageeditor' && 
			   this.$route.path !== '/setting' && 
			   !this.hideTitlebarElements;
	}

}
</script>
<style>
.sopia-title-bar {
	-webkit-app-region: drag;
	-webkit-user-select: none;
}
.sopia-title-bar .no-drag {
	-webkit-app-region: no-drag;
}
.cursor-pointer {
	cursor: pointer;
}

/* 프로필 팝업 스타일 */
.profile-popup {
	border-radius: 12px;
	overflow: hidden;
}

.profile-action-list {
	padding: 0;
}

.profile-action-item {
	transition: background-color 0.2s;
	border-radius: 0;
	min-height: 42px;
}

.profile-action-item:hover {
	background-color: rgba(0, 0, 0, 0.04);
}

/* 사용자 정보 칩 스타일 */
.user-info-container {
	display: flex;
	align-items: center;
}

.user-info-chip {
	font-size: 0.75rem !important;
	font-weight: 600;
	height: 24px !important;
	border-radius: 12px !important;
	text-transform: none !important;
	letter-spacing: 0.5px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.user-info-chip:hover {
	transform: scale(1.05);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
</style>
