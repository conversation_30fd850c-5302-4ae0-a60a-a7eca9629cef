/**
 * Kakao SDK for JavaScript - v2.1.0
 *
 * Copyright 2017 Kakao Corp.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 *
 * OSS Notice | KakaoSDK-JavaScript
 *
 * This application is Copyright © Kakao Corp. All rights reserved.
 * The following sets forth attribution notices for third party software that may be contained in this application.
 * If you have any questions about these notices, please email <NAME_EMAIL>
 *
 *
 * crypto-js
 *
 * https://github.com/brix/crypto-js
 *
 * Copyright 2009-2013 <PERSON>
 * Copyright 2013-2016 <PERSON>
 *
 * MIT License
 *
 *
 * ES6-Promise
 *
 * https://github.com/stefanpenner/es6-promise
 *
 * Copyright 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors
 *
 * MIT License
 *
 *
 * Kakao Web2App Library
 *
 * https://github.com/kakao/web2app
 *
 * Copyright 2015 Kakao Corp. http://www.kakaocorp.com
 *
 * MIT License
 *
 *
 * lodash
 *
 * https://github.com/lodash/lodash
 *
 * Copyright JS Foundation and other contributors
 *
 * MIT License
 *
 *
 * ua_parser
 *
 * https://github.com/html5crew/ua_parser
 *
 * Copyright HTML5 Tech. Team in Daum Communications Corp.
 *
 * MIT License
 *
 *
 * ``````````
 * MIT License
 *
 * Copyright (c)
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * 'Software'), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ``````````
 */

! function(e, t) {
    "object" == typeof exports && "undefined" != typeof module ? t(exports) : "function" == typeof define && define.amd ? define(["exports"], t) : t((e = "undefined" != typeof globalThis ? globalThis : e || self).Kakao = e.Kakao || {})
}(this, (function(e) {
    "use strict";
    var t = "object" == typeof global && global && global.Object === Object && global,
        n = "object" == typeof self && self && self.Object === Object && self,
        r = t || n || Function("return this")(),
        o = r.Symbol,
        i = Object.prototype,
        a = i.hasOwnProperty,
        c = i.toString,
        u = o ? o.toStringTag : void 0;
    var s = Object.prototype.toString;
    var l = o ? o.toStringTag : void 0;

    function p(e) {
        return null == e ? void 0 === e ? "[object Undefined]" : "[object Null]" : l && l in Object(e) ? function(e) {
            var t = a.call(e, u),
                n = e[u];
            try {
                e[u] = void 0;
                var r = !0
            } catch (e) {}
            var o = c.call(e);
            return r && (t ? e[u] = n : delete e[u]), o
        }(e) : function(e) {
            return s.call(e)
        }(e)
    }

    function f(e) {
        return null != e && "object" == typeof e
    }
    var d = Array.isArray,
        h = /\s/;
    var v = /^\s+/;

    function m(e) {
        return e ? e.slice(0, function(e) {
            for (var t = e.length; t-- && h.test(e.charAt(t)););
            return t
        }(e) + 1).replace(v, "") : e
    }

    function y(e) {
        var t = typeof e;
        return null != e && ("object" == t || "function" == t)
    }
    var g = /^[-+]0x[0-9a-f]+$/i,
        b = /^0b[01]+$/i,
        _ = /^0o[0-7]+$/i,
        w = parseInt;

    function k(e) {
        if ("number" == typeof e) return e;
        if (function(e) {
                return "symbol" == typeof e || f(e) && "[object Symbol]" == p(e)
            }(e)) return NaN;
        if (y(e)) {
            var t = "function" == typeof e.valueOf ? e.valueOf() : e;
            e = y(t) ? t + "" : t
        }
        if ("string" != typeof e) return 0 === e ? e : +e;
        e = m(e);
        var n = b.test(e);
        return n || _.test(e) ? w(e.slice(2), n ? 2 : 8) : g.test(e) ? NaN : +e
    }
    var S = 1 / 0;

    function j(e) {
        var t = function(e) {
                return e ? (e = k(e)) === S || e === -1 / 0 ? 17976931348623157e292 * (e < 0 ? -1 : 1) : e == e ? e : 0 : 0 === e ? e : 0
            }(e),
            n = t % 1;
        return t == t ? n ? t - n : t : 0
    }

    function x(e) {
        return e
    }

    function O(e) {
        if (!y(e)) return !1;
        var t = p(e);
        return "[object Function]" == t || "[object GeneratorFunction]" == t || "[object AsyncFunction]" == t || "[object Proxy]" == t
    }
    var A, T = r["__core-js_shared__"],
        C = (A = /[^.]+$/.exec(T && T.keys && T.keys.IE_PROTO || "")) ? "Symbol(src)_1." + A : "";
    var P = Function.prototype.toString;
    var I = /^\[object .+?Constructor\]$/,
        B = Function.prototype,
        E = Object.prototype,
        z = B.toString,
        F = E.hasOwnProperty,
        q = RegExp("^" + z.call(F).replace(/[\\^$.*+?()[\]{}|]/g, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$");

    function U(e) {
        return !(!y(e) || (t = e, C && C in t)) && (O(e) ? q : I).test(function(e) {
            if (null != e) {
                try {
                    return P.call(e)
                } catch (e) {}
                try {
                    return e + ""
                } catch (e) {}
            }
            return ""
        }(e));
        var t
    }

    function D(e, t, n) {
        switch (n.length) {
            case 0:
                return e.call(t);
            case 1:
                return e.call(t, n[0]);
            case 2:
                return e.call(t, n[0], n[1]);
            case 3:
                return e.call(t, n[0], n[1], n[2])
        }
        return e.apply(t, n)
    }
    var R = Date.now;
    var L, M, K, N = function() {
            try {
                var e = U(t = function(e, t) {
                    return null == e ? void 0 : e[t]
                }(Object, "defineProperty")) ? t : void 0;
                return e({}, "", {}), e
            } catch (e) {}
            var t
        }(),
        H = N,
        G = H ? function(e, t) {
            return H(e, "toString", {
                configurable: !0,
                enumerable: !1,
                value: (n = t, function() {
                    return n
                }),
                writable: !0
            });
            var n
        } : x,
        $ = (L = G, M = 0, K = 0, function() {
            var e = R(),
                t = 16 - (e - K);
            if (K = e, t > 0) {
                if (++M >= 800) return arguments[0]
            } else M = 0;
            return L.apply(void 0, arguments)
        }),
        J = $;

    function X(e, t) {
        for (var n = -1, r = null == e ? 0 : e.length; ++n < r && !1 !== t(e[n], n, e););
        return e
    }
    var W = /^(?:0|[1-9]\d*)$/;

    function V(e, t) {
        var n = typeof e;
        return !!(t = null == t ? 9007199254740991 : t) && ("number" == n || "symbol" != n && W.test(e)) && e > -1 && e % 1 == 0 && e < t
    }

    function Y(e, t) {
        return e === t || e != e && t != t
    }
    var Z = Math.max;

    function Q(e) {
        return "number" == typeof e && e > -1 && e % 1 == 0 && e <= 9007199254740991
    }

    function ee(e) {
        return null != e && Q(e.length) && !O(e)
    }
    var te = Object.prototype;

    function ne(e) {
        var t = e && e.constructor;
        return e === ("function" == typeof t && t.prototype || te)
    }

    function re(e) {
        return f(e) && "[object Arguments]" == p(e)
    }
    var oe = Object.prototype,
        ie = oe.hasOwnProperty,
        ae = oe.propertyIsEnumerable,
        ce = re(function() {
            return arguments
        }()) ? re : function(e) {
            return f(e) && ie.call(e, "callee") && !ae.call(e, "callee")
        },
        ue = ce;
    var se = "object" == typeof e && e && !e.nodeType && e,
        le = se && "object" == typeof module && module && !module.nodeType && module,
        pe = le && le.exports === se ? r.Buffer : void 0,
        fe = (pe ? pe.isBuffer : void 0) || function() {
            return !1
        },
        de = {};
    de["[object Float32Array]"] = de["[object Float64Array]"] = de["[object Int8Array]"] = de["[object Int16Array]"] = de["[object Int32Array]"] = de["[object Uint8Array]"] = de["[object Uint8ClampedArray]"] = de["[object Uint16Array]"] = de["[object Uint32Array]"] = !0, de["[object Arguments]"] = de["[object Array]"] = de["[object ArrayBuffer]"] = de["[object Boolean]"] = de["[object DataView]"] = de["[object Date]"] = de["[object Error]"] = de["[object Function]"] = de["[object Map]"] = de["[object Number]"] = de["[object Object]"] = de["[object RegExp]"] = de["[object Set]"] = de["[object String]"] = de["[object WeakMap]"] = !1;
    var he = "object" == typeof e && e && !e.nodeType && e,
        ve = he && "object" == typeof module && module && !module.nodeType && module,
        me = ve && ve.exports === he && t.process,
        ye = function() {
            try {
                var e = ve && ve.require && ve.require("util").types;
                return e || me && me.binding && me.binding("util")
            } catch (e) {}
        }(),
        ge = ye && ye.isTypedArray,
        be = ge ? function(e) {
            return function(t) {
                return e(t)
            }
        }(ge) : function(e) {
            return f(e) && Q(e.length) && !!de[p(e)]
        },
        _e = be,
        we = Object.prototype.hasOwnProperty;

    function ke(e, t) {
        var n = d(e),
            r = !n && ue(e),
            o = !n && !r && fe(e),
            i = !n && !r && !o && _e(e),
            a = n || r || o || i,
            c = a ? function(e, t) {
                for (var n = -1, r = Array(e); ++n < e;) r[n] = t(n);
                return r
            }(e.length, String) : [],
            u = c.length;
        for (var s in e) !t && !we.call(e, s) || a && ("length" == s || o && ("offset" == s || "parent" == s) || i && ("buffer" == s || "byteLength" == s || "byteOffset" == s) || V(s, u)) || c.push(s);
        return c
    }

    function Se(e, t) {
        return function(n) {
            return e(t(n))
        }
    }
    var je = Se(Object.keys, Object),
        xe = Object.prototype.hasOwnProperty;

    function Oe(e) {
        return ee(e) ? ke(e) : function(e) {
            if (!ne(e)) return je(e);
            var t = [];
            for (var n in Object(e)) xe.call(e, n) && "constructor" != n && t.push(n);
            return t
        }(e)
    }
    var Ae = Object.prototype.hasOwnProperty;

    function Te(e) {
        if (!y(e)) return function(e) {
            var t = [];
            if (null != e)
                for (var n in Object(e)) t.push(n);
            return t
        }(e);
        var t = ne(e),
            n = [];
        for (var r in e)("constructor" != r || !t && Ae.call(e, r)) && n.push(r);
        return n
    }

    function Ce(e) {
        return ee(e) ? ke(e, !0) : Te(e)
    }
    var Pe = Se(Object.getPrototypeOf, Object),
        Ie = Function.prototype,
        Be = Object.prototype,
        Ee = Ie.toString,
        ze = Be.hasOwnProperty,
        Fe = Ee.call(Object);
    var qe, Ue = function(e, t, n) {
        for (var r = -1, o = Object(e), i = n(e), a = i.length; a--;) {
            var c = i[qe ? a : ++r];
            if (!1 === t(o[c], c, o)) break
        }
        return e
    };
    var De = function(e, t) {
            return function(n, r) {
                if (null == n) return n;
                if (!ee(n)) return e(n, r);
                for (var o = n.length, i = t ? o : -1, a = Object(n);
                    (t ? i-- : ++i < o) && !1 !== r(a[i], i, a););
                return n
            }
        }((function(e, t) {
            return e && Ue(e, t, Oe)
        })),
        Re = De,
        Le = Object.prototype,
        Me = Le.hasOwnProperty,
        Ke = function(e, t) {
            return J(function(e, t, n) {
                return t = Z(void 0 === t ? e.length - 1 : t, 0),
                    function() {
                        for (var r = arguments, o = -1, i = Z(r.length - t, 0), a = Array(i); ++o < i;) a[o] = r[t + o];
                        o = -1;
                        for (var c = Array(t + 1); ++o < t;) c[o] = r[o];
                        return c[t] = n(a), D(e, this, c)
                    }
            }(e, t, x), e + "")
        }((function(e, t) {
            e = Object(e);
            var n = -1,
                r = t.length,
                o = r > 2 ? t[2] : void 0;
            for (o && function(e, t, n) {
                    if (!y(n)) return !1;
                    var r = typeof t;
                    return !!("number" == r ? ee(n) && V(t, n.length) : "string" == r && t in n) && Y(n[t], e)
                }(t[0], t[1], o) && (r = 1); ++n < r;)
                for (var i = t[n], a = Ce(i), c = -1, u = a.length; ++c < u;) {
                    var s = a[c],
                        l = e[s];
                    (void 0 === l || Y(l, Le[s]) && !Me.call(e, s)) && (e[s] = i[s])
                }
            return e
        })),
        Ne = Ke;

    function He(e, t) {
        var n;
        return (d(e) ? X : Re)(e, "function" == typeof(n = t) ? n : x)
    }

    function Ge(e) {
        return "string" == typeof e || !d(e) && f(e) && "[object String]" == p(e)
    }

    function $e(e) {
        return !0 === e || !1 === e || f(e) && "[object Boolean]" == p(e)
    }

    function Je(e) {
        return f(e) && 1 === e.nodeType && ! function(e) {
            if (!f(e) || "[object Object]" != p(e)) return !1;
            var t = Pe(e);
            if (null === t) return !0;
            var n = ze.call(t, "constructor") && t.constructor;
            return "function" == typeof n && n instanceof n && Ee.call(n) == Fe
        }(e)
    }

    function Xe(e) {
        return "number" == typeof e && e == j(e)
    }

    function We(e) {
        return "number" == typeof e || f(e) && "[object Number]" == p(e)
    }

    function Ve(e) {
        return function(t) {
            return Object.prototype.toString.call(t) === "[object ".concat(e, "]")
        }
    }

    function Ye(e) {
        return Ve("Blob")(e)
    }

    function Ze(e) {
        return Ve("File")(e)
    }

    function Qe(e) {
        return Ve("FileList")(e)
    }

    function et(e, t) {
        return Array.prototype.slice.call(e).map(t)
    }

    function tt(e, t) {
        return Array.prototype.slice.call(e).every(t)
    }

    function nt(e, t) {
        return [e, t].reduce((function(e, t) {
            return e.filter((function(e) {
                return -1 === t.indexOf(e)
            }))
        }))
    }

    function rt(e) {
        return Object.keys(e || {})
    }

    function ot() {}

    function it(e) {
        return Je(e) ? e : Ge(e) ? document.querySelector(e) : null
    }

    function at(e, t, n) {
        e.addEventListener && e.addEventListener(t, n, !1)
    }

    function ct(e, t, n) {
        e.removeEventListener && e.removeEventListener(t, n, !1)
    }

    function ut(e) {
        var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1],
            n = t ? encodeURIComponent : function(e) {
                return e
            };
        return et(rt(e), (function(t) {
            var r = e[t];
            return n(t) + "=" + n(f(r) ? JSON.stringify(r) : r)
        })).join("&")
    }

    function st(e) {
        if (!d(e)) throw new Error("elements should be an Array");
        return function(t) {
            return e.indexOf(t) > -1
        }
    }

    function lt(e) {
        if (!d(e)) throw new Error("validators should be an Array");
        return function(t) {
            return e.some((function(e) {
                return e(t)
            }))
        }
    }

    function pt(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter((function(t) {
                return Object.getOwnPropertyDescriptor(e, t).enumerable
            }))), n.push.apply(n, r)
        }
        return n
    }

    function ft(e) {
        for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? pt(Object(n), !0).forEach((function(t) {
                mt(e, t, n[t])
            })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : pt(Object(n)).forEach((function(t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
            }))
        }
        return e
    }

    function dt(e, t) {
        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
    }

    function ht(e, t) {
        for (var n = 0; n < t.length; n++) {
            var r = t[n];
            r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, At(r.key), r)
        }
    }

    function vt(e, t, n) {
        return t && ht(e.prototype, t), n && ht(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
        }), e
    }

    function mt(e, t, n) {
        return (t = At(t)) in e ? Object.defineProperty(e, t, {
            value: n,
            enumerable: !0,
            configurable: !0,
            writable: !0
        }) : e[t] = n, e
    }

    function yt(e, t) {
        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
        e.prototype = Object.create(t && t.prototype, {
            constructor: {
                value: e,
                writable: !0,
                configurable: !0
            }
        }), Object.defineProperty(e, "prototype", {
            writable: !1
        }), t && bt(e, t)
    }

    function gt(e) {
        return gt = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) {
            return e.__proto__ || Object.getPrototypeOf(e)
        }, gt(e)
    }

    function bt(e, t) {
        return bt = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) {
            return e.__proto__ = t, e
        }, bt(e, t)
    }

    function _t(e, t) {
        if (null == e) return {};
        var n, r, o = function(e, t) {
            if (null == e) return {};
            var n, r, o = {},
                i = Object.keys(e);
            for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || (o[n] = e[n]);
            return o
        }(e, t);
        if (Object.getOwnPropertySymbols) {
            var i = Object.getOwnPropertySymbols(e);
            for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (o[n] = e[n])
        }
        return o
    }

    function wt(e, t) {
        if (t && ("object" == typeof t || "function" == typeof t)) return t;
        if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined");
        return function(e) {
            if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            return e
        }(e)
    }

    function kt(e) {
        var t = function() {
            if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
            if (Reflect.construct.sham) return !1;
            if ("function" == typeof Proxy) return !0;
            try {
                return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))), !0
            } catch (e) {
                return !1
            }
        }();
        return function() {
            var n, r = gt(e);
            if (t) {
                var o = gt(this).constructor;
                n = Reflect.construct(r, arguments, o)
            } else n = r.apply(this, arguments);
            return wt(this, n)
        }
    }

    function St(e, t) {
        return function(e) {
            if (Array.isArray(e)) return e
        }(e) || function(e, t) {
            var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
            if (null != n) {
                var r, o, i, a, c = [],
                    u = !0,
                    s = !1;
                try {
                    if (i = (n = n.call(e)).next, 0 === t) {
                        if (Object(n) !== n) return;
                        u = !1
                    } else
                        for (; !(u = (r = i.call(n)).done) && (c.push(r.value), c.length !== t); u = !0);
                } catch (e) {
                    s = !0, o = e
                } finally {
                    try {
                        if (!u && null != n.return && (a = n.return(), Object(a) !== a)) return
                    } finally {
                        if (s) throw o
                    }
                }
                return c
            }
        }(e, t) || xt(e, t) || function() {
            throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }()
    }

    function jt(e) {
        return function(e) {
            if (Array.isArray(e)) return Ot(e)
        }(e) || function(e) {
            if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
        }(e) || xt(e) || function() {
            throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }()
    }

    function xt(e, t) {
        if (e) {
            if ("string" == typeof e) return Ot(e, t);
            var n = Object.prototype.toString.call(e).slice(8, -1);
            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Ot(e, t) : void 0
        }
    }

    function Ot(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
        return r
    }

    function At(e) {
        var t = function(e, t) {
            if ("object" != typeof e || null === e) return e;
            var n = e[Symbol.toPrimitive];
            if (void 0 !== n) {
                var r = n.call(e, t || "default");
                if ("object" != typeof r) return r;
                throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == typeof t ? t : String(t)
    }
    var Tt = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {},
        Ct = function() {
            function e(e) {
                var n = {},
                    r = /(dolfin)[ \/]([\w.]+)/.exec(e) || /(edge)[ \/]([\w.]+)/.exec(e) || /(chrome)[ \/]([\w.]+)/.exec(e) || /(tizen)[ \/]([\w.]+)/.exec(e) || /(opera)(?:.*version)?[ \/]([\w.]+)/.exec(e) || /(webkit)(?:.*version)?[ \/]([\w.]+)/.exec(e) || /(msie) ([\w.]+)/.exec(e) || e.indexOf("compatible") < 0 && /(mozilla)(?:.*? rv:([\w.]+))?/.exec(e) || ["", "unknown"];
                return "webkit" === r[1] ? r = /(iphone|ipad|ipod)[\S\s]*os ([\w._\-]+) like/.exec(e) || /(android)[ \/]([\w._\-]+);/.exec(e) || [r[0], "safari", r[2]] : "mozilla" === r[1] ? /trident/.test(e) ? r[1] = "msie" : r[1] = "firefox" : /polaris|natebrowser|([010|011|016|017|018|019]{3}\d{3,4}\d{4}$)/.test(e) && (r[1] = "polaris"), n[r[1]] = !0, n.name = r[1], n.version = t(r[2]), n
            }

            function t(e) {
                var t = {},
                    n = e ? e.split(/\.|-|_/) : ["0", "0", "0"];
                return t.info = n.join("."), t.major = n[0] || "0", t.minor = n[1] || "0", t.patch = n[2] || "0", t
            }

            function n(e) {
                return function(e) {
                    if (e.match(/ipad/) || e.match(/android/) && !e.match(/mobi|mini|fennec/) || e.match(/macintosh/) && window.navigator.maxTouchPoints > 1) return !0;
                    return !1
                }(e) ? "tablet" : function(e) {
                    if (e.match(/linux|windows (nt|98)|macintosh|cros/) && !e.match(/android|mobile|polaris|lgtelecom|uzard|natebrowser|ktf;|skt;/)) return !0;
                    return !1
                }(e) ? "pc" : function(e) {
                    return !!e.match(/ip(hone|od)|android.+mobile|windows (ce|phone)|blackberry|bb10|symbian|webos|firefox.+fennec|opera m(ob|in)i|tizen.+mobile|polaris|iemobile|lgtelecom|nokia|sonyericsson|dolfin|uzard|natebrowser|ktf;|skt;/)
                }(e) ? "mobile" : ""
            }

            function r(e) {
                var n = {},
                    r = /(iphone|ipad|ipod)[\S\s]*os ([\w._\-]+) like/.exec(e) || !!/polaris|natebrowser|([010|011|016|017|018|019]{3}\d{3,4}\d{4}$)/.test(e) && ["", "polaris", "0.0.0"] || /(windows)(?: nt | phone(?: os){0,1} | )([\w._\-]+)/.exec(e) || /(android)[ \/]([\w._\-]+);/.exec(e) || !!/android/.test(e) && ["", "android", "0.0.0"] || !!/(windows)/.test(e) && ["", "windows", "0.0.0"] || /(mac) os x ([\w._\-]+)/.exec(e) || /(tizen)[ \/]([\w._\-]+);/.exec(e) || !!/(linux)/.test(e) && ["", "linux", "0.0.0"] || !!/webos/.test(e) && ["", "webos", "0.0.0"] || /(cros)(?:\s[\w]+\s)([\d._\-]+)/.exec(e) || /(bada)[ \/]([\w._\-]+)/.exec(e) || !!/bada/.test(e) && ["", "bada", "0.0.0"] || !!/(rim|blackberry|bb10)/.test(e) && ["", "blackberry", "0.0.0"] || ["", "unknown", "0.0.0"];
                return "iphone" === r[1] || "ipad" === r[1] || "ipod" === r[1] ? r[1] = "ios" : "windows" === r[1] && "98" === r[2] && (r[2] = "0.98.0"), "mac" === r[1] && "undefined" != typeof window && window.navigator.maxTouchPoints > 1 && (r[1] = "ios"), "cros" === r[1] && (r[1] = "chrome"), n[r[1]] = !0, n.name = r[1], n.version = t(r[2]), n
            }
            Array.isArray || (Array.isArray = function(e) {
                return "[object Array]" === Object.prototype.toString.call(e)
            });
            var o = ["crios", "fxios", "daumapps"];

            function i(e, n) {
                var r = {},
                    i = null,
                    a = o;
                Array.isArray(n) ? a = o.concat(n) : "string" == typeof n && (a = o.concat([n]));
                for (var c = 0, u = a.length; c < u; c += 1) {
                    var s = a[c];
                    if (i = new RegExp("(" + s + ")[ \\/]([\\w._\\-]+)").exec(e)) break
                }
                return i || (i = ["", ""]), i[1] ? (r.isApp = !0, r.name = i[1], r.version = t(i[2])) : r.isApp = !1, r
            }
            return function(t, o) {
                var a = function(e) {
                    return e ? e.toLowerCase() : "undefined" != typeof window && window.navigator && "string" == typeof window.navigator.userAgent ? window.navigator.userAgent.toLowerCase() : ""
                }(t);
                return {
                    ua: a,
                    browser: e(a),
                    platform: n(a),
                    os: r(a),
                    app: i(a, o)
                }
            }
        }(),
        Pt = Ct();
    var It, Bt, Et, zt, Ft = "https://kauth.kakao.com",
        qt = "https://story.kakao.com",
        Ut = "https://developers.kakao.com",
        Dt = (It = location, Bt = It.protocol, Et = It.hostname, zt = It.port, "".concat(Bt, "//").concat(Et).concat(zt ? ":" + zt : "")),
        Rt = Pt,
        Lt = /KAKAOTALK/i.test(Rt.ua),
        Mt = "2.1.0".concat(""),
        Kt = navigator,
        Nt = ["sdk/".concat(Mt), "os/javascript", "sdk_type/javascript", "lang/".concat(Kt.userLanguage || Kt.language), "device/".concat(Kt.platform.replace(/ /g, "_")), "origin/".concat(encodeURIComponent(Dt))].join(" "),
        Ht = {
            apiDomain: "https://kapi.kakao.com",
            accountDomain: "https://accounts.kakao.com",
            authDomain: Ft,
            authorize: "".concat(Ft, "/oauth/authorize"),
            redirectUri: "JS-SDK",
            universalKakaoLink: "".concat("https://talk-apps.kakao.com", "/scheme/"),
            talkInappScheme: "kakaotalk://inappbrowser",
            talkSyncpluginScheme: "kakaotalk://bizplugin?plugin_id=6011263b74fc2b49c73a7298",
            sharerDomain: "https://sharer.kakao.com",
            pickerDomain: "https://friend-picker.kakao.com",
            appsDomain: "https://apps.kakao.com",
            talkLinkScheme: "kakaolink://send",
            talkAndroidPackage: "com.kakao.talk",
            channel: "https://pf.kakao.com",
            channelIcon: "".concat(Ut, "/assets/img/about/logos"),
            storyShare: "".concat(qt, "/s/share"),
            storyChannelFollow: "".concat(qt, "/s/follow"),
            storyIcon: "".concat(Ut, "/sdk/js/resources/story/icon_small.png"),
            storyPostScheme: "storylink://posting",
            naviScheme: "kakaonavi-sdk://navigate",
            naviFallback: "https://kakaonavi.kakao.com/launch/index.do"
        },
        Gt = null;

    function $t() {
        return Gt
    }

    function Jt(e) {
        Gt = e
    }

    function Xt(e) {
        Error.prototype.constructor.apply(this, arguments), this.name = "KakaoError", this.message = e
    }

    function Wt(e) {
        var t = e.reduce((function(e, t) {
            return ft(ft({}, e), t)
        }), {});
        return ft(ft({}, t), {}, {
            cleanup: function() {
                He(e, (function(e) {
                    return e.cleanup && e.cleanup()
                }))
            }
        })
    }

    function Vt(e) {
        He(e, (function(e) {
            e()
        })), e.length = 0
    }

    function Yt(e, t, n) {
        if (!1 === t(e)) throw new Xt("Illegal argument for ".concat(n))
    }

    function Zt() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
            t = arguments.length > 1 ? arguments[1] : void 0,
            n = arguments.length > 2 ? arguments[2] : void 0;
        if (!f(e)) throw new Error("params should be an Object");
        O(t.before) && t.before(e), O(t.defaults) ? Ne(e, t.defaults(e)) : Ne(e, t.defaults);
        var r = t.required,
            o = void 0 === r ? {} : r,
            i = t.optional,
            a = void 0 === i ? {} : i,
            c = nt(rt(o), rt(e));
        if (c.length > 0) throw new Xt("Missing required keys: ".concat(c.join(","), " at ").concat(n));
        var u = ft(ft({}, o), a),
            s = nt(rt(e), rt(u));
        if (s.length > 0) throw new Xt("Invalid parameter keys: ".concat(s.join(","), " at ").concat(n));
        return He(e, (function(e, t) {
            Yt(e, u[t], '"'.concat(t, '" in ').concat(n))
        })), O(t.after) && t.after(e), e
    }

    function Qt(e) {
        var t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",
            n = t.length;
        return et((window.crypto || window.msCrypto).getRandomValues(new Uint8Array(e)), (function(e) {
            return t[e % n]
        })).join("")
    }

    function en(e, t) {
        if (Rt.os.android) {
            var n = JSON.stringify({
                appKey: Gt,
                KA: Nt
            });
            return "market://details?id=".concat(e, "&referrer=").concat(n)
        }
        return Rt.os.ios ? "https://itunes.apple.com/app/id".concat(t) : location.href
    }
    Xt.prototype = new Error;
    var tn = {};

    function nn(e, t, n) {
        var r = tn[t];
        return r && r.close && !r.closed && r.close(), tn[t] = window.open(e, t, n), tn[t]
    }

    function rn() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 480,
            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 700,
            n = window.screenLeft ? window.screenLeft : window.screenX ? window.screenX : 0,
            r = window.screenTop ? window.screenTop : window.screenY ? window.screenY : 0;
        return ["width=".concat(e), "height=".concat(t), "left=".concat(screen.width / 2 - e / 2 + n), "top=".concat(screen.height / 2 - t / 2 + r), "scrollbars=yes", "resizable=1"].join(",")
    }

    function on(e, t, n) {
        He(n, (function(n, r) {
            var o = t.getAttribute(n);
            null !== o && (e[r] = "true" === o || "false" === o ? "true" === o : o)
        }))
    }

    function an(e, t, n, r) {
        var o = Rt.browser.msie ? {} : nn(e, n, r || rn());
        return o.focus && o.focus(), cn(e, t, n), o
    }

    function cn(e, t) {
        var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "_self",
            r = document.createElement("form");
        r.setAttribute("accept-charset", "utf-8"), r.setAttribute("method", "post"), r.setAttribute("action", e), r.setAttribute("target", n), r.setAttribute("style", "display:none"), He(t, (function(e, t) {
            var n = document.createElement("input");
            n.type = "hidden", n.name = t, n.value = Ge(e) ? e : JSON.stringify(e), r.appendChild(n)
        })), document.body.appendChild(r), r.submit(), document.body.removeChild(r)
    }
    var un = {
        exports: {}
    };

    function sn(e) {
        return new un.exports.Promise((function(t, n) {
            ! function(e, t) {
                var n = e.url,
                    r = e.method,
                    o = e.headers,
                    i = e.data,
                    a = new XMLHttpRequest;
                a.open(r, n),
                    function(e) {
                        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                        Object.keys(t).forEach((function(n) {
                            e.setRequestHeader(n, t[n])
                        }))
                    }(a, o), a.onreadystatechange = function() {
                        a.readyState === XMLHttpRequest.DONE && t(a)
                    }, a.send(i)
            }(e, (function(e) {
                var r = e.status,
                    o = function(e) {
                        try {
                            return JSON.parse(e)
                        } catch (t) {
                            return e
                        }
                    }(e.response);
                200 === r ? t(o) : n(o)
            }))
        }))
    }! function(e, t) {
        e.exports = function() {
            function e(e) {
                var t = typeof e;
                return null !== e && ("object" === t || "function" === t)
            }

            function t(e) {
                return "function" == typeof e
            }
            var n = Array.isArray ? Array.isArray : function(e) {
                    return "[object Array]" === Object.prototype.toString.call(e)
                },
                r = 0,
                o = void 0,
                i = void 0,
                a = function(e, t) {
                    b[r] = e, b[r + 1] = t, 2 === (r += 2) && (i ? i(_) : k())
                };

            function c(e) {
                i = e
            }

            function u(e) {
                a = e
            }
            var s = "undefined" != typeof window ? window : void 0,
                l = s || {},
                p = l.MutationObserver || l.WebKitMutationObserver,
                f = "undefined" == typeof self && "undefined" != typeof process && "[object process]" === {}.toString.call(process),
                d = "undefined" != typeof Uint8ClampedArray && "undefined" != typeof importScripts && "undefined" != typeof MessageChannel;

            function h() {
                return function() {
                    return process.nextTick(_)
                }
            }

            function v() {
                return void 0 !== o ? function() {
                    o(_)
                } : g()
            }

            function m() {
                var e = 0,
                    t = new p(_),
                    n = document.createTextNode("");
                return t.observe(n, {
                        characterData: !0
                    }),
                    function() {
                        n.data = e = ++e % 2
                    }
            }

            function y() {
                var e = new MessageChannel;
                return e.port1.onmessage = _,
                    function() {
                        return e.port2.postMessage(0)
                    }
            }

            function g() {
                var e = setTimeout;
                return function() {
                    return e(_, 1)
                }
            }
            var b = new Array(1e3);

            function _() {
                for (var e = 0; e < r; e += 2)(0, b[e])(b[e + 1]), b[e] = void 0, b[e + 1] = void 0;
                r = 0
            }

            function w() {
                try {
                    var e = Function("return this")().require("vertx");
                    return o = e.runOnLoop || e.runOnContext, v()
                } catch (e) {
                    return g()
                }
            }
            var k = void 0;

            function S(e, t) {
                var n = this,
                    r = new this.constructor(O);
                void 0 === r[x] && $(r);
                var o = n._state;
                if (o) {
                    var i = arguments[o - 1];
                    a((function() {
                        return K(o, r, i, n._result)
                    }))
                } else L(n, r, e, t);
                return r
            }

            function j(e) {
                var t = this;
                if (e && "object" == typeof e && e.constructor === t) return e;
                var n = new t(O);
                return q(n, e), n
            }
            k = f ? h() : p ? m() : d ? y() : void 0 === s ? w() : g();
            var x = Math.random().toString(36).substring(2);

            function O() {}
            var A = void 0,
                T = 1,
                C = 2;

            function P() {
                return new TypeError("You cannot resolve a promise with itself")
            }

            function I() {
                return new TypeError("A promises callback cannot return that same promise.")
            }

            function B(e, t, n, r) {
                try {
                    e.call(t, n, r)
                } catch (e) {
                    return e
                }
            }

            function E(e, t, n) {
                a((function(e) {
                    var r = !1,
                        o = B(n, t, (function(n) {
                            r || (r = !0, t !== n ? q(e, n) : D(e, n))
                        }), (function(t) {
                            r || (r = !0, R(e, t))
                        }), "Settle: " + (e._label || " unknown promise"));
                    !r && o && (r = !0, R(e, o))
                }), e)
            }

            function z(e, t) {
                t._state === T ? D(e, t._result) : t._state === C ? R(e, t._result) : L(t, void 0, (function(t) {
                    return q(e, t)
                }), (function(t) {
                    return R(e, t)
                }))
            }

            function F(e, n, r) {
                n.constructor === e.constructor && r === S && n.constructor.resolve === j ? z(e, n) : void 0 === r ? D(e, n) : t(r) ? E(e, n, r) : D(e, n)
            }

            function q(t, n) {
                if (t === n) R(t, P());
                else if (e(n)) {
                    var r = void 0;
                    try {
                        r = n.then
                    } catch (e) {
                        return void R(t, e)
                    }
                    F(t, n, r)
                } else D(t, n)
            }

            function U(e) {
                e._onerror && e._onerror(e._result), M(e)
            }

            function D(e, t) {
                e._state === A && (e._result = t, e._state = T, 0 !== e._subscribers.length && a(M, e))
            }

            function R(e, t) {
                e._state === A && (e._state = C, e._result = t, a(U, e))
            }

            function L(e, t, n, r) {
                var o = e._subscribers,
                    i = o.length;
                e._onerror = null, o[i] = t, o[i + T] = n, o[i + C] = r, 0 === i && e._state && a(M, e)
            }

            function M(e) {
                var t = e._subscribers,
                    n = e._state;
                if (0 !== t.length) {
                    for (var r = void 0, o = void 0, i = e._result, a = 0; a < t.length; a += 3) r = t[a], o = t[a + n], r ? K(n, r, o, i) : o(i);
                    e._subscribers.length = 0
                }
            }

            function K(e, n, r, o) {
                var i = t(r),
                    a = void 0,
                    c = void 0,
                    u = !0;
                if (i) {
                    try {
                        a = r(o)
                    } catch (e) {
                        u = !1, c = e
                    }
                    if (n === a) return void R(n, I())
                } else a = o;
                n._state !== A || (i && u ? q(n, a) : !1 === u ? R(n, c) : e === T ? D(n, a) : e === C && R(n, a))
            }

            function N(e, t) {
                try {
                    t((function(t) {
                        q(e, t)
                    }), (function(t) {
                        R(e, t)
                    }))
                } catch (t) {
                    R(e, t)
                }
            }
            var H = 0;

            function G() {
                return H++
            }

            function $(e) {
                e[x] = H++, e._state = void 0, e._result = void 0, e._subscribers = []
            }

            function J() {
                return new Error("Array Methods must be provided an Array")
            }
            var X = function() {
                function e(e, t) {
                    this._instanceConstructor = e, this.promise = new e(O), this.promise[x] || $(this.promise), n(t) ? (this.length = t.length, this._remaining = t.length, this._result = new Array(this.length), 0 === this.length ? D(this.promise, this._result) : (this.length = this.length || 0, this._enumerate(t), 0 === this._remaining && D(this.promise, this._result))) : R(this.promise, J())
                }
                return e.prototype._enumerate = function(e) {
                    for (var t = 0; this._state === A && t < e.length; t++) this._eachEntry(e[t], t)
                }, e.prototype._eachEntry = function(e, t) {
                    var n = this._instanceConstructor,
                        r = n.resolve;
                    if (r === j) {
                        var o = void 0,
                            i = void 0,
                            a = !1;
                        try {
                            o = e.then
                        } catch (e) {
                            a = !0, i = e
                        }
                        if (o === S && e._state !== A) this._settledAt(e._state, t, e._result);
                        else if ("function" != typeof o) this._remaining--, this._result[t] = e;
                        else if (n === ee) {
                            var c = new n(O);
                            a ? R(c, i) : F(c, e, o), this._willSettleAt(c, t)
                        } else this._willSettleAt(new n((function(t) {
                            return t(e)
                        })), t)
                    } else this._willSettleAt(r(e), t)
                }, e.prototype._settledAt = function(e, t, n) {
                    var r = this.promise;
                    r._state === A && (this._remaining--, e === C ? R(r, n) : this._result[t] = n), 0 === this._remaining && D(r, this._result)
                }, e.prototype._willSettleAt = function(e, t) {
                    var n = this;
                    L(e, void 0, (function(e) {
                        return n._settledAt(T, t, e)
                    }), (function(e) {
                        return n._settledAt(C, t, e)
                    }))
                }, e
            }();

            function W(e) {
                return new X(this, e).promise
            }

            function V(e) {
                var t = this;
                return n(e) ? new t((function(n, r) {
                    for (var o = e.length, i = 0; i < o; i++) t.resolve(e[i]).then(n, r)
                })) : new t((function(e, t) {
                    return t(new TypeError("You must pass an array to race."))
                }))
            }

            function Y(e) {
                var t = new this(O);
                return R(t, e), t
            }

            function Z() {
                throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")
            }

            function Q() {
                throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")
            }
            var ee = function() {
                function e(t) {
                    this[x] = G(), this._result = this._state = void 0, this._subscribers = [], O !== t && ("function" != typeof t && Z(), this instanceof e ? N(this, t) : Q())
                }
                return e.prototype.catch = function(e) {
                    return this.then(null, e)
                }, e.prototype.finally = function(e) {
                    var n = this,
                        r = n.constructor;
                    return t(e) ? n.then((function(t) {
                        return r.resolve(e()).then((function() {
                            return t
                        }))
                    }), (function(t) {
                        return r.resolve(e()).then((function() {
                            throw t
                        }))
                    })) : n.then(e, e)
                }, e
            }();

            function te() {
                var e = void 0;
                if (void 0 !== Tt) e = Tt;
                else if ("undefined" != typeof self) e = self;
                else try {
                    e = Function("return this")()
                } catch (e) {
                    throw new Error("polyfill failed because global object is unavailable in this environment")
                }
                var t = e.Promise;
                if (t) {
                    var n = null;
                    try {
                        n = Object.prototype.toString.call(t.resolve())
                    } catch (e) {}
                    if ("[object Promise]" === n && !t.cast) return
                }
                e.Promise = ee
            }
            return ee.prototype.then = S, ee.all = W, ee.race = V, ee.resolve = j, ee.reject = Y, ee._setScheduler = c, ee._setAsap = u, ee._asap = a, ee.polyfill = te, ee.Promise = ee, ee
        }()
    }(un);
    var ln = {};
    var pn = {
            subscribe: function(e, t) {
                ln[e] = ln[e] || [], ln[e].push(t)
            },
            unsubscribe: function(e, t) {
                for (var n = ln[e], r = 0; r < n.length; r++)
                    if (n[r] === t) return void n.splice(r, 1)
            },
            dispatch: function(e) {
                He(ln[e], (function(e) {
                    e()
                }))
            }
        },
        fn = function() {
            function e(t, n) {
                dt(this, e), this._interval = t, this._maxCount = n, this._count = 0, this._stopped = !1, this._timeout = null
            }
            return vt(e, [{
                key: "start",
                value: function(e, t) {
                    null !== this._timeout && this.stop(), this._count = 0, this._stopped = !1, this._doPolling(e, t)
                }
            }, {
                key: "_doPolling",
                value: function(e, t) {
                    var n = this;
                    this._stopped || (this._timeout = setTimeout((function() {
                        ++n._count > n._maxCount ? t() : (e(), n._doPolling(e, t))
                    }), this._interval))
                }
            }, {
                key: "stop",
                value: function() {
                    this._stopped = !0, clearTimeout(this._timeout), this._timeout = null
                }
            }]), e
        }(),
        dn = {
            optional: {
                close: O,
                returnUrl: Ge,
                forceMobileLayout: $e,
                enableBackButton: $e
            },
            defaults: {
                close: ot,
                forceMobileLayout: !1,
                enableBackButton: !0
            }
        },
        hn = {
            authorize: {
                optional: {
                    redirectUri: Ge,
                    approvalType: st(["project"]),
                    scope: Ge,
                    throughTalk: $e,
                    channelPublicId: Ge,
                    serviceTerms: Ge,
                    isPopup: $e,
                    state: Ge,
                    deviceType: st(["watch", "tv"]),
                    prompts: Ge,
                    throughSyncplugin: $e,
                    loginHint: Ge,
                    nonce: Ge
                },
                defaults: {
                    throughTalk: !0,
                    isPopup: !1,
                    throughSyncplugin: !0
                }
            },
            selectShippingAddress: dn,
            createShippingAddress: dn,
            updateShippingAddress: ft({
                required: {
                    addressId: Xe
                }
            }, dn)
        };

    function vn(e) {
        return e.throughSyncplugin && Lt && /ch-home/i.test(Rt.ua)
    }

    function mn(e, t) {
        return !!(e && e.indexOf(t) > -1)
    }

    function yn(e) {
        return "".concat(Ht.authorize, "?").concat(ut(e))
    }

    function gn(e) {
        return ft(ft({}, function(e) {
            var t = {
                client_id: $t()
            };
            return e.approvalType && (t.approval_type = e.approvalType), e.scope && (t.scope = e.scope), e.prompts && (t.prompt = e.prompts), e.state && (t.state = e.state), e.nonce && (t.nonce = e.nonce), e.loginHint && (t.login_hint = e.loginHint), e.deviceType && (t.device_type = e.deviceType), e.channelPublicId && (t.channel_public_id = e.channelPublicId), e.serviceTerms && (t["extra.service_terms"] = e.serviceTerms), t
        }(e)), {}, {
            redirect_uri: e.redirectUri || Ht.redirectUri,
            response_type: "code",
            auth_tran_id: Qt(60)
        })
    }

    function bn(e, t) {
        return ft(ft({}, t), {}, {
            ka: Nt,
            is_popup: e.isPopup
        })
    }
    var _n = ["status", "error_code"],
        wn = new fn(1e3, 600);

    function kn(e) {
        var t, n, r, o, i, a, c = gn(e),
            u = bn(e, c),
            s = (n = (t = e).prompts, r = t.throughTalk, o = !(!Rt.os.ios && !Rt.os.android || Lt), i = mn(n, "login"), a = mn(n, "none"), r && o && !(Rt.os.android && /instagram|fb_iab/g.test(Rt.ua)) && !i && !a),
            l = vn(e),
            p = yn(u),
            f = s ? function(e, t, n) {
                var r = ft(ft({}, t), {}, {
                        is_popup: !0
                    }),
                    o = function() {
                        return ["intent:#Intent", "action=com.kakao.talk.intent.action.CAPRI_LOGGED_IN_ACTIVITY", "launchFlags=0x08880000", "S.com.kakao.sdk.talk.appKey=".concat($t()), "S.com.kakao.sdk.talk.redirectUri=".concat(r.redirect_uri), "S.com.kakao.sdk.talk.kaHeader=".concat(Nt), "S.com.kakao.sdk.talk.extraparams=".concat(encodeURIComponent(JSON.stringify(r)))].concat(jt(e.state ? ["S.com.kakao.sdk.talk.state=".concat(e.state)] : []), ["S.browser_fallback_url=".concat(encodeURIComponent(n)), "end;"]).join(";")
                    },
                    i = function() {
                        var t = yn(r),
                            o = e.isPopup ? t : n,
                            i = "".concat(t, "&ka=").concat(encodeURIComponent(Nt)),
                            a = "".concat(Ht.talkInappScheme, "?url=").concat(encodeURIComponent(i));
                        return "".concat(Ht.universalKakaoLink).concat(encodeURIComponent(a), "&web=").concat(encodeURIComponent(o))
                    };
                return Rt.os.android ? o() : i()
            }(e, c, p) : p,
            d = null;
        return l ? function(e) {
            var t = ft(ft({}, e), {}, {
                    is_popup: !0,
                    approval_window_type: "v4_bizplugin"
                }),
                n = encodeURIComponent(ut(t));
            location.href = "".concat(Ht.talkSyncpluginScheme, "&query=").concat(n)
        }(u) : e.isPopup ? d = nn(f, "_blank", rn()) : location.href = f, new un.exports.Promise((function(t, n) {
            if (s || l || e.isPopup) {
                var r = (o = c.auth_tran_id, {
                    client_id: $t(),
                    auth_tran_id: o,
                    ka: Nt
                });
                wn.start((function() {
                    var o;
                    (o = r, sn({
                        method: "GET",
                        url: "".concat(Ht.authDomain, "/apiweb/code.json?").concat(ut(o))
                    })).then((function(r) {
                        var o = function(e) {
                            var t = e.status,
                                n = e.error_code,
                                r = _t(e, _n);
                            if ("300" === n) return null;
                            "error" === t && "700" === n && (location.href = "".concat(Ht.authDomain, "/error/network"));
                            return r
                        }(r);
                        null !== o && (wn.stop(), d && d.close && d.close(), o.error ? n(o) : t(o), Sn(e, o)), !s && d && d.closed && wn.stop()
                    }))
                }), (function() {
                    var t = ft({
                        error: "timeout",
                        error_description: "LOGIN_TIMEOUT"
                    }, e.state && {
                        state: e.state
                    });
                    n(t), Sn(e, t)
                }))
            }
            var o
        }))
    }

    function Sn(e, t) {
        var n = e.redirectUri;
        if (n) {
            var r = n.indexOf("?") > -1 ? "&" : "?";
            location.href = n + r + ut(t)
        }
    }
    var jn, xn = Object.freeze({
            __proto__: null,
            authorize: function(e) {
                if (mn((e = Zt(e, hn.authorize, "Auth.authorize")).prompts, "none") && !Lt) {
                    var t = ft({
                        error: "auto_login",
                        error_description: "NOT_SUPPORTED_BROWSER"
                    }, e.state && {
                        state: e.state
                    });
                    return Sn(e, t), un.exports.Promise.reject(t)
                }
                var n, r, o, i;
                if (pn.dispatch("LOGIN_START"), r = (n = e).throughSyncplugin, o = n.isPopup, i = n.prompts, !vn({
                        throughSyncplugin: r
                    }) || !1 !== o || mn(i, "cert") || !window.kakaoweb || "function" != typeof window.kakaoweb.reqSignInLocation) return kn(e);
                (function(e) {
                    var t = gn(e),
                        n = bn(e, t),
                        r = ut(ft(ft({}, n), {}, {
                            is_popup: !1,
                            prompt: "none"
                        }));
                    return kakaoweb.reqSignInLocation(r).then((function(t) {
                        var n = Object.fromEntries(new window.URL(t).searchParams);
                        return "consent_required" === n.error && !mn(e.prompts, "none") || "interaction_required" === n.error || (Sn(e, n), !1)
                    })).catch((function(e) {
                        return !1
                    }))
                })(e).then((function(t) {
                    t && kn(e)
                }))
            }
        }),
        On = {
            exports: {}
        },
        An = {
            exports: {}
        };

    function Tn() {
        return jn || (jn = 1, function(e, t) {
            var n;
            e.exports = (n = n || function(e, t) {
                var n;
                if ("undefined" != typeof window && window.crypto && (n = window.crypto), "undefined" != typeof self && self.crypto && (n = self.crypto), "undefined" != typeof globalThis && globalThis.crypto && (n = globalThis.crypto), !n && "undefined" != typeof window && window.msCrypto && (n = window.msCrypto), !n && void 0 !== Tt && Tt.crypto && (n = Tt.crypto), !n) try {
                    n = require("crypto")
                } catch (e) {}
                var r = function() {
                        if (n) {
                            if ("function" == typeof n.getRandomValues) try {
                                return n.getRandomValues(new Uint32Array(1))[0]
                            } catch (e) {}
                            if ("function" == typeof n.randomBytes) try {
                                return n.randomBytes(4).readInt32LE()
                            } catch (e) {}
                        }
                        throw new Error("Native crypto module could not be used to get secure random number.")
                    },
                    o = Object.create || function() {
                        function e() {}
                        return function(t) {
                            var n;
                            return e.prototype = t, n = new e, e.prototype = null, n
                        }
                    }(),
                    i = {},
                    a = i.lib = {},
                    c = a.Base = {
                        extend: function(e) {
                            var t = o(this);
                            return e && t.mixIn(e), t.hasOwnProperty("init") && this.init !== t.init || (t.init = function() {
                                t.$super.init.apply(this, arguments)
                            }), t.init.prototype = t, t.$super = this, t
                        },
                        create: function() {
                            var e = this.extend();
                            return e.init.apply(e, arguments), e
                        },
                        init: function() {},
                        mixIn: function(e) {
                            for (var t in e) e.hasOwnProperty(t) && (this[t] = e[t]);
                            e.hasOwnProperty("toString") && (this.toString = e.toString)
                        },
                        clone: function() {
                            return this.init.prototype.extend(this)
                        }
                    },
                    u = a.WordArray = c.extend({
                        init: function(e, n) {
                            e = this.words = e || [], this.sigBytes = n != t ? n : 4 * e.length
                        },
                        toString: function(e) {
                            return (e || l).stringify(this)
                        },
                        concat: function(e) {
                            var t = this.words,
                                n = e.words,
                                r = this.sigBytes,
                                o = e.sigBytes;
                            if (this.clamp(), r % 4)
                                for (var i = 0; i < o; i++) {
                                    var a = n[i >>> 2] >>> 24 - i % 4 * 8 & 255;
                                    t[r + i >>> 2] |= a << 24 - (r + i) % 4 * 8
                                } else
                                    for (var c = 0; c < o; c += 4) t[r + c >>> 2] = n[c >>> 2];
                            return this.sigBytes += o, this
                        },
                        clamp: function() {
                            var t = this.words,
                                n = this.sigBytes;
                            t[n >>> 2] &= 4294967295 << 32 - n % 4 * 8, t.length = e.ceil(n / 4)
                        },
                        clone: function() {
                            var e = c.clone.call(this);
                            return e.words = this.words.slice(0), e
                        },
                        random: function(e) {
                            for (var t = [], n = 0; n < e; n += 4) t.push(r());
                            return new u.init(t, e)
                        }
                    }),
                    s = i.enc = {},
                    l = s.Hex = {
                        stringify: function(e) {
                            for (var t = e.words, n = e.sigBytes, r = [], o = 0; o < n; o++) {
                                var i = t[o >>> 2] >>> 24 - o % 4 * 8 & 255;
                                r.push((i >>> 4).toString(16)), r.push((15 & i).toString(16))
                            }
                            return r.join("")
                        },
                        parse: function(e) {
                            for (var t = e.length, n = [], r = 0; r < t; r += 2) n[r >>> 3] |= parseInt(e.substr(r, 2), 16) << 24 - r % 8 * 4;
                            return new u.init(n, t / 2)
                        }
                    },
                    p = s.Latin1 = {
                        stringify: function(e) {
                            for (var t = e.words, n = e.sigBytes, r = [], o = 0; o < n; o++) {
                                var i = t[o >>> 2] >>> 24 - o % 4 * 8 & 255;
                                r.push(String.fromCharCode(i))
                            }
                            return r.join("")
                        },
                        parse: function(e) {
                            for (var t = e.length, n = [], r = 0; r < t; r++) n[r >>> 2] |= (255 & e.charCodeAt(r)) << 24 - r % 4 * 8;
                            return new u.init(n, t)
                        }
                    },
                    f = s.Utf8 = {
                        stringify: function(e) {
                            try {
                                return decodeURIComponent(escape(p.stringify(e)))
                            } catch (e) {
                                throw new Error("Malformed UTF-8 data")
                            }
                        },
                        parse: function(e) {
                            return p.parse(unescape(encodeURIComponent(e)))
                        }
                    },
                    d = a.BufferedBlockAlgorithm = c.extend({
                        reset: function() {
                            this._data = new u.init, this._nDataBytes = 0
                        },
                        _append: function(e) {
                            "string" == typeof e && (e = f.parse(e)), this._data.concat(e), this._nDataBytes += e.sigBytes
                        },
                        _process: function(t) {
                            var n, r = this._data,
                                o = r.words,
                                i = r.sigBytes,
                                a = this.blockSize,
                                c = i / (4 * a),
                                s = (c = t ? e.ceil(c) : e.max((0 | c) - this._minBufferSize, 0)) * a,
                                l = e.min(4 * s, i);
                            if (s) {
                                for (var p = 0; p < s; p += a) this._doProcessBlock(o, p);
                                n = o.splice(0, s), r.sigBytes -= l
                            }
                            return new u.init(n, l)
                        },
                        clone: function() {
                            var e = c.clone.call(this);
                            return e._data = this._data.clone(), e
                        },
                        _minBufferSize: 0
                    });
                a.Hasher = d.extend({
                    cfg: c.extend(),
                    init: function(e) {
                        this.cfg = this.cfg.extend(e), this.reset()
                    },
                    reset: function() {
                        d.reset.call(this), this._doReset()
                    },
                    update: function(e) {
                        return this._append(e), this._process(), this
                    },
                    finalize: function(e) {
                        return e && this._append(e), this._doFinalize()
                    },
                    blockSize: 16,
                    _createHelper: function(e) {
                        return function(t, n) {
                            return new e.init(n).finalize(t)
                        }
                    },
                    _createHmacHelper: function(e) {
                        return function(t, n) {
                            return new h.HMAC.init(e, n).finalize(t)
                        }
                    }
                });
                var h = i.algo = {};
                return i
            }(Math), n)
        }(An)), An.exports
    }! function(e, t) {
        var n;
        e.exports = (n = Tn(), function(e) {
            var t = n,
                r = t.lib,
                o = r.WordArray,
                i = r.Hasher,
                a = t.algo,
                c = [];
            ! function() {
                for (var t = 0; t < 64; t++) c[t] = 4294967296 * e.abs(e.sin(t + 1)) | 0
            }();
            var u = a.MD5 = i.extend({
                _doReset: function() {
                    this._hash = new o.init([1732584193, 4023233417, 2562383102, 271733878])
                },
                _doProcessBlock: function(e, t) {
                    for (var n = 0; n < 16; n++) {
                        var r = t + n,
                            o = e[r];
                        e[r] = 16711935 & (o << 8 | o >>> 24) | 4278255360 & (o << 24 | o >>> 8)
                    }
                    var i = this._hash.words,
                        a = e[t + 0],
                        u = e[t + 1],
                        d = e[t + 2],
                        h = e[t + 3],
                        v = e[t + 4],
                        m = e[t + 5],
                        y = e[t + 6],
                        g = e[t + 7],
                        b = e[t + 8],
                        _ = e[t + 9],
                        w = e[t + 10],
                        k = e[t + 11],
                        S = e[t + 12],
                        j = e[t + 13],
                        x = e[t + 14],
                        O = e[t + 15],
                        A = i[0],
                        T = i[1],
                        C = i[2],
                        P = i[3];
                    A = s(A, T, C, P, a, 7, c[0]), P = s(P, A, T, C, u, 12, c[1]), C = s(C, P, A, T, d, 17, c[2]), T = s(T, C, P, A, h, 22, c[3]), A = s(A, T, C, P, v, 7, c[4]), P = s(P, A, T, C, m, 12, c[5]), C = s(C, P, A, T, y, 17, c[6]), T = s(T, C, P, A, g, 22, c[7]), A = s(A, T, C, P, b, 7, c[8]), P = s(P, A, T, C, _, 12, c[9]), C = s(C, P, A, T, w, 17, c[10]), T = s(T, C, P, A, k, 22, c[11]), A = s(A, T, C, P, S, 7, c[12]), P = s(P, A, T, C, j, 12, c[13]), C = s(C, P, A, T, x, 17, c[14]), A = l(A, T = s(T, C, P, A, O, 22, c[15]), C, P, u, 5, c[16]), P = l(P, A, T, C, y, 9, c[17]), C = l(C, P, A, T, k, 14, c[18]), T = l(T, C, P, A, a, 20, c[19]), A = l(A, T, C, P, m, 5, c[20]), P = l(P, A, T, C, w, 9, c[21]), C = l(C, P, A, T, O, 14, c[22]), T = l(T, C, P, A, v, 20, c[23]), A = l(A, T, C, P, _, 5, c[24]), P = l(P, A, T, C, x, 9, c[25]), C = l(C, P, A, T, h, 14, c[26]), T = l(T, C, P, A, b, 20, c[27]), A = l(A, T, C, P, j, 5, c[28]), P = l(P, A, T, C, d, 9, c[29]), C = l(C, P, A, T, g, 14, c[30]), A = p(A, T = l(T, C, P, A, S, 20, c[31]), C, P, m, 4, c[32]), P = p(P, A, T, C, b, 11, c[33]), C = p(C, P, A, T, k, 16, c[34]), T = p(T, C, P, A, x, 23, c[35]), A = p(A, T, C, P, u, 4, c[36]), P = p(P, A, T, C, v, 11, c[37]), C = p(C, P, A, T, g, 16, c[38]), T = p(T, C, P, A, w, 23, c[39]), A = p(A, T, C, P, j, 4, c[40]), P = p(P, A, T, C, a, 11, c[41]), C = p(C, P, A, T, h, 16, c[42]), T = p(T, C, P, A, y, 23, c[43]), A = p(A, T, C, P, _, 4, c[44]), P = p(P, A, T, C, S, 11, c[45]), C = p(C, P, A, T, O, 16, c[46]), A = f(A, T = p(T, C, P, A, d, 23, c[47]), C, P, a, 6, c[48]), P = f(P, A, T, C, g, 10, c[49]), C = f(C, P, A, T, x, 15, c[50]), T = f(T, C, P, A, m, 21, c[51]), A = f(A, T, C, P, S, 6, c[52]), P = f(P, A, T, C, h, 10, c[53]), C = f(C, P, A, T, w, 15, c[54]), T = f(T, C, P, A, u, 21, c[55]), A = f(A, T, C, P, b, 6, c[56]), P = f(P, A, T, C, O, 10, c[57]), C = f(C, P, A, T, y, 15, c[58]), T = f(T, C, P, A, j, 21, c[59]), A = f(A, T, C, P, v, 6, c[60]), P = f(P, A, T, C, k, 10, c[61]), C = f(C, P, A, T, d, 15, c[62]), T = f(T, C, P, A, _, 21, c[63]), i[0] = i[0] + A | 0, i[1] = i[1] + T | 0, i[2] = i[2] + C | 0, i[3] = i[3] + P | 0
                },
                _doFinalize: function() {
                    var t = this._data,
                        n = t.words,
                        r = 8 * this._nDataBytes,
                        o = 8 * t.sigBytes;
                    n[o >>> 5] |= 128 << 24 - o % 32;
                    var i = e.floor(r / 4294967296),
                        a = r;
                    n[15 + (o + 64 >>> 9 << 4)] = 16711935 & (i << 8 | i >>> 24) | 4278255360 & (i << 24 | i >>> 8), n[14 + (o + 64 >>> 9 << 4)] = 16711935 & (a << 8 | a >>> 24) | 4278255360 & (a << 24 | a >>> 8), t.sigBytes = 4 * (n.length + 1), this._process();
                    for (var c = this._hash, u = c.words, s = 0; s < 4; s++) {
                        var l = u[s];
                        u[s] = 16711935 & (l << 8 | l >>> 24) | 4278255360 & (l << 24 | l >>> 8)
                    }
                    return c
                },
                clone: function() {
                    var e = i.clone.call(this);
                    return e._hash = this._hash.clone(), e
                }
            });

            function s(e, t, n, r, o, i, a) {
                var c = e + (t & n | ~t & r) + o + a;
                return (c << i | c >>> 32 - i) + t
            }

            function l(e, t, n, r, o, i, a) {
                var c = e + (t & r | n & ~r) + o + a;
                return (c << i | c >>> 32 - i) + t
            }

            function p(e, t, n, r, o, i, a) {
                var c = e + (t ^ n ^ r) + o + a;
                return (c << i | c >>> 32 - i) + t
            }

            function f(e, t, n, r, o, i, a) {
                var c = e + (n ^ (t | ~r)) + o + a;
                return (c << i | c >>> 32 - i) + t
            }
            t.MD5 = i._createHelper(u), t.HmacMD5 = i._createHmacHelper(u)
        }(Math), n.MD5)
    }(On);
    var Cn, Pn = On.exports,
        In = {
            exports: {}
        },
        Bn = {
            exports: {}
        };

    function En() {
        return Cn || (Cn = 1, function(e, t) {
            var n;
            e.exports = (n = Tn(), function() {
                var e = n,
                    t = e.lib.WordArray;

                function r(e, n, r) {
                    for (var o = [], i = 0, a = 0; a < n; a++)
                        if (a % 4) {
                            var c = r[e.charCodeAt(a - 1)] << a % 4 * 2 | r[e.charCodeAt(a)] >>> 6 - a % 4 * 2;
                            o[i >>> 2] |= c << 24 - i % 4 * 8, i++
                        }
                    return t.create(o, i)
                }
                e.enc.Base64 = {
                    stringify: function(e) {
                        var t = e.words,
                            n = e.sigBytes,
                            r = this._map;
                        e.clamp();
                        for (var o = [], i = 0; i < n; i += 3)
                            for (var a = (t[i >>> 2] >>> 24 - i % 4 * 8 & 255) << 16 | (t[i + 1 >>> 2] >>> 24 - (i + 1) % 4 * 8 & 255) << 8 | t[i + 2 >>> 2] >>> 24 - (i + 2) % 4 * 8 & 255, c = 0; c < 4 && i + .75 * c < n; c++) o.push(r.charAt(a >>> 6 * (3 - c) & 63));
                        var u = r.charAt(64);
                        if (u)
                            for (; o.length % 4;) o.push(u);
                        return o.join("")
                    },
                    parse: function(e) {
                        var t = e.length,
                            n = this._map,
                            o = this._reverseMap;
                        if (!o) {
                            o = this._reverseMap = [];
                            for (var i = 0; i < n.length; i++) o[n.charCodeAt(i)] = i
                        }
                        var a = n.charAt(64);
                        if (a) {
                            var c = e.indexOf(a); - 1 !== c && (t = c)
                        }
                        return r(e, t, o)
                    },
                    _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
                }
            }(), n.enc.Base64)
        }(Bn)), Bn.exports
    }
    var zn, Fn = {
            exports: {}
        },
        qn = {
            exports: {}
        };

    function Un() {
        return zn || (zn = 1, function(e, t) {
            var n, r, o, i, a, c, u, s;
            e.exports = (s = Tn(), r = (n = s).lib, o = r.WordArray, i = r.Hasher, a = n.algo, c = [], u = a.SHA1 = i.extend({
                _doReset: function() {
                    this._hash = new o.init([1732584193, 4023233417, 2562383102, 271733878, 3285377520])
                },
                _doProcessBlock: function(e, t) {
                    for (var n = this._hash.words, r = n[0], o = n[1], i = n[2], a = n[3], u = n[4], s = 0; s < 80; s++) {
                        if (s < 16) c[s] = 0 | e[t + s];
                        else {
                            var l = c[s - 3] ^ c[s - 8] ^ c[s - 14] ^ c[s - 16];
                            c[s] = l << 1 | l >>> 31
                        }
                        var p = (r << 5 | r >>> 27) + u + c[s];
                        p += s < 20 ? 1518500249 + (o & i | ~o & a) : s < 40 ? 1859775393 + (o ^ i ^ a) : s < 60 ? (o & i | o & a | i & a) - 1894007588 : (o ^ i ^ a) - 899497514, u = a, a = i, i = o << 30 | o >>> 2, o = r, r = p
                    }
                    n[0] = n[0] + r | 0, n[1] = n[1] + o | 0, n[2] = n[2] + i | 0, n[3] = n[3] + a | 0, n[4] = n[4] + u | 0
                },
                _doFinalize: function() {
                    var e = this._data,
                        t = e.words,
                        n = 8 * this._nDataBytes,
                        r = 8 * e.sigBytes;
                    return t[r >>> 5] |= 128 << 24 - r % 32, t[14 + (r + 64 >>> 9 << 4)] = Math.floor(n / 4294967296), t[15 + (r + 64 >>> 9 << 4)] = n, e.sigBytes = 4 * t.length, this._process(), this._hash
                },
                clone: function() {
                    var e = i.clone.call(this);
                    return e._hash = this._hash.clone(), e
                }
            }), n.SHA1 = i._createHelper(u), n.HmacSHA1 = i._createHmacHelper(u), s.SHA1)
        }(qn)), qn.exports
    }
    var Dn, Rn, Ln = {
        exports: {}
    };

    function Mn() {
        return Rn || (Rn = 1, function(e, t) {
            var n;
            e.exports = (n = Tn(), Un(), Dn || (Dn = 1, function(e, t) {
                var n;
                e.exports = (n = Tn(), void
                    function() {
                        var e = n,
                            t = e.lib.Base,
                            r = e.enc.Utf8;
                        e.algo.HMAC = t.extend({
                            init: function(e, t) {
                                e = this._hasher = new e.init, "string" == typeof t && (t = r.parse(t));
                                var n = e.blockSize,
                                    o = 4 * n;
                                t.sigBytes > o && (t = e.finalize(t)), t.clamp();
                                for (var i = this._oKey = t.clone(), a = this._iKey = t.clone(), c = i.words, u = a.words, s = 0; s < n; s++) c[s] ^= 1549556828, u[s] ^= 909522486;
                                i.sigBytes = a.sigBytes = o, this.reset()
                            },
                            reset: function() {
                                var e = this._hasher;
                                e.reset(), e.update(this._iKey)
                            },
                            update: function(e) {
                                return this._hasher.update(e), this
                            },
                            finalize: function(e) {
                                var t = this._hasher,
                                    n = t.finalize(e);
                                return t.reset(), t.finalize(this._oKey.clone().concat(n))
                            }
                        })
                    }())
            }(Ln)), function() {
                var e = n,
                    t = e.lib,
                    r = t.Base,
                    o = t.WordArray,
                    i = e.algo,
                    a = i.MD5,
                    c = i.EvpKDF = r.extend({
                        cfg: r.extend({
                            keySize: 4,
                            hasher: a,
                            iterations: 1
                        }),
                        init: function(e) {
                            this.cfg = this.cfg.extend(e)
                        },
                        compute: function(e, t) {
                            for (var n, r = this.cfg, i = r.hasher.create(), a = o.create(), c = a.words, u = r.keySize, s = r.iterations; c.length < u;) {
                                n && i.update(n), n = i.update(e).finalize(t), i.reset();
                                for (var l = 1; l < s; l++) n = i.finalize(n), i.reset();
                                a.concat(n)
                            }
                            return a.sigBytes = 4 * u, a
                        }
                    });
                e.EvpKDF = function(e, t, n) {
                    return c.create(n).compute(e, t)
                }
            }(), n.EvpKDF)
        }(Fn)), Fn.exports
    }
    var Kn, Nn = {
        exports: {}
    };
    ! function(e, t) {
        var n;
        e.exports = (n = Tn(), En(), Mn(), Kn || (Kn = 1, function(e, t) {
            var n;
            e.exports = (n = Tn(), Mn(), void(n.lib.Cipher || function(e) {
                var t = n,
                    r = t.lib,
                    o = r.Base,
                    i = r.WordArray,
                    a = r.BufferedBlockAlgorithm,
                    c = t.enc;
                c.Utf8;
                var u = c.Base64,
                    s = t.algo.EvpKDF,
                    l = r.Cipher = a.extend({
                        cfg: o.extend(),
                        createEncryptor: function(e, t) {
                            return this.create(this._ENC_XFORM_MODE, e, t)
                        },
                        createDecryptor: function(e, t) {
                            return this.create(this._DEC_XFORM_MODE, e, t)
                        },
                        init: function(e, t, n) {
                            this.cfg = this.cfg.extend(n), this._xformMode = e, this._key = t, this.reset()
                        },
                        reset: function() {
                            a.reset.call(this), this._doReset()
                        },
                        process: function(e) {
                            return this._append(e), this._process()
                        },
                        finalize: function(e) {
                            return e && this._append(e), this._doFinalize()
                        },
                        keySize: 4,
                        ivSize: 4,
                        _ENC_XFORM_MODE: 1,
                        _DEC_XFORM_MODE: 2,
                        _createHelper: function() {
                            function e(e) {
                                return "string" == typeof e ? b : y
                            }
                            return function(t) {
                                return {
                                    encrypt: function(n, r, o) {
                                        return e(r).encrypt(t, n, r, o)
                                    },
                                    decrypt: function(n, r, o) {
                                        return e(r).decrypt(t, n, r, o)
                                    }
                                }
                            }
                        }()
                    });
                r.StreamCipher = l.extend({
                    _doFinalize: function() {
                        return this._process(!0)
                    },
                    blockSize: 1
                });
                var p = t.mode = {},
                    f = r.BlockCipherMode = o.extend({
                        createEncryptor: function(e, t) {
                            return this.Encryptor.create(e, t)
                        },
                        createDecryptor: function(e, t) {
                            return this.Decryptor.create(e, t)
                        },
                        init: function(e, t) {
                            this._cipher = e, this._iv = t
                        }
                    }),
                    d = p.CBC = function() {
                        var t = f.extend();

                        function n(t, n, r) {
                            var o, i = this._iv;
                            i ? (o = i, this._iv = e) : o = this._prevBlock;
                            for (var a = 0; a < r; a++) t[n + a] ^= o[a]
                        }
                        return t.Encryptor = t.extend({
                            processBlock: function(e, t) {
                                var r = this._cipher,
                                    o = r.blockSize;
                                n.call(this, e, t, o), r.encryptBlock(e, t), this._prevBlock = e.slice(t, t + o)
                            }
                        }), t.Decryptor = t.extend({
                            processBlock: function(e, t) {
                                var r = this._cipher,
                                    o = r.blockSize,
                                    i = e.slice(t, t + o);
                                r.decryptBlock(e, t), n.call(this, e, t, o), this._prevBlock = i
                            }
                        }), t
                    }(),
                    h = (t.pad = {}).Pkcs7 = {
                        pad: function(e, t) {
                            for (var n = 4 * t, r = n - e.sigBytes % n, o = r << 24 | r << 16 | r << 8 | r, a = [], c = 0; c < r; c += 4) a.push(o);
                            var u = i.create(a, r);
                            e.concat(u)
                        },
                        unpad: function(e) {
                            var t = 255 & e.words[e.sigBytes - 1 >>> 2];
                            e.sigBytes -= t
                        }
                    };
                r.BlockCipher = l.extend({
                    cfg: l.cfg.extend({
                        mode: d,
                        padding: h
                    }),
                    reset: function() {
                        var e;
                        l.reset.call(this);
                        var t = this.cfg,
                            n = t.iv,
                            r = t.mode;
                        this._xformMode == this._ENC_XFORM_MODE ? e = r.createEncryptor : (e = r.createDecryptor, this._minBufferSize = 1), this._mode && this._mode.__creator == e ? this._mode.init(this, n && n.words) : (this._mode = e.call(r, this, n && n.words), this._mode.__creator = e)
                    },
                    _doProcessBlock: function(e, t) {
                        this._mode.processBlock(e, t)
                    },
                    _doFinalize: function() {
                        var e, t = this.cfg.padding;
                        return this._xformMode == this._ENC_XFORM_MODE ? (t.pad(this._data, this.blockSize), e = this._process(!0)) : (e = this._process(!0), t.unpad(e)), e
                    },
                    blockSize: 4
                });
                var v = r.CipherParams = o.extend({
                        init: function(e) {
                            this.mixIn(e)
                        },
                        toString: function(e) {
                            return (e || this.formatter).stringify(this)
                        }
                    }),
                    m = (t.format = {}).OpenSSL = {
                        stringify: function(e) {
                            var t = e.ciphertext,
                                n = e.salt;
                            return (n ? i.create([1398893684, 1701076831]).concat(n).concat(t) : t).toString(u)
                        },
                        parse: function(e) {
                            var t, n = u.parse(e),
                                r = n.words;
                            return 1398893684 == r[0] && 1701076831 == r[1] && (t = i.create(r.slice(2, 4)), r.splice(0, 4), n.sigBytes -= 16), v.create({
                                ciphertext: n,
                                salt: t
                            })
                        }
                    },
                    y = r.SerializableCipher = o.extend({
                        cfg: o.extend({
                            format: m
                        }),
                        encrypt: function(e, t, n, r) {
                            r = this.cfg.extend(r);
                            var o = e.createEncryptor(n, r),
                                i = o.finalize(t),
                                a = o.cfg;
                            return v.create({
                                ciphertext: i,
                                key: n,
                                iv: a.iv,
                                algorithm: e,
                                mode: a.mode,
                                padding: a.padding,
                                blockSize: e.blockSize,
                                formatter: r.format
                            })
                        },
                        decrypt: function(e, t, n, r) {
                            return r = this.cfg.extend(r), t = this._parse(t, r.format), e.createDecryptor(n, r).finalize(t.ciphertext)
                        },
                        _parse: function(e, t) {
                            return "string" == typeof e ? t.parse(e, this) : e
                        }
                    }),
                    g = (t.kdf = {}).OpenSSL = {
                        execute: function(e, t, n, r) {
                            r || (r = i.random(8));
                            var o = s.create({
                                    keySize: t + n
                                }).compute(e, r),
                                a = i.create(o.words.slice(t), 4 * n);
                            return o.sigBytes = 4 * t, v.create({
                                key: o,
                                iv: a,
                                salt: r
                            })
                        }
                    },
                    b = r.PasswordBasedCipher = y.extend({
                        cfg: y.cfg.extend({
                            kdf: g
                        }),
                        encrypt: function(e, t, n, r) {
                            var o = (r = this.cfg.extend(r)).kdf.execute(n, e.keySize, e.ivSize);
                            r.iv = o.iv;
                            var i = y.encrypt.call(this, e, t, o.key, r);
                            return i.mixIn(o), i
                        },
                        decrypt: function(e, t, n, r) {
                            r = this.cfg.extend(r), t = this._parse(t, r.format);
                            var o = r.kdf.execute(n, e.keySize, e.ivSize, t.salt);
                            return r.iv = o.iv, y.decrypt.call(this, e, t, o.key, r)
                        }
                    })
            }()))
        }(Nn)), function() {
            var e = n,
                t = e.lib.BlockCipher,
                r = e.algo,
                o = [],
                i = [],
                a = [],
                c = [],
                u = [],
                s = [],
                l = [],
                p = [],
                f = [],
                d = [];
            ! function() {
                for (var e = [], t = 0; t < 256; t++) e[t] = t < 128 ? t << 1 : t << 1 ^ 283;
                var n = 0,
                    r = 0;
                for (t = 0; t < 256; t++) {
                    var h = r ^ r << 1 ^ r << 2 ^ r << 3 ^ r << 4;
                    h = h >>> 8 ^ 255 & h ^ 99, o[n] = h, i[h] = n;
                    var v = e[n],
                        m = e[v],
                        y = e[m],
                        g = 257 * e[h] ^ 16843008 * h;
                    a[n] = g << 24 | g >>> 8, c[n] = g << 16 | g >>> 16, u[n] = g << 8 | g >>> 24, s[n] = g, g = 16843009 * y ^ 65537 * m ^ 257 * v ^ 16843008 * n, l[h] = g << 24 | g >>> 8, p[h] = g << 16 | g >>> 16, f[h] = g << 8 | g >>> 24, d[h] = g, n ? (n = v ^ e[e[e[y ^ v]]], r ^= e[e[r]]) : n = r = 1
                }
            }();
            var h = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54],
                v = r.AES = t.extend({
                    _doReset: function() {
                        if (!this._nRounds || this._keyPriorReset !== this._key) {
                            for (var e = this._keyPriorReset = this._key, t = e.words, n = e.sigBytes / 4, r = 4 * ((this._nRounds = n + 6) + 1), i = this._keySchedule = [], a = 0; a < r; a++) a < n ? i[a] = t[a] : (s = i[a - 1], a % n ? n > 6 && a % n == 4 && (s = o[s >>> 24] << 24 | o[s >>> 16 & 255] << 16 | o[s >>> 8 & 255] << 8 | o[255 & s]) : (s = o[(s = s << 8 | s >>> 24) >>> 24] << 24 | o[s >>> 16 & 255] << 16 | o[s >>> 8 & 255] << 8 | o[255 & s], s ^= h[a / n | 0] << 24), i[a] = i[a - n] ^ s);
                            for (var c = this._invKeySchedule = [], u = 0; u < r; u++) {
                                if (a = r - u, u % 4) var s = i[a];
                                else s = i[a - 4];
                                c[u] = u < 4 || a <= 4 ? s : l[o[s >>> 24]] ^ p[o[s >>> 16 & 255]] ^ f[o[s >>> 8 & 255]] ^ d[o[255 & s]]
                            }
                        }
                    },
                    encryptBlock: function(e, t) {
                        this._doCryptBlock(e, t, this._keySchedule, a, c, u, s, o)
                    },
                    decryptBlock: function(e, t) {
                        var n = e[t + 1];
                        e[t + 1] = e[t + 3], e[t + 3] = n, this._doCryptBlock(e, t, this._invKeySchedule, l, p, f, d, i), n = e[t + 1], e[t + 1] = e[t + 3], e[t + 3] = n
                    },
                    _doCryptBlock: function(e, t, n, r, o, i, a, c) {
                        for (var u = this._nRounds, s = e[t] ^ n[0], l = e[t + 1] ^ n[1], p = e[t + 2] ^ n[2], f = e[t + 3] ^ n[3], d = 4, h = 1; h < u; h++) {
                            var v = r[s >>> 24] ^ o[l >>> 16 & 255] ^ i[p >>> 8 & 255] ^ a[255 & f] ^ n[d++],
                                m = r[l >>> 24] ^ o[p >>> 16 & 255] ^ i[f >>> 8 & 255] ^ a[255 & s] ^ n[d++],
                                y = r[p >>> 24] ^ o[f >>> 16 & 255] ^ i[s >>> 8 & 255] ^ a[255 & l] ^ n[d++],
                                g = r[f >>> 24] ^ o[s >>> 16 & 255] ^ i[l >>> 8 & 255] ^ a[255 & p] ^ n[d++];
                            s = v, l = m, p = y, f = g
                        }
                        v = (c[s >>> 24] << 24 | c[l >>> 16 & 255] << 16 | c[p >>> 8 & 255] << 8 | c[255 & f]) ^ n[d++], m = (c[l >>> 24] << 24 | c[p >>> 16 & 255] << 16 | c[f >>> 8 & 255] << 8 | c[255 & s]) ^ n[d++], y = (c[p >>> 24] << 24 | c[f >>> 16 & 255] << 16 | c[s >>> 8 & 255] << 8 | c[255 & l]) ^ n[d++], g = (c[f >>> 24] << 24 | c[s >>> 16 & 255] << 16 | c[l >>> 8 & 255] << 8 | c[255 & p]) ^ n[d++], e[t] = v, e[t + 1] = m, e[t + 2] = y, e[t + 3] = g
                    },
                    keySize: 8
                });
            e.AES = t._createHelper(v)
        }(), n.AES)
    }(In);
    var Hn = In.exports,
        Gn = {
            exports: {}
        };
    ! function(e, t) {
        e.exports = Tn().enc.Utf8
    }(Gn);
    var $n = Gn.exports;

    function Jn() {
        return $t()
    }
    var Xn = null;

    function Wn() {
        var e, t, n, r;
        return null === Xn && (e = er(), t = window.sessionStorage.getItem(e), Xn = t ? (n = t, r = Jn(), Hn.decrypt(n, r).toString($n)) : null), Xn
    }

    function Vn(e) {
        var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
        Xn = e, null === e || !1 === t ? Zn(er()) : Yn(er(), e)
    }

    function Yn(e, t) {
        var n, r, o = (n = t, r = Jn(), Hn.encrypt(n, r).toString());
        window.sessionStorage.setItem(e, o)
    }

    function Zn(e) {
        window.sessionStorage.removeItem(e)
    }
    var Qn = {};

    function er() {
        var e;
        return Qn.accessTokenKey || (Qn.accessTokenKey = "kakao_" + (e = "kat" + Jn(), Pn(e).toString())), Qn.accessTokenKey
    }
    var tr = Object.freeze({
        __proto__: null,
        getAppKey: Jn,
        getAccessToken: Wn,
        setAccessToken: Vn
    });

    function nr() {
        return "Bearer ".concat(Wn())
    }

    function rr() {
        return "KakaoAK ".concat(Jn())
    }
    var or = {
            permission: st(["A", "F", "M"]),
            enable_share: $e,
            android_exec_param: Ge,
            ios_exec_param: Ge,
            android_market_param: Ge,
            ios_market_param: Ge
        },
        ir = {
            secure_resource: $e
        };

    function ar(e) {
        !1 === e.secure_resource && (console && console.warn("KakaoWarning: The secure_resource parameter is deprecated."), e.secure_resource = !0)
    }

    function cr(e) {
        if (!Ge(e)) return !1;
        if (0 === e.length || e.length > 2048) throw new Xt("content length should be between 0 and 2048");
        return !0
    }
    var ur = {
            "/v1/user/signup": {
                method: "POST",
                data: {
                    optional: {
                        properties: f
                    }
                }
            },
            "/v1/user/unlink": {
                method: "POST",
                data: {}
            },
            "/v2/user/me": {
                method: "GET",
                data: {
                    optional: ft({
                        property_keys: d
                    }, ir)
                }
            },
            "/v1/user/logout": {
                method: "POST",
                data: {}
            },
            "/v1/user/update_profile": {
                method: "POST",
                data: {
                    required: {
                        properties: f
                    }
                }
            },
            "/v1/user/access_token_info": {
                method: "GET",
                data: {}
            },
            "/v2/user/scopes": {
                method: "GET",
                data: {
                    optional: {
                        scopes: d
                    }
                }
            },
            "/v2/user/revoke/scopes": {
                method: "POST",
                data: {
                    required: {
                        scopes: d
                    }
                }
            },
            "/v1/user/service/terms": {
                method: "GET",
                data: {
                    optional: {
                        extra: Ge
                    }
                }
            },
            "/v1/user/shipping_address": {
                method: "GET",
                data: {
                    optional: {
                        address_id: Xe,
                        from_updated_at: Xe,
                        page_size: Xe
                    }
                }
            },
            "/v1/api/talk/profile": {
                method: "GET",
                data: {
                    optional: ir,
                    after: ar
                }
            },
            "/v1/api/talk/friends": {
                method: "GET",
                data: {
                    optional: ft({
                        offset: Xe,
                        limit: Xe,
                        order: Ge,
                        friend_order: Ge
                    }, ir),
                    after: ar
                }
            },
            "/v1/friends": {
                method: "GET",
                data: {
                    optional: ft({
                        offset: Xe,
                        limit: Xe,
                        order: Ge,
                        friend_order: Ge
                    }, ir),
                    after: ar
                }
            },
            "/v2/api/talk/memo/send": {
                method: "POST",
                data: {
                    required: {
                        template_id: Xe
                    },
                    optional: {
                        template_args: f
                    }
                }
            },
            "/v2/api/talk/memo/scrap/send": {
                method: "POST",
                data: {
                    required: {
                        request_url: Ge
                    },
                    optional: {
                        template_id: Xe,
                        template_args: f
                    }
                }
            },
            "/v2/api/talk/memo/default/send": {
                method: "POST",
                data: {
                    required: {
                        template_object: f
                    }
                }
            },
            "/v1/api/talk/friends/message/send": {
                method: "POST",
                data: {
                    required: {
                        template_id: Xe,
                        receiver_uuids: d,
                        receiver_id_type: Ge
                    },
                    optional: {
                        template_args: f
                    },
                    defaults: {
                        receiver_id_type: "uuid"
                    }
                }
            },
            "/v1/api/talk/friends/message/scrap/send": {
                method: "POST",
                data: {
                    required: {
                        request_url: Ge,
                        receiver_uuids: d,
                        receiver_id_type: Ge
                    },
                    optional: {
                        template_id: Xe,
                        template_args: f
                    },
                    defaults: {
                        receiver_id_type: "uuid"
                    }
                }
            },
            "/v1/api/talk/friends/message/default/send": {
                method: "POST",
                data: {
                    required: {
                        template_object: f,
                        receiver_uuids: d,
                        receiver_id_type: Ge
                    },
                    defaults: {
                        receiver_id_type: "uuid"
                    }
                }
            },
            "/v2/api/kakaolink/talk/template/validate": {
                method: "GET",
                data: {
                    required: {
                        link_ver: Ge,
                        template_id: Xe
                    },
                    optional: {
                        template_args: f
                    }
                },
                authType: rr
            },
            "/v2/api/kakaolink/talk/template/scrap": {
                method: "GET",
                data: {
                    required: {
                        link_ver: Ge,
                        request_url: Ge
                    },
                    optional: {
                        template_id: Xe,
                        template_args: f
                    }
                },
                authType: rr
            },
            "/v2/api/kakaolink/talk/template/default": {
                method: "GET",
                data: {
                    required: {
                        link_ver: Ge,
                        template_object: f
                    }
                },
                authType: rr
            },
            "/v2/api/talk/message/image/upload": {
                method: "POST",
                data: {
                    required: {
                        file: f
                    }
                },
                authType: rr
            },
            "/v2/api/talk/message/image/delete": {
                method: "DELETE",
                data: {
                    required: {
                        image_url: Ge
                    }
                },
                authType: rr
            },
            "/v2/api/talk/message/image/scrap": {
                method: "POST",
                data: {
                    required: {
                        image_url: Ge
                    }
                },
                authType: rr
            },
            "/v1/api/story/profile": {
                method: "GET",
                data: {
                    optional: ir
                }
            },
            "/v1/api/story/isstoryuser": {
                method: "GET",
                data: {}
            },
            "/v1/api/story/mystory": {
                method: "GET",
                data: {
                    required: {
                        id: Ge
                    }
                }
            },
            "/v1/api/story/mystories": {
                method: "GET",
                data: {
                    optional: {
                        last_id: Ge
                    }
                }
            },
            "/v1/api/story/linkinfo": {
                method: "GET",
                data: {
                    required: {
                        url: Ge
                    }
                }
            },
            "/v1/api/story/post/note": {
                method: "POST",
                data: {
                    required: {
                        content: cr
                    },
                    optional: or
                }
            },
            "/v1/api/story/post/photo": {
                method: "POST",
                data: {
                    required: {
                        image_url_list: function(e) {
                            return !!d(e) && tt(e, (function(e) {
                                if (!Ge(e)) return !1;
                                if (/(http|ftp|https):\/\/[\w-]+(\.[\w-]+)+([\w.,@?^=%&amp;:\/~+#-]*[\w@?^=%&amp;\/~+#-])?/.test(e)) throw new Xt("url in image_url_list should be a kage url, obtained from '/v1/api/story/upload/multi'.");
                                return !0
                            }))
                        }
                    },
                    optional: ft({
                        content: cr
                    }, or)
                }
            },
            "/v1/api/story/post/link": {
                method: "POST",
                data: {
                    required: {
                        link_info: f
                    },
                    optional: ft({
                        content: cr
                    }, or)
                }
            },
            "/v1/api/story/upload/multi": {
                method: "POST",
                data: {}
            },
            "/v1/api/story/delete/mystory": {
                method: "DELETE",
                data: {
                    required: {
                        id: Ge
                    }
                }
            },
            "/v1/api/talk/channels": {
                method: "GET",
                data: {
                    optional: {
                        channel_public_ids: d
                    }
                }
            }
        },
        sr = {
            apiRules: ur,
            request: {
                required: {
                    url: st(rt(ur))
                },
                optional: {
                    data: f,
                    files: function(e) {
                        return lt([d, Qe])(e) && tt(e, lt([Ze, Ye]))
                    },
                    file: Ze
                },
                defaults: {
                    data: {}
                }
            }
        };

    function lr(e) {
        var t = e = Zt(e, sr.request, "API.request"),
            n = t.url,
            r = t.data,
            o = sr.apiRules[n].data;
        e.data = Zt(r, o, "API.request - ".concat(n));
        var i = function(e) {
            var t = sr.apiRules[e.url],
                n = t.method,
                r = t.authType,
                o = function(e) {
                    var t = e.url,
                        n = e.data,
                        r = e.files;
                    if (function(e) {
                            return "/v1/api/story/upload/multi" === e || "/v2/api/talk/message/image/upload" === e
                        }(t) || n.file) {
                        var o = r || n.file;
                        if (!o) throw new Xt("'files' parameter should be set for ".concat(t));
                        var i = new FormData;
                        return He(o, (function(e) {
                            return i.append("file", e)
                        })), [null, i]
                    }
                    var a = {};
                    return He(n, (function(e, t) {
                        a[t] = Ge(e) ? e : JSON.stringify(e)
                    })), ["application/x-www-form-urlencoded", ut(a)]
                }(e),
                i = St(o, 2),
                a = i[0],
                c = i[1],
                u = (f = Ht.apiDomain + e.url, "POST" === n ? [f, c] : ["".concat(f, "?").concat(c), null]),
                s = St(u, 2),
                l = s[0],
                p = s[1];
            var f;
            return {
                url: l,
                method: n,
                headers: ft(ft({}, a && {
                    "Content-Type": a
                }), {}, {
                    KA: Nt,
                    Authorization: (r || nr)(),
                    "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate",
                    Pragma: "no-cache"
                }),
                data: p
            }
        }(e);
        return sn(i)
    }
    var pr = Object.freeze({
        __proto__: null,
        request: lr
    });
    var fr = Object.freeze({
            __proto__: null,
            logout: function() {
                return lr({
                    url: "/v1/user/logout"
                }).finally((function() {
                    Vn(null), pn.dispatch("LOGOUT")
                }))
            },
            getStatusInfo: function() {
                return Wn() ? lr({
                    url: "/v2/user/me"
                }).then((function(e) {
                    return {
                        status: "connected",
                        user: e
                    }
                })).catch((function() {
                    return {
                        status: "not_connected"
                    }
                })) : un.exports.Promise.reject({
                    status: "not_connected"
                })
            }
        }),
        dr = function() {
            function e(t) {
                dt(this, e), this.domain = t
            }
            return vt(e, [{
                key: "createHiddenIframe",
                value: function(e, t) {
                    this.iframe && this.destroy(!0);
                    var n = document.createElement("iframe");
                    n.id = n.name = e, n.src = this.domain + t, n.setAttribute("style", "border:none; width:0; height:0; display:none; overflow:hidden;"), document.body.appendChild(n), this.iframe = n
                }
            }, {
                key: "retrieveMessage",
                value: function(e, t, n) {
                    var r = this;
                    return this.popup = an(this.domain + e, t, n), new un.exports.Promise((function(e, t) {
                        r.callback = function(n) {
                            var o = n.data,
                                i = n.origin;
                            if (o && i === r.domain) try {
                                var a = JSON.parse(o);
                                a.code ? t(a) : e(a)
                            } catch (e) {
                                ct(window, "message", r.callback)
                            }
                        }, at(window, "message", r.callback), r.interval = setInterval((function() {
                            return r.destroy()
                        }), 1e3)
                    }))
                }
            }, {
                key: "destroy",
                value: function() {
                    var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
                    (e || this.popup && this.popup.closed) && (clearInterval(this.interval), ct(window, "message", this.callback), document.body.removeChild(this.iframe), this.iframe = null)
                }
            }]), e
        }();
    var hr = new dr(Ht.appsDomain);

    function vr(e, t) {
        var n, r, o = Qt(60),
            i = ft({
                app_key: $t(),
                access_token: Wn(),
                ka: Nt,
                trans_id: o,
                mobile_view: e.forceMobileLayout,
                enable_back_button: e.enableBackButton
            }, e.addressId && {
                address_id: e.addressId
            });
        if (!e.returnUrl) return n = e.close, r = function e(t) {
            var r = t.data,
                o = t.origin;
            o !== Ht.appsDomain && o !== Ht.accountDomain || "closed" !== r || (n(), ct(window, "message", e))
        }, at(window, "message", r), hr.createHiddenIframe(o, "/proxy?trans_id=".concat(o)), hr.retrieveMessage(t, i, "shipping_address");
        i.return_url = e.returnUrl, cn(Ht.appsDomain + t, i)
    }
    var mr = Wt([xn, tr, fr, Object.freeze({
            __proto__: null,
            selectShippingAddress: function(e) {
                return vr(e = Zt(e, hn.selectShippingAddress, "Auth.selectShippingAddress"), "/user/address")
            },
            createShippingAddress: function(e) {
                return vr(e = Zt(e, hn.createShippingAddress, "Auth.createShippingAddress"), "/user/create/address")
            },
            updateShippingAddress: function(e) {
                return vr(e = Zt(e, hn.updateShippingAddress, "Auth.updateShippingAddress"), "/user/edit/address")
            }
        })]),
        yr = Wt([pr]);

    function gr(e) {
        return e.charAt(0).toUpperCase() + e.slice(1)
    }

    function br(e) {
        return e.replace(/[A-Z]/g, (function(e) {
            return "_".concat(e.toLowerCase())
        }))
    }

    function _r(e) {
        return f(e) ? JSON.stringify(e) : e
    }

    function wr(e, t) {
        return lr({
            url: e,
            data: t
        })
    }

    function kr(e, t, n) {
        return Zt(e, t, 'parameter "'.concat(n, '" in Share')), !0
    }

    function Sr(e) {
        return rt(e).reduce((function(t, n) {
            return t[br(n)] = e[n], t
        }), {})
    }
    var jr = {
            optional: {
                webUrl: Ge,
                mobileWebUrl: Ge,
                androidExecutionParams: Ge,
                iosExecutionParams: Ge
            },
            builder: Sr
        },
        xr = {
            required: {
                item: Ge,
                itemOp: Ge
            }
        };

    function Or(e) {
        return {
            title: e.title,
            link: Sr(e.link)
        }
    }

    function Ar(e) {
        var t = Sr(e);
        return t.link = Sr(t.link), t
    }
    var Tr = {
        headerLink: jr,
        link: jr,
        button: {
            required: {
                title: Ge,
                link: function(e) {
                    kr(e, jr, "link")
                }
            },
            builder: Or
        },
        buttons: {
            optional: {
                0: function(e) {
                    kr(e, Tr.button, "button")
                },
                1: function(e) {
                    kr(e, Tr.button, "button")
                }
            },
            builder: function(e) {
                return et(e, Or)
            }
        },
        content: {
            required: {
                title: Ge,
                imageUrl: Ge,
                link: function(e) {
                    kr(e, jr, "link")
                }
            },
            optional: {
                imageWidth: Xe,
                imageHeight: Xe,
                description: Ge
            },
            builder: Ar
        },
        contents: {
            optional: {
                0: function(e) {
                    kr(e, Tr.content, "content")
                },
                1: function(e) {
                    kr(e, Tr.content, "content")
                },
                2: function(e) {
                    kr(e, Tr.content, "content")
                }
            },
            builder: function(e) {
                return et(e, Ar)
            }
        },
        commerce: {
            required: {
                regularPrice: Xe
            },
            optional: {
                discountPrice: Xe,
                discountRate: Xe,
                fixedDiscountPrice: Xe,
                currencyUnit: Ge,
                currencyUnitPosition: st([0, 1]),
                productName: Ge
            },
            builder: Sr
        },
        social: {
            optional: {
                likeCount: Xe,
                commentCount: Xe,
                sharedCount: Xe,
                viewCount: Xe,
                subscriberCount: Xe
            },
            builder: Sr
        },
        itemContent: {
            optional: {
                profileText: Ge,
                profileImageUrl: Ge,
                titleImageUrl: Ge,
                titleImageText: Ge,
                titleImageCategory: Ge,
                items: function(e) {
                    return d(e) && e.length < 6 && tt(e, (function(e) {
                        return kr(e, xr, "items.item")
                    }))
                },
                sum: Ge,
                sumOp: Ge
            },
            builder: function(e) {
                var t = Sr(e);
                return t.items && (t.items = et(t.items, (function(e) {
                    return Sr(e)
                }))), t
            }
        }
    };
    var Cr = {
            create: function(e, t, n) {
                var r = Tr[t];
                if (r) return e = Zt(e, r, 'parameter "'.concat(t, '" in ').concat(n || "Share")), r.builder(e)
            }
        },
        Pr = "4.0",
        Ir = vt((function e(t, n) {
            dt(this, e), this.appkey = $t(), this.appver = "1.0", this.linkver = Pr, this.extras = ft(ft({
                KA: Nt
            }, t.extras), t.serverCallbackArgs && {
                lcba: _r(t.serverCallbackArgs)
            }), this.template_json = n.template_msg, this.template_args = n.template_args, this.template_id = n.template_id
        }));
    var Br = vt((function e(t) {
            var n = this;
            dt(this, e), this.link_ver = Pr, this.template_object = ft({
                object_type: t.objectType
            }, t.buttonTitle && {
                button_title: t.buttonTitle
            }), He(t, (function(e, t) {
                var r = Cr.create(e, t, "defaultObject");
                r && (n.template_object[br(t)] = r)
            }))
        })),
        Er = {
            FeedLink: Br,
            CommerceLink: Br,
            ListLink: function(e) {
                yt(n, e);
                var t = kt(n);

                function n(e) {
                    var r;
                    return dt(this, n), (r = t.call(this, e)).template_object.header_title = e.headerTitle, r
                }
                return vt(n)
            }(Br),
            LocationLink: function(e) {
                yt(n, e);
                var t = kt(n);

                function n(e) {
                    var r;
                    return dt(this, n), (r = t.call(this, e)).template_object.address = e.address || "", r.template_object.address_title = e.addressTitle || "", r
                }
                return vt(n)
            }(Br),
            CalendarLink: function(e) {
                yt(n, e);
                var t = kt(n);

                function n(e) {
                    var r;
                    return dt(this, n), (r = t.call(this, e)).template_object.id_type = e.idType || "", r.template_object.id = e.id || "", r
                }
                return vt(n)
            }(Br),
            TextLink: function(e) {
                yt(n, e);
                var t = kt(n);

                function n(e) {
                    var r;
                    return dt(this, n), (r = t.call(this, e)).template_object.text = e.text || "", r
                }
                return vt(n)
            }(Br)
        },
        zr = vt((function e(t) {
            dt(this, e), this.link_ver = Pr, this.request_url = t.requestUrl, t.templateId && (this.template_id = t.templateId), t.templateArgs && (this.template_args = t.templateArgs)
        })),
        Fr = vt((function e(t) {
            dt(this, e), this.link_ver = Pr, this.template_id = t.templateId, this.template_args = t.templateArgs
        }));
    var qr = {
            send: function(e, t, n) {
                var r = ft({
                        app_key: $t(),
                        ka: Nt,
                        validation_action: t,
                        validation_params: JSON.stringify(n)
                    }, e.serverCallbackArgs && {
                        lcba: _r(e.serverCallbackArgs)
                    }),
                    o = an("".concat(Ht.sharerDomain, "/picker/link"), r, "sharer");
                e.callback && function(e, t) {
                    if (Rt.browser.msie) return void(console && console.warn("KakaoWarning: The callback parameter does not support the IE browser."));
                    var n = function(e) {
                        "sent" === e.data && e.origin === Ht.sharerDomain && t()
                    };
                    at(window, "message", n);
                    var r = setInterval((function() {
                        e.closed && (clearInterval(r), ct(window, "message", n))
                    }), 1e3)
                }(o, e.callback)
            }
        },
        Ur = function() {
            var e = Ct(),
                t = e.os,
                n = ["opr/"],
                r = ["firefox", "KAKAOTALK"];

            function o(e) {
                window.top.location.href = e
            }

            function i(e, t, n) {
                var r = (new Date).getTime();
                return setTimeout((function() {
                    var o = (new Date).getTime();
                    a() && o - r < e + 100 && n(t)
                }), e)
            }

            function a() {
                for (var e = ["hidden", "webkitHidden"], t = 0, n = e.length; t < n; t++)
                    if (void 0 !== document[e[t]]) return !document[e[t]];
                return !0
            }

            function c(e) {
                setTimeout((function() {
                    (function(e) {
                        var t = document.createElement("iframe");
                        return t.id = e, t.style.border = "none", t.style.width = "0", t.style.height = "0", t.style.display = "none", t.style.overflow = "hidden", document.body.appendChild(t), t
                    }("appLauncher")).src = e
                }), 100)
            }
            return function(u) {
                var s, l, p, f, d = "function" == typeof u.willInvokeApp ? u.willInvokeApp : function() {},
                    h = "function" == typeof u.onAppMissing ? u.onAppMissing : o,
                    v = "function" == typeof u.onUnsupportedEnvironment ? u.onUnsupportedEnvironment : function() {};
                d(), t.android ? (l = e.browser.chrome && +e.browser.version.major >= 25, p = new RegExp(n.join("|"), "i"), f = new RegExp(r.join("|"), "i"), (l && !p.test(e.ua) || f.test(e.ua)) && u.intentURI && !u.useUrlScheme ? function(t) {
                    e.browser.chrome ? n() : setTimeout(n, 100);

                    function n() {
                        top.location.href = t
                    }
                }(u.intentURI) : u.storeURL && (s = u.urlScheme, i(300, u.storeURL, h), c(s))) : t.ios && u.storeURL ? function(t, n, r, o) {
                    var u = i(5e3, n, r);
                    parseInt(e.os.version.major, 10) < 8 ? function(e) {
                        window.addEventListener("pagehide", (function t() {
                            a() && (clearTimeout(e), window.removeEventListener("pagehide", t))
                        }))
                    }(u) : function(e) {
                        document.addEventListener("visibilitychange", (function t() {
                            a() && (clearTimeout(e), document.removeEventListener("visibilitychange", t))
                        }))
                    }(u);
                    parseInt(e.os.version.major, 10) > 8 && e.os.ios ? (void 0 === o ? o = t : clearTimeout(u), function(e) {
                        window.top.location.href = e
                    }(o)) : c(t)
                }(u.urlScheme, u.storeURL, h, u.universalLink) : setTimeout((function() {
                    v()
                }), 100)
            }
        }();

    function Dr() {
        return Rt.os.android && (2 == Rt.os.version.major && /Version\/\d+.\d+|/i.test(Rt.ua) || 4 == Rt.os.version.major && Rt.os.version.minor < 4 && /Version\/\d+.\d+|/i.test(Rt.ua) || /Version\/\d+\.\d+/i.test(Rt.ua) && (/Chrome\/\d+\.\d+\.\d+\.\d+ Mobile/i.test(Rt.ua) || /; wv\)/i.test(Rt.ua)))
    }

    function Rr() {
        return Rt.os.ios && Lt
    }
    var Lr = "362057947";
    var Mr = {
            send: function(e, t, n) {
                return wr(t, n).then((function(t) {
                    var n = function(e, t) {
                        var n = new Ir(e, t);
                        if (JSON.stringify(n).length > 1e4) throw new Xt("Failed to send message because it exceeds the message size limit. Please contact the app administrator.");
                        return ut(n)
                    }(e, t);
                    ! function(e, t) {
                        var n = "".concat(Rt.os.ios ? Ht.talkLinkScheme : "kakaolink://send", "?").concat(e),
                            r = ["intent://send?".concat(e, "#Intent"), "scheme=kakaolink", "launchFlags=0x14008000"].concat(jt(t ? ["package=".concat(Ht.talkAndroidPackage)] : []), ["end;"]).join(";"),
                            o = ft(ft({}, !Rr() && {
                                universalLink: Ht.universalKakaoLink + encodeURIComponent(n)
                            }), {}, {
                                urlScheme: n,
                                intentURI: r,
                                appName: "KakaoTalk",
                                storeURL: en(Ht.talkAndroidPackage, Lr),
                                onUnsupportedEnvironment: function() {
                                    throw new Xt("unsupported environment")
                                }
                            });
                        (!t || Rr() || Dr()) && (o.onAppMissing = ot);
                        try {
                            Ur(o)
                        } catch (e) {}
                    }(n, e.installTalk)
                })).catch((function(e) {
                    var t, n = JSON.stringify(ft({
                        name: "KAPIError"
                    }, e));
                    location.href = "".concat(Ht.sharerDomain, "/picker/failed?app_key=").concat($t(), "&error=").concat((t = n, window.btoa(t).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "")))
                }))
            }
        },
        Kr = {
            callback: O,
            installTalk: $e,
            throughTalk: $e,
            extras: f,
            serverCallbackArgs: lt([function(e) {
                try {
                    JSON.parse(e)
                } catch (e) {
                    return !1
                }
                return !0
            }, f])
        },
        Nr = {
            installTalk: !1,
            throughTalk: !0
        };

    function Hr(e) {
        if (!d(e)) return !1;
        if (e.length > 2) throw new Xt('Illegal argument for "buttons" in Share: size of buttons should be up to 2');
        return !0
    }
    var Gr = {
            required: {
                objectType: function(e) {
                    return "feed" === e
                },
                content: f
            },
            optional: ft(ft({}, Kr), {}, {
                itemContent: f,
                social: f,
                buttonTitle: Ge,
                buttons: Hr
            }),
            defaults: Nr
        },
        $r = {
            required: {
                objectType: function(e) {
                    return "list" === e
                },
                headerTitle: Ge,
                headerLink: f,
                contents: function(e) {
                    if (!d(e)) return !1;
                    if (e.length < 2 || e.length > 3) throw new Xt('Illegal argument for "contents" in Share: size of contents should be more than 1 and up to 3');
                    return !0
                }
            },
            optional: ft(ft({}, Kr), {}, {
                buttonTitle: Ge,
                buttons: Hr
            }),
            defaults: Nr
        },
        Jr = {
            required: {
                objectType: function(e) {
                    return "commerce" === e
                },
                content: f,
                commerce: f
            },
            optional: ft(ft({}, Kr), {}, {
                buttonTitle: Ge,
                buttons: Hr
            }),
            defaults: Nr
        },
        Xr = {
            required: {
                objectType: function(e) {
                    return "location" === e
                },
                content: f,
                address: Ge
            },
            optional: ft(ft({}, Kr), {}, {
                addressTitle: Ge,
                social: f,
                buttonTitle: Ge,
                buttons: Hr
            }),
            defaults: Nr
        },
        Wr = {
            required: {
                objectType: function(e) {
                    return "calendar" === e
                },
                idType: st(["event", "calendar"]),
                id: Ge,
                content: f
            },
            optional: ft(ft({}, Kr), {}, {
                buttons: Hr
            }),
            defaults: Nr
        },
        Vr = {
            required: {
                objectType: function(e) {
                    return "text" === e
                },
                text: Ge,
                link: f
            },
            optional: ft(ft({}, Kr), {}, {
                buttonTitle: Ge,
                buttons: Hr
            }),
            defaults: Nr
        },
        Yr = {
            required: {
                requestUrl: Ge
            },
            optional: ft(ft({}, Kr), {}, {
                templateId: Xe,
                templateArgs: f
            }),
            defaults: ft(ft({}, Nr), {}, {
                templateArgs: {}
            })
        },
        Zr = {
            required: {
                templateId: Xe
            },
            optional: ft(ft({}, Kr), {}, {
                templateArgs: f
            }),
            defaults: ft(ft({}, Nr), {}, {
                templateArgs: {}
            })
        };

    function Qr(e) {
        return Ne({
            required: ft(ft({}, e.required), {}, {
                container: lt([Je, Ge])
            })
        }, e)
    }
    var eo = {
        objectTypes: ["feed", "list", "commerce", "location", "calendar", "text"],
        sendFeed: Gr,
        createFeedButton: Qr(Gr),
        sendList: $r,
        createListButton: Qr($r),
        sendCommerce: Jr,
        createCommerceButton: Qr(Jr),
        sendLocation: Xr,
        createLocationButton: Qr(Xr),
        sendCalendar: Wr,
        createCalendarButton: Qr(Wr),
        sendText: Vr,
        createTextButton: Qr(Vr),
        sendScrap: Yr,
        createScrapButton: Qr(Yr),
        sendCustom: Zr,
        createCustomButton: Qr(Zr),
        uploadImage: {
            required: {
                file: f
            }
        },
        deleteImage: {
            required: {
                imageUrl: Ge
            }
        },
        scrapImage: {
            required: {
                imageUrl: Ge
            }
        }
    };

    function to(e, t) {
        var n = it(e.container);
        if (!n) throw new Xt("container is required for KakaoTalk sharing: pass in element or id");
        var r = function(n) {
            n.preventDefault(), n.stopPropagation(), ro(e, t)
        };
        at(n, "click", r), oo.push((function() {
            ct(n, "click", r)
        }))
    }
    var no = {
        default: [function(e) {
            return new(0, Er["".concat(gr(e.objectType), "Link")])(e)
        }, "/v2/api/kakaolink/talk/template/default"],
        scrap: [function(e) {
            return new zr(e)
        }, "/v2/api/kakaolink/talk/template/scrap"],
        custom: [function(e) {
            return new Fr(e)
        }, "/v2/api/kakaolink/talk/template/validate"]
    };

    function ro(e, t) {
        var n, r, o, i, a = St(no[t], 2),
            c = a[0],
            u = a[1],
            s = c(e);
        n = e.throughTalk, r = /opr\/|opt\/|huawei/g.test(Rt.ua), o = Rt.os.ios && "tablet" === Rt.platform, i = !r && ("mobile" === Rt.platform || o), Lt || n && i ? Mr.send(e, u, s) : qr.send(e, t, s)
    }
    var oo = [];
    var io = Wt([Object.freeze({
            __proto__: null,
            createDefaultButton: function(e) {
                if (!e.objectType || !st(eo.objectTypes)(e.objectType)) throw new Xt("objectType should be one of (".concat(eo.objectTypes.join(", "), ")"));
                to(e = Zt(e, eo["create".concat(gr(e.objectType), "Button")], "Share.createDefaultButton"), "default")
            },
            sendDefault: function(e) {
                if (!e.objectType || !st(eo.objectTypes)(e.objectType)) throw new Xt("objectType should be one of (".concat(eo.objectTypes.join(", "), ")"));
                ro(e = Zt(e, eo["send".concat(gr(e.objectType))], "Share.sendDefault"), "default")
            },
            createScrapButton: function(e) {
                to(e = Zt(e, eo.createScrapButton, "Share.createScrapButton"), "scrap")
            },
            sendScrap: function(e) {
                ro(e = Zt(e, eo.sendScrap, "Share.sendScrap"), "scrap")
            },
            createCustomButton: function(e) {
                to(e = Zt(e, eo.createCustomButton, "Share.createCustomButton"), "custom")
            },
            sendCustom: function(e) {
                ro(e = Zt(e, eo.sendCustom, "Share.sendCustom"), "custom")
            },
            cleanup: function() {
                Vt(oo)
            }
        }), Object.freeze({
            __proto__: null,
            uploadImage: function(e) {
                return wr("/v2/api/talk/message/image/upload", {
                    file: (e = Zt(e, eo.uploadImage, "Share.uploadImage")).file
                })
            },
            deleteImage: function(e) {
                return wr("/v2/api/talk/message/image/delete", {
                    image_url: (e = Zt(e, eo.deleteImage, "Share.deleteImage")).imageUrl
                })
            },
            scrapImage: function(e) {
                return wr("/v2/api/talk/message/image/scrap", {
                    image_url: (e = Zt(e, eo.scrapImage, "Share.scrapImage")).imageUrl
                })
            }
        })]),
        ao = ["small", "large"],
        co = ["yellow", "mono"],
        uo = ["pc", "mobile"],
        so = ["consult", "question"],
        lo = ["ko", "en", "ja"];

    function po(e) {
        return Ge(e) && !/(.{1,2}\/)/g.test(e)
    }
    var fo = {
        createAddChannelButton: {
            required: {
                container: lt([Je, Ge]),
                channelPublicId: po
            },
            optional: {
                size: st(ao),
                lang: st(lo),
                supportMultipleDensities: $e
            },
            defaults: {
                size: ao[0],
                supportMultipleDensities: !1
            }
        },
        addChannel: {
            required: {
                channelPublicId: po
            },
            optional: {
                lang: st(lo)
            }
        },
        createChatButton: {
            required: {
                container: lt([Je, Ge]),
                channelPublicId: po
            },
            optional: {
                size: st(ao),
                color: st(co),
                shape: st(uo),
                title: st(so),
                lang: st(lo),
                supportMultipleDensities: $e
            },
            defaults: {
                size: ao[0],
                color: co[0],
                shape: uo[0],
                title: so[0],
                supportMultipleDensities: !1
            }
        },
        chat: {
            required: {
                channelPublicId: po
            },
            optional: {
                lang: st(lo)
            }
        }
    };

    function ho(e) {
        var t = "".concat(Ht.channel, "/").concat(e.channelPublicId, "/friend");
        null !== $t() && (t += "?".concat(mo("1.1", e.lang))), nn(t, "channel_add_social_plugin", rn(350, 510))
    }

    function vo(e) {
        var t = "".concat(Ht.channel, "/").concat(e.channelPublicId, "/chat");
        null !== $t() && (t += "?".concat(mo("1.1", e.lang))), nn(t, "channel_chat_social_plugin", rn(350, 510))
    }

    function mo(e, t) {
        return ut(ft({
            api_ver: e,
            kakao_agent: Nt,
            app_key: $t(),
            referer: Dt + location.pathname + location.search
        }, t && {
            lang: t
        }))
    }

    function yo(e, t, n) {
        var r = document.createElement("a");
        r.setAttribute("href", "#");
        var o = document.createElement("img");
        return o.setAttribute("src", t), o.setAttribute("title", n), o.setAttribute("alt", n), e.supportMultipleDensities && o.setAttribute("srcset", [t.replace(".png", "_2X.png 2x"), t.replace(".png", "_3X.png 3x")].join(", ")), r.appendChild(o), r
    }
    var go = [];
    var bo = Wt([Object.freeze({
            __proto__: null,
            createAddChannelButton: function(e) {
                var t = it(e.container);
                if (!t) throw new Xt("container is required for Channel.createAddChannelButton: pass in element or id");
                on(e, t, {
                    channelPublicId: "data-channel-public-id",
                    size: "data-size",
                    supportMultipleDensities: "data-support-multiple-densities"
                });
                var n = function(e) {
                        var t = "friendadd_".concat(e.size, "_yellow_rect.png");
                        return "".concat(Ht.channelIcon, "/channel/").concat(t)
                    }(e = Zt(e, fo.createAddChannelButton, "Channel.createAddChannelButton")),
                    r = yo(e, n, "카카오톡 채널 추가 버튼");
                t.appendChild(r);
                var o = function(t) {
                    t.preventDefault(), ho(e)
                };
                at(r, "click", o), go.push((function() {
                    ct(r, "click", o), t.removeChild(r)
                }))
            },
            addChannel: function(e) {
                ho(e = Zt(e, fo.addChannel, "Channel.addChannel"))
            },
            createChatButton: function(e) {
                var t = it(e.container);
                if (!t) throw new Xt("container is required for Channel.createChatButton: pass in element or id");
                on(e, t, {
                    channelPublicId: "data-channel-public-id",
                    size: "data-size",
                    color: "data-color",
                    shape: "data-shape",
                    title: "data-title",
                    supportMultipleDensities: "data-support-multiple-densities"
                });
                var n = function(e) {
                        var t = "".concat(e.title, "_").concat(e.size, "_").concat(e.color, "_").concat(e.shape, ".png");
                        return "".concat(Ht.channelIcon, "/channel/").concat(t)
                    }(e = Zt(e, fo.createChatButton, "Channel.createChatButton")),
                    r = yo(e, n, "카카오톡 채널 1:1 채팅 버튼");
                t.appendChild(r);
                var o = function(t) {
                    t.preventDefault(), vo(e)
                };
                at(r, "click", o), go.push((function() {
                    ct(r, "click", o), t.removeChild(r)
                }))
            },
            chat: function(e) {
                vo(e = Zt(e, fo.chat, "Channel.chat"))
            },
            cleanup: function() {
                Vt(go)
            }
        })]),
        _o = {
            required: {
                title: Ge
            },
            optional: {
                desc: Ge,
                name: Ge,
                images: d,
                type: Ge
            },
            defaults: {
                type: "website"
            },
            after: function(e) {
                e.images && (e.imageurl = e.images, delete e.images)
            }
        },
        wo = {
            createShareButton: {
                required: {
                    container: lt([Je, Ge])
                },
                optional: {
                    url: Ge,
                    text: Ge
                },
                defaults: {
                    url: location.href
                }
            },
            share: {
                optional: {
                    url: Ge,
                    text: Ge
                },
                defaults: {
                    url: location.href
                }
            },
            open: {
                optional: {
                    url: Ge,
                    text: Ge,
                    urlInfo: function(e) {
                        return f(e) && !!Zt(e, _o, "Story.open")
                    },
                    install: $e
                },
                defaults: {
                    url: location.href,
                    install: !1
                }
            },
            createFollowButton: {
                required: {
                    container: lt([Je, Ge]),
                    id: Ge
                },
                optional: {
                    showFollowerCount: $e,
                    type: st(["horizontal", "vertical"])
                },
                defaults: {
                    showFollowerCount: !0,
                    type: "horizontal"
                },
                after: function(e) {
                    "@" !== e.id[0] && (e.id = "@".concat(e.id))
                }
            }
        };

    function ko(e) {
        var t = ft(ft({}, jo()), {}, {
            url: e.url
        });
        e.text && (t.text = e.text), nn("".concat(Ht.storyShare, "?").concat(ut(t)), "kakaostory_social_plugin", rn())
    }
    var So = 0;

    function jo() {
        var e = {
            kakao_agent: Nt
        };
        return null !== $t() && (e.app_key = $t()), e
    }
    var xo = [];
    var Oo = Wt([Object.freeze({
            __proto__: null,
            createShareButton: function(e) {
                var t = it(e.container);
                if (!t) throw new Xt("container is required for Story.createShareButton: pass in element or id");
                on(e, t, {
                    url: "data-url"
                }), e = Zt(e, wo.createShareButton, "Story.createShareButton");
                var n = function(e, t) {
                    var n = document.createElement("a");
                    n.setAttribute("href", "#");
                    var r = document.createElement("img");
                    return r.setAttribute("src", e), r.setAttribute("title", t), r.setAttribute("alt", t), n.appendChild(r), n
                }(Ht.storyIcon, "카카오스토리 웹 공유 버튼");
                t.appendChild(n);
                var r = function(t) {
                    t.preventDefault(), ko(e)
                };
                at(n, "click", r), xo.push((function() {
                    ct(n, "click", r), t.removeChild(n)
                }))
            },
            share: function(e) {
                ko(e = Zt(e, wo.share, "Story.share"))
            },
            open: function(e) {
                var t = function(e) {
                        var t = location.hostname || "",
                            n = ft(ft({}, jo()), {}, {
                                apiver: "1.0",
                                appver: Mt,
                                appid: t,
                                appname: t,
                                post: e.text ? "".concat(e.text, "\n").concat(e.url) : e.url
                            });
                        e.urlInfo && (n.urlinfo = JSON.stringify(e.urlInfo), n.appname = e.urlInfo.name || n.appname);
                        return "".concat(Ht.storyPostScheme, "?").concat(ut(n))
                    }(e = Zt(e, wo.open, "Story.open")),
                    n = {
                        urlScheme: t,
                        intentURI: ["intent:".concat(t, "#Intent"), "".concat(e.install ? "package=com.kakao.story;" : "", "end;")].join(";"),
                        appName: "KakaoStory",
                        storeURL: en("com.kakao.story", "486244601"),
                        onUnsupportedEnvironment: function() {
                            e.fail && e.fail()
                        }
                    };
                try {
                    Ur(n)
                } catch (e) {}
            },
            createFollowButton: function(e) {
                var t = it(e.container);
                if (!t) throw new Xt("container is required for Story.createFollowButton: pass in element or id");
                on(e, t, {
                    id: "data-id",
                    showFollowerCount: "data-show-follower-count",
                    type: "data-type"
                });
                var n = function(e) {
                        var t = So++,
                            n = e.showFollowerCount && "horizontal" === e.type ? 85 : 59,
                            r = e.showFollowerCount && "vertical" === e.type ? 46 : 20,
                            o = document.createElement("iframe");
                        o.src = function(e, t) {
                            var n = ft(ft({}, jo()), {}, {
                                id: e.id,
                                type: e.type,
                                hideFollower: !e.showFollowerCount,
                                frameId: t
                            });
                            return "".concat(Ht.storyChannelFollow, "?").concat(ut(n))
                        }(e, t), o.setAttribute("frameborder", "0"), o.setAttribute("marginwidth", "0"), o.setAttribute("marginheight", "0"), o.setAttribute("scrolling", "no"), o.setAttribute("style", "width:".concat(n, "px; height:").concat(r, "px;"));
                        var i = function(e) {
                            if (e.data && /\.kakao\.com$/.test(e.origin) && "string" == typeof e.data) {
                                var i = St(et(e.data.split(","), (function(e) {
                                        return parseInt(e, 10)
                                    })), 3),
                                    a = i[0],
                                    c = i[1],
                                    u = i[2];
                                a === t && (n !== c && (o.style.width = "".concat(c, "px")), r !== u && (o.style.height = "".concat(u, "px")))
                            }
                        };
                        return {
                            iframe$: o,
                            messageHandler: i
                        }
                    }(e = Zt(e, wo.createFollowButton, "Story.createFollowButton")),
                    r = n.iframe$,
                    o = n.messageHandler;
                t.appendChild(r), at(window, "message", o), xo.push((function() {
                    ct(window, "message", o), t.removeChild(r)
                }))
            },
            cleanup: function() {
                Vt(xo)
            }
        })]),
        Ao = ["wgs84", "katec"],
        To = {
            required: {
                name: Ge,
                x: We,
                y: We
            },
            optional: {
                rpflag: Ge,
                cid: Ge
            }
        },
        Co = {
            start: {
                required: {
                    name: Ge,
                    x: We,
                    y: We
                },
                optional: {
                    coordType: st(Ao),
                    vehicleType: st([1, 2, 3, 4, 5, 6, 7]),
                    rpOption: st([1, 2, 3, 4, 5, 6, 8, 100]),
                    routeInfo: $e,
                    sX: We,
                    sY: We,
                    sAngle: We,
                    returnUri: Ge,
                    rpflag: Ge,
                    cid: Ge,
                    guideId: We,
                    viaPoints: function(e) {
                        if (d(e)) {
                            if (e.length > 3) throw new Xt("Invalid parameter keys: via points should not be exceed 3. at Navi.start");
                            return He(e, (function(e) {
                                return Zt(e, To, "Navi.start")
                            })), !0
                        }
                        return !1
                    }
                },
                defaults: {
                    coordType: "katec",
                    vehicleType: 1,
                    rpOption: 100,
                    routeInfo: !1
                }
            },
            share: {
                required: {
                    name: Ge,
                    x: We,
                    y: We
                },
                optional: {
                    coordType: st(Ao),
                    rpflag: Ge,
                    cid: Ge,
                    guideId: We
                },
                defaults: {
                    coordType: "katec"
                }
            }
        };

    function Po() {
        return {
            appkey: $t(),
            apiver: "1.0",
            extras: {
                KA: Nt
            }
        }
    }

    function Io(e, t) {
        var n = {
            urlScheme: e,
            intentURI: ["intent:".concat(e, "#Intent"), "S.browser_fallback_url=".concat(encodeURIComponent(t)), "end;"].join(";"),
            storeURL: t,
            universalLink: t
        };
        try {
            Ur(n)
        } catch (e) {}
    }
    var Bo = Wt([Object.freeze({
        __proto__: null,
        start: function(e) {
            var t = ut(function(e) {
                var t = {
                        name: e.name,
                        x: e.x,
                        y: e.y,
                        rpflag: e.rpflag,
                        cid: e.cid,
                        guide_id: e.guideId
                    },
                    n = {
                        coord_type: e.coordType,
                        vehicle_type: e.vehicleType,
                        rpoption: e.rpOption,
                        route_info: e.routeInfo,
                        s_x: e.sX,
                        s_y: e.sY,
                        s_angle: e.sAngle,
                        return_uri: e.returnUri
                    };
                return ft(ft({}, Po()), {}, {
                    param: {
                        destination: t,
                        option: n,
                        via_list: e.viaPoints
                    }
                })
            }(e = Zt(e, Co.start, "Navi.start")));
            Io("".concat(Ht.naviScheme, "?").concat(t), "".concat(Ht.naviFallback, "?").concat(t))
        },
        share: function(e) {
            var t = ut(function(e) {
                var t = {
                        name: e.name,
                        x: e.x,
                        y: e.y,
                        rpflag: e.rpflag,
                        cid: e.cid,
                        guide_id: e.guideId
                    },
                    n = {
                        route_info: !0,
                        coord_type: e.coordType
                    };
                return ft(ft({}, Po()), {}, {
                    param: {
                        destination: t,
                        option: n
                    }
                })
            }(e = Zt(e, Co.share, "Navi.share")));
            Io("".concat(Ht.naviScheme, "?").concat(t), "".concat(Ht.naviFallback, "?").concat(t))
        }
    })]);

    function Eo(e) {
        return Xe(e) && e > 0 && e < 101
    }

    function zo(e) {
        if (e.maxPickableCount < e.minPickableCount) throw new Xt('"minPickableCount" should not larger than "maxPickableCount"')
    }

    function Fo(e) {
        var t = {
            required: {
                reason: st(["msgBlocked", "registered", "unregistered", "notFriend", "custom"])
            },
            optional: {
                message: Ge,
                uuids: d
            },
            after: function(e) {
                if (!("custom" !== e.reason || e.message && e.uuids)) throw new Xt('"message" and "uuids" must be set for "custom" option in disableSelectOption')
            }
        };
        return d(e) && tt(e, (function(e) {
            return f(e) && !!Zt(e, t, "disableSelectOption")
        }))
    }

    function qo(e) {
        if ("chatMember" === e.selectionType) {
            var t = e.chatFilters;
            if (t.indexOf("open") > -1) throw new Xt('"open" is not allowed in "chatFilters"');
            if ((t.indexOf("direct") > -1 || t.indexOf("multi") > -1) && -1 === t.indexOf("regular")) throw new Xt('"regular" should be included in "chatFilters"')
        }
    }
    var Uo = ["none", "invitable", "registered"],
        Do = ["talk", "story", "talkstory"],
        Ro = ["chat", "chatMember"],
        Lo = ["regular", "direct", "multi", "open"],
        Mo = ["all", "ios", "android"],
        Ko = {
            returnUrl: Ge,
            friendFilter: st(Uo),
            serviceTypeFilter: st(Do),
            title: Ge,
            enableSearch: $e,
            countryCodeFilters: d,
            usingOsFilter: st(Mo),
            showMyProfile: $e,
            showFavorite: $e,
            disableSelectOptions: Fo,
            displayAllProfile: $e,
            enableBackButton: $e
        },
        No = {
            optional: {
                friendFilter: st(Uo),
                serviceTypeFilter: st(Do),
                countryCodeFilters: d,
                usingOsFilter: st(Mo),
                showMyProfile: $e,
                showFavorite: $e,
                showPickedFriend: $e
            }
        },
        Ho = {
            optional: {
                selectionType: st(Ro),
                chatFilters: function(e) {
                    return d(e) && tt(e, (function(e) {
                        return st(Lo)(e)
                    }))
                }
            },
            defaults: {
                selectionType: Ro[0],
                chatFilters: [Lo[0]]
            },
            after: qo
        },
        Go = {
            selectFriend: {
                optional: Ko
            },
            selectFriends: {
                optional: ft(ft({}, Ko), {}, {
                    showPickedFriend: $e,
                    maxPickableCount: Eo,
                    minPickableCount: Eo
                }),
                after: zo
            },
            selectChat: {
                optional: {
                    returnUrl: Ge,
                    selectionType: st(Ro),
                    chatFilters: function(e) {
                        return d(e) && tt(e, (function(e) {
                            return st(Lo)(e)
                        }))
                    },
                    title: Ge,
                    enableSearch: $e,
                    disableSelectOptions: Fo,
                    displayAllProfile: $e,
                    maxPickableCount: Eo,
                    minPickableCount: Eo,
                    enableBackButton: $e
                },
                defaults: {
                    selectionType: Ro[0],
                    chatFilters: [Lo[0]]
                },
                after: function(e) {
                    zo(e), qo(e)
                }
            },
            select: {
                optional: {
                    returnUrl: Ge,
                    title: Ge,
                    enableSearch: $e,
                    disableSelectOptions: Fo,
                    displayAllProfile: $e,
                    maxPickableCount: Eo,
                    minPickableCount: Eo,
                    enableBackButton: $e,
                    friendsParams: function(e) {
                        return f(e) && !!Zt(e, No, "Picker.select")
                    },
                    chatParams: function(e) {
                        return f(e) && !!Zt(e, Ho, "Picker.select")
                    }
                },
                after: zo
            }
        },
        $o = ["returnUrl", "friendsParams", "chatParams"];
    var Jo = new dr(Ht.pickerDomain);

    function Xo(e, t) {
        var n = Qt(60),
            r = ft(ft({
                transId: n,
                appKey: $t(),
                ka: Nt
            }, Wn() && {
                token: Wn()
            }), function(e) {
                e.returnUrl;
                var t = e.friendsParams,
                    n = e.chatParams,
                    r = _t(e, $o);
                return function(e) {
                    ["countryCodeFilters", "chatFilters"].forEach((function(t) {
                        void 0 !== e[t] && (e[t] = e[t].join(","))
                    })), e.disableSelectOptions && (e.disableSelectOptions = JSON.stringify(e.disableSelectOptions));
                    return e
                }(ft(ft(ft({}, r), t), n))
            }(e));
        if (!e.returnUrl) return Jo.createHiddenIframe(n, "/proxy?transId=".concat(n)), Jo.retrieveMessage(t, r, "picker");
        r.returnUrl = e.returnUrl, cn(Ht.pickerDomain + t, r)
    }
    var Wo = Wt([Object.freeze({
        __proto__: null,
        selectFriends: function(e) {
            return Xo(e = Zt(e, Go.selectFriends, "Picker.selectFriends"), "/select/multiple")
        },
        selectFriend: function(e) {
            return Xo(e = Zt(e, Go.selectFriend, "Picker.selectFriend"), "/select/single")
        },
        selectChat: function(e) {
            return Xo(e = Zt(e, Go.selectChat, "Picker.selectChat"), "/chat/select")
        },
        select: function(e) {
            return Xo(e = Zt(e, Go.select, "Picker.select"), "/tab/select")
        }
    })]);

    function Vo() {
        return null !== $t()
    }
    "function" == typeof define && define.amd && (window.Kakao = e), "function" == typeof window.kakaoAsyncInit && setTimeout((function() {
        window.kakaoAsyncInit()
    }), 0), e.VERSION = Mt, e.cleanup = function() {
        var e = this;
        Object.keys(this).filter((function(t) {
            return f(e[t])
        })).forEach((function(t) {
            return e[t].cleanup && e[t].cleanup()
        })), Jt(null)
    }, e.init = function(e) {
        if (Rt.browser.msie && Rt.browser.version.major < 11) throw new Xt("Kakao.init: Unsupported browser");
        if (Vo()) throw new Xt("Kakao.init: Already initialized");
        if (!Ge(e)) throw new Xt("Kakao.init: App key must be provided");
        Jt(e), this.Auth = mr, this.API = yr, this.Share = io, this.Channel = bo, this.Story = Oo, this.Navi = Bo, this.Picker = Wo
    }, e.isInitialized = Vo, Object.defineProperty(e, "__esModule", {
        value: !0
    })
}));