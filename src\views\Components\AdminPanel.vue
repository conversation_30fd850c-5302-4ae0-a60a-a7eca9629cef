<template>
	<v-dialog v-model="dialog" max-width="1200" persistent>
		<v-card class="admin-panel-card" elevation="24">
			<!-- 헤더 섹션 -->
			<div class="admin-header">
				<div class="header-content">
					<div class="header-icon">
						<v-icon size="32" color="white">mdi-shield-crown</v-icon>
					</div>
					<div class="header-text">
						<h2 class="header-title">관리자 패널</h2>
						<p class="header-subtitle">시스템 사용자 관리 및 승인</p>
					</div>
				</div>
				<v-btn 
					icon 
					large 
					color="white" 
					@click="close" 
					class="close-button"
					elevation="2"
				>
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</div>

			<v-divider class="divider-gradient"></v-divider>

			<!-- 메인 콘텐츠 -->
			<v-card-text class="main-content">
				<!-- 로딩 상태 -->
				<div v-if="isLoadingPendingUsers" class="loading-section">
					<div class="loading-content">
						<v-progress-circular 
							indeterminate 
							color="primary" 
							size="64"
							width="4"
						></v-progress-circular>
						<h3 class="loading-title">승인 대기 목록 로드 중</h3>
						<p class="loading-subtitle">잠시만 기다려주세요...</p>
					</div>
				</div>

				<!-- 메인 콘텐츠 -->
				<div v-else class="content-section">
					<!-- 통계 카드 -->
					<div class="stats-row">
						<v-card class="stats-card pending-card" elevation="8">
							<div class="stats-icon">
								<v-icon size="40" color="orange">mdi-clock-outline</v-icon>
							</div>
							<div class="stats-content">
								<h3 class="stats-number">{{ pendingUsers.length }}</h3>
								<p class="stats-label">승인 대기</p>
							</div>
						</v-card>

						<v-card class="stats-card approved-card" elevation="8">
							<div class="stats-icon">
								<v-icon size="40" color="green">mdi-account-check</v-icon>
							</div>
							<div class="stats-content">
								<h3 class="stats-number">{{ approvedCount }}</h3>
								<p class="stats-label">승인 완료</p>
							</div>
						</v-card>

						<v-card class="stats-card total-card" elevation="8">
							<div class="stats-icon">
								<v-icon size="40" color="blue">mdi-account-group</v-icon>
							</div>
							<div class="stats-content">
								<h3 class="stats-number">{{ totalUsers }}</h3>
								<p class="stats-label">전체 사용자</p>
							</div>
						</v-card>
					</div>

					<!-- 사용자 목록 섹션 -->
					<v-card class="users-section" elevation="12">
						<div class="section-header">
							<div class="section-title">
								<v-icon color="primary" class="mr-3">mdi-account-multiple</v-icon>
								<h3>승인 대기 중인 사용자</h3>
							</div>
							<v-btn 
								color="primary" 
								@Click="loadPendingUsers"
								:loading="isLoadingPendingUsers"
								class="refresh-btn"
								elevation="2"
							>
								<v-icon left>mdi-refresh</v-icon>
								새로고침
							</v-btn>
						</div>

						<!-- 빈 상태 -->
						<div v-if="pendingUsers.length === 0" class="empty-state">
							<div class="empty-icon">
								<v-icon size="120" color="grey lighten-2">mdi-account-check-outline</v-icon>
							</div>
							<h3 class="empty-title">모든 사용자가 승인되었습니다</h3>
							<p class="empty-subtitle">현재 승인 대기 중인 사용자가 없습니다.</p>
						</div>

						<!-- 사용자 카드 리스트 -->
						<div v-else class="users-grid">
							<v-card 
								v-for="user in pendingUsers" 
								:key="user.id"
								class="user-card"
								elevation="6"
								:class="{ 'approving': approvingUsers.includes(user.id) }"
							>
								<div class="user-header">
									<v-avatar size="56" class="user-avatar">
										<v-icon size="32" color="white">mdi-account</v-icon>
									</v-avatar>
									<div class="user-info">
										<h4 class="user-name">{{ user.name }}</h4>
										<p class="user-email">{{ user.email }}</p>
										<div class="user-date">
											<v-icon small color="grey">mdi-calendar</v-icon>
											<span>{{ formatDate(user.created_at) }}</span>
										</div>
									</div>
								</div>

								<v-divider class="my-3"></v-divider>

								<div class="user-actions">
									<v-btn 
										color="success" 
										@click="approveUser(user.id)"
										:loading="approvingUsers.includes(user.id)"
										class="action-btn"
										elevation="2"
									>
										<v-icon left small>mdi-check-circle</v-icon>
										승인
									</v-btn>
									<v-btn 
										color="error" 
										@click="rejectUser(user)"
										class="action-btn ml-2"
										elevation="2"
									>
										<v-icon left small>mdi-close-circle</v-icon>
										거부
									</v-btn>
								</div>
							</v-card>
						</div>
					</v-card>
				</div>
			</v-card-text>
		</v-card>
	</v-dialog>
</template>

<script lang="ts">
import { Component, Prop, Mixins, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { authService } from '@/plugins/auth-service';

@Component
export default class AdminPanel extends Mixins(GlobalMixins) {
	@Prop({ type: Boolean, default: false }) value!: boolean;

	public isLoadingPendingUsers: boolean = false;
	public pendingUsers: any[] = [];
	public approvingUsers: string[] = [];

	public get dialog() {
		return this.value;
	}

	public set dialog(value: boolean) {
		this.$emit('input', value);
	}

	// 통계 데이터
	public get approvedCount(): number {
		// 실제로는 authService에서 승인된 사용자 수를 가져와야 함
		return 0;
	}

	public get totalUsers(): number {
		// 실제로는 authService에서 전체 사용자 수를 가져와야 함
		return this.pendingUsers.length + this.approvedCount;
	}

	@Watch('value')
	public onDialogOpen(newVal: boolean) {
		if (newVal) {
			this.loadPendingUsers();
		}
	}

	public close() {
		this.dialog = false;
	}

	// 대기 중인 사용자 목록 로드
	public async loadPendingUsers() {
		this.isLoadingPendingUsers = true;
		this.pendingUsers = [];
		this.approvingUsers = [];
		
		try {
			this.pendingUsers = authService.getPendingUsers();
		} catch (error) {
			console.error('대기 중인 사용자 로드 실패:', error);
			this.showError('승인 대기 목록을 불러오는데 실패했습니다.');
		} finally {
			this.isLoadingPendingUsers = false;
		}
	}

	// 사용자 승인
	public async approveUser(userId: string) {
		if (this.approvingUsers.includes(userId)) return;
		
		this.approvingUsers.push(userId);
		try {
			const result = await authService.approveUser(userId);
			if (result.success) {
				this.showSuccess('사용자가 승인되었습니다.');
				await this.loadPendingUsers();
			} else {
				this.showError(result.message || '사용자 승인에 실패했습니다.');
			}
		} catch (error) {
			console.error('사용자 승인 오류:', error);
			this.showError('사용자 승인 중 오류가 발생했습니다.');
		} finally {
			this.approvingUsers = this.approvingUsers.filter(id => id !== userId);
		}
	}

	// 승인 대기 중인 사용자 거부
	public async rejectUser(user: any) {
		try {
			const result = await this.$swal.fire({
				icon: 'warning',
				title: '사용자 거부',
				html: `<div style="text-align: center;">
					<p><strong>${user.name}님</strong>의 가입을 거부하시겠습니까?</p>
					<p class="text-muted">이 작업은 되돌릴 수 없습니다.</p>
				</div>`,
				showCancelButton: true,
				confirmButtonText: '<i class="mdi mdi-close-circle"></i> 거부',
				cancelButtonText: '<i class="mdi mdi-arrow-left"></i> 취소',
				confirmButtonColor: '#e53e3e',
				cancelButtonColor: '#6c757d',
				customClass: {
					confirmButton: 'v-btn v-btn--contained theme--light v-size--default error',
					cancelButton: 'v-btn v-btn--outlined theme--light v-size--default'
				}
			});

			if (result.isConfirmed) {
				// 거부 로직 (현재는 목록에서 제거만)
				const index = this.pendingUsers.findIndex(u => u.id === user.id);
				if (index > -1) {
					this.pendingUsers.splice(index, 1);
				}
				
				this.$swal.fire({
					icon: 'success',
					title: '거부 완료',
					html: `<p><strong>${user.name}님</strong>의 가입이 거부되었습니다.</p>`,
					timer: 3000,
					showConfirmButton: false,
					toast: true,
					position: 'top-end'
				});
			}
		} catch (error) {
			console.error('사용자 거부 오류:', error);
			this.showError('사용자 거부 중 오류가 발생했습니다.');
		}
	}

	// 날짜 포맷팅
	public formatDate(dateString: string): string {
		try {
			const date = new Date(dateString);
			const now = new Date();
			const diffTime = Math.abs(now.getTime() - date.getTime());
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			
			if (diffDays === 1) {
				return '오늘';
			} else if (diffDays === 2) {
				return '어제';
			} else if (diffDays <= 7) {
				return `${diffDays - 1}일 전`;
			} else {
				return date.toLocaleDateString('ko-KR');
			}
		} catch (error) {
			return dateString;
		}
	}

	// 성공 메시지 표시
	private showSuccess(message: string) {
		this.$emit('success', message);
	}

	// 오류 메시지 표시
	private showError(message: string) {
		this.$emit('error', message);
	}
}
</script>

<style scoped>
.admin-panel-card {
	border-radius: 20px !important;
	overflow: hidden;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 헤더 스타일 */
.admin-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 24px 32px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	overflow: hidden;
}

.admin-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
	opacity: 0.3;
}

.header-content {
	display: flex;
	align-items: center;
	z-index: 1;
	position: relative;
}

.header-icon {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16px;
	padding: 16px;
	margin-right: 20px;
	backdrop-filter: blur(10px);
}

.header-text {
	color: white;
}

.header-title {
	font-size: 28px;
	font-weight: 700;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
	font-size: 16px;
	margin: 4px 0 0 0;
	opacity: 0.9;
	font-weight: 400;
}

.close-button {
	z-index: 1;
	position: relative;
	background: rgba(255, 255, 255, 0.2) !important;
	backdrop-filter: blur(10px);
}

.divider-gradient {
	height: 4px !important;
	background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
	border: none !important;
}

/* 메인 콘텐츠 */
.main-content {
	padding: 32px !important;
	background: #f8fafc;
	min-height: 500px;
}

/* 로딩 섹션 */
.loading-section {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 400px;
}

.loading-content {
	text-align: center;
}

.loading-title {
	margin-top: 24px;
	font-size: 24px;
	color: #2d3748;
	font-weight: 600;
}

.loading-subtitle {
	margin-top: 8px;
	color: #718096;
	font-size: 16px;
}

/* 통계 카드 */
.stats-row {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 20px;
	margin-bottom: 32px;
}

.stats-card {
	padding: 24px;
	border-radius: 16px !important;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stats-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
	z-index: 0;
}

.stats-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
}

.pending-card {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	color: white;
}

.approved-card {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
	color: white;
}

.total-card {
	background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
	color: white;
}

.stats-icon {
	margin-right: 16px;
	z-index: 1;
	position: relative;
}

.stats-content {
	z-index: 1;
	position: relative;
}

.stats-number {
	font-size: 32px;
	font-weight: 700;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stats-label {
	font-size: 16px;
	margin: 4px 0 0 0;
	opacity: 0.9;
	font-weight: 500;
}

/* 사용자 섹션 */
.users-section {
	border-radius: 20px !important;
	background: white;
	padding: 32px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24px;
}

.section-title {
	display: flex;
	align-items: center;
}

.section-title h3 {
	font-size: 24px;
	font-weight: 600;
	color: #2d3748;
	margin: 0;
}

.refresh-btn {
	border-radius: 12px !important;
	text-transform: none !important;
	font-weight: 600;
}

/* 빈 상태 */
.empty-state {
	text-align: center;
	padding: 64px 32px;
}

.empty-icon {
	margin-bottom: 24px;
}

.empty-title {
	font-size: 24px;
	color: #2d3748;
	font-weight: 600;
	margin-bottom: 12px;
}

.empty-subtitle {
	font-size: 16px;
	color: #718096;
	margin: 0;
}

/* 사용자 그리드 */
.users-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
	gap: 20px;
}

.user-card {
	border-radius: 16px !important;
	padding: 24px;
	transition: all 0.3s ease;
	background: white;
	border: 2px solid transparent;
}

.user-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1) !important;
	border-color: #e2e8f0;
}

.user-card.approving {
	background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
	border-color: #38a169;
}

.user-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16px;
}

.user-avatar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
	margin-right: 16px;
	flex-shrink: 0;
}

.user-info {
	flex: 1;
	min-width: 0;
}

.user-name {
	font-size: 18px;
	font-weight: 600;
	color: #2d3748;
	margin: 0 0 4px 0;
	word-break: break-word;
}

.user-email {
	font-size: 14px;
	color: #718096;
	margin: 0 0 8px 0;
	word-break: break-all;
}

.user-date {
	display: flex;
	align-items: center;
	font-size: 12px;
	color: #a0aec0;
}

.user-date span {
	margin-left: 4px;
}

.user-actions {
	display: flex;
	gap: 8px;
}

.action-btn {
	flex: 1;
	border-radius: 12px !important;
	text-transform: none !important;
	font-weight: 600;
	font-size: 14px;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
	.admin-header {
		padding: 20px;
		flex-direction: column;
		text-align: center;
	}

	.header-content {
		margin-bottom: 16px;
	}

	.main-content {
		padding: 20px !important;
	}

	.stats-row {
		grid-template-columns: 1fr;
		gap: 16px;
	}

	.users-section {
		padding: 20px;
	}

	.section-header {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.users-grid {
		grid-template-columns: 1fr;
	}

	.user-actions {
		flex-direction: column;
	}
}

/* Custom 애니메이션 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.user-card {
	animation: fadeInUp 0.4s ease-out;
}

.user-card:nth-child(1) { animation-delay: 0.1s; }
.user-card:nth-child(2) { animation-delay: 0.2s; }
.user-card:nth-child(3) { animation-delay: 0.3s; }
.user-card:nth-child(4) { animation-delay: 0.4s; }
</style> 