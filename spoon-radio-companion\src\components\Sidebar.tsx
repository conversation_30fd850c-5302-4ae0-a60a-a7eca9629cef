import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuthContext } from '../contexts/AuthContext';
import { db } from '../firebaseConfig';
import { collection, getDocs, query, where } from 'firebase/firestore';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onMenuSelect?: (menu: string) => void;
}

const SidebarContainer = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 240px;
  height: 100vh;
  background-color: #ffffff;
  border-right: 1px solid #e5e5e5;
  z-index: 999;
  transform: translateX(${props => props.$isOpen ? '0' : '-100%'});
  transition: transform 0.3s ease;
  overflow-y: auto;
`;

const SidebarHeader = styled.div`
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const LogoText = styled.span`
  font-size: 20px;
  font-weight: 700;
  color: #8b5cf6;
`;

const CloseButton = styled.button`
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #666;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f0f0f0;
  }
`;

const SidebarContent = styled.div`
  padding: 20px 0;
`;

const MenuSection = styled.div`
  margin-bottom: 24px;
`;

const MenuTitle = styled.h3`
  font-size: 12px;
  font-weight: 600;
  color: #999;
  text-transform: uppercase;
  margin: 0 0 12px 20px;
  letter-spacing: 0.5px;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #1a1a1a;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  
  &:hover {
    background-color: #f8f9fa;
    color: #8b5cf6;
  }
  
  &.active {
    background-color: #f3f4f6;
    color: #8b5cf6;
    border-right: 3px solid #8b5cf6;
  }
`;

const MenuIcon = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
`;

const NotificationBadge = styled.div`
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #ef4444;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const UserSection = styled.div`
  padding: 20px;
  border-top: 1px solid #e5e5e5;
  margin-top: auto;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #8b5cf6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 600;
`;

const UserName = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
`;

const UserEmail = styled.div`
  font-size: 12px;
  color: #666;
`;

const LoginPrompt = styled.div`
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
`;

const LoginButton = styled.button`
  width: 100%;
  background-color: #8b5cf6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #7c3aed;
  }
`;

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, onMenuSelect }) => {
  const { user } = useAuthContext();
  const [pendingCount, setPendingCount] = useState(0);

  // 임시로 관리자 확인 (실제로는 백엔드에서 역할 확인)
  const isAdmin = user?.email === '<EMAIL>' || user?.email?.includes('admin');

  // Firebase에서 승인 요청 개수 가져오기
  const loadPendingRequestsCount = async () => {
    if (!isAdmin) {
      setPendingCount(0);
      return;
    }

    try {
      const usersCollection = collection(db, 'users');
      const pendingQuery = query(usersCollection, where('isApproved', '==', false));
      const querySnapshot = await getDocs(pendingQuery);
      
      const count = querySnapshot.size;
      setPendingCount(count);
      
      console.log('✅ 사이드바 승인 요청 개수 로드 성공:', count);
    } catch (error) {
      console.error('❌ 사이드바 승인 요청 개수 로드 실패:', error);
      setPendingCount(0);
    }
  };

  // 컴포넌트 마운트 시 및 사이드바 열릴 때 승인 요청 개수 로드
  useEffect(() => {
    if (isOpen && isAdmin) {
      loadPendingRequestsCount();
    }
  }, [isOpen, isAdmin]);

  // 주기적으로 승인 요청 개수 업데이트 (30초마다)
  useEffect(() => {
    if (!isAdmin) return;

    const interval = setInterval(() => {
      loadPendingRequestsCount();
    }, 30000); // 30초마다 업데이트

    return () => clearInterval(interval);
  }, [isAdmin]);

  // 승인 요청 개수 확인 (기존 함수 제거)
  // const getPendingRequestsCount = () => {
  //   const pendingUsers = JSON.parse(localStorage.getItem('pendingUsers') || '[]');
  //   return pendingUsers.length;
  // };

  // const pendingCount = isAdmin ? getPendingRequestsCount() : 0;

  const menuItems = [
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
        </svg>
      ), 
      label: '홈', 
      active: true, 
      key: 'home' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-1.74L12 1z"/>
        </svg>
      ), 
      label: '라이브', 
      active: false, 
      key: 'live' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      ), 
      label: '탐색', 
      active: false, 
      key: 'search' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
        </svg>
      ), 
      label: '캐스트', 
      active: false, 
      key: 'cast' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01 1.01l-2.54 3.4c-.44.59-.44 1.4 0 1.98l2.54 3.4c.47.64 1.21 1.01 2.01 1.01h2.46L20 22h-2z"/>
        </svg>
      ), 
      label: '커뮤니티', 
      active: false, 
      key: 'community' 
    },
  ];

  const personalItems = [
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
        </svg>
      ), 
      label: '좋아요', 
      active: false, 
      key: 'likes' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      ), 
      label: '팔로우', 
      active: false, 
      key: 'follow' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
        </svg>
      ), 
      label: '플레이리스트', 
      active: false, 
      key: 'playlist' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
        </svg>
      ), 
      label: 'DJ 대시보드', 
      active: false, 
      key: 'dashboard' 
    },
  ];

  const adminItems = [
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
        </svg>
      ), 
      label: '승인 요청', 
      active: false, 
      key: 'approval-requests' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01 1.01l-2.54 3.4c-.44.59-.44 1.4 0 1.98l2.54 3.4c.47.64 1.21 1.01 2.01 1.01h2.46L20 22h-2z"/>
        </svg>
      ), 
      label: '계정 관리', 
      active: false, 
      key: 'account-management' 
    }
  ];

  const settingsItems = [
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
        </svg>
      ), 
      label: '설정', 
      active: false, 
      key: 'settings' 
    },
    { 
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      ), 
      label: '연동 가이드', 
      active: false, 
      key: 'spoon-guide' 
    },
  ];

  const handleMenuClick = (menuKey: string) => {
    if (onMenuSelect) {
      onMenuSelect(menuKey);
    }
    onClose();
  };

  return (
    <SidebarContainer $isOpen={isOpen}>
      <SidebarHeader>
        <LogoText>Tamm</LogoText>
        <CloseButton onClick={onClose}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </CloseButton>
      </SidebarHeader>

      <SidebarContent>
        <MenuSection>
          {menuItems.map((item, index) => (
            <MenuItem 
              key={index} 
              className={item.active ? 'active' : ''}
              onClick={() => handleMenuClick(item.key)}
            >
              <MenuIcon>{item.icon}</MenuIcon>
              <span>{item.label}</span>
            </MenuItem>
          ))}
        </MenuSection>

        {user && (
          <MenuSection>
            <MenuTitle>개인</MenuTitle>
            {personalItems.map((item, index) => (
              <MenuItem 
                key={index} 
                className={item.active ? 'active' : ''}
                onClick={() => handleMenuClick(item.key)}
              >
                <MenuIcon>{item.icon}</MenuIcon>
                <span>{item.label}</span>
              </MenuItem>
            ))}
          </MenuSection>
        )}

        {user && isAdmin && (
          <MenuSection>
            <MenuTitle>관리자</MenuTitle>
            {adminItems.map((item, index) => (
              <MenuItem 
                key={index} 
                className={item.active ? 'active' : ''}
                onClick={() => handleMenuClick(item.key)}
              >
                <MenuIcon>{item.icon}</MenuIcon>
                <span>{item.label}</span>
                {item.key === 'approval-requests' && pendingCount > 0 && (
                  <NotificationBadge>{pendingCount}</NotificationBadge>
                )}
              </MenuItem>
            ))}
          </MenuSection>
        )}

        <MenuSection>
          <MenuTitle>기타</MenuTitle>
          {settingsItems.map((item, index) => (
            <MenuItem 
              key={index} 
              className={item.active ? 'active' : ''}
              onClick={() => handleMenuClick(item.key)}
            >
              <MenuIcon>{item.icon}</MenuIcon>
              <span>{item.label}</span>
            </MenuItem>
          ))}
        </MenuSection>
      </SidebarContent>

      <UserSection>
        {user ? (
          <UserInfo>
            <UserAvatar>
              {user.displayName ? user.displayName.charAt(0) : user.email?.charAt(0) || 'U'}
            </UserAvatar>
            <div>
              <UserName>{user.displayName || user.email?.split('@')[0] || '사용자'}</UserName>
              <UserEmail>{user.email}</UserEmail>
            </div>
          </UserInfo>
        ) : (
          <>
            <LoginPrompt>로그인하고 더 많은 기능을 이용하세요</LoginPrompt>
            <LoginButton onClick={onClose}>로그인</LoginButton>
          </>
        )}
      </UserSection>
    </SidebarContainer>
  );
};

export default Sidebar; 