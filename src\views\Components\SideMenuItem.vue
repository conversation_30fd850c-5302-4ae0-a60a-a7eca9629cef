<template>
	<div class="modern-sidebar-item" :class="{ 'active': active, 'expanded': expanded }" @click="handleClick">
		<div class="item-content">
			<!-- 아이콘 영역 -->
			<div class="icon-container">
				<v-icon 
					:color="active ? '#ffffff' : 'rgba(255, 255, 255, 0.7)'" 
					:size="expanded ? 24 : 20">
					{{ active ? activeIcon : icon }}
				</v-icon>
			</div>
			
			<!-- 라벨 영역 -->
			<div v-if="expanded" class="label-container">
				<span class="sidebar-label">{{ label }}</span>
			</div>
			
			<!-- 미니 모드에서의 라벨 -->
			<div v-if="!expanded" class="mini-label-container">
				<span class="mini-label">{{ label }}</span>
			</div>
			
			<!-- 활성 표시 인디케이터 -->
			<div v-if="active" class="active-indicator"></div>
			
			<!-- 호버 효과 -->
			<div class="hover-effect"></div>
		</div>
	</div>
</template>
<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';

@Component
export default class SideMenuItem extends Mixins(GlobalMixins) {
	@Prop(Boolean) public active!: boolean;
	@Prop(String) public label!: string;
	@Prop(String) public icon!: string;
	@Prop(String) public activeIcon!: string;
	@Prop(String) public href!: string;
	@Prop(Boolean) public openNew!: boolean;
	@Prop({ type: Boolean, default: false }) public expanded!: boolean;

	public handleClick() {
		this.$emit('click');
		this.$assign(this.href, this.openNew);
	}
}
</script>
<style scoped>
.modern-sidebar-item {
	position: relative;
	margin: 4px 0;
	cursor: pointer;
	border-radius: 12px;
	overflow: hidden;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.item-content {
	position: relative;
	display: flex;
	align-items: center;
	padding: 12px 16px;
	min-height: 48px;
	border-radius: 12px;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
}

.modern-sidebar-item:not(.expanded) .item-content {
	padding: 12px 8px;
	justify-content: center;
	flex-direction: column;
	gap: 4px;
}

.icon-container {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	transition: all 0.3s ease;
}

.modern-sidebar-item.expanded .icon-container {
	margin-right: 16px;
}

.label-container {
	flex: 1;
	display: flex;
	align-items: center;
}

.sidebar-label {
	color: rgba(255, 255, 255, 0.9);
	font-size: 0.95rem;
	font-weight: 500;
	letter-spacing: 0.25px;
	transition: all 0.3s ease;
}

.mini-label-container {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 2px;
}

.mini-label {
	color: rgba(255, 255, 255, 0.6);
	font-size: 0.65rem;
	font-weight: 400;
	text-align: center;
	line-height: 1.2;
	transition: all 0.3s ease;
}

/* 활성 상태 스타일 */
.modern-sidebar-item.active .item-content {
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-sidebar-item.active .sidebar-label {
	color: white;
	font-weight: 600;
}

.modern-sidebar-item.active .mini-label {
	color: rgba(255, 255, 255, 0.9);
}

/* 호버 효과 */
.hover-effect {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.08);
	opacity: 0;
	transition: opacity 0.3s ease;
	border-radius: 12px;
}

.modern-sidebar-item:hover .hover-effect {
	opacity: 1;
}

.modern-sidebar-item:hover:not(.active) .sidebar-label {
	color: white;
}

.modern-sidebar-item:hover:not(.active) .mini-label {
	color: rgba(255, 255, 255, 0.8);
}

/* 활성 인디케이터 */
.active-indicator {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 24px;
	background: linear-gradient(135deg, #ffffff, rgba(255, 255, 255, 0.8));
	border-radius: 0 4px 4px 0;
	box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

/* 리플 효과 */
.modern-sidebar-item:active .item-content {
	transform: scale(0.98);
}

/* 포커스 상태 */
.modern-sidebar-item:focus-visible {
	outline: 2px solid rgba(255, 255, 255, 0.5);
	outline-offset: 2px;
}

/* 애니메이션 최적화 */
.modern-sidebar-item * {
	will-change: transform, opacity;
}

/* 접근성 개선 */
@media (prefers-reduced-motion: reduce) {
	.modern-sidebar-item,
	.item-content,
	.hover-effect,
	.sidebar-label,
	.mini-label,
	.icon-container {
		transition: none;
	}
}
</style>
