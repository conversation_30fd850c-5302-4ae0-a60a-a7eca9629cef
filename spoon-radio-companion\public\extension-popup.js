function isSpoonPage(url) {
    return /https?:\/\/(?:www\.)?spooncast\.net/.test(url);
}

let currentUserInfo = null;

window.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 확장프로그램 팝업 로드:', new Date().toLocaleString());
    
    try {
        const [tab] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
        console.log('📱 현재 탭:', tab.url);
        
        if (!isSpoonPage(tab.url)) {
            console.log('❌ 스푼캐스트 페이지가 아님');
            document.querySelector('#wrong-page').classList.remove('hidden');
            return;
        }

        console.log('📬 content script에 메시지 전송');
        chrome.tabs.sendMessage(tab.id, { method: 'getUserInfo' }, function(res) {
            console.log('📬 응답 수신:', res);
            
            if (chrome.runtime.lastError) {
                console.error('❌ 메시지 전송 실패:', chrome.runtime.lastError);
                document.querySelector('#not-login').classList.remove('hidden');
                return;
            }
            
            if (res && res.success) {
                const userInfo = res.data;
                currentUserInfo = userInfo;
                console.log('✅ 사용자 정보 수신:', {
                    id: userInfo.id,
                    nickname: userInfo.nickname,
                    hasToken: !!userInfo.token,
                    tokenLength: userInfo.token ? userInfo.token.length : 0
                });
                
                // UI 업데이트
                document.querySelector('#user-avatar').src = userInfo.profile_url || '/favicon.ico';
                document.querySelector('#user-name').textContent = userInfo.nickname || '사용자';
                document.querySelector('#user-tag').textContent = userInfo.tag ? `@${userInfo.tag}` : '';
                document.querySelector('#login-page').classList.remove('hidden');
                
                console.log('✅ 로그인 페이지 표시 완료');
            } else {
                console.log('❌ 로그인되지 않음:', res?.error);
                document.querySelector('#not-login').classList.remove('hidden');
            }
        });
    } catch (error) {
        console.error('❌ 초기화 실패:', error);
        document.querySelector('#not-login').classList.remove('hidden');
    }
});

// 버튼 클릭 이벤트 리스너
document.addEventListener('DOMContentLoaded', () => {
    const loginBtn = document.querySelector('#login-btn');
    if (loginBtn) {
        console.log('🔘 버튼 이벤트 리스너 등록 중...');
        loginBtn.addEventListener('click', async () => {
            console.log('🔗 TAMM 연동 버튼 클릭됨!');
            
            if (!currentUserInfo) {
                console.error('❌ 사용자 정보가 없습니다');
                alert('사용자 정보를 찾을 수 없습니다. 스푼캐스트에 로그인되어 있는지 확인해주세요.');
                return;
            }
            
            try {
                loginBtn.disabled = true;
                loginBtn.textContent = '연동 중...';
                console.log('🔄 연동 프로세스 시작');
                
                // 1. 사용자 IP 가져오기
                console.log('🌐 사용자 IP 조회 시작');
                const ipResponse = await fetch('https://api.ipify.org?format=json');
                const ipData = await ipResponse.json();
                const userIP = ipData.ip;
                console.log('🌐 사용자 IP 조회 완료:', userIP);
                
                // 2. Firebase Firestore에 IP와 토큰 저장
                console.log('🔥 Firebase Firestore에 IP와 토큰 저장 시작');
                const tokenData = {
                    id: currentUserInfo.id,
                    nickname: currentUserInfo.nickname,
                    profile_url: currentUserInfo.profile_url,
                    tag: currentUserInfo.tag,
                    token: currentUserInfo.token,
                    refresh_token: currentUserInfo.refresh_token,
                    userIP: userIP,
                    timestamp: Date.now()
                };
                
                console.log('📤 Firestore에 저장할 토큰 데이터:', {
                    id: tokenData.id,
                    nickname: tokenData.nickname,
                    userIP: tokenData.userIP,
                    tokenLength: tokenData.token ? tokenData.token.length : 0
                });
                
                // originalData 디버깅
                console.log('🔍 currentUserInfo 상태:', {
                    hasOriginalData: !!currentUserInfo.originalData,
                    originalDataKeys: currentUserInfo.originalData ? Object.keys(currentUserInfo.originalData) : [],
                    currentUserInfoKeys: Object.keys(currentUserInfo),
                    originalDataSize: currentUserInfo.originalData ? JSON.stringify(currentUserInfo.originalData).length : 0,
                    fallbackSize: JSON.stringify(currentUserInfo).length
                });
                
                // Firebase Firestore에 토큰 저장
                try {
                    console.log('🔥 Firebase 연결 시작');
                    
                    // 간단한 REST API 방식으로 Firestore에 저장
                    const firestoreUrl = `https://firestore.googleapis.com/v1/projects/tammapp-4cbb3/databases/(default)/documents/spoon_tokens/${userIP}`;
                    
                    const firestoreData = {
                        fields: {
                            token: { stringValue: tokenData.token },
                            refreshToken: { stringValue: tokenData.refresh_token || '' },
                            username: { stringValue: tokenData.nickname },
                            userId: { stringValue: tokenData.id.toString() },
                            timestamp: { integerValue: tokenData.timestamp.toString() },
                            userIP: { stringValue: userIP },
                            user: {
                                mapValue: {
                                    fields: {
                                        id: { stringValue: tokenData.id.toString() },
                                        nickname: { stringValue: tokenData.nickname },
                                        tag: { stringValue: tokenData.tag || '' },
                                        profile_url: { stringValue: tokenData.profile_url || '' }
                                    }
                                }
                            },
                            originalData: {
                                mapValue: {
                                    fields: {
                                        // currentUserInfo.originalData 또는 currentUserInfo 전체를 저장
                                        data: { stringValue: JSON.stringify(currentUserInfo.originalData || currentUserInfo) }
                                    }
                                }
                            },
                            createdAt: { timestampValue: new Date().toISOString() }
                            // expiresAt 설정 제거 - 토큰 만료 기능 비활성화
                        }
                    };
                    
                    const response = await fetch(firestoreUrl + '?key=AIzaSyDt1d7J8QS-mrRmVcNZ21AEd8f03ePL3Xs', {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(firestoreData)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`Firestore API 호출 실패: ${response.status} ${response.statusText}`);
                    }
                    
                    console.log('✅ Firestore에 토큰 저장 성공 (REST API)');
                    
                } catch (firestoreError) {
                    console.error('❌ Firestore 저장 실패:', firestoreError);
                    throw new Error(`Firestore에 토큰을 저장할 수 없습니다: ${firestoreError.message}`);
                }
                
                // 3. TAMM 웹앱에도 메시지 전송 (선택적)
                console.log('📡 TAMM 웹앱 탭 검색 시작');
                const tabs = await chrome.tabs.query({});
                let messageSent = false;
                
                console.log('📋 전체 탭 수:', tabs.length);
                
                for (const tab of tabs) {
                    try {
                        // Firebase web.app 도메인 포함
                        const isTargetTab = tab.url && (
                            tab.url.includes('localhost') || 
                            tab.url.includes('tamm') || 
                            tab.url.includes('firebaseapp.com') ||
                            tab.url.includes('web.app') ||
                            tab.url.includes('tammapp-4cbb3')
                        );
                        
                        if (isTargetTab) {
                            console.log('🎯 TAMM 웹앱 탭 발견:', tab.url);
                            
                            await chrome.tabs.sendMessage(tab.id, {
                                type: 'SPOON_AUTH_SUCCESS',
                                tokenData: tokenData
                            });
                            
                            console.log('📤 TAMM 웹앱으로 알림 메시지 전송 완료:', tab.id);
                            messageSent = true;
                        }
                    } catch (error) {
                        // 탭에 content script가 없을 수 있으므로 무시
                        console.log('📝 탭 메시지 전송 실패 (정상):', tab.id, error.message);
                    }
                }
                
                // 성공 처리
                console.log('✅ 토큰 저장 및 연동 완료');
                document.querySelector('#success-message').classList.remove('hidden');
                loginBtn.textContent = '연동 완료';
                
                // 3초 후 팝업 닫기
                setTimeout(() => {
                    window.close();
                }, 3000);
                
            } catch (error) {
                console.error('❌ 연동 실패:', error);
                alert('연동에 실패했습니다: ' + error.message);
                loginBtn.disabled = false;
                loginBtn.textContent = 'TAMM에 연동하기';
            }
        });
        console.log('✅ 버튼 이벤트 리스너 등록 완료');
    } else {
        console.error('❌ 로그인 버튼을 찾을 수 없음');
    }
}); 