<template>
  <v-card class="mx-auto mt-10 modern-card" max-width="450" elevation="12">
    <div class="card-header">
      <div class="header-icon">
        <v-icon size="32" color="white">mdi-login</v-icon>
      </div>
      <h2 class="header-title">로그인</h2>
      <p class="header-subtitle">계정에 로그인하세요</p>
    </div>
    
    <v-card-text class="pt-8 px-8">
      <v-form ref="form" v-model="valid" lazy-validation @submit.prevent="handleLogin">
        <div class="input-group">
          <v-text-field
            v-model="form.email"
            :rules="emailRules"
            label="이메일"
            prepend-inner-icon="mdi-email-outline"
            solo
            rounded
            required
            class="modern-input"
            background-color="rgba(0,0,0,0.03)"
            flat
          ></v-text-field>
        </div>

        <div class="input-group">
          <v-text-field
            v-model="form.password"
            :rules="passwordRules"
            label="비밀번호"
            prepend-inner-icon="mdi-lock-outline"
            :type="showPassword ? 'text' : 'password'"
            :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append="showPassword = !showPassword"
            solo
            rounded
            required
            class="modern-input"
            background-color="rgba(0,0,0,0.03)"
            flat
            @keypress.enter="handleLogin"
          ></v-text-field>
        </div>

        <!-- 로그인 정보 저장 체크박스 -->
        <div class="input-group remember-section">
          <v-checkbox
            v-model="rememberCredentials"
            color="primary"
            class="remember-checkbox"
          >
            <template v-slot:label>
              <span class="remember-label">
                <v-icon left size="18" color="primary">mdi-content-save-outline</v-icon>
                로그인 정보 저장
              </span>
            </template>
          </v-checkbox>
        </div>

        <v-fade-transition>
          <v-alert
            v-if="message"
            :type="messageType"
            dense
            rounded
            class="mb-6 modern-alert"
          >
            {{ message }}
          </v-alert>
        </v-fade-transition>
      </v-form>
    </v-card-text>

    <v-card-actions class="px-8 pb-8">
      <v-btn
        :disabled="!valid || loading"
        :loading="loading"
        @click="handleLogin"
        color="primary"
        x-large
        rounded
        block
        class="modern-button elevation-2"
        elevation="0"
      >
        <v-icon left>mdi-login</v-icon>
        로그인
      </v-btn>
    </v-card-actions>

    <v-divider class="mx-6"></v-divider>

    <v-card-actions class="px-8 pt-4 pb-8">
      <v-btn
        @click="$emit('switch-to-register')"
        color="grey"
        text
        rounded
        block
        class="modern-link-button"
      >
        계정이 없으신가요? 회원가입
      </v-btn>
    </v-card-actions>

    <!-- 관리자 전용 승인 패널 -->
    <template v-if="isAdmin">
      <v-divider class="mx-6"></v-divider>
      <div class="admin-panel-header">
        <div class="admin-icon">
          <v-icon size="24" color="white">mdi-account-supervisor</v-icon>
        </div>
        <h3 class="admin-title">관리자 패널</h3>
        <p class="admin-subtitle">회원 승인 관리</p>
      </div>
      
      <v-card-text class="px-8 pb-8">
        <div v-if="pendingUsers.length > 0" class="pending-users">
          <div
            v-for="user in pendingUsers"
            :key="user.id"
            class="user-card"
          >
            <div class="user-info">
              <div class="user-avatar">
                <v-icon color="primary">mdi-account</v-icon>
              </div>
              <div class="user-details">
                <h4 class="user-name">{{ user.name }}</h4>
                <p class="user-email">{{ user.email }}</p>
                <span class="user-date">{{ formatDate(user.created_at) }}</span>
              </div>
            </div>
            
            <v-btn
              @click="approveUser(user.id)"
              color="success"
              small
              rounded
              class="approve-btn"
              :loading="approvingUserId === user.id"
            >
              <v-icon left small>mdi-check</v-icon>
              승인
            </v-btn>
          </div>
        </div>
        
        <div v-else class="no-pending">
          <v-icon size="48" color="grey lighten-2">mdi-account-check</v-icon>
          <p class="no-pending-text">승인 대기 중인 회원이 없습니다</p>
        </div>
      </v-card-text>
    </template>
  </v-card>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { authService, PendingUser } from '@/plugins/auth-service';
import { User } from 'firebase/auth';

@Component
export default class TammLogin extends Vue {
  public valid = false;
  public loading = false;
  public showPassword = false;
  public message = '';
  public messageType: 'success' | 'error' | 'warning' | 'info' = 'info';
  public isAdmin = false;
  public pendingUsers: PendingUser[] = [];
  public approvingUserId: string | null = null;
  public rememberCredentials = false;
  public email = '';
  public password = '';
  public loginMessage = '';
  public adminMessage = '';

  public form = {
    email: '',
    password: ''
  };

  public emailRules = [
    (v: string) => !!v || '이메일을 입력해주세요.',
    (v: string) => /.+@.+\..+/.test(v) || '올바른 이메일 형식이 아닙니다.'
  ];

  public passwordRules = [
    (v: string) => !!v || '비밀번호를 입력해주세요.'
  ];

  public mounted() {
    // 현재 로그인된 사용자 확인
    const currentUser = authService.getCurrentUser();
    if (currentUser) {
      this.isAdmin = currentUser.role === 'admin';
      if (this.isAdmin) {
        this.loadPendingUsers();
      }
    }

    // 저장된 로그인 정보 불러오기
    this.loadSavedCredentials();
  }

  public loadSavedCredentials() {
    const savedCredentials = authService.getSavedCredentials();
    if (savedCredentials) {
      this.form.email = savedCredentials.email;
      this.form.password = savedCredentials.password;
      this.rememberCredentials = true;
    }
  }

  public async handleLogin() {
    if (!(this.$refs.form as any).validate()) {
      return;
    }

    this.loading = true;
    this.message = '';

    try {
      // 로그인 정보 저장/삭제
      if (this.rememberCredentials) {
        authService.saveCredentials(this.form.email, this.form.password);
      } else {
        authService.clearSavedCredentials();
      }

      const result = await authService.login(this.form.email, this.form.password);

      if (result.success && result.user) {
        this.messageType = 'success';
        this.message = result.message;
        
        // 관리자인지 확인
        this.isAdmin = result.user.role === 'admin';
        if (this.isAdmin) {
          this.loadPendingUsers();
        }

        // 로그인 성공 시 메인 애플리케이션으로 이동
        this.$emit('login-success', result.user);
        
      } else {
        this.messageType = 'error';
        this.message = result.message;
      }
    } catch (error) {
      console.error('로그인 오류:', error);
      this.messageType = 'error';
      this.message = '로그인 중 오류가 발생했습니다.';
    } finally {
      this.loading = false;
    }
  }

  public async loadPendingUsers() {
    try {
      this.pendingUsers = authService.getPendingUsers();
    } catch (error) {
      console.error('대기 중인 사용자 로드 오류:', error);
    }
  }

  public async approveUser(userId: string) {
    if (!this.isAdmin) return;

    this.approvingUserId = userId;
    
    try {
      const result = await authService.approveUser(userId);
      
      if (result.success) {
        await this.$swal({
          icon: 'success',
          title: '승인 완료',
          text: result.message,
          timer: 2000,
          showConfirmButton: false
        });
        
        // 목록 새로고침
        this.loadPendingUsers();
      } else {
        await this.$swal({
          icon: 'error',
          title: '승인 실패',
          text: result.message
        });
      }
    } catch (error) {
      console.error('사용자 승인 오류:', error);
      await this.$swal({
        icon: 'error',
        title: '오류',
        text: '승인 처리 중 오류가 발생했습니다.'
      });
    } finally {
      this.approvingUserId = null;
    }
  }

  public formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ko-KR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  public async checkAdminStatus(uid: string) {
    try {
      // Firestore에서 사용자 역할 정보 가져오기
      const userDoc = await authService.getAllUsers().then(users => users.find(u => u.id === uid)); // getAllUsers는 임시로 모든 사용자 가져옴
      if (userDoc && userDoc.role === 'admin') {
        this.isAdmin = true;
        await this.fetchPendingUsers(); // 관리자라면 대기 사용자 목록 로드
      } else {
        this.isAdmin = false;
      }
    } catch (error) {
      console.error('관리자 상태 확인 오류:', error);
      this.isAdmin = false;
    }
  }

  public async fetchPendingUsers() {
    this.adminMessage = '';
    try {
      this.pendingUsers = await authService.getPendingUsers();
      this.adminMessage = `현재 ${this.pendingUsers.length}명의 승인 대기 중인 사용자가 있습니다.`;
    } catch (error: any) {
      console.error('승인 대기 사용자 가져오기 오류:', error);
      this.adminMessage = `승인 대기 사용자 가져오기 실패: ${error.message}`;
    }
  }

  public async handleLogout() {
    try {
      await authService.logout();
      this.isAdmin = false;
      this.email = '';
      this.password = '';
      this.loginMessage = '로그아웃 되었습니다.';
      this.$router.push('/login'); // 로그인 화면으로 리디렉션
    } catch (error) {
      console.error('로그아웃 오류:', error);
      this.loginMessage = '로그아웃 중 오류가 발생했습니다.';
    }
  }
}
</script>

<style scoped>
.modern-card {
  border-radius: 24px !important;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-header {
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
  padding: 32px 24px 24px;
  text-align: center;
  color: white;
  position: relative;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px;
  letter-spacing: -0.5px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.input-group {
  margin-bottom: 20px;
}

.modern-input {
  margin-bottom: 0 !important;
}

.modern-input .v-input__slot {
  border-radius: 50px !important;
  min-height: 56px !important;
  padding: 0 20px !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-input .v-input__slot:hover {
  border-color: rgba(33, 150, 243, 0.3) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
}

.modern-input.v-input--is-focused .v-input__slot {
  border-color: #2196f3 !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(33, 150, 243, 0.15) !important;
}

.modern-input .v-text-field__details {
  padding: 8px 20px 0 !important;
}

.modern-button {
  height: 56px !important;
  border-radius: 28px !important;
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3) !important;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4) !important;
}

.modern-button:active {
  transform: translateY(0);
}

.modern-link-button {
  height: 48px !important;
  border-radius: 24px !important;
  color: #666 !important;
  font-weight: 500 !important;
  text-transform: none !important;
  transition: all 0.3s ease;
}

.modern-link-button:hover {
  background: rgba(33, 150, 243, 0.05) !important;
  color: #2196f3 !important;
}

.modern-alert {
  border-radius: 16px !important;
  border: none !important;
  backdrop-filter: blur(10px);
}

/* 관리자 패널 스타일 */
.admin-panel-header {
  background: linear-gradient(135deg, #ff9800 0%, #ff5722 100%);
  padding: 24px;
  text-align: center;
  color: white;
  position: relative;
}

.admin-panel-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.admin-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.admin-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px;
  letter-spacing: -0.3px;
}

.admin-subtitle {
  font-size: 12px;
  opacity: 0.9;
  margin: 0;
}

.pending-users {
  margin-top: 8px;
}

.user-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.user-card:last-child {
  margin-bottom: 0;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(33, 150, 243, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  border: 2px solid rgba(33, 150, 243, 0.2);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px;
  color: #333;
}

.user-email {
  font-size: 14px;
  color: #666;
  margin: 0 0 4px;
}

.user-date {
  font-size: 12px;
  color: #999;
}

.approve-btn {
  margin-left: 12px;
  height: 36px !important;
  min-width: 80px;
  border-radius: 18px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3) !important;
}

.approve-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4) !important;
}

.no-pending {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.no-pending-text {
  margin: 12px 0 0;
  font-size: 14px;
  color: #666;
}

.v-divider {
  opacity: 0.3;
}

/* 반응형 디자인 */
@media (max-width: 600px) {
  .modern-card {
    margin: 20px 16px !important;
    max-width: none !important;
  }
  
  .card-header {
    padding: 24px 16px 20px;
  }
  
  .header-title {
    font-size: 20px;
  }
  
  .v-card-text {
    padding: 24px 20px !important;
  }
  
  .v-card-actions {
    padding: 0 20px 24px !important;
  }
  
  .admin-panel-header {
    padding: 20px 16px;
  }
  
  .user-card {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .user-info {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .approve-btn {
    margin-left: 0;
    width: 100%;
  }
}

/* 로그인 정보 저장 체크박스 스타일 */
.remember-section {
  margin-bottom: 16px !important;
  margin-top: 8px;
}

.remember-checkbox {
  margin: 0 !important;
  padding: 0 !important;
}

.remember-checkbox .v-input__slot {
  margin-bottom: 0 !important;
  padding: 8px 0 !important;
}

.remember-label {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.remember-label:hover {
  color: #2196f3;
}

.remember-checkbox .v-input--selection-controls__input {
  margin-right: 8px !important;
}

.remember-checkbox .v-icon {
  font-size: 20px !important;
  color: #2196f3 !important;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-group {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

.input-group:nth-child(1) { animation-delay: 0.1s; }
.input-group:nth-child(2) { animation-delay: 0.2s; }

.user-card {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

.user-card:nth-child(1) { animation-delay: 0.1s; }
.user-card:nth-child(2) { animation-delay: 0.2s; }
.user-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 