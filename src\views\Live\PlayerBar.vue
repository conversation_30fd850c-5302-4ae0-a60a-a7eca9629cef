<template>
	<div>
		<!-- 플레이어 바 컨테이너 -->
		<div class="player-bar-container">
			<div class="top-row">
				<!-- 메인 컨테이너 (프로필 이미지, 방송 정보) -->
				<div class="player-bar-main">
					<!-- 방송자 프로필 이미지 -->
					<v-avatar
						size="40"
						class="profile-avatar"
						@click="$assign('/user/' + live.author.id)">
						<v-img :src="live.author.profile_url"></v-img>
					</v-avatar>
					
					<!-- 방송 정보 컨테이너 -->
					<div class="stream-content">
						<!-- 방송 제목 및 DJ 이름 -->
						<div class="stream-info">
							<span class="stream-title">{{ live.title }}</span>
							<span class="dj-name">{{ live.author.nickname }}</span>
						</div>
					</div>
				</div>
				
				<!-- 매니저 상태 표시 -->
				<div v-if="!isManager" class="manager-status-container">
					<div class="manager-status-badge">
						No Manager
					</div>
				</div>
				
				<!-- 컨트롤 버튼 컨테이너 (소리 설정, 최소화, 닫기) -->
				<div class="control-container">
					<!-- 소리 설정 버튼 추가 -->
					<v-btn icon text class="control-button sound-btn" @click="toggleSoundMenu">
						<v-icon size="16">{{ isMuted ? 'mdi-volume-off' : 'mdi-volume-high' }}</v-icon>
					</v-btn>
					<v-btn icon text class="control-button minimize-btn" @click.stop="$emit('screen:close')">
						<v-icon size="16">mdi-window-minimize</v-icon>
					</v-btn>
					<v-btn icon text class="control-button close-btn" @click.stop="$emit('close')">
						<v-icon size="16">mdi-close</v-icon>
					</v-btn>
				</div>
			</div>
			
			<!-- 방송 경과 시간만 표시 -->
			<div class="stats-row">
				<!-- 경과 시간 컨테이너 -->
				<div class="stat-container time-stat">
					<v-icon size="12" color="white" class="mr-1">mdi-clock-outline</v-icon>
					<span>{{ formattedDuration }}</span>
				</div>
				
				<!-- 좋아요 수 컨테이너 -->
				<div class="stat-container likes-stat">
					<v-icon size="12" color="white" class="mr-1">mdi-heart</v-icon>
					<span>{{ formatNumber(likeCount) }}</span>
				</div>
				
				<!-- 시청자 수 컨테이너 -->
				<div class="stat-container viewers-stat">
					<v-icon size="12" color="white" class="mr-1">mdi-account-group</v-icon>
					<span>{{ formatNumber(viewerCount) }}</span>
				</div>
			</div>
		</div>
		
		<!-- 사운드 설정 메뉴 (볼륨 조절 메뉴) 추가 -->
		<v-menu
			v-model="showSoundMenu"
			:close-on-content-click="false"
			:nudge-width="150"
			:position-x="soundMenuPosition.x"
			:position-y="soundMenuPosition.y"
			absolute
			@mouseenter="resetSoundMenuTimer"
			@mouseleave="startSoundMenuTimer"
		>
			<div class="sound-menu-container" 
				@mouseenter="resetSoundMenuTimer" 
				@mouseleave="startSoundMenuTimer"
				@wheel.prevent="handleVolumeWheel">
				<v-row class="ma-0" align="center">
					<v-col cols="2" class="pa-0">
						<v-btn icon dark small @click="toggleMute">
							<v-icon>{{ isMuted ? 'mdi-volume-off' : 'mdi-volume-high' }}</v-icon>
						</v-btn>
					</v-col>
					<v-col cols="10" class="pa-0 pl-2">
						<v-slider
							dark
							v-model="volume"
							tick-size="1"
							hide-details
							dense
							@input="onVolumeChange"
							:label="volume.toString()"
						></v-slider>
					</v-col>
				</v-row>
			</div>
		</v-menu>
	</div>
</template>
<script lang="ts">
import { Component, Prop, Mixins, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { Live, LiveEvent } from '@sopia-bot/core';

@Component
export default class LivePlayerBar extends Mixins(GlobalMixins) {
	@Prop(Object) public live!: Live;
	@Prop({ type: Boolean, default: false }) public isManager!: boolean;
	@Prop({ type: Object, default: null }) public player!: any;
	
	private intervalId: number = 0;
	private startTime: number = Date.now();
	private currentDuration: number = 0;
	private showSoundMenu: boolean = false;
	private isMuted: boolean = this.$cfg.get('player.isMute') || false;
	private volume: number = this.$cfg.get('player.volume') || 50;
	private volumeTimer: any = null;
	private soundMenuTimer: any = null;
	private soundMenuPosition = { x: 0, y: 0 };
	
	// 상태 추적 변수들
	private likeCount: number = 0;
	private viewerCount: number = 0;
	
	mounted() {
		// 시작 시간 설정
		this.startTime = Date.now() - (this.live.created ? (Date.now() - new Date(this.live.created).getTime()) : 0);
		
		// 타이머 시작
		this.updateDuration();
		this.intervalId = window.setInterval(() => {
			this.updateDuration();
		}, 1000);
		
		// 초기값 설정
		this.initLiveStats();
		
		// 라이브 소켓 이벤트 리스닝
		this.registerSocketEvents();
	}

	beforeDestroy() {
		if (this.intervalId) {
			clearInterval(this.intervalId);
		}
		
		if (this.volumeTimer) {
			clearTimeout(this.volumeTimer);
		}
		
		if (this.soundMenuTimer) {
			clearTimeout(this.soundMenuTimer);
		}
	}
	
	// 숫자 포맷 함수 (1000 -> 1K, 1000000 -> 1M 형식으로 변환)
	formatNumber(num: number): string {
		if (num < 1000) return num.toString();
		if (num < 1000000) return (num / 1000).toFixed(1) + 'K';
		return (num / 1000000).toFixed(1) + 'M';
	}
	
	updateDuration() {
		this.currentDuration = Math.floor((Date.now() - this.startTime) / 1000);
	}
	
	// 볼륨 메뉴 타이머 시작 (3초 후에 메뉴 닫기)
	startSoundMenuTimer() {
		if (this.soundMenuTimer) {
			clearTimeout(this.soundMenuTimer);
		}
		
		this.soundMenuTimer = setTimeout(() => {
			this.showSoundMenu = false;
		}, 3000); // 3초 후 사라짐
	}
	
	// 마우스 호버 시 타이머 리셋
	resetSoundMenuTimer() {
		if (this.soundMenuTimer) {
			clearTimeout(this.soundMenuTimer);
			this.soundMenuTimer = null;
		}
	}
	
	toggleSoundMenu() {
		// 클릭 위치를 기반으로 메뉴 위치 설정
		const playerBarElement = this.$el.querySelector('.player-bar-main');
		
		if (playerBarElement) {
			const rect = playerBarElement.getBoundingClientRect();
			// 방송정보 컨테이너와 같은 위치에 메뉴 표시
			this.soundMenuPosition = {
				x: rect.left,
				y: rect.top
			};
		}
		
		this.showSoundMenu = !this.showSoundMenu;
		
		if (this.showSoundMenu) {
			// 메뉴가 열리면 3초 타이머 시작
			this.startSoundMenuTimer();
		} else if (this.soundMenuTimer) {
			// 메뉴가 닫히면 타이머 정리
			clearTimeout(this.soundMenuTimer);
			this.soundMenuTimer = null;
		}
	}
	
	toggleMute() {
		this.isMuted = !this.isMuted;
		
		if (this.player) {
			if (this.isMuted) {
				this.player.volume = 0;
			} else {
				this.player.volume = this.volume * 0.01;
			}
		}
		
		this.$cfg.set('player.isMute', this.isMuted);
		this.$cfg.save();
	}
	
	onVolumeChange() {
		// 음소거 상태였을 경우 해제
		if (this.isMuted) {
			this.isMuted = false;
			this.$cfg.set('player.isMute', false);
		}
		
		if (this.player) {
			this.player.volume = this.volume * 0.01;
		}
		
		if (this.volumeTimer) {
			clearTimeout(this.volumeTimer);
		}
		
		this.volumeTimer = setTimeout(() => {
			this.$cfg.set('player.volume', this.volume);
			this.$cfg.save();
		}, 500);
	}
	
	get formattedDuration() {
		const seconds = this.currentDuration;
		const hours = Math.floor(seconds / 3600);
		const mins = Math.floor((seconds % 3600) / 60);
		const secs = seconds % 60;
		
		// 60분 이상이면 시:분:초 형식으로 표시
		if (hours > 0) {
			return `${hours < 10 ? '0' + hours : hours}:${mins < 10 ? '0' + mins : mins}:${secs < 10 ? '0' + secs : secs}`;
		}
		
		// 60분 미만이면 분:초 형식으로 표시
		return `${mins}:${secs < 10 ? '0' + secs : secs}`;
	}
	
	// 라이브 방송 통계 초기화
	initLiveStats() {
		// 좋아요 수 초기화
		this.likeCount = this.live.like_count || 0;
		
		// 시청자 수 초기화 (다양한 속성 이름을 시도)
		const liveAny = this.live as any;
		this.viewerCount = liveAny.member_count || liveAny.join_count || liveAny.viewer_count || 0;
		
		console.log('Live stats initialized:', { 
			likeCount: this.likeCount, 
			viewerCount: this.viewerCount 
		});
	}
	
	// 소켓 이벤트 등록
	registerSocketEvents() {
		if (!this.live || !this.live.socket) return;
		
		// 좋아요 이벤트
		this.live.socket.on(LiveEvent.LIVE_LIKE, (evt) => {
			this.likeCount++;
			console.log('Like received, new count:', this.likeCount);
		});
		
		// 입장 이벤트
		this.live.socket.on(LiveEvent.LIVE_JOIN, (evt) => {
			this.viewerCount++;
			console.log('Viewer joined, new count:', this.viewerCount);
		});
		
		// 퇴장 이벤트
		this.live.socket.on(LiveEvent.LIVE_LEAVE, (evt) => {
			if (this.viewerCount > 0) {
				this.viewerCount--;
				console.log('Viewer left, new count:', this.viewerCount);
			}
		});
		
		// 방송 상태 업데이트 이벤트
		this.live.socket.on(LiveEvent.LIVE_STATE, (evt) => {
			if (!evt.data) return;
			
			// 다양한 속성 이름 처리 (API 응답 형식이 일관되지 않을 수 있음)
			if (evt.data.like_count !== undefined) {
				this.likeCount = evt.data.like_count;
				console.log('Updated like count from state:', this.likeCount);
			}
			
			// 시청자 수 업데이트 (다양한 속성 이름 시도)
			const viewerData = evt.data.member_count || evt.data.join_count || evt.data.viewer_count;
			if (viewerData !== undefined) {
				this.viewerCount = viewerData;
				console.log('Updated viewer count from state:', this.viewerCount);
			}
		});
		
		// 추가: 주기적으로 방송 정보를 갱신하는 로직
		const refreshInterval = window.setInterval(async () => {
			try {
				if (!this.live || !this.live.id) {
					clearInterval(refreshInterval);
					return;
				}
				
				// 라이브 API로 최신 정보 요청
				const response = await this.$sopia.api.lives.info(this.live.id);
				if (response && response.res && response.res.results && response.res.results.length > 0) {
					const updatedLive = response.res.results[0];
					
					// 좋아요 수 업데이트
					if (updatedLive.like_count !== undefined) {
						this.likeCount = updatedLive.like_count;
					}
					
					// 시청자 수 업데이트 (다양한 속성 이름 시도)
					const updatedLiveAny = updatedLive as any;
					const newViewerCount = updatedLiveAny.member_count || 
											updatedLiveAny.join_count || 
											updatedLiveAny.viewer_count;
					
					if (newViewerCount !== undefined) {
						this.viewerCount = newViewerCount;
					}
					
					console.log('Stats refreshed from API:', {
						likeCount: this.likeCount,
						viewerCount: this.viewerCount
					});
				}
			} catch (error) {
				console.error('Error refreshing live stats:', error);
			}
		}, 30000); // 30초마다 갱신
		
		// 컴포넌트 제거 시 인터벌 정리
		this.$once('hook:beforeDestroy', () => {
			clearInterval(refreshInterval);
		});
	}
	
	// 마우스 휠로 볼륨 조절
	handleVolumeWheel(event: WheelEvent) {
		// 휠 아래로 = 볼륨 감소, 휠 위로 = 볼륨 증가
		const direction = event.deltaY > 0 ? -1 : 1;
		const newVolume = this.volume + (direction * 5); // 5씩 증감
		
		// 볼륨 범위 제한 (0-100)
		this.volume = Math.max(0, Math.min(100, newVolume));
		
		// 볼륨 변경 함수 호출
		this.onVolumeChange();
		
		// 타이머 리셋 (사용자가 휠 조작 중 메뉴가 사라지지 않도록)
		this.resetSoundMenuTimer();
	}
}
</script>
<style scoped>
.player-bar-container {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	flex-direction: column;
	padding: 8px;
	z-index: 100;
}

.top-row {
	display: flex;
	justify-content: space-between;
	width: 100%;
}

.stats-row {
	display: flex;
	margin-top: 8px;
	margin-left: 14px;
}

.player-bar-main {
	display: flex;
	align-items: center;
	background-color: rgba(20, 20, 20, 0.4);
	border-radius: 50px;
	padding: 6px 14px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	flex: 0 1 auto;
	min-width: auto;
	max-width: 50%;
	width: auto;
}

.profile-avatar {
	border: 2px solid rgba(80, 170, 255, 0.6);
	cursor: pointer;
	margin-right: 10px;
	flex-shrink: 0;
}

.stream-content {
	display: flex;
	flex-direction: column;
	flex: 0 1 auto;
	min-width: 120px;
}

.stream-info {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	max-width: 100%;
}

.stream-title {
	font-size: 0.85rem;
	font-weight: 500;
	color: white;
	margin-bottom: 1px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.dj-name {
	font-size: 0.75rem;
	color: rgba(255, 255, 255, 0.7);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.stat-container {
	display: flex;
	align-items: center;
	background-color: rgba(20, 20, 20, 0.4);
	border-radius: 50px;
	padding: 4px 12px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	margin-right: 8px;
	backdrop-filter: blur(4px);
	-webkit-backdrop-filter: blur(4px);
	transition: all 0.2s ease;
	border: 1px solid transparent;
}

.stat-container:hover {
	background-color: rgba(30, 30, 30, 0.5);
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
}

.time-stat {
	border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.likes-stat {
	border-left: 1px solid rgba(255, 64, 129, 0.3);
}

.likes-stat:hover {
	background-color: rgba(255, 64, 129, 0.1);
}

.viewers-stat {
	border-left: 1px solid rgba(66, 165, 245, 0.3);
}

.viewers-stat:hover {
	background-color: rgba(66, 165, 245, 0.1);
}

.stat-container span {
	font-size: 0.65rem;
	color: white;
	font-weight: 500;
}

.manager-status-container {
	display: flex;
	align-items: center;
	margin: 0 8px;
}

.manager-status-badge {
	background-color: rgba(244, 67, 54, 0.9);
	color: white;
	font-size: 0.7rem;
	font-weight: 600;
	border-radius: 50px;
	padding: 4px 12px;
	white-space: nowrap;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-container {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(20, 20, 20, 0.4);
	border-radius: 50px;
	padding: 4px 12px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	height: 40px;
}

.control-button {
	margin: 0 2px;
	width: 24px;
	height: 24px;
	min-width: 0;
	border-radius: 50%;
	transition: all 0.2s ease;
}

.minimize-btn {
	color: #fff;
}

.minimize-btn:hover {
	background-color: rgba(255, 255, 255, 0.2);
}

.close-btn {
	color: #ff5252;
}

.close-btn:hover {
	background-color: rgba(255, 82, 82, 0.2);
}

.sound-btn {
	color: #2196F3;
}

.sound-btn:hover {
	background-color: rgba(33, 150, 243, 0.2);
}

.sound-menu-container {
	background-color: rgba(20, 20, 20, 0.4);
	color: white;
	padding: 6px 14px;
	border-radius: 50px;
	width: auto;
	min-width: auto;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(4px);
	-webkit-backdrop-filter: blur(4px);
	border: none;
	z-index: 101;
	display: flex;
	align-items: center;
}
</style>