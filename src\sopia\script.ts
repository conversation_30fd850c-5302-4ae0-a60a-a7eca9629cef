/*
 * script.ts
 * Created on Wed Oct 14 2020
 *
 * Copyright (c) Tree Some. Licensed under the MIT License.
 */
const fs = window.require('fs');
const path = window.require('path');
import { BundleInfo } from '@/interface/bundle';
import './context';

import logger from '@/plugins/logger';
const { ipcRenderer } = window.require('electron');

// 파일 접근 캐싱을 위한 클래스
class ConfigCache {
	private cache: Map<string, { data: any, timestamp: number }> = new Map();
	private readonly TTL = 5000; // 5초 캐시 유효시간

	get(key: string, loader: () => any): any {
		const now = Date.now();
		const cached = this.cache.get(key);
		
		if (cached && (now - cached.timestamp < this.TTL)) {
			return cached.data;
		}
		
		// 캐시 없거나 만료됨
		const data = loader();
		this.cache.set(key, { data, timestamp: now });
		return data;
	}
	
	invalidate(key: string): void {
		this.cache.delete(key);
	}
	
	clear(): void {
		this.cache.clear();
	}
}

export class Script {

	public boxs: any[] = [];
	private loadedPaths: Set<string> = new Set(); // 이미 로드된 경로 추적
	private configCache = new ConfigCache(); // 설정 캐싱

	constructor() {
		this.add = this.add.bind(this);
		this.abort = this.abort.bind(this);
		this.clear = this.clear.bind(this);
		this.run = this.run.bind(this);
		this.reload = this.reload.bind(this);
		
		// 전역 설정 캐시 접근 함수 제공
		(window as any).getCachedConfig = (configPath: string, key: string) => {
			const cacheKey = `${configPath}:${key}`;
			return this.configCache.get(cacheKey, () => {
				// 원래 설정 파일 로드 로직
				try {
					const cfg = (window as any).appCfg.get(key);
					return cfg;
				} catch (e) {
					logger.err('script', `설정 로드 오류: ${configPath} - ${key}`, e);
					return null;
				}
			});
		};
	}

	public async add(folder: string) {
		// 이미 로드된 경로인지 확인
		const normalizedPath = path.normalize(folder);
		if (this.loadedPaths.has(normalizedPath)) {
			logger.debug('script', `번들이 이미 로드됨: ${normalizedPath}`);
			return;
		}
		
		logger.debug('script', `번들 로드 시도: ${normalizedPath}`);
		
		let pkg = {} as BundleInfo;
		let index = '';
		try {
			const packageTarget = path.join(folder, 'package.json');
			if (fs.existsSync(packageTarget)) {
				pkg = JSON.parse(fs.readFileSync(packageTarget, 'utf-8')) as BundleInfo;
				index = path.join(folder, pkg.main ?? 'index.js');
				// https://github.com/sopia-bot/sopia-v3/issues/5
				try {
					index = window.require.resolve(index);
				} catch (e) {
					logger.err('script', `모듈 경로 해결 오류: ${index}`, e);
					return;
				}
			} else {
				try {
					index = window.require.resolve(path.join(folder, 'index.js'));
				} catch (e) {
					logger.err('script', `index.js 모듈 경로 해결 오류`, e);
					return;
				}
			}
		} catch (e) {
			logger.err('script', `번들 로드 오류 (package.json 파싱):`, pkg, `index: [${index}]`, e);
			return;
		}

		if (fs.existsSync(index)) {
			const name = path.basename(folder);
			const context = (window as any)['bctx'].new(name);
			try {
				// 모듈 로드 시도
				logger.debug('script', `모듈 로드 시도: ${index}`);
				const module = window.require(index);
				let stpTargetFile = '';
				
				if (pkg['stp']) {
					if (pkg['stp']['domain'] && pkg['stp']['file']) {
						stpTargetFile = pkg['stp']['file'];
						if (!fs.existsSync(stpTargetFile)) {
							stpTargetFile = path.join(folder, pkg['stp']['file']);
						}
						try {
							stpTargetFile = window.require.resolve(stpTargetFile);
							logger.debug('sopia', `Bundle ${name} is using stp protocol ${stpTargetFile}`);
							ipcRenderer.invoke('stp:regist', pkg['stp']['domain'], stpTargetFile, folder);
						} catch (e) {
							logger.err('script', `STP 파일 경로 해결 오류: ${stpTargetFile}`, e);
						}
					}
				}
				
				const box = {
					name,
					file: index,
					stpFile: stpTargetFile,
					dir: folder,
					module,
					context,
				};
				
				this.boxs.push(box);
				this.loadedPaths.add(normalizedPath); // 로드된 경로 기록
				logger.info('script', `번들 로드 성공: ${name}`);
			} catch (e) {
				logger.err('sopia', `스크립트 파일 로드 실패 [${index}]`, e);
				(window as any)['bctx'].destroy(name);
			}
		} else {
			logger.err('sopia', `스크립트 파일을 찾을 수 없음 [${index}].`);
		}
	}

	public abort(name: string) {
		const idx = this.boxs.findIndex((b: any) => b.name === name);
		const box = this.boxs[idx];
		if (box) {
			const module = box.module;
			if (module && typeof module.onAbort === 'function') {
				try {
					module.onAbort();
				} catch (e) {
					logger.err('script', `onAbort 호출 중 오류 발생: ${name}`, e);
				}
			}
			
			// 경로 추적에서 제거
			this.loadedPaths.delete(path.normalize(box.dir));
			
			(window as any)['bctx'].destroy(name);
			logger.info('sopia', '모듈 캐시 제거', box.file);
			delete window.require.cache[box.file];
			this.boxs.splice(idx, 1);
		}
	}

	public clear() {
		logger.debug('script', `모든 스크립트 클리어 (${this.boxs.length}개)`);
		let idx = 0;
		if (Array.isArray(this.boxs)) {
			while (this.boxs.length) {
				const module = this.boxs[0];
				this.abort.call(this, module.name);
				if (idx++ > 10000) {
					logger.err('script', '무한 루프 방지: 스크립트 클리어 중단');
					break;
				}
			}
		}
		this.boxs = [];
		this.loadedPaths.clear(); // 모든 경로 추적 초기화
		this.configCache.clear(); // 설정 캐시 초기화
	}

	public run(event: any, sock: any) {
		if (Array.isArray(this.boxs) && this.boxs.length > 0) {
			for (const { module, name } of this.boxs) {
				if (typeof module[event.event] === 'function') {
					try {
						module[event.event](event, sock);
					} catch (err) {
						logger.err('script', `이벤트 실행 중 오류 발생 (${name}): ${event.event}`, err);
					}
				}
			}
		}
	}

	public reload(name: string) {
		logger.debug('script', `스크립트 리로드 요청: ${name}`);
		const box = this.boxs.find((b: any) => b.name === name);
		if (box) {
			this.abort(name);
			this.add(box.dir);
		} else {
			logger.warn('script', `리로드할 스크립트를 찾을 수 없음: ${name}`);
		}
	}

}

export default new Script();
