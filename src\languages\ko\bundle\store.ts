export default {
	'title': 'Store',
	'subtitle': '소피아 번들 훔쳐가기',
	'description': '소피아 번들 훔치기. 절대 말하면 안됨',
	'description-2': '',
	'upload': 'Bundle Upload',
	'manager': '설치되있는거 훔쳐오기',
	'steal-confirm': '이 번들을 훔쳐오시겠습니까?',
	'steal-success': '번들을 성공적으로 훔쳐왔습니다!',
	'steal-error': '번들을 훔쳐오는데 실패했습니다.',
	'error': {
		'create-error': '번들을 압축하는 데 실패하였습니다.',
		'exists_bundle_name': '서버에 이미 등록된 번들과 중복됩니다.',
		'duplicated_version': '서버에 이미 같은 버전의 번들이 존재합니다.',
		'must-be': 'package.json에 필수 항목이 들어가야 합니다. ($0)',
		'page-version': '구버전 형식의 번들은 업로드할 수 없습니다.',
	},
	'upload-success': '번들이 성공적으로 등록되었습니다.',
	'remove-bundle': '번들 삭제',
	'remove-bundle-desc': '$0 번들을 삭제하시겠습니까?<br>* 방송에서 사용하고 있는 경우 오류가 발생할 수 있습니다.',
	'remove-bundle-success': '$0 번들이 삭제되었습니다.',
	'install-scucess': '$0 번들이 설치되었습니다.',
	'move-bundle-page': 'Bundle Settings',
	'local-bundle': 'Local Bundle List',
	'search': 'Bundle Search...',
	'show-release-note': 'Show Release Note',
	'add-local-bundle': 'Add',
};
