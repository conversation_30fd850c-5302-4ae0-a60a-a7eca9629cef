<template>
	<v-dialog v-model="dialog" max-width="700" persistent>
		<v-card class="read-card" elevation="24">
			<!-- 헤더 섹션 -->
			<div class="read-header">
				<div class="header-content">
					<div class="header-icon">
						<v-icon size="32" color="white">mdi-email-open</v-icon>
					</div>
					<div class="header-text">
						<h2 class="header-title">편지 읽기</h2>
						<p class="header-subtitle">받은 메시지를 확인하세요</p>
					</div>
				</div>
				<v-btn 
					icon 
					large 
					color="white" 
					@click="close" 
					class="close-button"
					elevation="2"
				>
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</div>

			<v-divider class="divider-gradient"></v-divider>

			<!-- 메인 콘텐츠 -->
			<v-card-text class="main-content">
				<div v-if="message" class="letter-container">
					<!-- 편지 헤더 정보 -->
					<v-card class="letter-header-card" elevation="8">
						<v-card-text class="letter-header-content">
							<div class="sender-info">
								<v-avatar size="64" class="sender-avatar">
									<v-icon size="32" color="white">mdi-account</v-icon>
								</v-avatar>
								<div class="sender-details">
									<h3 class="sender-name">{{ message.sender_name }}</h3>
									<p class="sender-email">{{ message.sender_email || '익명 사용자' }}</p>
									<div class="message-meta">
										<v-chip small color="primary" outlined class="meta-chip">
											<v-icon left small>mdi-calendar</v-icon>
											{{ formatDate(message.created_at) }}
										</v-chip>
										<v-chip 
											small 
											:color="message.read ? 'success' : 'warning'" 
											text-color="white"
											class="meta-chip ml-2"
										>
											<v-icon left small>
												{{ message.read ? 'mdi-email-open' : 'mdi-email' }}
											</v-icon>
											{{ message.read ? '읽음' : '새 메시지' }}
										</v-chip>
									</div>
								</div>
							</div>
						</v-card-text>
					</v-card>

					<!-- 편지 내용 -->
					<v-card class="letter-content-card" elevation="12">
						<div class="letter-paper">
							<div class="paper-header">
								<div class="paper-decoration"></div>
								<h2 class="letter-subject">{{ message.subject }}</h2>
								<div class="paper-decoration"></div>
							</div>
							
							<div class="letter-body">
								<div class="letter-content">{{ message.content }}</div>
							</div>
						</div>
					</v-card>

					<!-- 액션 버튼 -->
					<div class="action-section">
						<v-btn 
							large 
							color="primary" 
							@click="close" 
							class="action-btn"
							elevation="2"
						>
							<v-icon left>mdi-check</v-icon>
							확인
						</v-btn>
					</div>
				</div>

				<!-- 메시지가 없을 때 -->
				<div v-else class="no-message">
					<v-icon size="120" color="grey lighten-2">mdi-email-off-outline</v-icon>
					<h3>메시지를 불러올 수 없습니다</h3>
					<p>메시지 정보가 없거나 손상되었습니다.</p>
				</div>
			</v-card-text>
		</v-card>
	</v-dialog>
</template>

<script lang="ts">
import { Component, Prop, Mixins, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { MailboxMessage, mailboxService } from '@/plugins/mailbox-service';

@Component
export default class MessageRead extends Mixins(GlobalMixins) {
	@Prop({ type: Boolean, default: false }) value!: boolean;
	@Prop({ type: Object, default: null }) message!: MailboxMessage | null;

	public get dialog() {
		return this.value;
	}

	public set dialog(value: boolean) {
		this.$emit('input', value);
	}

	@Watch('dialog')
	public onDialogOpen(newVal: boolean) {
		if (newVal && this.message && !this.message.read) {
			this.markAsRead();
		}
	}

	public close() {
		this.dialog = false;
		this.$emit('message-read', this.message);
	}

	public async markAsRead() {
		if (!this.message || this.message.read) return;

		try {
			await mailboxService.markAsRead(this.message.id);
			if (this.message) {
				this.message.read = true;
			}
		} catch (error) {
			console.error('메시지 읽음 처리 실패:', error);
		}
	}

	public formatDate(dateString: string): string {
		try {
			const date = new Date(dateString);
			const now = new Date();
			const diffTime = Math.abs(now.getTime() - date.getTime());
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			
			if (diffDays === 1) {
				return '오늘';
			} else if (diffDays === 2) {
				return '어제';
			} else if (diffDays <= 7) {
				return `${diffDays - 1}일 전`;
			} else {
				return date.toLocaleDateString('ko-KR', {
					year: 'numeric',
					month: 'long',
					day: 'numeric'
				});
			}
		} catch (error) {
			return dateString;
		}
	}
}
</script>

<style scoped>
.read-card {
	border-radius: 20px !important;
	overflow: hidden;
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* 헤더 스타일 */
.read-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 24px 32px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	overflow: hidden;
}

.read-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="mail" width="30" height="30" patternUnits="userSpaceOnUse"><rect x="5" y="10" width="20" height="15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/><path d="M5,10 L15,18 L25,10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23mail)"/></svg>');
	opacity: 0.3;
}

.header-content {
	display: flex;
	align-items: center;
	z-index: 1;
	position: relative;
}

.header-icon {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16px;
	padding: 16px;
	margin-right: 20px;
	backdrop-filter: blur(10px);
}

.header-text {
	color: white;
}

.header-title {
	font-size: 28px;
	font-weight: 700;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
	font-size: 16px;
	margin: 4px 0 0 0;
	opacity: 0.9;
	font-weight: 400;
}

.close-button {
	z-index: 1;
	position: relative;
	background: rgba(255, 255, 255, 0.2) !important;
	backdrop-filter: blur(10px);
}

.divider-gradient {
	height: 4px !important;
	background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
	border: none !important;
}

/* 메인 콘텐츠 */
.main-content {
	padding: 32px !important;
	background: #f8fafc;
}

.letter-container {
	max-width: 600px;
	margin: 0 auto;
}

/* 편지 헤더 카드 */
.letter-header-card {
	border-radius: 20px !important;
	background: white;
	margin-bottom: 24px;
	border: 2px solid #e2e8f0;
}

.letter-header-content {
	padding: 24px !important;
}

.sender-info {
	display: flex;
	align-items: center;
}

.sender-avatar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
	margin-right: 20px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sender-details {
	flex: 1;
}

.sender-name {
	font-size: 24px;
	font-weight: 700;
	color: #2d3748;
	margin: 0 0 4px 0;
}

.sender-email {
	font-size: 14px;
	color: #718096;
	margin: 0 0 12px 0;
}

.message-meta {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.meta-chip {
	font-size: 12px !important;
	height: 28px !important;
}

/* 편지 내용 카드 */
.letter-content-card {
	border-radius: 20px !important;
	background: white;
	margin-bottom: 24px;
	overflow: hidden;
}

.letter-paper {
	background: white;
	padding: 40px;
	position: relative;
}

.paper-header {
	text-align: center;
	margin-bottom: 32px;
	position: relative;
}

.paper-decoration {
	width: 60px;
	height: 2px;
	background: linear-gradient(90deg, #667eea, #764ba2);
	margin: 0 auto;
	border-radius: 1px;
}

.letter-subject {
	font-size: 28px;
	font-weight: 700;
	color: #2d3748;
	margin: 16px 0;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.letter-body {
	line-height: 1.8;
	color: #4a5568;
	font-size: 16px;
}

.letter-content {
	white-space: pre-wrap;
	word-break: break-word;
	text-align: left;
}

/* 액션 섹션 */
.action-section {
	text-align: center;
}

.action-btn {
	border-radius: 12px !important;
	text-transform: none !important;
	font-weight: 600;
	padding: 0 32px !important;
	height: 48px !important;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.action-btn:hover {
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6) !important;
	transform: translateY(-2px);
}

/* 메시지 없음 상태 */
.no-message {
	text-align: center;
	padding: 64px 32px;
	color: #718096;
}

.no-message h3 {
	font-size: 24px;
	margin: 24px 0 12px 0;
	color: #2d3748;
}

.no-message p {
	font-size: 16px;
	margin: 0;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
	.read-header {
		padding: 20px;
		flex-direction: column;
		text-align: center;
	}

	.header-content {
		margin-bottom: 16px;
	}

	.main-content {
		padding: 20px !important;
	}

	.letter-paper {
		padding: 24px;
	}

	.sender-info {
		flex-direction: column;
		text-align: center;
	}

	.sender-avatar {
		margin-right: 0;
		margin-bottom: 16px;
	}

	.letter-subject {
		font-size: 24px;
	}

	.message-meta {
		justify-content: center;
	}
}

/* 애니메이션 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.letter-header-card {
	animation: fadeInUp 0.3s ease-out;
}

.letter-content-card {
	animation: fadeInUp 0.4s ease-out 0.1s both;
}

.action-section {
	animation: fadeInUp 0.5s ease-out 0.2s both;
}

/* 종이 효과 애니메이션 */
@keyframes paperFloat {
	0%, 100% {
		transform: translateY(0) rotate(0deg);
	}
	25% {
		transform: translateY(-2px) rotate(0.5deg);
	}
	75% {
		transform: translateY(2px) rotate(-0.5deg);
	}
}

.letter-content-card:hover .letter-paper {
	animation: paperFloat 3s ease-in-out infinite;
}
</style> 