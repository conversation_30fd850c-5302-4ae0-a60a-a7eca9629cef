<template>
	<div style="display: contents; position: relative;" @click.stop="() => {}">
		<v-text-field
			ref="searchBox"
			v-model="searchText"
			dense
			outlined
			rounded
			class="no-drag search-input"
			color="primary"
			:label="$t('app.title.search')"
			append-icon="mdi-magnify"
			hide-details
			style="max-width: 300px;"
			@keydown="searchKeyDown"
			@click:append="searchContent"></v-text-field>
			
		<v-card
			:elevation="3"
			class="search-preview-card"
			style="position: fixed; z-index: 100;"
			:style="boxStyle">
			
			<div v-if="users.length > 0" class="pa-2">
				<div class="d-flex flex-wrap justify-center">
					<div 
						v-for="user in users" 
						:key="user.tag" 
						class="search-preview-user text-center pa-2 mx-1"
						@click="goToUserProfile(user)">
						<div v-if="user.current_live_id" class="live-user-container">
							<div class="live-border">
								<v-avatar size="48" class="mb-1">
									<v-img :src="user.profile_url"></v-img>
								</v-avatar>
							</div>
							<div class="live-badge">LIVE</div>
						</div>
						<v-avatar v-else size="48" class="mb-1 elevation-2">
							<v-img :src="user.profile_url"></v-img>
						</v-avatar>
						
						<div class="user-nickname text-truncate">
							{{ user.nickname }}
						</div>
					</div>
				</div>
				
				<div class="d-flex justify-end mt-2">
					<v-btn
						small
						text
						color="primary"
						@click="searchContent">
						{{ $t('more-results') || '더 보기' }}
					</v-btn>
				</div>
			</div>
			
			<div v-else class="pa-4 text-center grey--text">
				<v-icon>mdi-magnify</v-icon>
				<div>{{ $t('no-results') || '검색 결과가 없습니다' }}</div>
			</div>
		</v-card>
	</div>
</template>
<script lang="ts">
import { Component, Mixins, Prop, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { User } from '@sopia-bot/core';

@Component
export default class SearchBox extends Mixins(GlobalMixins) {
	@Prop({ type: String, default: '' }) value!: string;

	public searchText: string = '';
	public boxStyle = {
		left: '0',
		top: '0',
		width: '0',
		display: 'none',
	};
	public increment = 0;
	public users: User[] = [];

	@Watch('value')
	onValueChange(newValue: string) {
		this.searchText = newValue;
	}

	public mounted() {
		this.searchText = this.value;
		this.outerClick = this.outerClick.bind(this);
		this.$nextTick(() => {
			window.addEventListener('click', this.outerClick);
		});
	}

	public beforeUnmount() {
		window.removeEventListener('click', this.outerClick);
	}

	public goToUserProfile(user: User) {
		if (user.current_live_id) {
			this.$evt.$emit('live-join', user.current_live_id);
		} else {
			this.$assign(`/user/${user.id}/`);
		}
		this.outerClick();
	}

	public async searchContent() {
		if (!this.searchText.trim()) return;
		
		const text = this.searchText.trim();
		this.$emit('search', text);
		
		// 부모 컴포넌트가 검색 이벤트를 처리하지 않는 경우 직접 검색 페이지로 이동
		if (this.$listeners && !this.$listeners.search) {
			const encodedText = encodeURI(text);
			this.$assign(`/search/user/${encodedText}`);
		}
		
		this.outerClick();
	}

	public async searchKeyDown(evt: KeyboardEvent) {
		if (evt.key === 'Enter') {
			// 엔터키를 누르면 검색 실행
			this.searchContent();
			return;
		}
		
		// 검색어가 있을 때만 미리보기 위치 및 내용 갱신
		if (this.searchText.trim().length > 0) {
			// 검색어 입력 위치에 맞게 미리보기 위치 조정
			this.boxStyle.left = Math.floor(this.getCardLeft()) + 'px';
			this.boxStyle.top = Math.floor(this.getCardTop()) + 'px';
			this.boxStyle.width = Math.floor(this.getBoxRect().width) + 'px';
			
			// 디바운스 처리로 연속 API 호출 방지
			this.increment += 1;
			setTimeout(() => {
				this.increment -= 1;
				if (this.increment <= 0) {
					this.search();
					this.increment = 0;
				}
			}, 500);
		} else {
			// 검색어가 없으면 미리보기 숨김
			this.boxStyle.display = 'none';
		}
	}

	public async search() {
		const req = await this.$sopia.api.search.user({
			params: {
				keyword: this.searchText,
			},
		});
		this.boxStyle.display = 'block';
		this.users = req.res.results.splice(0, 5);
	}

	public getBoxRect(): DOMRect {
		const box = (this.$refs.searchBox as Vue)?.$el as HTMLElement;
		return box?.getBoundingClientRect();
	}

	public getCardLeft() {
		return this.getBoxRect()?.left || 0;
	}

	public getCardTop() {
		return (this.getBoxRect()?.top + this.getBoxRect()?.height) + 10 || 0;
	}

	public outerClick() {
		this.boxStyle.display = 'none';
	}

}
</script>
<style scoped>
.search-preview-card {
	border-radius: 12px;
	max-width: 360px;
	overflow: hidden;
}

.search-preview-user {
	width: 70px;
	cursor: pointer;
	transition: all 0.2s;
	border-radius: 8px;
}

.search-preview-user:hover {
	background-color: rgba(0, 0, 0, 0.05);
}

.user-nickname {
	font-size: 0.75rem;
	max-width: 100%;
}

/* 방송 중 사용자 스타일 */
.live-user-container {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.live-border {
	padding: 2px;
	border-radius: 50%;
	border: 2px solid #f44336;
	background-color: transparent;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.live-badge {
	position: relative;
	top: -5px;
	background-color: #f44336;
	color: white;
	font-size: 10px;
	font-weight: bold;
	padding: 1px 5px;
	border-radius: 8px;
	letter-spacing: 0.5px;
}

/* 검색창 스타일 */
.search-input {
  transition: all 0.3s;
}

.search-input:hover, .search-input:focus {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}
</style>
<style>
/* 기존 search-rounded 클래스는 더 이상 필요 없으므로 제거 */
/* 대신 v-text-field의 글로벌 스타일 추가 */
.v-text-field.v-text-field--outlined.v-text-field--rounded >>> .v-input__slot {
  border-radius: 28px !important;
}

.v-text-field.v-text-field--outlined.v-text-field--rounded >>> .v-input__control {
  min-height: 40px;
}

.v-text-field.v-text-field--outlined.v-text-field--rounded >>> .v-label {
  transition: all 0.2s ease-in-out;
}

.v-text-field.v-text-field--outlined.v-text-field--rounded >>> .v-label--active {
  transform: translateY(-16px) scale(0.75);
}
</style>
