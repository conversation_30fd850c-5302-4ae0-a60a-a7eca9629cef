<template>
  <div class="music-tab-container">
    <component v-if="dynamicComponent" :is="dynamicComponent"></component>
    <v-alert v-else-if="error" type="error" outlined>
      {{ error }}
    </v-alert>
    <v-skeleton-loader v-else type="article" class="mx-auto"></v-skeleton-loader>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import * as VuetifyComponents from 'vuetify/lib/components';

const fs = window.require('fs');
const vm = window.require('vm');
const path = window.require('path');
const os = window.require('os');

@Component
export default class MusicTab extends Mixins(GlobalMixins) {
  dynamicComponent: any = null;
  error: string | null = null;
  
  mounted() {
    this.loadExternalVueFile();
  }
  
  getInnerString(txt: string, tag: string): string | null {
    const regx = new RegExp(`<${tag}>((?:.|\r|\n)*)?</${tag}>`);
    const m = txt.match(regx);
    if (m) {
      return m[1];
    }
    return null;
  }
  
  async loadExternalVueFile() {
    try {
      // 사용자 홈 디렉토리 가져오기
      const homedir = os.homedir();
      const filePath = path.join(homedir, 'AppData', 'Roaming', 'tamm-v1', 'bundles', 'request-song', 'page.vue');
      
      console.log('로드할 파일 경로:', filePath);
      
      // 파일 존재 확인
      if (!fs.existsSync(filePath)) {
        this.error = `파일을 찾을 수 없습니다: ${filePath}`;
        console.error(this.error);
        return;
      }
      
      // Vue 파일 읽기
      const vueFileContent = fs.readFileSync(filePath, 'utf8');
      
      // template 및 script 부분 추출
      const template = this.getInnerString(vueFileContent, 'template');
      let script = this.getInnerString(vueFileContent, 'script');
      const style = this.getInnerString(vueFileContent, 'style');
      
      if (!template || !script) {
        this.error = 'Vue 파일의 template 또는 script 태그를 찾을 수 없습니다.';
        console.error(this.error);
        return;
      }
      
      // script 내용 수정: export default { ... } -> module = { ... }
      script = script.replace(/export\s+default\s+{/, 'module = {');
      
      // VM 컨텍스트 생성 및 스크립트 실행
      const vmScript = new vm.Script(script);
      const context: any = {};
      context.module = {};
      context.window = window;
      context.__dirname = path.dirname(filePath);
      context.console = console;
      
      // 스크립트 실행
      vmScript.runInNewContext(context);
      
      // 컴포넌트 생성
      const component: any = {
        template,
        ...context.module,
        mixins: [Mixins(GlobalMixins)],
      };
      
      // 필요한 메서드 및 컴포넌트 추가
      if (!component.methods) {
        component.methods = {};
      }
      
      // Vuetify 컴포넌트 추가
      component.components = { ...VuetifyComponents };
      
      // 스타일 추가 (있는 경우)
      if (style) {
        const styleEl = document.createElement('style');
        styleEl.textContent = style;
        styleEl.setAttribute('data-music-tab-style', '');
        document.head.appendChild(styleEl);
      }
      
      // 컴포넌트 설정 완료
      this.dynamicComponent = component;
      
      console.log('Vue 컴포넌트가 성공적으로 로드되었습니다.');
    } catch (error: any) {
      console.error('Vue 컴포넌트 로드 오류:', error);
      this.error = `컴포넌트 로드 중 오류가 발생했습니다: ${error.message}`;
    }
  }
  
  // 컴포넌트가 제거될 때 추가한 스타일 시트도 제거
  beforeDestroy() {
    const styleEl = document.querySelector('style[data-music-tab-style]');
    if (styleEl) {
      document.head.removeChild(styleEl);
    }
  }
}
</script>

<style scoped>
.music-tab-container {
  padding: 16px;
  height: calc(100vh - 64px);
  overflow-y: auto;
}
</style> 