// 스푼캐스트 페이지에서 사용자 정보 추출
console.log('🔍 Tamm 스푼 연동 Content Script 로드됨');

// 확장프로그램 설치 마커 추가
const marker = document.createElement('div');
marker.setAttribute('data-tamm-extension', 'installed');
marker.style.display = 'none';
document.body.appendChild(marker);

// 사용자 정보 추출 함수
function getUserInfo() {
    try {
        console.log('👤 사용자 정보 추출 시작');
        
        // localStorage에서 사용자 정보 추출
        const userDataStr = localStorage.getItem('user');
        const tokenStr = localStorage.getItem('token');
        
        if (!userDataStr || !tokenStr) {
            console.log('❌ localStorage에 사용자 정보 또는 토큰 없음');
            return { success: false, error: 'Not logged in' };
        }
        
        const userData = JSON.parse(userDataStr);
        const tokenData = JSON.parse(tokenStr);
        
        // 원본 데이터도 함께 포함 (tamm-v1 호환성)
        const userInfo = {
            id: userData.id,
            nickname: userData.nickname,
            profile_url: userData.profile_url,
            tag: userData.tag,
            token: tokenData.access_token,
            refresh_token: tokenData.refresh_token,
            originalData: userData // 전체 사용자 데이터를 originalData로 저장
        };
        
        console.log('✅ 사용자 정보 추출 성공:', {
            id: userInfo.id,
            nickname: userInfo.nickname,
            hasToken: !!userInfo.token,
            hasOriginalData: !!userInfo.originalData
        });
        
        return {
            success: true,
            data: userInfo
        };
    } catch (error) {
        console.error('❌ 사용자 정보 추출 실패:', error);
        return { success: false, error: error.message };
    }
}

// 메시지 리스너
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📬 메시지 수신:', request);
    
    if (request.method === 'getUserInfo') {
        const userInfo = getUserInfo();
        console.log('📤 사용자 정보 응답:', userInfo);
        sendResponse(userInfo);
        return true; // 비동기 응답
    }
    
    if (request.method === 'sendToTamm') {
        console.log('📡 TAMM으로 토큰 데이터 전송:', request.tokenData);
        
        // TAMM 웹앱에 메시지 전송 시도
        window.postMessage({
            type: 'SPOON_AUTH_SUCCESS',
            tokenData: request.tokenData
        }, '*');
        
        sendResponse({ success: true });
        return true;
    }
});

// 확장프로그램 감지 메시지 리스너
window.addEventListener('message', (event) => {
    if (event.data.type === 'CHECK_EXTENSION') {
        console.log('🔍 확장프로그램 감지 요청 수신');
        window.postMessage({ type: 'EXTENSION_RESPONSE' }, '*');
    }
});

console.log('✅ Content Script 초기화 완료'); 