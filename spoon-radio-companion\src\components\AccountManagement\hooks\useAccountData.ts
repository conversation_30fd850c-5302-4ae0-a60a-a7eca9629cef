import { useState, useEffect, useCallback } from 'react';
import { db } from '../../../firebaseConfig';
import { collection, getDocs, deleteDoc, doc, query, where } from 'firebase/firestore';
import { User, SpoonTokenData, UnifiedData } from '../types';

// 사용자 IP 가져오기
const getUserIP = async (): Promise<string> => {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.error('IP 조회 실패:', error);
    return 'unknown';
  }
};

export const useAccountData = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [spoonTokens, setSpoonTokens] = useState<SpoonTokenData[]>([]);
  const [loading, setLoading] = useState(true);
  const [tokensLoading, setTokensLoading] = useState(true);
  const [currentUserIP, setCurrentUserIP] = useState<string>('');

  // 스푼 토큰 데이터 로드 - useCallback으로 메모이제이션
  const loadSpoonTokens = useCallback(async () => {
    setTokensLoading(true);
    try {
      const tokensCollection = collection(db, 'spoon_tokens');
      const tokensSnapshot = await getDocs(tokensCollection);
      
      const tokensList: SpoonTokenData[] = [];
      tokensSnapshot.forEach((doc) => {
        const data = doc.data();
        const tokenData = {
          id: doc.id, // IP 주소
          ...data
        } as SpoonTokenData;
        
        // originalData 파싱 처리 (REST API 형식 지원)
        if (data.originalData) {
          try {
            if (typeof data.originalData === 'string') {
              tokenData.originalData = JSON.parse(data.originalData);
            } else if (data.originalData.data && typeof data.originalData.data === 'string') {
              tokenData.originalData = JSON.parse(data.originalData.data);
            } else if (typeof data.originalData === 'object') {
              tokenData.originalData = data.originalData;
            }
          } catch (parseError) {
            console.warn(`⚠️ 토큰 ${doc.id} originalData 파싱 실패:`, parseError);
            tokenData.originalData = null;
          }
        }
        
        tokensList.push(tokenData);
      });

      tokensList.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
      setSpoonTokens(tokensList);
      
      console.log('✅ 스푼 토큰 데이터 로드 성공:', {
        총토큰수: tokensList.length,
        현재IP: currentUserIP
      });
    } catch (error) {
      console.error('❌ 스푼 토큰 데이터 로드 실패:', error);
      setSpoonTokens([]);
    } finally {
      setTokensLoading(false);
    }
  }, [currentUserIP]);

  // 사용자 데이터 로드 - useCallback으로 메모이제이션
  const loadUsers = useCallback(async () => {
    try {
      // 현재 사용자 IP 가져오기
      const currentIP = await getUserIP();
      setCurrentUserIP(currentIP);

      // Firebase Firestore에서 승인된 사용자 가져오기
      const usersCollection = collection(db, 'users');
      const approvedQuery = query(usersCollection, where('isApproved', '==', true));
      const querySnapshot = await getDocs(approvedQuery);
      
      const firestoreUsers: User[] = [];
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        firestoreUsers.push({
          id: doc.id,
          displayName: userData.name || userData.displayName || '알 수 없음',
          email: userData.email || '',
          password: 'user1234', // 보안상 실제 비밀번호는 표시하지 않음
          joinDate: userData.createdAt ? new Date(userData.createdAt).toLocaleDateString() : '2024-01-01',
          lastLogin: userData.lastLogin || '로그인 기록 없음',
          status: userData.status || 'active',
          role: userData.role || 'user',
          currentIP: userData.currentIP || currentIP
        });
      });
      
      // 관리자 계정 추가 (하드코딩)
      const adminUser: User = {
        id: 'admin-tamm',
        displayName: 'TAMM 관리자',
        email: '<EMAIL>',
        password: 'tamm1234',
        joinDate: '2024-01-01',
        lastLogin: '방금 전',
        status: 'active',
        role: 'admin',
        currentIP: 'admin' // 관리자는 특별한 IP
      };

      const allUsers = [adminUser, ...firestoreUsers];
      setUsers(allUsers);
      
      console.log('✅ 계정관리 사용자 데이터 로드 성공:', {
        총사용자수: allUsers.length,
        승인된사용자수: firestoreUsers.length,
        관리자: 1
      });
    } catch (error) {
      console.error('❌ 사용자 로딩 실패:', error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 사용자 삭제
  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('정말로 이 계정을 삭제하시겠습니까?')) {
      try {
        if (userId === 'admin-tamm') {
          alert('관리자 계정은 삭제할 수 없습니다.');
          return;
        }

        // Firebase Firestore에서 사용자 삭제
        const userRef = doc(db, 'users', userId);
        await deleteDoc(userRef);

        // 로컬 상태 업데이트
        setUsers(prev => prev.filter(user => user.id !== userId));
        
        console.log('✅ 사용자 삭제 성공:', userId);
        alert('계정이 삭제되었습니다.');
      } catch (error) {
        console.error('❌ 계정 삭제 실패:', error);
        alert('계정 삭제 중 오류가 발생했습니다.');
      }
    }
  };

  // 토큰 삭제
  const handleDeleteToken = async (ip: string) => {
    if (window.confirm(`IP ${ip}의 스푼 토큰을 삭제하시겠습니까?`)) {
      try {
        console.log('🗑️ 토큰 삭제 시작:', ip);
        
        const tokenRef = doc(db, 'spoon_tokens', ip);
        await deleteDoc(tokenRef);
        
        setSpoonTokens(prev => prev.filter(token => token.id !== ip));
        
        console.log('✅ 토큰 삭제 성공:', ip);
        alert('토큰이 삭제되었습니다.');
      } catch (error) {
        console.error('❌ 토큰 삭제 실패:', error);
        alert('토큰 삭제 중 오류가 발생했습니다.');
      }
    }
  };

  // 토큰 연결 해제 (더 이상 필요하지 않음 - IP 기반 매칭이므로)
  const handleUnlinkToken = async (ip: string) => {
    alert('IP 기반 매칭을 사용하므로 연결 해제 기능이 필요하지 않습니다.');
  };

  // 통합 데이터 생성 (올바른 연결 로직)
  const createUnifiedData = (): UnifiedData[] => {
    const unifiedData: UnifiedData[] = [];

    // 모든 사용자 추가 (linkedUserId 기반으로 토큰 찾기)
    users.forEach(user => {
      const linkedToken = spoonTokens.find(token => token.linkedUserId === user.id);
      
      unifiedData.push({
        id: `user_${user.id}`,
        type: 'user',
        userAccount: user,
        spoonToken: linkedToken,
        connectionStatus: linkedToken ? 'linked' : 'unlinked',
        searchableText: `${user.displayName} ${user.email} ${user.role} ${linkedToken?.user?.nickname || ''} ${user.currentIP}`.toLowerCase(),
        profileUrl: linkedToken?.user?.profile_url || '/default-profile.png',
        displayName: user.displayName || '알 수 없음',
        subtitle: user.email,
        lastActivity: user.lastLogin || '기록 없음'
      });
    });

    // 연결되지 않은 토큰들 추가 (linkedUserId가 없거나 해당 사용자가 존재하지 않는 토큰)
    spoonTokens.forEach(token => {
      // 토큰이 사용자와 연결되어 있고 해당 사용자가 존재하는 경우 건너뛰기
      if (token.linkedUserId && users.find(user => user.id === token.linkedUserId)) {
        return;
      }
      
      unifiedData.push({
        id: `token_${token.id}`,
        type: 'token',
        userAccount: undefined,
        spoonToken: token,
        connectionStatus: 'unlinked',
        searchableText: `${token.user?.nickname || ''} ${token.user?.tag || ''} ${token.id}`.toLowerCase(),
        profileUrl: token.user?.profile_url || '/default-profile.png',
        displayName: token.user?.nickname || '알 수 없음',
        subtitle: `#${token.user?.tag || ''}`,
        lastActivity: token.lastUpdated ? new Date(token.lastUpdated).toLocaleDateString() : '기록 없음'
      });
    });

    return unifiedData;
  };

  // 연결된 사용자 정보 가져오기 (linkedUserId 기반)
  const getLinkedUserInfo = (tokenId: string): User | null => {
    const token = spoonTokens.find(t => t.id === tokenId);
    if (!token || !token.linkedUserId) return null;
    return users.find(user => user.id === token.linkedUserId) || null;
  };

  useEffect(() => {
    const initializeData = async () => {
      await loadUsers();
      await loadSpoonTokens();
    };
    
    setTimeout(() => {
      initializeData();
    }, 500);
  }, [loadUsers, loadSpoonTokens]); // 의존성 배열에 메모이제이션된 함수들 추가

  return {
    users,
    spoonTokens,
    loading,
    tokensLoading,
    currentUserIP,
    handleDeleteUser,
    handleDeleteToken,
    handleUnlinkToken,
    createUnifiedData,
    getLinkedUserInfo,
    loadSpoonTokens
  };
}; 