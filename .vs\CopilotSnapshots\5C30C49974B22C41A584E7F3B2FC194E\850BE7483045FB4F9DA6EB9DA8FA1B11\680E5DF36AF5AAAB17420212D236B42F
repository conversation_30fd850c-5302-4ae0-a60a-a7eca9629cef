﻿import { auth, db } from '../main';
import { onAuthStateChanged, User } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { setPersistence, browserLocalPersistence } from 'firebase/auth';
import store from '../store';

/**
 * Firebase 인증 상태 관찰자 서비스
 * 인증 상태 변경을 감지하고 적절한 처리를 수행합니다.
 */
export class FirebaseAuthObserver {
  private initialized = false;

  /**
   * 인증 상태 관찰자를 초기화합니다.
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    try {
      // 로컬 지속성 설정 (브라우저를 닫아도 로그인 상태 유지)
      await setPersistence(auth, browserLocalPersistence);
      
      // 인증 상태 변경 감지
      onAuthStateChanged(auth, async (user) => {
        await this.handleAuthStateChanged(user);
      });
      
      this.initialized = true;
      console.log('✅ Firebase 인증 관찰자가 초기화되었습니다.');
    } catch (error) {
      console.error('Firebase 인증 관찰자 초기화 오류:', error);
    }
  }

  /**
   * 인증 상태 변경을 처리합니다.
   * @param user Firebase User 객체 또는 null
   */
  private async handleAuthStateChanged(user: User | null): Promise<void> {
    if (user) {
      try {
        // Firestore에서 사용자 데이터 가져오기
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnap = await getDoc(userDocRef);
        
        if (userDocSnap.exists()) {
          const userData = userDocSnap.data();
          
          // 사용자 데이터 및 인증 상태를 Vuex 스토어에 저장
          store.commit('setTammUser', {
            uid: user.uid,
            email: user.email,
            ...userData,
          });
          
          // 사용자가 승인되지 않은 경우 자동 로그아웃
          if (!userData.isApproved) {
            console.log('❌ 승인되지 않은 사용자입니다. 자동 로그아웃합니다.');
            auth.signOut();
          }
        } else {
          console.warn('⚠️ 로그인된 사용자에 대한 Firestore 데이터가 없습니다.');
          store.commit('clearTammUser');
        }
      } catch (error) {
        console.error('사용자 데이터 조회 오류:', error);
        store.commit('clearTammUser');
      }
    } else {
      // 로그아웃 상태
      store.commit('clearTammUser');
    }
  }
}

// 싱글톤 인스턴스로 내보냅니다.
export const firebaseAuthObserver = new FirebaseAuthObserver();