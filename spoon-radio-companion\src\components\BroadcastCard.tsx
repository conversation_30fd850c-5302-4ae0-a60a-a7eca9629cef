import React from 'react';
import styled from 'styled-components';

// 스타일 컴포넌트들
const BroadcastCardContainer = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  /* 모바일 반응형 - 가로 레이아웃 */
  @media (max-width: 768px) {
    display: flex;
    flex-direction: row;
    height: 120px;
    border-radius: 12px;
    gap: 8px;
  }
`;

const ThumbnailContainer = styled.div`
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 12px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    border-radius: 12px;
    margin-bottom: 0;
    margin-left: 8px;
  }
`;

// 데스크톱용 뱃지 컨테이너 (썸네일 위 오버레이)
const ThumbnailBadgeContainer = styled.div`
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  z-index: 2;
  
  /* 모바일에서는 숨김 */
  @media (max-width: 768px) {
    display: none;
  }
`;

// 모바일용 뱃지 컨테이너 (콘텐츠 영역)
const ContentBadgeContainer = styled.div`
  display: none;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 8px;
  
  /* 모바일에서만 표시 */
  @media (max-width: 768px) {
    display: flex;
    gap: 2px;
    margin-bottom: 6px;
  }
`;

const ThumbnailImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ContentContainer = styled.div`
  padding: 0 12px 12px 12px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    flex: 1;
    padding: 8px 12px 8px 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
`;

const BroadcastTitle = styled.h3`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 36px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    font-size: 13px;
    margin: 0 0 4px 0;
    min-height: auto;
    -webkit-line-clamp: 2;
    flex: 1;
  }
`;

const AuthorName = styled.p`
  font-size: 12px;
  color: #666;
  margin: 0 0 8px 0;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    font-size: 11px;
    margin: 0 0 6px 0;
  }
`;

const StatsContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    gap: 8px;
    margin-bottom: 6px;
  }
`;

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #888;
  
  span {
    font-weight: 500;
  }
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    font-size: 10px;
    gap: 3px;
  }
`;

const UserIcon = styled.svg`
  width: 12px;
  height: 12px;
  fill: #888;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    width: 10px;
    height: 10px;
  }
`;

const HeartIcon = styled.svg`
  width: 12px;
  height: 12px;
  fill: #888;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    width: 10px;
    height: 10px;
  }
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    gap: 3px;
  }
`;

const Tag = styled.span`
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    font-size: 9px;
    padding: 1px 4px;
  }
`;

// 뱃지 스타일 컴포넌트들
const VipBadge = styled.div`
  background: linear-gradient(135deg, #c0c0c0 0%, #2c2c2c 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: 700;
  border: 1px solid #ffd700;
  display: flex;
  align-items: center;
  gap: 2px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    padding: 1px 4px;
    font-size: 8px;
    border-radius: 8px;
    gap: 1px;
  }
`;

const VipText = styled.span`
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
`;

const CrownIcon = styled.span`
  font-size: 8px;
  color: #ffd700;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    font-size: 7px;
  }
`;

const TierBadge = styled.div<{ tier: string }>`
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: 600;
  color: white;
  position: relative;
  overflow: hidden;
  background: ${props => {
    switch (props.tier) {
      case 'red-choice': return 'linear-gradient(90deg, #8B0000 0%, #FF6B6B 50%, #8B0000 100%)';
      case 'yellow-choice': return 'linear-gradient(180deg, #FFB366 0%, #FF8C42 50%, #FFB366 100%)';
      case 'orange-choice': return 'linear-gradient(180deg, #FF7F00 0%, #FF4500 50%, #FF7F00 100%)';
      case 'voice': return 'linear-gradient(135deg, #5f27cd 0%, #341f97 100%)';
      default: return 'linear-gradient(135deg, #ff6348 0%, #ff4757 100%)';
    }
  }};
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    padding: 1px 4px;
    font-size: 8px;
    border-radius: 8px;
  }
  
  /* 레드 초이스 전용 빛반사 효과 */
  ${props => props.tier === 'red-choice' && `
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 40%;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
      border-radius: 10px 10px 0 0;
      pointer-events: none;
    }
    
    &::after {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      right: 1px;
      bottom: 1px;
      background: linear-gradient(90deg, #8B0000 0%, #FF6B6B 50%, #8B0000 100%);
      border-radius: 9px;
      z-index: -1;
    }
    
    @media (max-width: 768px) {
      &::before {
        border-radius: 8px 8px 0 0;
      }
      &::after {
        border-radius: 7px;
      }
    }
  `}
  
  /* 옐로우 초이스 전용 빛반사 효과 */
  ${props => props.tier === 'yellow-choice' && `
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 35%;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 70%, transparent 100%);
      border-radius: 10px 10px 0 0;
      pointer-events: none;
    }
    
    @media (max-width: 768px) {
      &::before {
        border-radius: 8px 8px 0 0;
      }
    }
  `}
  
  /* 오렌지 초이스 전용 빛반사 효과 (더 진한 색) */
  ${props => props.tier === 'orange-choice' && `
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 35%;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.08) 70%, transparent 100%);
      border-radius: 10px 10px 0 0;
      pointer-events: none;
    }
    
    @media (max-width: 768px) {
      &::before {
        border-radius: 8px 8px 0 0;
      }
    }
  `}
`;

const ChoiceBadge = styled.div`
  background: linear-gradient(135deg, #ff6348 0%, #ff4757 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    padding: 1px 4px;
    font-size: 8px;
    border-radius: 8px;
  }
`;

const CurationBadge = styled.div`
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    padding: 1px 4px;
    font-size: 8px;
    border-radius: 8px;
  }
`;

const AwardsBadge = styled.div`
  background: linear-gradient(135deg, #FF8C42 0%, #FF69B4 50%, #8A2BE2 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 9px;
  font-weight: 700;
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 1px;
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    padding: 1px 4px;
    font-size: 8px;
    border-radius: 10px;
    gap: 0.5px;
  }
  
  /* 빛반사 효과 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);
    border-radius: 12px 12px 0 0;
    pointer-events: none;
  }
  
  @media (max-width: 768px) {
    &::before {
      border-radius: 10px 10px 0 0;
    }
  }
`;

const SpoonAwardsText = styled.span`
  font-weight: 800;
  color: white;
  font-family: 'Segoe UI', 'Apple SD Gothic Neo', 'Noto Sans KR', sans-serif;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    letter-spacing: 0.3px;
  }
`;

const DiamondIcon = styled.span`
  font-size: 7px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3));
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    font-size: 6px;
  }
`;

const SNBadge = styled.div`
  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  /* 모바일 반응형 */
  @media (max-width: 768px) {
    padding: 1px 4px;
    font-size: 8px;
    border-radius: 8px;
  }
`;

// 인터페이스 정의
interface SpoonBroadcast {
  id: number;
  author: {
    id: number;
    nickname: string;
    profile_url: string;
    is_active: boolean;
    country: string;
    tag: string;
  };
  title: string;
  type: number;
  url_hls: string;
  img_url: string;
  like_count: number;
  member_count: number;
  total_member_count: number;
  is_editors: boolean;
  tier: {
    name: string;
    title: string;
  } | null;
  is_vip: boolean;
  is_adult: boolean;
  created: string;
  engine_name: string;
  live_call: {
    guests: Array<{
      id: number;
      nick_name: string;
      profile_url: string;
      guest_status: number;
    }>;
    version: number;
  } | null;
  is_live_call: boolean;
  categories: string[];
  is_verified: boolean;
  room_token: string | null;
  tags: string[];
  hashtags: Array<{
    id: number;
    name: string;
  }>;
  badge_style_ids: string[];
}

interface BroadcastCardProps {
  broadcast: SpoonBroadcast;
  onSelect: (broadcast: SpoonBroadcast) => void;
}

const BroadcastCard: React.FC<BroadcastCardProps> = ({ broadcast, onSelect }) => {
  const renderBadges = () => {
    const badges = [];
    
    // VIP 뱃지
    if (broadcast.is_vip || broadcast.badge_style_ids.includes('vip')) {
      badges.push(
        <VipBadge key="vip">
          <CrownIcon>♔</CrownIcon>
          <VipText>VIP</VipText>
        </VipBadge>
      );
    }
    
    // Tier 뱃지 (Red, Yellow, Orange Choice) - SA 뱃지보다 먼저
    if (broadcast.tier && broadcast.tier.name) {
      switch (broadcast.tier.name) {
        case 'Red_Choice':
          badges.push(<TierBadge key="tier" tier="red-choice">RED</TierBadge>);
          break;
        case 'Yellow_Choice':
          badges.push(<TierBadge key="tier" tier="yellow-choice">YELLOW</TierBadge>);
          break;
        case 'Orange_Choice':
          badges.push(<TierBadge key="tier" tier="orange-choice">ORANGE</TierBadge>);
          break;
        case 'voice':
          badges.push(<TierBadge key="tier" tier="voice">VOICE</TierBadge>);
          break;
        default:
          console.log('알 수 없는 tier:', broadcast.tier.name);
          break;
      }
    }
    
    // Choice 뱃지 (is_editors가 true이고 위의 tier 조건에 해당하지 않는 경우) - SA 뱃지보다 먼저
    if (broadcast.is_editors && (!broadcast.tier || !broadcast.tier.name)) {
      badges.push(<ChoiceBadge key="choice">CHOICE</ChoiceBadge>);
    }
    
    // Awards 뱃지 (Spoon Awards) - Choice 뱃지들 다음에
    if (broadcast.badge_style_ids.includes('awards_2024')) {
      badges.push(
        <AwardsBadge key="awards">
          <DiamondIcon>♦</DiamondIcon>
          <SpoonAwardsText>SA</SpoonAwardsText>
          <DiamondIcon>♦</DiamondIcon>
        </AwardsBadge>
      );
    }
    
    // Curation 뱃지 (에디터 추천)
    if (broadcast.badge_style_ids.includes('curation')) {
      badges.push(<CurationBadge key="curation">HOT</CurationBadge>);
    }
    
    // SN (Spoon Network) 뱃지
    if (broadcast.badge_style_ids.includes('sn') || broadcast.badge_style_ids.includes('spoon_network')) {
      badges.push(<SNBadge key="sn">SN</SNBadge>);
    }
    
    return badges;
  };
  
  return (
    <BroadcastCardContainer onClick={() => onSelect(broadcast)}>
      <ThumbnailContainer>
        <ThumbnailImage
          src={broadcast.img_url}
          alt={broadcast.title}
          onError={(e) => {
            e.currentTarget.src = 'https://via.placeholder.com/280x200?text=No+Image';
          }}
        />
        <ThumbnailBadgeContainer>
          {renderBadges()}
        </ThumbnailBadgeContainer>
      </ThumbnailContainer>
      
      <ContentContainer>
        <ContentBadgeContainer>
          {renderBadges()}
        </ContentBadgeContainer>
        <BroadcastTitle className="emoji-text">{broadcast.title}</BroadcastTitle>
        <AuthorName className="emoji-text">{broadcast.author.nickname}</AuthorName>
        <StatsContainer>
          <StatItem>
            <UserIcon viewBox="0 0 24 24">
              <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </UserIcon>
            <span>{broadcast.member_count}</span>
          </StatItem>
          <StatItem>
            <HeartIcon viewBox="0 0 24 24">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </HeartIcon>
            <span>{broadcast.like_count}</span>
          </StatItem>
        </StatsContainer>
        {broadcast.tags.length > 0 && (
          <TagsContainer>
            {broadcast.tags.slice(0, 3).map((tag, index) => (
              <Tag key={index}>#{tag}</Tag>
            ))}
          </TagsContainer>
        )}
      </ContentContainer>
    </BroadcastCardContainer>
  );
};

export default BroadcastCard; 