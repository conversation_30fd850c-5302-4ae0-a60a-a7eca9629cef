﻿import { auth, db } from '../main'; // src/main.ts에서 초기화된 auth와 db 임포트

// Firebase Authentication에서 필요한 함수들을 임포트합니다.
import {
  createUserWithEmailAndPassword, // 사용자 생성 (회원가입)
  signInWithEmailAndPassword,     // 사용자 로그인
  signOut,                        // 사용자 로그아웃
  User,                           // Firebase User 객체 타입
} from 'firebase/auth';

// Firestore에서 필요한 함수들을 임포트합니다.
import {
  doc,        // 문서 참조를 생성
  setDoc,     // 문서 생성 또는 업데이트
  getDoc,     // 문서 가져오기
  collection, // 컬렉션 참조를 생성
  query,      // 쿼리 생성
  where,      // 쿼리 조건 (필요한 경우)
  getDocs,    // 쿼리 결과 문서들 가져오기
} from 'firebase/firestore';

// 사용자 데이터 인터페이스 (Firestore 문서 스키마에 맞게 정의)
interface UserData {
  email: string;
  name: string;
  role: 'user' | 'admin'; // 사용자 역할
  isApproved: boolean;    // 관리자 승인 여부
  createdAt: Date;        // 가입 시간
  // 여기에 필요한 다른 사용자 정보 필드를 추가할 수 있습니다.
}

export const authService = {
  /**
   * 사용자 회원가입 처리 (Firebase Authentication 및 Firestore 사용)
   * 사용자는 Firebase Auth에 생성되고, Firestore 'users' 컬렉션에 초기 데이터가 저장됩니다.
   * @param email 사용자 이메일
   * @param password 사용자 비밀번호
   * @param name 사용자 이름
   * @returns Firebase User 객체 또는 null
   */
  async register(email: string, password: string, name: string): Promise<User | null> {
    try {
      // 1. Firebase Authentication에 사용자 생성
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (user) {
        // 2. Firestore 'users' 컬렉션에 사용자 정보 저장
        //    사용자 UID를 문서 ID로 사용하여 특정 사용자 문서에 접근합니다.
        await setDoc(doc(db, 'users', user.uid), {
          email: email,
          name: name,
          role: 'user',       // 기본 역할은 'user'
          isApproved: false,  // 초기에는 관리자 승인 대기 상태
          createdAt: new Date(),
        } as UserData);

        console.log('회원가입 성공, 관리자 승인 대기 중:', user.email);
        return user;
      }
      return null;
    } catch (error: any) {
      console.error('회원가입 오류:', error.message);
      // Firebase Auth 오류 코드에 따라 더 상세한 메시지 처리 가능합니다. (예: 'auth/email-already-in-use')
      throw error;
    }
  },

  /**
   * 사용자 로그인 처리 (Firebase Authentication 및 Firestore 사용)
   * 로그인 후 Firestore에서 사용자의 승인 상태를 확인합니다.
   * @param email 사용자 이메일
   * @param password 사용자 비밀번호
   * @returns Firebase User 객체 또는 null
   */
  async login(email: string, password: string): Promise<User | null> {
    try {
      // 1. Firebase Authentication으로 로그인 시도
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (user) {
        // 2. Firestore에서 사용자 승인 상태 확인
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnap = await getDoc(userDocRef);

        // 문서가 존재하고 isApproved 필드가 true인지 확인
        if (userDocSnap.exists() && userDocSnap.data()?.isApproved) {
          console.log('로그인 성공:', user.email);
          return user;
        } else {
          // 승인되지 않은 사용자이거나 Firestore에 사용자 데이터가 없는 경우
          await signOut(auth); // 로그인된 세션 강제 해제
          throw new Error('계정이 승인되지 않았습니다. 관리자에게 문의하세요.');
        }
      }
      return null;
    } catch (error: any) {
      console.error('로그인 오류:', error.message);
      throw error;
    }
  },

  /**
   * 현재 로그인된 사용자 객체를 가져옵니다.
   * @returns Firebase User 객체 또는 null
   */
  getCurrentUser(): User | null {
    return auth.currentUser;
  },

  /**
   * 현재 사용자를 로그아웃 처리합니다.
   */
  async logout(): Promise<void> {
    try {
      await signOut(auth);
      console.log('로그아웃 성공');
    } catch (error: any) {
      console.error('로그아웃 오류:', error.message);
      throw error;
    }
  },

  /**
   * 관리자가 사용자 승인을 처리합니다. (Firestore 문서 업데이트)
   * 특정 사용자의 'isApproved' 필드를 true로 변경합니다.
   * @param userId 승인할 사용자의 UID (Firebase Authentication에서 얻은 ID)
   */
  async approveUser(userId: string): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId);
      // isApproved 필드만 true로 업데이트하고 다른 필드는 그대로 둡니다.
      await setDoc(userRef, { isApproved: true }, { merge: true });
      console.log(`사용자 ${userId} 승인 완료.`);
    } catch (error: any) {
      console.error('사용자 승인 오류:', error.message);
      throw error;
    }
  },

  /**
   * 승인 대기 중인 사용자 목록을 가져옵니다. (관리자 패널용)
   * 'users' 컬렉션에서 'isApproved' 필드가 false인 문서들을 쿼리합니다.
   * 🚨 중요: 이 쿼리를 효율적으로 사용하려면 Firebase 콘솔의 Firestore '색인' 탭에서
   *    `users` 컬렉션의 `isApproved` 필드에 대한 단일 필드 인덱스를 생성해야 할 수 있습니다.
   * @returns 승인 대기 중인 사용자 데이터 배열
   */
  async getPendingUsers(): Promise<UserData[]> {
    try {
      const q = query(collection(db, 'users'), where('isApproved', '==', false));
      const querySnapshot = await getDocs(q);

      const pendingUsers: UserData[] = [];
      querySnapshot.forEach((doc) => {
        // 문서 ID (UID)도 필요하다면 doc.id를 사용하여 추가할 수 있습니다.
        pendingUsers.push(doc.data() as UserData);
      });
      return pendingUsers;
    } catch (error: any) {
      console.error('승인 대기 사용자 가져오기 오류:', error.message);
      throw error;
    }
  },

  /**
   * 모든 사용자 목록을 가져옵니다. (예시, 관리자용)
   * @returns 모든 사용자 데이터 배열
   */
  async getAllUsers(): Promise<UserData[]> {
    try {
      const querySnapshot = await getDocs(collection(db, 'users'));
      const allUsers: UserData[] = [];
      querySnapshot.forEach((doc) => {
        // Firestore 문서의 ID(UID)를 포함하여 반환합니다.
        allUsers.push({ id: doc.id, ...doc.data() } as UserData);
      });
      return allUsers;
    } catch (error: any) {
      console.error('모든 사용자 가져오기 오류:', error.message);
      throw error;
    }
  }
}; 