<!--
 * LoginSpoon.vue
 * Created on Fri Jul 02 2021
 *
 * Copyright (c) raravel. Licensed under the MIT License.
-->
<template>
	<div style="position:absolute; top: 0; left: 0; height: 100vh; width: 100%;">
		<v-row class="ma-0 h-100v" align="center">
			<v-col cols="12" align="center" style="width: 100%;">
				<v-btn
					rounded
					elevation="2"
					x-large
					class="mt-5 login-button"
					dark
					color="purple darken-1"
					:loading="loading"
					@click="loginSpoonExtension">
					<v-icon left>mdi-login</v-icon>
					Login
				</v-btn>
			</v-col>
		</v-row>

		<!-- 크롬 실행 중 확인 다이얼로그 -->
		<v-dialog
			v-model="chromeRunningDialog"
			max-width="400"
			persistent
		>
			<v-card>
				<v-card-title class="headline purple lighten-5">
					<v-icon left>mdi-information</v-icon>
					로그인 준비
				</v-card-title>
				<v-card-text class="pt-4 dialog-text">
					로그인을 위해 Chrome 브라우저를 실행해야 합니다. 현재 실행 중인 Chrome 브라우저가 있다면 종료하시겠습니까?
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn
						text
						color="grey darken-1"
						@click="handleChromeRunning(false)"
					>
						<v-icon left small>mdi-close</v-icon>
						취소
					</v-btn>
					<v-btn
						text
						color="purple darken-1"
						@click="handleChromeRunning(true)"
					>
						<v-icon left small>mdi-check</v-icon>
						계속 진행
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<!-- 크롬을 찾을 수 없을 때 다이얼로그 -->
		<v-dialog
			v-model="chromeNotFoundDialog"
			max-width="400"
			persistent
		>
			<v-card>
				<v-card-title class="headline purple lighten-5">
					<v-icon left color="red">mdi-alert</v-icon>
					Chrome 필요
				</v-card-title>
				<v-card-text class="pt-4 dialog-text">
					Chrome이 설치되어 있지 않습니다. 로그인을 위해서는 Chrome 브라우저가 필요합니다.
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn
						text
						color="purple darken-1"
						@click="chromeNotFoundDialog = false"
					>
						<v-icon left small>mdi-check</v-icon>
						확인
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<!-- 오류 다이얼로그 -->
		<v-dialog
			v-model="errorDialog"
			max-width="400"
			persistent
		>
			<v-card>
				<v-card-title class="headline purple lighten-5">
					<v-icon left color="red">mdi-alert-circle</v-icon>
					오류 발생
				</v-card-title>
				<v-card-text class="pt-4 dialog-text">
					{{ errorMessage }}
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn
						text
						color="purple darken-1"
						@click="errorDialog = false"
					>
						<v-icon left small>mdi-check</v-icon>
						확인
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { SnsType, LogonUser } from '@sopia-bot/core';
import Picture from '@/views/Components/Picture.vue';
const { ipcRenderer } = window.require('electron');
const fs = window.require('fs');

@Component({
	components: {
		Picture,
	},
})
export default class LoginSpoon extends Mixins(GlobalMixins) {

	public tabItem: SnsType[] = [ SnsType.PHONE, SnsType.EMAIL ];
	public tab: number = 0;
	public auth: any = { id: '', pw: '' };
	public errorMsg: string = '';
	public loading = false;
	public chromeRunningDialog = false;
	public chromeNotFoundDialog = false;
	public errorDialog = false;
	public errorMessage = '';
	public guidImgs: Array<{ src: any }> = [
		{
			src: './assets/로그인가이드1.png',
		},
	];

	public get snsType() {
		return this.tabItem[this.tab] || this.tabItem[0];
	}

	public async loginSpoonExtension() {
		// Chrome이 실행 중인지 확인하기 전에 먼저 대화상자 표시
		this.chromeRunningDialog = true;
	}

	public async handleChromeRunning(shouldClose: boolean) {
		// 대화상자 닫기
		this.chromeRunningDialog = false;
		
		if (!shouldClose) {
			// '아니오'를 선택한 경우 종료
			return;
		}
		
		try {
			// 로딩 상태 시작
			this.loading = true;
			
			// Chrome 바로 종료 시도 (빠른 진행을 위해 확인 생략)
			console.log('Chrome 종료 요청 중...');
			await ipcRenderer.invoke('close-chrome');
			
			// 바로 로그인 진행
			console.log('로그인 진행 중...');
			await this.directLogin();
		} catch (error) {
			console.error('Chrome 종료 및 로그인 중 오류:', error);
			this.showError('Chrome 종료 중 오류가 발생했습니다.');
		} finally {
			this.loading = false;
		}
	}

	public async directLogin() {
		console.log('로그인 진행 중...');
		const result = await ipcRenderer.invoke('ext-login-open');
		console.log('로그인 결과:', result);
		
		if (!result.success) {
			if (result.status === '101') {
				// Chrome을 찾을 수 없음
				this.chromeNotFoundDialog = true;
				return;
			}
			// 상태 코드 102(Chrome 실행 중)는 이미 처리되었으므로 여기서는 제거
			else if (result.status === '999') {
				// 기타 오류
				this.showError(result.error || '알 수 없는 오류가 발생했습니다.');
				return;
			}
			return;
		}
		
		const userInfo = result.data as LogonUser;
		await this.$sopia.loginToken(userInfo.id, userInfo.token, userInfo.refresh_token);
		
		this.$emit('logon', userInfo);
	}

	public showError(message: string) {
		this.errorMessage = message;
		this.errorDialog = true;
	}
}
</script>
<style>
.link {
	cursor: pointer;
	text-decoration: underline;
}

.link:hover {
	color: #E53935;
}

.login-button {
	font-size: 18px;
	min-width: 150px;
	letter-spacing: 1px;
	text-transform: none;
	transition: all 0.3s ease;
}

.login-button:hover {
	transform: translateY(-3px);
	box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
}

.headline {
	font-size: 1.25rem !important;
	font-weight: 500;
	line-height: 2rem;
}

.dialog-text {
	font-size: 1.05rem !important;
	line-height: 1.6rem !important;
	letter-spacing: 0.02em;
	font-weight: 400;
}
</style>