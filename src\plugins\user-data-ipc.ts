/**
 * user-data-ipc.ts
 * Created on 2023
 *
 * Copyright (c) tamm-v1. Licensed under the MIT License.
 * 
 * 유저 데이터를 IPC 통신을 통해 관리하는 유틸리티
 */

// import { ipc<PERSON>ender<PERSON> } from 'electron';
import logger from './logger';

// 브라우저 환경과 Electron 환경을 구분하여 처리
const electron = window.require ? window.require('electron') : null;
const ipcRenderer = electron ? electron.ipcRenderer : null;

// 사용자 데이터 파일 이름
const USER_DATA_FILENAME = 'tamm';

// Mock IPC 객체 (브라우저 환경용)
const mockIpc = {
  async invoke(channel: string, ...args: any[]): Promise<any> {
    logger.info(`Mock IPC called: ${channel}`, args);
    
    // 로컬 스토리지를 사용한 임시 구현
    if (channel === 'user-data:load') {
      try {
        const data = localStorage.getItem('tamm-user-data');
        return data ? JSON.parse(data) : { user_info: [] };
      } catch (e) {
        logger.err('Mock IPC load error:', e);
        return { user_info: [] };
      }
    }
    
    if (channel === 'user-data:save') {
      try {
        localStorage.setItem('tamm-user-data', JSON.stringify(args[0]));
        return true;
      } catch (e) {
        logger.err('Mock IPC save error:', e);
        return false;
      }
    }
    
    return null;
  }
};

// 사용할 IPC 결정 (실제 또는, 브라우저 환경에서는 Mock)
const ipc = ipcRenderer || mockIpc;

// fan_level 번들과 동일한 사용자 데이터 인터페이스
export interface UserData {
  nickname: string;
  tag: string;
  last_attend: number;  // 타임스탬프
  level: number;
  point: number;
  attend_count: number;
  heart_count: number;
  chat_count: number;
  is_double: boolean;
  spoon: number[];
  money?: number;  // 사용자 보유금
}

// 사용자 데이터 응답 인터페이스
export interface UserDataResponse {
  user_info: UserData[];
}

class UserDataManager {
  private userData: UserDataResponse = { user_info: [] };
  private initialized = false;
  private currentDjTag: string = 'default';

  // 초기화 함수
  async init(djTag: string = 'default'): Promise<void> {
    if (this.initialized) return;
    
    this.currentDjTag = djTag;
    
    try {
      this.initialized = true;
      logger.info(`UserDataManager 초기화 완료`);
      
      // 별도로 loadData()를 호출하지 않고 반환 - 컴포넌트에서 명시적으로 호출하도록 함
      return;
    } catch (error) {
      logger.err('UserDataManager 초기화 실패:', error);
      throw error;
    }
  }

  // DJ 태그 설정
  setDjTag(djTag: string): void {
    this.currentDjTag = djTag;
  }

  // 현재 DJ 태그 반환
  getDjTag(): string {
    return this.currentDjTag;
  }

  // 데이터 로드 함수
  async loadData(): Promise<UserDataResponse> {
    try {
      this.userData = await ipc.invoke('user-data:load', USER_DATA_FILENAME);
      
      // 응답이 user_info 구조가 아닌 경우 변환
      if (!this.userData.user_info) {
        this.userData = { user_info: Array.isArray(this.userData) ? this.userData : [] };
      }
      
      // money 필드가 없는 유저에게 money 필드 추가
      for (const user of this.userData.user_info) {
        if (user.money === undefined) {
          user.money = 0;
          logger.info(`${user.nickname} 유저에게 money 필드 추가 (초기값: 0)`);
        }
      }
      
      logger.info(`${this.userData.user_info.length}명의 사용자 데이터를 로드했습니다.`);
      return this.userData;
    } catch (error) {
      logger.err('사용자 데이터 로드 실패:', error);
      this.userData = { user_info: [] };
      return this.userData;
    }
  }

  // 데이터 저장 함수
  async saveData(): Promise<boolean> {
    try {
      const result = await ipc.invoke('user-data:save', this.userData, USER_DATA_FILENAME);
      logger.info('사용자 데이터 저장 완료');
      return result;
    } catch (error) {
      logger.err('사용자 데이터 저장 실패:', error);
      return false;
    }
  }

  // 사용자 추가 함수
  async addUser(userData: UserData): Promise<UserData> {
    // 이미 존재하는 사용자인지 확인
    const existingUser = this.getUserByNickname(userData.nickname);
    if (existingUser) {
      throw new Error(`이미 존재하는 사용자입니다: ${userData.nickname}`);
    }
    
    // 사용자 추가
    this.userData.user_info.push(userData);
    await this.saveData();
    logger.info(`사용자 추가됨: ${userData.nickname}`);
    return userData;
  }

  // 사용자 업데이트 함수
  async updateUser(userData: UserData): Promise<UserData> {
    const index = this.userData.user_info.findIndex(user => user.nickname === userData.nickname);
    if (index === -1) {
      throw new Error(`사용자를 찾을 수 없습니다: ${userData.nickname}`);
    }
    
    this.userData.user_info[index] = userData;
    await this.saveData();
    logger.info(`사용자 업데이트됨: ${userData.nickname}`);
    return userData;
  }

  // 사용자 삭제 함수
  async deleteUser(nickname: string): Promise<void> {
    const index = this.userData.user_info.findIndex(user => user.nickname === nickname);
    if (index === -1) {
      throw new Error(`사용자를 찾을 수 없습니다: ${nickname}`);
    }
    
    const deletedUser = this.userData.user_info[index];
    this.userData.user_info.splice(index, 1);
    await this.saveData();
    logger.info(`사용자 삭제됨: ${deletedUser.nickname}`);
  }

  // 사용자 검색 함수
  searchUsers(query: string): UserData[] {
    if (!query) return this.userData.user_info;
    
    const lowercaseQuery = query.toLowerCase();
    return this.userData.user_info.filter(user => 
      user.nickname.toLowerCase().includes(lowercaseQuery) || 
      user.tag.toLowerCase().includes(lowercaseQuery)
    );
  }

  // 전체 사용자 가져오기
  getAllUsers(): UserData[] {
    return [...this.userData.user_info];
  }

  // 닉네임으로 사용자 가져오기
  getUserByNickname(nickname: string): UserData | undefined {
    return this.userData.user_info.find(user => user.nickname === nickname);
  }

  // 태그로 사용자 가져오기
  getUserByTag(tag: string): UserData | undefined {
    return this.userData.user_info.find(user => user.tag === tag);
  }

  // 포인트 추가 함수
  async addUserPoints(nickname: string, points: number): Promise<UserData | undefined> {
    const user = this.getUserByNickname(nickname);
    if (!user) return undefined;
    
    user.point += points;
    await this.saveData();
    logger.info(`사용자 ${user.nickname}에게 ${points} 포인트 추가됨`);
    return user;
  }

  // 레벨 업데이트 함수
  async updateUserLevel(nickname: string, level: number): Promise<UserData | undefined> {
    const user = this.getUserByNickname(nickname);
    if (!user) return undefined;
    
    user.level = level;
    await this.saveData();
    logger.info(`사용자 ${user.nickname}의 레벨이 ${level}로 업데이트됨`);
    return user;
  }

  // 출석 카운트 증가
  async increaseAttendCount(nickname: string): Promise<UserData | undefined> {
    const user = this.getUserByNickname(nickname);
    if (!user) return undefined;
    
    user.attend_count += 1;
    user.last_attend = Date.now();
    await this.saveData();
    logger.info(`사용자 ${user.nickname}의 출석 수 증가됨`);
    return user;
  }

  // 하트 카운트 증가
  async increaseHeartCount(nickname: string): Promise<UserData | undefined> {
    const user = this.getUserByNickname(nickname);
    if (!user) return undefined;
    
    user.heart_count += 1;
    await this.saveData();
    logger.info(`사용자 ${user.nickname}의 하트 수 증가됨`);
    return user;
  }

  // 채팅 카운트 증가
  async increaseChatCount(nickname: string): Promise<UserData | undefined> {
    const user = this.getUserByNickname(nickname);
    if (!user) return undefined;
    
    user.chat_count += 1;
    await this.saveData();
    logger.info(`사용자 ${user.nickname}의 채팅 수 증가됨`);
    return user;
  }

  // 복권 지급
  async giveLottery(tag: string, amount: number): Promise<UserData | undefined> {
    const user = this.getUserByTag(tag);
    if (!user) return undefined;
    
    // spoon 배열이 없는 경우 초기화
    if (!user.spoon) {
      user.spoon = [0, 0, 0];
    }
    
    // 세 번째 요소(index 2)가 복권 수량을 나타냄
    user.spoon[2] = (user.spoon[2] || 0) + amount;
    
    await this.saveData();
    logger.info(`사용자 ${user.nickname}에게 복권 ${amount}장 지급됨`);
    return user;
  }
}

// 싱글톤 인스턴스 생성
export const userDataManager = new UserDataManager();

export default userDataManager; 