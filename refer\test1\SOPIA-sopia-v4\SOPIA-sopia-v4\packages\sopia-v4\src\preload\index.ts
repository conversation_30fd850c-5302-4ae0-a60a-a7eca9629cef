import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

contextBridge.exposeInMainWorld('electron', {
  chat: (msg: string): Promise<string> => ipcR<PERSON>er.invoke('chat', msg),
  printLog: (log: string): void => ipcRenderer.send('print-log', log),
  sendMultiParams: (param1: string, param2: string): void =>
    ipcRenderer.send('multi-params', param1, param2),
  exit: (): void => ipcRenderer.send('exit')
})
