import { AuthApi } from '../api/auth.api'
import { LiveApi } from '../api/live.api'
import { UserApi } from '../api/users.api'
import { ApiHttpClient, HttpClient } from './http.client'

export class ApiClient {
  public instance: ApiHttpClient

  public user: UserApi
  public auth: AuthApi
  public live: LiveApi

  constructor(private httpClient: HttpClient) {
    this.instance = new ApiHttpClient(this.httpClient)

    this.user = new UserApi(this.instance)
    this.auth = new AuthApi(this.instance)
    this.live = new LiveApi(this.instance)
  }
}
