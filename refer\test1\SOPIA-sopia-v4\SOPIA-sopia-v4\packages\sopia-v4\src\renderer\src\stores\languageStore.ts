import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import i18n from '../i18n'

type Language = 'ko' | 'en'

interface LanguageStore {
  language: Language
  setLanguage: (language: Language) => void
}

export const useLanguageStore = create<LanguageStore>()(
  persist(
    (set) => ({
      language: 'ko',
      setLanguage: (language) => {
        i18n.changeLanguage(language)
        set({ language })
      }
    }),
    {
      name: 'language-storage'
    }
  )
)
