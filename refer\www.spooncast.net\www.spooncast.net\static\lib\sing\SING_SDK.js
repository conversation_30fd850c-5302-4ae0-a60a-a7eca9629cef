var SING_SDK;
(() => {
    "use strict";
    var e = {
            765: (e, t, s) => {
                function i(e) {
                    this.message = e
                }
                s.r(t), s.d(t, {
                    InvalidTokenError: () => r,
                    default: () => a
                }), i.prototype = new Error, i.prototype.name = "InvalidCharacterError";
                var n = "undefined" != typeof window && window.atob && window.atob.bind(window) || function(e) {
                    var t = String(e).replace(/=+$/, "");
                    if (t.length % 4 == 1) throw new i("'atob' failed: The string to be decoded is not correctly encoded.");
                    for (var s, n, o = 0, r = 0, a = ""; n = t.charAt(r++); ~n && (s = o % 4 ? 64 * s + n : n, o++ % 4) ? a += String.fromCharCode(255 & s >> (-2 * o & 6)) : 0) n = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(n);
                    return a
                };

                function o(e) {
                    var t = e.replace(/-/g, "+").replace(/_/g, "/");
                    switch (t.length % 4) {
                        case 0:
                            break;
                        case 2:
                            t += "==";
                            break;
                        case 3:
                            t += "=";
                            break;
                        default:
                            throw "Illegal base64url string!"
                    }
                    try {
                        return function(e) {
                            return decodeURIComponent(n(e).replace(/(.)/g, (function(e, t) {
                                var s = t.charCodeAt(0).toString(16).toUpperCase();
                                return s.length < 2 && (s = "0" + s), "%" + s
                            })))
                        }(t)
                    } catch (e) {
                        return n(t)
                    }
                }

                function r(e) {
                    this.message = e
                }
                r.prototype = new Error, r.prototype.name = "InvalidTokenError";
                const a = function(e, t) {
                    if ("string" != typeof e) throw new r("Invalid token specified");
                    var s = !0 === (t = t || {}).header ? 0 : 1;
                    try {
                        return JSON.parse(o(e.split(".")[s]))
                    } catch (e) {
                        throw new r("Invalid token specified: " + e.message)
                    }
                }
            },
            692: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = s(831),
                    o = i(s(444)),
                    r = i(s(765));
                t.default = class {
                    static async GetRoomToken(e) {
                        try {
                            const t = await fetch(e.singApi + o.default.ROOM_URL, {
                                    method: "POST",
                                    body: JSON.stringify({
                                        type: "create",
                                        ow: e.tag,
                                        too: !1,
                                        expd: 7200,
                                        rm: "sfu",
                                        mp: o.default.maxPub,
                                        noti: !0,
                                        bnoti: !0,
                                        liveid: e.liveID,
                                        cast: e.cast,
                                        sid: e.sid,
                                        maxInvite: o.default.maxInvite,
                                        tx: (0, n.v4)()
                                    })
                                }),
                                s = await t.json();
                            if (200 !== s.status) throw new Error(`result.status : ${s.status}`);
                            return s.response
                        } catch (e) {
                            throw new Error(`CommonAPI.GetRoomToken : ${e.message}`)
                        }
                    }
                    static async GetSessionToken(e) {
                        try {
                            const t = await fetch(e.singApi + o.default.SESSION_URL, {
                                    method: "POST",
                                    body: JSON.stringify({
                                        type: "create",
                                        tag: e.tag,
                                        dup: 1,
                                        expd: 7200,
                                        kcnt: 10,
                                        rtk: (0, r.default)(e.roomToken).id,
                                        pt: "sndrecv",
                                        st: `web/${o.default.SDK_VERSION}`,
                                        iow: e.iow,
                                        ua: navigator.userAgent,
                                        v: o.default.SING_VERSION,
                                        tx: (0, n.v4)()
                                    })
                                }),
                                s = await t.json();
                            if (200 !== s.status) throw new Error(`result.status : ${s.status}`);
                            return s.response
                        } catch (e) {
                            throw new Error(`CommonAPI.GetSessionToken : ${e.message}`)
                        }
                    }
                }
            },
            648: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const i = s(630);
                t.default = class {
                    constructor() {
                        this.waitMilliseconds = 12e4, this.intervalMilliseconds = 100, this.responseCredentialOK = !1, this.responseSingCompletedOK = !1, this.responseExceededPub = !1, this.responseLiveAPI = null, this.responsePublishers = null, this.timerIDs = [], this.allTimerPause = !1
                    }
                    waitOpenSocket(e) {
                        return new Promise((t => {
                            const s = Date.now() + this.waitMilliseconds,
                                i = window.setInterval((() => {
                                    this.allTimerPause || (Date.now() > s ? (clearInterval(i), t(!1)) : 1 === e.readyState && (clearInterval(i), t(!0)))
                                }), this.intervalMilliseconds);
                            this.timerIDs.push(i)
                        }))
                    }
                    waitIceServers() {
                        return this.responseCredentialOK = !1, new Promise((e => {
                            const t = Date.now() + this.waitMilliseconds,
                                s = window.setInterval((() => {
                                    this.allTimerPause || (Date.now() > t ? (clearInterval(s), e(!1)) : this.responseCredentialOK && (clearInterval(s), e(!0)))
                                }), this.intervalMilliseconds);
                            this.timerIDs.push(s)
                        }))
                    }
                    waitSingCompleted() {
                        return this.responseSingCompletedOK = !1, new Promise(((e, t) => {
                            const s = Date.now() + this.waitMilliseconds,
                                n = window.setInterval((() => {
                                    this.allTimerPause || (this.responseSingCompletedOK ? (clearInterval(n), this.responseSingCompletedOK = !1, e(i.ResStatusMsg.SING_COMPLETED)) : Date.now() > s ? (clearInterval(n), t(new Error(i.ResStatusMsg.SING_COMPLETED_TIMEOUT))) : this.responseExceededPub && (clearInterval(n), this.responseExceededPub = !1, t(new Error(i.ResStatusMsg.MAXPUB))))
                                }), this.intervalMilliseconds);
                            this.timerIDs.push(n)
                        }))
                    }
                    waitNoticeService() {
                        return this.responseLiveAPI = null, new Promise((e => {
                            const t = Date.now() + this.waitMilliseconds,
                                s = window.setInterval((() => {
                                    this.allTimerPause || (Date.now() > t ? (clearInterval(s), e(!1)) : this.responseLiveAPI && (clearInterval(s), e(!0)))
                                }), this.intervalMilliseconds);
                            this.timerIDs.push(s)
                        }))
                    }
                    waitPublishers() {
                        return this.responsePublishers = {
                            status: i.ResStatus.READY,
                            pubs: []
                        }, new Promise((e => {
                            const t = Date.now() + this.waitMilliseconds,
                                s = window.setInterval((() => {
                                    this.allTimerPause || (Date.now() > t ? (clearInterval(s), e(i.ResStatus.TIMEOUT)) : this.responsePublishers.status === i.ResStatus.OK ? (clearInterval(s), e(i.ResStatus.OK)) : this.responsePublishers.status === i.ResStatus.EMPTY && (clearInterval(s), e(i.ResStatus.EMPTY)))
                                }), this.intervalMilliseconds);
                            this.timerIDs.push(s)
                        }))
                    }
                    waitOnLine() {
                        return new Promise((e => {
                            const t = window.setInterval((() => {
                                this.allTimerPause || navigator.onLine && (clearInterval(t), e(!0))
                            }), this.intervalMilliseconds);
                            this.timerIDs.push(t)
                        }))
                    }
                    end() {
                        this.allTimerPause = !0;
                        for (let e = 0; e < this.timerIDs.length; e++) clearInterval(this.timerIDs[e])
                    }
                }
            },
            630: (e, t) => {
                var s, i, n, o, r, a, c, u, l, d;
                Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.LogAttr = t.LogType = t.LocalStorageKey = t.ServiceCMD = t.ServiceName = t.MyPubStatus = t.ResStatusMsg = t.ResStatus = t.GFIType = t.PeerType = t.PeerRole = t.EnterType = t.PersonType = t.LogLevel = t.MoveType = t.LiveStatus = t.SingStatus = t.SingInfoMode = t.CommandType = t.MessageMsg = t.MessageType = void 0,
                    function(e) {
                        e.ROOM = "room", e.CREDENTIAL = "credential", e.ENTER = "enter", e.ANSWER = "answer", e.OFFER = "offer", e.PUB = "pub", e.SUB = "sub", e.UNPUB = "unpub", e.OP = "op", e.EXIT = "exit", e.CANIDATE = "candidate", e.EVENT = "event", e.MSG = "msg", e.SERVICE = "service", e.KEEP_ALIVE = "keep_alive", e.ATTRIBUTE = "attribute", e.PUB_RESPONSE = "pub_response", e.PUBLISHER_EXCEEDED = "publisher_exceeded", e.SING_COMPLETED = "sing_completed"
                    }(s || (t.MessageType = s = {})),
                    function(e) {
                        e.PUBLISHERS = "publishers", e.MUTE = "mute", e.UNMUTE = "unmute", e.GUEST = "guest", e.DISCONNECTED = "disconnected", e.UNPUB = "unpub", e.DESTROY = "destroy", e.MOVING = "moving", e.MOVED = "moved"
                    }(i || (t.MessageMsg = i = {})),
                    function(e) {
                        e.ADD_LISTENER = "add-listener", e.PUBLISHERS = "publishers", e.PUBLIST = "pubList", e.RES_PUBLIST = "res-pubList", e.OK_GUEST = "ok-guest", e.ASK_GUEST = "ask-guest", e.GET_ASK_GUEST_LIST = "guest-ask-list", e.GET_GUEST_LIST = "guest-ing-list", e.CANCEL_ASK_GUEST = "cancel-guest", e.REJECT_GUEST = "reject-guest", e.ISMUTE = "ismute", e.MUTE = "mute", e.UNMUTE = "unmute", e.MUTE_GUEST = "mute-guest", e.UNMUTE_GUEST = "unmute-guest", e.MOVED = "moved", e.INVITE = "invite", e.CANCEL_INVITE = "cancel-invite", e.REJECT_INVITE = "reject-invite", e.GET_INVITE_LIST = "invite-list", e.KICK_GUEST = "kick-guest"
                    }(n || (t.CommandType = n = {})),
                    function(e) {
                        e.TEST_ALIVE = "TEST_ALIVE", e.DEV = "DEV", e.PRD = "PRD"
                    }(o || (t.SingInfoMode = o = {})),
                    function(e) {
                        e.Ready = "READY", e.Active = "ACTIVE"
                    }(r || (t.SingStatus = r = {})),
                    function(e) {
                        e.Ready = "READY", e.Active = "ACTIVE", e.Reconn = "RECONN", e.Moving = "MOVING", e.MovingDone = "MOVING_DONE", e.MovingError = "MOVING_ERROR"
                    }(a || (t.LiveStatus = a = {})),
                    function(e) {
                        e.Destroy = "DESTROY", e.SFU = "SFU"
                    }(c || (t.MoveType = c = {})), t.LogLevel = {
                        ERROR: "error",
                        INFO: "info",
                        TRACE: "trace",
                        DEBUG: "debug"
                    }, t.PersonType = {
                        OWNER: "owner",
                        PARTICIPNT: "participant"
                    }, t.EnterType = {
                        NEW: "new",
                        MOVE: "move",
                        RECONNECT: "reconnect"
                    }, t.PeerRole = {
                        PUB: "pub",
                        SUB: "sub"
                    }, t.PeerType = {
                        DJ: "dj",
                        GUEST: "guest",
                        LISTENER: "listener"
                    }, t.GFIType = {
                        DJ: "op",
                        GUEST: "o"
                    }, t.ResStatus = {
                        READY: 100,
                        OK: 200,
                        EMPTY: 300,
                        TIMEOUT: 400,
                        ERROR: 500,
                        MAXPUB: 501,
                        ING: 600
                    }, t.ResStatusMsg = {
                        SING_COMPLETED: "sing completed",
                        SING_COMPLETED_TIMEOUT: "sing completed timeout",
                        MAXPUB: "maxPub limit"
                    }, t.MyPubStatus = {
                        NOT: {
                            status: 100,
                            msg: "you are not a Pub"
                        },
                        OK: {
                            status: 200,
                            msg: "you have completed your pub"
                        },
                        TIMEOUT: {
                            status: 400,
                            msg: "a pub timeout"
                        }
                    },
                    function(e) {
                        e.LIVE = "live"
                    }(u || (t.ServiceName = u = {})),
                    function(e) {
                        e.PUBLISH = "publish", e.UNPUBLISH = "unpublish"
                    }(l || (t.ServiceCMD = l = {})),
                    function(e) {
                        e.SESSIONID = "sing:sessionid"
                    }(d || (t.LocalStorageKey = d = {})), t.LogType = {
                        DJ: "dj",
                        LISTENER: "listener"
                    }, t.LogAttr = {
                        actor: {
                            DJ: "DJ",
                            Listener: "Listener"
                        },
                        type: {
                            iceConnState: "Ice",
                            device: "Device",
                            movesfu: "MoveSFU",
                            moveany: "MoveAnyting",
                            reconnection: "Reconnection",
                            destroy: "Destroy",
                            websocket: "WebSocket",
                            start: "Start",
                            end: "End",
                            msg: "Message",
                            pub: "Pub",
                            sub: "Sub",
                            op: "OP",
                            enter: "Enter",
                            offer: "Offer",
                            answer: "Answer",
                            guest: "Guest"
                        }
                    }
            },
            303: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.sing = void 0;
                const n = i(s(652));
                t.sing = n.default
            },
            897: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = i(s(777)),
                    o = s(831),
                    r = i(s(444)),
                    a = s(630),
                    c = i(s(692)),
                    u = i(s(835)),
                    l = i(s(567)),
                    d = i(s(603)),
                    h = i(s(145)),
                    g = i(s(648));
                t.default = class {
                    constructor(e) {
                        this.countryCode = e.countryCode, this.singApi = e.singApi, this.wsUrl = e.wsUrl, this.relayMode = e.front.relayMode, this.liveID = e.front.liveID || e.liveID, this.roomToken = e.front.token, this.cast = e.front.cast || !1, this.user = new l.default(e.front.userID, e.front.userTag, e.isDJ), this.streamID = e.front.streamID, this.isLiveCall = e.front.isLiveCall, n.default.setSingInfo(this.user, this.liveID), this.callback = e.callback, this.peers = new Map, this.asyncResponse = new g.default, this.mic = e.mic, this.status = a.LiveStatus.Active
                    }
                    initSocketEvent() {
                        this.socket.onopen = e => {
                            this.socket ? (this.socketID = (0, o.v4)(), n.default.info({
                                type: a.LogAttr.type.websocket,
                                loc: "live.socket.onopen",
                                desc: "websocket open",
                                live_id: this.liveID,
                                socket_id: this.socketID
                            }), this.callback.socket({
                                status: "open",
                                liveID: this.liveID,
                                socketID: this.socketID
                            }), this.sendKeepAlive()) : n.default.err({
                                type: a.LogAttr.type.websocket,
                                loc: "live.socket.onopen",
                                desc: "websocket is null",
                                live_id: this.liveID,
                                socket_id: this.socketID
                            })
                        }, this.socket.onerror = () => {
                            this.callback.socket({
                                status: "error",
                                liveID: this.liveID,
                                socketID: this.socketID
                            })
                        }, this.socket.onclose = async e => {
                            n.default.info({
                                type: a.LogAttr.type.websocket,
                                loc: "live.socket.onclose",
                                desc: "websocket close",
                                code: e.code,
                                wasClean: e.wasClean,
                                live_id: this.liveID,
                                socket_id: this.socketID
                            }), this.callback.socket({
                                status: "close",
                                desc: "websocket close",
                                code: e.code,
                                wasClean: e.wasClean,
                                liveID: this.liveID,
                                socketID: this.socketID,
                                roomToken: this.roomToken
                            });
                            try {
                                const e = (new Date).getTime();
                                switch (this.asyncResponse && await this.asyncResponse.waitOnLine(), this.initResponse(), this.status) {
                                    case a.LiveStatus.Moving:
                                        this.move.getMoveType() === a.MoveType.Destroy && (this.closeByDestroy(), this.user.isDJ() ? this.moveDJByDestroy() : await this.createWebSocket());
                                        break;
                                    case a.LiveStatus.Active:
                                        this.user.isDJ() ? this.reconnectionByDJ() : this.reconnectionByListener(e)
                                }
                            } catch (e) {
                                n.default.err({
                                    type: a.LogAttr.type.websocket,
                                    loc: "live.socket.onclose",
                                    desc: `${e}`,
                                    live_id: this.liveID,
                                    socket_id: this.socketID
                                })
                            }
                        }, this.socket.onmessage = e => {
                            if (!this.socket) return void n.default.trace({
                                type: a.LogAttr.type.msg,
                                loc: "live.socket.onmessage",
                                desc: `websocket STOP socket is null, liveID: ${this.liveID}, socketID: ${this.socketID}, roomToken: ${this.roomToken}`
                            });
                            const t = JSON.parse(e.data);
                            switch (t.type !== a.MessageType.CREDENTIAL && t.type !== a.MessageType.KEEP_ALIVE && n.default.trace({
                                type: a.LogAttr.type.msg,
                                loc: `onmessage-${t.type}`,
                                desc: JSON.stringify(t)
                            }), t.type) {
                                case a.MessageType.ROOM:
                                    this.onMsgRoom(t);
                                    break;
                                case a.MessageType.CREDENTIAL:
                                    this.onMsgCredential(t);
                                    break;
                                case a.MessageType.ENTER:
                                    this.onMsgEnter(t);
                                    break;
                                case a.MessageType.ANSWER:
                                    this.onMsgAnswer(t);
                                    break;
                                case a.MessageType.OFFER:
                                    this.onMsgOffer(t);
                                    break;
                                case a.MessageType.PUB:
                                    return;
                                case a.MessageType.SUB:
                                    this.onMsgSub(t);
                                    break;
                                case a.MessageType.OP:
                                    this.onMsgOp(t);
                                    break;
                                case a.MessageType.EXIT:
                                    this.onMsgExit(t);
                                    break;
                                case a.MessageType.CANIDATE:
                                    this.onMsgCandidate(t);
                                    break;
                                case a.MessageType.EVENT:
                                    this.onMsgEvent(t);
                                    break;
                                case a.MessageType.MSG:
                                    this.onMsgMsg(t);
                                    break;
                                case a.MessageType.SERVICE:
                                    this.onMsgService(t);
                                    break;
                                case a.MessageType.KEEP_ALIVE:
                                    break;
                                case a.MessageType.PUB_RESPONSE:
                                    this.onPubResponse(t);
                                    break;
                                default:
                                    this.onMsgDefault(t)
                            }
                        }
                    }
                    sendKeepAlive() {
                        const e = window.setInterval((() => {
                            this.send({
                                type: a.MessageType.KEEP_ALIVE,
                                tx: (0, o.v4)()
                            }, !1)
                        }), 5e3);
                        this.asyncResponse.timerIDs.push(e)
                    }
                    async noticeLiveAPIPublish() {
                        try {
                            if (this.send({
                                    type: a.MessageType.SERVICE,
                                    service_name: a.ServiceName.LIVE,
                                    service_cmd: a.ServiceCMD.PUBLISH,
                                    live_id: this.liveID,
                                    room_token: this.roomToken,
                                    tx: (0, o.v4)()
                                }), r.default.ExecMode === a.SingInfoMode.TEST_ALIVE) return;
                            if (!await this.asyncResponse.waitNoticeService()) throw new Error("waitLiveAPINotice : Timeout");
                            if (this.asyncResponse.responseLiveAPI.status_code != a.ResStatus.OK) throw new Error(`waitLiveAPINotice : ${JSON.stringify(this.asyncResponse.responseLiveAPI)}`)
                        } catch (e) {
                            throw new Error(`live.noticeLiveAPI : ${e.message}`)
                        }
                    }
                    async reconnectionByDJ() {
                        n.default.info({
                            type: a.LogAttr.type.reconnection,
                            loc: "live.reconnectionByDJ",
                            desc: "start"
                        });
                        let e = {
                            status: a.ResStatus.READY,
                            statusMsg: "start reconnection DJ"
                        };
                        this.callback.reconnection(e);
                        try {
                            this.status = a.LiveStatus.Reconn;
                            const t = this.getSelfPeer().isMute();
                            this.initPeers(), await this.createWebSocket(), await this.startPub({
                                person: a.PersonType.OWNER,
                                etype: a.EnterType.RECONNECT
                            }), await this.getPublishers(), await this.loopStartSub(), await this.noticeLiveAPIPublish();
                            const s = this.getPubListByPeers();
                            t && this.send({
                                type: a.MessageType.ATTRIBUTE,
                                cmd: a.CommandType.MUTE,
                                to: "",
                                tx: (0, o.v4)()
                            }), this.status = a.LiveStatus.Active, e = {
                                status: a.ResStatus.OK,
                                statusMsg: "ok",
                                result: s
                            }
                        } catch (t) {
                            n.default.err(t), e = {
                                status: a.ResStatus.ERROR,
                                statusMsg: t.message
                            }
                        }
                        this.callback.reconnection(e)
                    }
                    async reconnectionByListener(e) {
                        this.status = a.LiveStatus.Reconn, n.default.info({
                            type: a.LogAttr.type.reconnection,
                            loc: "live.reconnectionByListener",
                            desc: "start"
                        });
                        let t = {
                            status: a.ResStatus.READY,
                            statusMsg: "start reconnection listener"
                        };
                        this.callback.reconnection(t);
                        try {
                            const s = this.isPub();
                            this.initPeers(), await this.createWebSocket(), await this.getPublishers(), await this.loopStartSub();
                            const i = this.getPubListByPeers(),
                                n = await this.reconnPub(s, e);
                            t = {
                                status: a.ResStatus.OK,
                                statusMsg: "ok",
                                result: i,
                                mypubStatus: n
                            }
                        } catch (e) {
                            t = {
                                status: a.ResStatus.ERROR,
                                statusMsg: e.message
                            }, n.default.err({
                                loc: "live.reconnectionByListener",
                                desc: JSON.stringify(t.statusMsg)
                            })
                        }
                        this.status = a.LiveStatus.Active, this.callback.reconnection(t)
                    }
                    async reconnPub(e, t) {
                        return e ? this.isOverTimeReconnForPub(t) ? a.MyPubStatus.TIMEOUT : (await this.startPub({
                            person: a.PersonType.PARTICIPNT,
                            etype: a.EnterType.RECONNECT
                        }), a.MyPubStatus.OK) : a.MyPubStatus.NOT
                    }
                    isOverTimeReconnForPub(e) {
                        return (new Date).getTime() - e >= 6e4
                    }
                    async moveDJByDestroy() {
                        n.default.info({
                            type: a.LogAttr.type.destroy,
                            loc: "live.moveDJByDestroy",
                            desc: "start"
                        });
                        try {
                            await this.createWebSocket(), await this.startPub({
                                person: a.PersonType.OWNER,
                                etype: a.EnterType.NEW
                            }), this.status = a.LiveStatus.Active
                        } catch (e) {
                            return n.default.err(e), this.callback.move({
                                status: a.ResStatus.ERROR,
                                liveStatus: a.LiveStatus.MovingError,
                                moveType: a.MoveType.Destroy,
                                statusMsg: `Error moving server destroy : ${e.message}`
                            }), this.move = null, void this.close()
                        }
                        const e = this.move.getPeer(this.user.id());
                        e.muteState && this.send({
                            type: a.MessageType.ATTRIBUTE,
                            cmd: a.CommandType.MUTE,
                            to: "",
                            tx: (0, o.v4)()
                        });
                        const t = this.getSelfPeer().gfi;
                        this.send({
                            type: a.MessageType.ROOM,
                            cmd: a.CommandType.MOVED,
                            ogfi: t,
                            payload: {
                                moveType: a.MoveType.Destroy,
                                gfi: t,
                                isMute: e.muteState
                            }
                        });
                        let s = {
                            status: a.ResStatus.OK,
                            liveStatus: a.LiveStatus.MovingDone,
                            moveType: a.MoveType.Destroy,
                            statusMsg: "movingDone",
                            retult: null
                        };
                        this.callback.move(s), this.move = null, n.default.info({
                            type: a.LogAttr.type.destroy,
                            loc: "live.moveDJByDestroy",
                            desc: `end : ${JSON.stringify(s)}`
                        })
                    }
                    async movePlayByAnything(e) {
                        const t = e.tag,
                            s = JSON.parse(e.payload);
                        if (t !== this.user.id()) {
                            n.default.info({
                                type: a.LogAttr.type.moveany,
                                loc: "live.movePlayByAnything",
                                desc: `start ${this.move.getMoveType()}`
                            });
                            try {
                                await this.startSub(s.gfi);
                                const e = this.peers.get(t);
                                e.muteState = s.isMute, this.status = a.LiveStatus.MovingDone;
                                let i = {
                                    status: a.ResStatus.OK,
                                    liveStatus: a.LiveStatus.MovingDone,
                                    moveType: this.move.getMoveType(),
                                    statusMsg: "ok",
                                    result: [{
                                        type: e.role,
                                        userID: t,
                                        isMute: e.muteState,
                                        stream: e.stream
                                    }]
                                };
                                this.callback.move(i), n.default.info({
                                    type: a.LogAttr.type.moveany,
                                    loc: "movePlayByAnything",
                                    desc: `end start ${this.move.getMoveType()} : ${JSON.stringify(i)}`
                                })
                            } catch (e) {
                                n.default.err(e), this.status = a.LiveStatus.MovingError, this.callback.move({
                                    status: a.ResStatus.ERROR,
                                    liveStatus: a.LiveStatus.MovingError,
                                    moveType: a.MoveType.Destroy,
                                    statusMsg: e
                                }), this.close()
                            } finally {
                                this.move = null
                            }
                        }
                    }
                    async moveDJBySFU() {
                        n.default.info({
                            type: a.LogAttr.type.movesfu,
                            loc: "live.moveDJBySFU",
                            desc: "start"
                        });
                        try {
                            this.closeByMoveSFU(), await this.startPub({
                                person: a.PersonType.OWNER,
                                etype: a.EnterType.MOVE,
                                relayMode: this.relayMode
                            }), this.status = a.LiveStatus.Active
                        } catch (e) {
                            return n.default.err(e), this.callback.move({
                                status: a.ResStatus.ERROR,
                                liveStatus: a.LiveStatus.MovingError,
                                moveType: a.MoveType.SFU,
                                statusMsg: "movingError"
                            }), this.move = null, void this.close()
                        }
                        const e = this.move.getPeer(this.user.id());
                        e.muteState && this.send({
                            type: a.MessageType.ATTRIBUTE,
                            cmd: "mute",
                            to: "",
                            tx: (0, o.v4)()
                        });
                        const t = this.peers.get(this.user.id()).gfi;
                        this.send({
                            type: a.MessageType.ROOM,
                            cmd: "moved",
                            ogfi: t,
                            payload: {
                                moveType: a.MoveType.SFU,
                                gfi: t,
                                isMute: e.muteState
                            }
                        });
                        let s = {
                            status: a.ResStatus.OK,
                            liveStatus: a.LiveStatus.MovingDone,
                            moveType: a.MoveType.SFU,
                            statusMsg: "movingDone",
                            retult: null
                        };
                        this.callback.move(s), this.move = null, n.default.info({
                            type: a.LogAttr.type.movesfu,
                            loc: "live.moveDJBySFU",
                            desc: `end : ${JSON.stringify(s)}`
                        })
                    }
                    async startPub(e) {
                        n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.pub,
                            loc: "live.startPub",
                            desc: "call live.startPUb()"
                        });
                        try {
                            let t = null;
                            e.person === a.PersonType.OWNER ? (t = a.PeerType.DJ, this.djID = this.user.id()) : (t = a.PeerType.GUEST, e.mic && (this.mic = e.mic), this.ogfi = this.peers.get(this.djID).gfi), this.peers.set(this.user.id(), new u.default({
                                peerType: t,
                                role: t,
                                socket: this.socket,
                                isPub: !0,
                                iceServers: this.iceServers,
                                mic: this.mic
                            }));
                            const s = {
                                type: a.MessageType.ENTER,
                                pt: a.PeerRole.PUB,
                                person: e.person,
                                ogfi: this.ogfi,
                                etype: e.etype,
                                tx: (0, o.v4)()
                            };
                            this.send(s), await this.asyncResponse.waitSingCompleted()
                        } catch (e) {
                            throw new Error(e.message)
                        }
                    }
                    getPubListByPeers() {
                        const e = [];
                        for (let t of this.peers.values()) {
                            const s = this.getUserIDByGFI(t.gfi);
                            t.role === a.PeerType.DJ && (this.djID = s);
                            const i = {
                                type: t.role,
                                userID: s,
                                isMute: t.muteState,
                                nickname: t.nickName,
                                profile: t.profile,
                                stream: t.stream
                            };
                            e.push(i)
                        }
                        return e
                    }
                    async startSub(e) {
                        n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.sub,
                            gfi: this.getPtByGFI(e),
                            loc: "live.startSub",
                            desc: `gfi : ${e}`,
                            user: this.user
                        });
                        try {
                            const t = this.getUserIDByGFI(e);
                            this.peers.has(t) && this.peers.get(t).close(), this.peers.set(t, new u.default({
                                peerType: a.PeerType.LISTENER,
                                role: this.getPtByGFI(e) === a.GFIType.DJ ? a.PeerType.DJ : a.PeerType.GUEST,
                                socket: this.socket,
                                isPub: !1,
                                gfi: e,
                                iceServers: this.iceServers,
                                relayMode: this.relayMode
                            }));
                            const s = {
                                type: a.MessageType.ENTER,
                                person: this.user.isDJ() ? a.PersonType.OWNER : a.PersonType.PARTICIPNT,
                                pt: a.PeerRole.SUB,
                                gfi: e,
                                tx: (0, o.v4)()
                            };
                            this.send(s), await this.asyncResponse.waitSingCompleted()
                        } catch (e) {
                            throw new Error(e.message)
                        }
                    }
                    send(e, t = !0) {
                        if (1 !== this.socket.OPEN) return !1;
                        e.payload && (e.payload = JSON.stringify(e.payload));
                        const s = JSON.stringify(e);
                        return n.default.trace({
                            type: "",
                            loc: "live.send",
                            desc: JSON.stringify(e)
                        }, t), this.socket.send(s), !0
                    }
                    async getPublishers() {
                        switch (this.send({
                            type: a.MessageType.ROOM,
                            cmd: a.CommandType.PUBLISHERS,
                            payload: {
                                detail: !0
                            },
                            tx: (0, o.v4)()
                        }), await this.asyncResponse.waitPublishers()) {
                            case a.ResStatus.EMPTY:
                                throw new Error("live.getPublishers : Empty Pub List");
                            case a.ResStatus.TIMEOUT:
                                throw new Error("live.getPublishers : Timeout");
                            default:
                                return
                        }
                    }
                    async createRoom() {
                        this.roomToken = await c.default.GetRoomToken({
                            tag: this.user.id(),
                            singApi: this.singApi,
                            liveID: this.liveID,
                            cast: this.cast,
                            sid: this.streamID
                        }), this.sessionToken = await c.default.GetSessionToken({
                            tag: this.user.id(),
                            singApi: this.singApi,
                            liveID: this.liveID,
                            roomToken: this.roomToken,
                            iow: !0
                        }), await this.createWebSocket()
                    }
                    async connectRoom() {
                        this.sessionToken = await c.default.GetSessionToken({
                            tag: this.user.id(),
                            singApi: this.singApi,
                            liveID: this.liveID,
                            roomToken: this.roomToken,
                            iow: !1
                        }), await this.createWebSocket()
                    }
                    async createWebSocket(e = "") {
                        e && (this.sessionToken = e);
                        try {
                            if (this.socket = new WebSocket(`${this.wsUrl}/ws/${this.sessionToken}`), this.initSocketEvent(), !await this.asyncResponse.waitOpenSocket(this.socket)) throw new Error("asyncResponse.waitOpenSocket : Timeout")
                        } catch (e) {
                            throw new Error(`live.createWebSocket : ${e.message}`)
                        }
                    }
                    async getIceServers() {
                        if (this.send({
                                type: a.MessageType.CREDENTIAL,
                                code: this.countryCode,
                                force_relay: this.relayMode,
                                tx: (0, o.v4)()
                            }), !await this.asyncResponse.waitIceServers()) throw new Error("live.getIceServers : Timeout")
                    }
                    onMsgService(e) {
                        this.asyncResponse.responseLiveAPI = e
                    }
                    onMsgMsg(e) {
                        let t = JSON.parse(e.payload);
                        switch (t.from = e.from, t.to = e.to, t.cmd) {
                            case a.CommandType.ADD_LISTENER:
                                this.callback.addListener(e);
                                break;
                            case a.CommandType.ASK_GUEST:
                                this.callback.ReceiveAskGuest(e);
                                break;
                            case a.CommandType.CANCEL_ASK_GUEST:
                                this.callback.ReceiveCancelAskGuest(e.from);
                                break;
                            case a.CommandType.OK_GUEST:
                                this.callback.ReceiveOKGuest(e);
                                break;
                            case a.CommandType.REJECT_GUEST:
                                this.callback.ReceiveRejectGuest(e);
                                break;
                            case a.CommandType.INVITE:
                                this.callback.ReceiveInvite(e);
                                break;
                            case a.CommandType.CANCEL_INVITE:
                                this.callback.ReceiveCancelInvite(e);
                                break;
                            case a.CommandType.REJECT_INVITE:
                                this.callback.ReceiveRejectInvite(e);
                                break;
                            case a.CommandType.KICK_GUEST:
                                t.target === this.user.id() ? this.callback.ReceiveKickGuest(e) : (this.peers.has(t.target) && (this.peers.get(t.target).close(), this.peers.delete(t.target)), this.callback.removeGuest({
                                    userID: t.target
                                }));
                                break;
                            case a.CommandType.GET_ASK_GUEST_LIST:
                                null == t.list && (t.list = [], e.payload = JSON.stringify(t)), this.callback.ReceiveGetAskGuestList(e);
                                break;
                            case a.CommandType.GET_GUEST_LIST:
                                e.payload = this.ReceiveGetGuestList(t), this.callback.ReceiveGetGuestList(e);
                                break;
                            case a.CommandType.GET_INVITE_LIST:
                                null == t.list && (t.list = [], e.payload = JSON.stringify(t)), this.callback.ReceiveGetInviteList(e);
                                break;
                            case a.CommandType.MUTE_GUEST:
                                this.callback.ReceiveMuteGuest(e);
                                break;
                            case a.CommandType.UNMUTE_GUEST:
                                this.callback.ReceiveUnmuteGuest(e);
                                break;
                            default:
                                this.callback.eventMsg(e)
                        }
                    }
                    ReceiveGetGuestList(e) {
                        var t;
                        if (null == e.list) return e.list = [], e;
                        const s = [];
                        for (const i of e.list) {
                            const e = {
                                userID: i.id,
                                nickname: i.nickname,
                                profile: i.profile_url,
                                isMute: i.mute,
                                stream: (null === (t = this.getPeerByUserID(i.id)) || void 0 === t ? void 0 : t.stream) || null
                            };
                            s.push(e)
                        }
                        return e.list = s, e
                    }
                    async onMsgRoom(e) {
                        if (e.msg === a.MessageMsg.PUBLISHERS) {
                            if (!e.result) return void(this.asyncResponse.responsePublishers.status = a.ResStatus.EMPTY);
                            this.asyncResponse.responsePublishers.pubs = e.result, this.asyncResponse.responsePublishers.status = a.ResStatus.OK
                        }
                    }
                    async loopStartSub() {
                        try {
                            for (let e of this.asyncResponse.responsePublishers.pubs) {
                                if (this.user.id() === e.user_id) {
                                    this.getSelfPeer().muteState = e.mute;
                                    continue
                                }
                                await this.startSub(e.gfi);
                                const t = this.getPeerByGFI(e.gfi);
                                t.muteState = e.mute, t.nickName = e.nickname, t.profile = e.profile
                            }
                        } catch (e) {
                            throw new Error(`live.loopStartSub : ${e.message}`)
                        }
                    }
                    onMsgCredential(e) {
                        this.iceServers = [JSON.parse(e.response)], this.asyncResponse.responseCredentialOK = !0
                    }
                    async onMsgEnter(e) {
                        n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.enter,
                            loc: "live.onMsgEnter",
                            desc: `${JSON.stringify(e)}`
                        });
                        try {
                            if (!this.user) throw new Error("user is undefined");
                            const t = this.peers.get(this.user.id());
                            t.msid = e.msid, this.user.isDJ() ? (t.gfi = e.ogfi, this.ogfi = e.ogfi) : t.gfi = e.gfi, this.send(await t.sendOffer())
                        } catch (e) {
                            n.default.err({
                                type: a.LogAttr.type.msg,
                                loc: "live.onMsgEnter",
                                desc: e.message
                            })
                        }
                    }
                    async onMsgOffer(e) {
                        n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.offer,
                            loc: "live.onMsgOffer",
                            desc: `${JSON.stringify(e)}`
                        });
                        try {
                            const t = this.getPeerByGFI(e.gfi);
                            t.msid = e.msid, await t.setOffer(e), this.send(await t.sendAnswer())
                        } catch (e) {
                            n.default.err({
                                type: a.LogAttr.type.msg,
                                loc: "live.onMsgOffer",
                                desc: e.message
                            })
                        }
                    }
                    async onMsgAnswer(e) {
                        n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.answer,
                            loc: "live.onMsgAnswer",
                            desc: `${JSON.stringify(e)}`
                        });
                        try {
                            const t = this.peers.get(this.user.id());
                            await t.setAnswer(e)
                        } catch (e) {
                            n.default.err({
                                type: a.LogAttr.type.msg,
                                loc: "live.onMsgAnswer",
                                desc: e.message
                            })
                        }
                    }
                    onMsgSub(e) {
                        n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.sub,
                            loc: "live.onMsgSub",
                            desc: `${JSON.stringify(e)}`
                        }), this.getPeerByGFI(e.gfi).ready = !0
                    }
                    async onMsgOp(e) {
                        switch (n.default.info({
                            actor: this.user.isDJ() ? a.LogAttr.actor.DJ : a.LogAttr.actor.Listener,
                            type: a.LogAttr.type.op,
                            msg: e.msg,
                            loc: "live.onMsgOp",
                            desc: `${JSON.stringify(e)}`
                        }), e.msg) {
                            case a.MessageMsg.MUTE:
                            case a.MessageMsg.UNMUTE:
                                this.resMuteStatus(e);
                                break;
                            case a.MessageMsg.GUEST:
                                if (this.status === a.LiveStatus.Moving) return;
                                setTimeout((() => this.addRealTimePub(e)), 1e3);
                                break;
                            case a.MessageMsg.DISCONNECTED:
                                this.disconByOP(e);
                                break;
                            case a.MessageMsg.UNPUB:
                                this.unpubByOP(e);
                                break;
                            case a.MessageMsg.DESTROY:
                                this.destroyByOP(e);
                                break;
                            case a.MessageMsg.MOVING:
                                this.movingByOP();
                                break;
                            case a.MessageMsg.MOVED:
                                if (this.isDJ()) return;
                                await this.movePlayByAnything(e)
                        }
                    }
                    onPubResponse(e) {}
                    destroyByOP(e) {
                        this.status = a.LiveStatus.Moving, this.move = new d.default({
                            peers: this.peers
                        }), this.callback.move({
                            liveStatus: this.status,
                            moveType: a.MoveType.Destroy,
                            statusMsg: "moving server destroy"
                        })
                    }
                    movingByOP() {
                        this.status = a.LiveStatus.Moving, this.move = new h.default({
                            peers: this.peers
                        }), this.callback.move({
                            liveStatus: this.status,
                            moveType: a.MoveType.SFU,
                            statusMsg: "moving server SFU"
                        }), this.moveDJBySFU()
                    }
                    onMsgExit(e) {
                        this.callback.exit({
                            userID: e.tag
                        })
                    }
                    onMsgCandidate(e) {}
                    onMsgEvent(e) {
                        200 === e.status ? e.response === a.MessageType.SING_COMPLETED && (this.asyncResponse.responseSingCompletedOK = !0) : e.case === a.MessageType.PUBLISHER_EXCEEDED && (this.asyncResponse.responseExceededPub = !0)
                    }
                    resMuteStatus(e) {
                        const t = this.peers.get(e.tag);
                        t && (t.muteState = e.msg === a.MessageMsg.MUTE, this.callback.getMuteState({
                            userID: e.tag,
                            isMute: t.muteState
                        }))
                    }
                    onMsgDefault(e) {
                        e.response && e.response.includes("No such room") && this.callback.notice({
                            type: "end-dj",
                            msg: "This is a closed broadcast"
                        })
                    }
                    disconByOP(e) {
                        this.callback.disconnected(e)
                    }
                    unpubByOP(e) {
                        this.status !== a.LiveStatus.Moving && (this.peers.has(e.tag) && (this.peers.get(e.tag).close(), this.peers.delete(e.tag)), this.callback.removeGuest({
                            userID: e.tag
                        }))
                    }
                    async addRealTimePub(e) {
                        (this.isLiveCall || this.getDjID() === this.getUserIDByGFI(e.gfi)) && await this.callbackAddRealTimePub(e)
                    }
                    async callbackAddRealTimePub(e) {
                        let t = null;
                        try {
                            const s = e.tag,
                                i = JSON.parse(e.payload).result;
                            if (t = {
                                    type: "",
                                    userID: i.Guest ? i.Guest.id : s,
                                    nickname: i.Guest ? i.Guest.nickname : "",
                                    profile: i.Guest ? i.Guest.profile_url : "",
                                    isMute: i.Mute,
                                    stream: null,
                                    statusCode: i.StatusCode,
                                    status: i.Status
                                }, this.defenseWrongGuest(e)) {
                                const e = "WrongGuest";
                                throw t.statusCode = a.ResStatus.ERROR, t.status = e, new Error(e)
                            }
                            if (s !== this.user.id()) {
                                await this.startSub(e.gfi);
                                const i = this.peers.get(s);
                                t.type = i.role, t.stream = i.stream
                            }
                        } catch (t) {
                            n.default.err({
                                type: a.LogAttr.type.op,
                                msg: e.msg,
                                loc: "live.addRealTimePub",
                                desc: t.message
                            })
                        }
                        this.callback.addRealTimePub(t)
                    }
                    defenseWrongGuest(e) {
                        return this.getPtByGFI(e.gfi) === a.GFIType.DJ && this.getDjID() !== this.getUserIDByGFI(e.gfi)
                    }
                    initPeers() {
                        if (this.peers) {
                            for (let e of this.peers.values()) e.close();
                            this.peers.clear(), this.peers = new Map
                        }
                    }
                    initWebSocket() {
                        this.socket && (this.socket.close(), this.socket = null)
                    }
                    initResponse() {
                        this.asyncResponse && (this.asyncResponse.end(), this.asyncResponse = new g.default)
                    }
                    end() {
                        if (!this.socket) return;
                        let e = "";
                        const t = this.peers.get(this.user.id());
                        t && (e = t.gfi), this.user.isDJ() && (window.localStorage.removeItem(a.LocalStorageKey.SESSIONID), this.send({
                            type: a.MessageType.SERVICE,
                            service_name: a.ServiceName.LIVE,
                            service_cmd: a.ServiceCMD.UNPUBLISH,
                            live_id: this.liveID,
                            tx: (0, o.v4)()
                        })), this.send({
                            type: a.MessageType.EXIT,
                            tx: (0, o.v4)(),
                            gfi: e
                        }), this.close()
                    }
                    endGuest() {
                        const e = this.peers.get(this.user.id());
                        this.send({
                            type: a.MessageType.UNPUB,
                            msid: e.msid,
                            tx: (0, o.v4)()
                        })
                    }
                    close() {
                        this.status = a.LiveStatus.Ready, this.initResponse(), this.initPeers(), this.initWebSocket()
                    }
                    closeByDestroy() {
                        this.closeByMove()
                    }
                    closeByMoveSFU() {
                        this.closeByMove()
                    }
                    closeByMove() {
                        this.initPeers(), this.initResponse()
                    }
                    async getAudioLevelPeer(e) {
                        const t = await e.peerConnection.getStats();
                        for (let e of t.values())
                            if (e.audioLevel) return e.audioLevel;
                        return 0
                    }
                    getSelfPeer() {
                        return this.peers.get(this.user.id())
                    }
                    isPub() {
                        return !!this.peers.has(this.user.id()) && this.peers.get(this.user.id()).isPub
                    }
                    getDjID() {
                        return this.djID
                    }
                    isDJ() {
                        return this.user.isDJ()
                    }
                    getPeerByUserID(e) {
                        return this.peers.get(e)
                    }
                    getPeerByGFI(e) {
                        return this.peers.get(this.getUserIDByGFI(e))
                    }
                    getUserIDByGFI(e) {
                        return e.split(".")[0]
                    }
                    getPtByGFI(e) {
                        return e.split(".")[1]
                    }
                    askGuest(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            to: this.getDjID(),
                            from: this.user.id(),
                            tx: (0, o.v4)(),
                            payload: {
                                cmd: a.CommandType.ASK_GUEST,
                                userInfo: e
                            }
                        })
                    }
                    cancelAskGuest() {
                        this.send({
                            type: a.MessageType.MSG,
                            to: this.getDjID(),
                            from: this.user.id(),
                            tx: (0, o.v4)(),
                            payload: {
                                cmd: a.CommandType.CANCEL_ASK_GUEST
                            }
                        })
                    }
                    permissionGuest(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            to: e,
                            from: this.user.id(),
                            tx: (0, o.v4)(),
                            payload: {
                                cmd: a.CommandType.OK_GUEST
                            }
                        })
                    }
                    rejectGuest(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            to: e,
                            from: this.user.id(),
                            tx: (0, o.v4)(),
                            payload: {
                                cmd: a.CommandType.REJECT_GUEST
                            }
                        })
                    }
                    mute() {
                        const e = this.getSelfPeer();
                        e && (e.isMute() || (e.mute(), this.send({
                            type: a.MessageType.ATTRIBUTE,
                            cmd: a.CommandType.MUTE,
                            to: "",
                            tx: (0, o.v4)()
                        })))
                    }
                    unmute() {
                        const e = this.getSelfPeer();
                        e && e.isMute() && (e.unmute(), this.send({
                            type: a.MessageType.ATTRIBUTE,
                            cmd: a.CommandType.UNMUTE,
                            to: "",
                            tx: (0, o.v4)()
                        }))
                    }
                    muteGuest(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            to: e,
                            from: this.user.id(),
                            payload: {
                                cmd: a.CommandType.MUTE_GUEST
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    unmuteGuest(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            to: e,
                            from: this.user.id(),
                            payload: {
                                cmd: a.CommandType.UNMUTE_GUEST
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    invite(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            to: e.id,
                            payload: {
                                cmd: a.CommandType.INVITE,
                                userInfo: e
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    cancelInvite(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            to: e,
                            payload: {
                                cmd: a.CommandType.CANCEL_INVITE
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    rejectInvite(e) {
                        return this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            to: e,
                            payload: {
                                cmd: a.CommandType.REJECT_INVITE
                            },
                            tx: (0, o.v4)()
                        }), !0
                    }
                    getInviteList() {
                        this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            to: this.user.id(),
                            payload: {
                                cmd: a.CommandType.GET_INVITE_LIST
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    kickGuest(e) {
                        this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            payload: {
                                cmd: a.CommandType.KICK_GUEST,
                                target: e
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    getAskGuestList(e, t) {
                        this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            to: this.user.id(),
                            payload: {
                                cmd: a.CommandType.GET_ASK_GUEST_LIST,
                                page: e,
                                limit: t
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    getGuestList() {
                        this.send({
                            type: a.MessageType.MSG,
                            from: this.user.id(),
                            to: this.user.id(),
                            payload: {
                                cmd: a.CommandType.GET_GUEST_LIST
                            },
                            tx: (0, o.v4)()
                        })
                    }
                    isSelfMute() {
                        return this.peers.get(this.user.id()).isMute()
                    }
                    getLiveID() {
                        return this.liveID
                    }
                    getRoomToken() {
                        return this.roomToken
                    }
                    async changeMicConnPeer(e) {
                        const t = this.getSelfPeer();
                        if (!t) return;
                        if (!t.isPub) return;
                        const s = t.isMute();
                        t.getSender(e), s && (t.mute(), this.send({
                            type: a.MessageType.ATTRIBUTE,
                            cmd: a.CommandType.MUTE,
                            to: "",
                            tx: (0, o.v4)()
                        }))
                    }
                    setLiveStatus(e) {
                        this.status = e
                    }
                }
            },
            777: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = s(630),
                    o = i(s(518));
                var r;
                ! function(e) {
                    e[e.Error = 0] = "Error", e[e.Info = 1] = "Info", e[e.Trace = 2] = "Trace", e[e.Debug = 3] = "Debug"
                }(r || (r = {})), t.default = new class {
                    constructor() {}
                    setLevel(e) {
                        e === n.SingInfoMode.DEV ? this.level = r.Debug : this.level = r.Info
                    }
                    setSingInfo(e, t) {
                        this.user = e, this.liveID = t
                    }
                    setCallback(e) {
                        this.log = e
                    }
                    err(e) {
                        this.Send(n.LogLevel.ERROR, e)
                    }
                    info(e) {
                        this.level < r.Info || this.Send(n.LogLevel.INFO, e)
                    }
                    trace(e, t = !0) {
                        this.level < r.Trace || this.Send(n.LogLevel.TRACE, e, t)
                    }
                    debug(e) {
                        this.level < r.Debug || this.Send(n.LogLevel.DEBUG, e)
                    }
                    Send(e, t, s = !0) {
                        s && (t.status_description = this.overCutDescription(e, t), this.log(Object.assign(Object.assign({
                            event_type: `live_sing_${e}`,
                            category: "sing"
                        }, t), {
                            status_description: t.status_description,
                            loc: t.loc,
                            live_id: this.liveID,
                            user_id: this.user ? this.user.id() : "",
                            user_tag: this.user ? this.user.tag() : "",
                            tx: this.user ? this.user.transactionID() : "",
                            time: (new Date).toString()
                        })))
                    }
                    overCutDescription(e, t) {
                        let s = e === n.LogLevel.TRACE ? t.desc : o.default.textLengthOverCut(t.desc);
                        return delete t.desc, s
                    }
                }
            },
            121: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.LogError = t.LogInfo = void 0;
                const i = s(630);
                t.LogInfo = {
                    StartDJ: {
                        actor: i.LogAttr.actor.DJ,
                        type: i.LogAttr.type.start,
                        loc: "sing.startDJ",
                        desc: "Broadcasting start completed"
                    },
                    StartListener: {
                        actor: i.LogAttr.actor.Listener,
                        type: i.LogAttr.type.start,
                        loc: "sing.play",
                        desc: "play start completed"
                    }
                }, t.LogError = {
                    StartDJ: {
                        actor: i.LogAttr.actor.DJ,
                        type: i.LogAttr.type.start,
                        loc: "sing.startDJ"
                    },
                    StartListener: {
                        actor: i.LogAttr.actor.Listener,
                        type: i.LogAttr.type.start,
                        loc: "sing.play"
                    },
                    ReEnterDJ: {
                        actor: i.LogAttr.actor.DJ,
                        type: i.LogAttr.type.start,
                        loc: "sing.reEnterDJ"
                    }
                }
            },
            599: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = i(s(777));
                class o {
                    constructor() {}
                    static async build(e) {
                        const t = new o;
                        return t.setAudioContext(), await t.setMic(e), t
                    }
                    setAudioContext() {
                        try {
                            this.audioContext = new(window.AudioContext || window.webkitAudioContext)
                        } catch (e) {
                            throw new Error("Microphone.setMicContext() : The web audio API is not supported by your browser")
                        }
                    }
                    static async getDevices() {
                        try {
                            return (await navigator.mediaDevices.enumerateDevices()).filter((e => "audioinput" === e.kind && "default" !== e.deviceId))
                        } catch (e) {
                            throw new Error(`microphone.getDevices : ${e.message}`)
                        }
                    }
                    async changeMic(e) {
                        return await this.setMic(e), this.stream.getAudioTracks()[0]
                    }
                    async getUserMedia(e) {
                        n.default.debug({
                            type: "Media",
                            loc: "microphone",
                            desc: `getUserMedia : ${JSON.stringify(e)}`
                        }), this.musicMode = e.musicMode || !1;
                        const t = {
                            audio: {
                                echoCancellation: !this.musicMode,
                                noiseSuppression: !this.musicMode,
                                autoGainControl: !1,
                                channelCount: 2,
                                sampleRate: 48e3,
                                sampleSize: 16,
                                deviceId: e.audioInputDeviceId ? {
                                    exact: e.audioInputDeviceId
                                } : void 0
                            },
                            video: !1
                        };
                        return new Promise(((e, s) => {
                            navigator.mediaDevices && navigator.mediaDevices.getUserMedia ? navigator.mediaDevices.getUserMedia(t).then((t => e(t))).catch((e => s(e))) : (navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia)(t, (t => e(t)), (e => s(e)))
                        }))
                    }
                    async setMic(e) {
                        try {
                            const t = this.audioContext.state;
                            "suspended" != t && "running" === t || await this.audioContext.resume(), n.default.info({
                                type: "Microphone",
                                loc: "Microphone.setMic",
                                desc: `audio state : ${this.audioContext.state}`
                            }), this.stream = await this.getUserMedia(e), this.micSource = this.audioContext.createMediaStreamSource(this.stream), this.micSourceDestination = this.audioContext.createMediaStreamDestination(), this.gainNode = this.audioContext.createGain(), this.gainNode.gain.value = .5, this.gainNode.channelCount = 2, this.analyser = this.audioContext.createAnalyser(), this.analyser.fftSize = 512, this.analyser.minDecibels = -127, this.analyser.maxDecibels = 0, this.analyser.smoothingTimeConstant = .4, this.gainNode.connect(this.micSourceDestination), this.micSource.connect(this.analyser), this.micSource.connect(this.gainNode)
                        } catch (e) {
                            throw new Error(`microphone.setMic : ${e.message}`)
                        }
                    }
                    getMicMeterLevel() {
                        if (!this.audioContext) return n.default.info({
                            type: "Microphone",
                            loc: "Microphone.getMicMeterLevel",
                            desc: "not this.audioContext"
                        }), "0";
                        if (!this.analyser) return "0";
                        const e = new Uint8Array(this.analyser.frequencyBinCount);
                        this.analyser.getByteFrequencyData(e);
                        let t = 0;
                        for (const s of e) t += s;
                        return (t / e.length * 100 / 127).toFixed(0)
                    }
                    setMicVolume(e) {
                        this.gainNode.gain.value = e
                    }
                    isMicStream() {
                        return !!this.micSourceDestination
                    }
                    getMicStream() {
                        if (!this.isMicStream()) throw new Error("microphone.getMicStream : stream is false");
                        return this.micSourceDestination.stream
                    }
                    getMusicMode() {
                        return this.musicMode
                    }
                    async offMic() {
                        this.audioContext.close(), this.stream.getTracks().forEach((e => {
                            e.stop()
                        }))
                    }
                }
                t.default = o
            },
            603: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = s(630),
                    o = i(s(284));
                class r extends o.default {
                    constructor(e) {
                        super(e), super.setMoveType(n.MoveType.Destroy)
                    }
                }
                t.default = r
            },
            284: (e, t) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = class {
                    constructor(e) {
                        this.peers = new Map;
                        for (const [t, s] of e.peers) this.peers.set(t, {
                            muteState: s.muteState
                        })
                    }
                    getPeer(e) {
                        return this.peers.get(e)
                    }
                    getMoveType() {
                        return this.moveType
                    }
                    setMoveType(e) {
                        return this.moveType = e
                    }
                }
            },
            145: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = s(630),
                    o = i(s(284));
                class r extends o.default {
                    constructor(e) {
                        super(e), super.setMoveType(n.MoveType.SFU)
                    }
                }
                t.default = r
            },
            835: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = s(831),
                    o = i(s(777)),
                    r = s(630);
                t.default = class {
                    constructor(e) {
                        this.isPub = !1, this.ready = !1, this.isAutoPlay = !1, this.muteState = !1, this.peerType = e.peerType, this.role = e.role, this.socket = e.socket, this.isPub = e.isPub, this.gfi = e.gfi, this.msid = e.msid;
                        const t = {
                            iceServers: e.iceServers
                        };
                        o.default.trace({
                            type: "",
                            loc: "peer.iceServers",
                            desc: JSON.stringify(e.iceServers)
                        });
                        const s = window.RTCPeerConnection || (null === window || void 0 === window ? void 0 : window.webkitRTCPeerConnection);
                        this.peerConnection = new s(t), e.mic && (this.audioTrack = e.mic.getMicStream().getAudioTracks()[0], this.peerConnection.addTrack(this.audioTrack, e.mic.getMicStream())), this.setEvent()
                    }
                    async sendOffer() {
                        try {
                            const e = await this.peerConnection.createOffer();
                            return await this.peerConnection.setLocalDescription(e), {
                                type: r.MessageType.PUB,
                                sdp: e,
                                msid: this.msid,
                                tx: (0, n.v4)()
                            }
                        } catch (e) {
                            throw e
                        }
                    }
                    async sendAnswer() {
                        try {
                            const e = await this.peerConnection.createAnswer();
                            return await this.peerConnection.setLocalDescription(e), {
                                type: r.MessageType.SUB,
                                sdp: e,
                                msid: this.msid,
                                tx: (0, n.v4)()
                            }
                        } catch (e) {
                            throw e
                        }
                    }
                    async setAnswer(e) {
                        await this.peerConnection.setRemoteDescription(e)
                    }
                    async setOffer(e) {
                        await this.peerConnection.setRemoteDescription(e)
                    }
                    setEvent() {
                        this.peerConnection.onicecandidate = e => {
                            o.default.trace({
                                type: "PeerIcecandidate",
                                loc: "peer.peerConnection.onicecandidate",
                                desc: JSON.stringify(e.candidate)
                            }), 1 === this.socket.OPEN && (e.candidate ? (this.socket.send(JSON.stringify({
                                type: r.MessageType.CANIDATE,
                                msid: this.msid,
                                sdpMLineIndex: e.candidate.sdpMLineIndex,
                                sdpMid: e.candidate.sdpMid,
                                candidate: e.candidate.candidate,
                                tx: (0, n.v4)()
                            })), this.peerConnection.remoteDescription && this.peerConnection.addIceCandidate(new RTCIceCandidate(e.candidate)).catch((e => {
                                o.default.trace({
                                    type: "PeerIcecandidate",
                                    loc: "peer.peerConnection.addIceCandidate",
                                    desc: e.name
                                })
                            }))) : this.socket.send(JSON.stringify({
                                type: r.MessageType.CANIDATE,
                                completed: !0,
                                msid: this.msid,
                                tx: (0, n.v4)()
                            })))
                        }, this.peerConnection.ontrack = e => {
                            this.stream = e.streams[0]
                        }, this.peerConnection.oniceconnectionstatechange = e => {
                            switch (o.default.trace({
                                type: "PeerIceConnectionState",
                                loc: "peer.peerConnection.oniceconnectionstatechange",
                                desc: this.peerConnection.iceConnectionState
                            }), this.peerConnection.iceConnectionState) {
                                case "failed":
                                    o.default.info({
                                        type: r.LogAttr.type.iceConnState,
                                        loc: "oniceconnectionstatechange",
                                        desc: "failed"
                                    });
                                    break;
                                case "disconnected":
                                    o.default.info({
                                        type: r.LogAttr.type.iceConnState,
                                        loc: "oniceconnectionstatechange",
                                        desc: "disconnected"
                                    });
                                    break;
                                case "closed":
                                    o.default.info({
                                        type: r.LogAttr.type.iceConnState,
                                        loc: "oniceconnectionstatechange",
                                        desc: "closed"
                                    })
                            }
                        }, this.peerConnection.onicegatheringstatechange = e => {
                            o.default.trace({
                                type: "PeerIceGatheringState",
                                loc: "peer.peerConnection.onicegatheringstatechange",
                                desc: this.peerConnection.iceGatheringState
                            })
                        }, this.peerConnection.onconnectionstatechange = e => {
                            o.default.trace({
                                type: "PeerConnectionstate",
                                loc: "peer.peerConnection.onconnectionstatechange",
                                desc: this.peerConnection.connectionState
                            })
                        }
                    }
                    close() {
                        this.peerConnection.close(), this.peerConnection = null, this.stream = null, this.audioTrack = null, this.socket = null, o.default.trace({
                            type: "",
                            loc: "close",
                            desc: "peer close"
                        })
                    }
                    isMute() {
                        return !this.audioTrack.enabled
                    }
                    mute() {
                        this.muteState = !0, this.audioTrack.enabled = !1
                    }
                    unmute() {
                        this.muteState = !1, this.audioTrack.enabled = !0
                    }
                    getSender(e) {
                        this.audioTrack = e, this.peerConnection.getSenders().find((e => e.track.kind === this.audioTrack.kind)).replaceTrack(this.audioTrack)
                    }
                    addStereo(e) {
                        let t, s = e.split("\r\n"),
                            i = this.findLine(s, "a=rtpmap", "opus/48000/2");
                        i && (t = this.getCodecPayloadType(s[i]));
                        const n = this.findLine(s, "a=fmtp:" + t.toString());
                        return null === n ? e : (s[n] = s[n].concat("; stereo=1; sprop-stereo=1; maxaveragebitrate=262144; x-google-min-bitrate=256; x-google-max-bitrate=256; cbr=1;"), e = s.join("\r\n"))
                    }
                    findLine(e, t, s) {
                        return this.findLineInRange(e, 0, -1, t, s)
                    }
                    findLineInRange(e, t, s, i, n) {
                        const o = -1 !== s ? s : e.length;
                        for (let s = t; s < o; ++s)
                            if (0 === e[s].indexOf(i) && (!n || -1 !== e[s].toLowerCase().indexOf(n.toLowerCase()))) return s;
                        return null
                    }
                    getCodecPayloadType(e) {
                        let t = new RegExp("a=rtpmap:(\\d+) \\w+\\/\\d+"),
                            s = e.match(t);
                        return s && 2 === s.length ? s[1] : null
                    }
                    setMediaBitrate(e, t, s) {
                        let i = e.split("\n"),
                            n = -1;
                        for (let e = 0; e < i.length; e++)
                            if (0 === i[e].indexOf("m=" + t)) {
                                n = e;
                                break
                            }
                        if (-1 === n) return console.debug("Could not find the m line for", t), e;
                        for (console.debug("Found the m line for", t, "at line", n), n++; 0 === i[n].indexOf("i=") || 0 === i[n].indexOf("c=");) n++;
                        if (0 === i[n].indexOf("b")) return console.debug("Replaced b line at line", n), i[n] = "b=AS:" + s, i.join("\n");
                        console.debug("Adding new b line before line", n);
                        let o = i.slice(0, n);
                        return o.push("b=AS:" + s), o = o.concat(i.slice(n, i.length)), o.join("\n")
                    }
                }
            },
            652: function(e, t, s) {
                var i = this && this.__importDefault || function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                };
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const n = i(s(765)),
                    o = i(s(777)),
                    r = i(s(444)),
                    a = s(831),
                    c = i(s(599)),
                    u = i(s(897)),
                    l = s(630),
                    d = s(121),
                    h = i(s(518));
                t.default = class {
                    constructor(e) {
                        r.default.ExecMode = e.execMode || l.SingInfoMode.PRD, o.default.setLevel(r.default.ExecMode), o.default.setCallback(e.callback.log), this.singInfo = new r.default, this.singInfo.countryCode = e.countryCode, this.singInfo.wsUrl = e.domains.websocket_url, this.singInfo.singApi = e.domains.sing_api, this.singInfo.callback = e.callback, this.singInfo.status = l.SingStatus.Ready, this.singInfo.playProcess = !1, r.default.maxPub = e.maxPub ? e.maxPub : 5, r.default.maxInvite = e.maxInvite ? e.maxInvite : 20, this.initBrowserEvent()
                    }
                    singVer() {
                        return r.default.SING_VERSION
                    }
                    sdkVer() {
                        return r.default.SDK_VERSION
                    }
                    initBrowserEvent() {
                        window.onoffline = e => {
                            this.singInfo.callback.notice("The network connection has been lost."), this.live && this.live.socket && this.closeWebSocket()
                        }, navigator.mediaDevices.ondevicechange = async e => {
                            await this.onDevicechange()
                        }
                    }
                    async onDevicechange() {
                        try {
                            if (!this.microphone) return;
                            const e = await this.getAudioInputDevices(),
                                t = e[0].deviceId,
                                s = await this.microphone.changeMic({
                                    musicMode: this.microphone.getMusicMode(),
                                    audioInputDeviceId: t
                                });
                            if (this.singInfo.status === l.SingStatus.Active) {
                                if (!this.live) return;
                                const e = this.live.isSelfMute();
                                await this.live.changeMicConnPeer(s), e ? this.live.mute() : this.live.unmute
                            }
                            this.singInfo.callback.changeAudioInput(e)
                        } catch (e) {
                            o.default.err({
                                type: l.LogAttr.type.device,
                                code: "10",
                                loc: "initBrowserEvent",
                                desc: `ondevicechange : ${e.message}`
                            })
                        }
                    }
                    async getAudioInputDevices() {
                        return await c.default.getDevices()
                    }
                    async setDeviceConstraints(e) {
                        let t;
                        try {
                            if (!this.microphone) return await this.getInstanceMicrophone(e), {
                                status: l.ResStatus.OK,
                                statusMsg: "ok"
                            };
                            const s = await this.microphone.changeMic(e);
                            this.singInfo.status === l.SingStatus.Active && await this.live.changeMicConnPeer(s), t = {
                                status: l.ResStatus.OK,
                                statusMsg: "ok"
                            }
                        } catch (e) {
                            o.default.err(Object.assign({
                                actor: l.LogAttr.actor.DJ,
                                type: l.LogAttr.type.device,
                                loc: "sing.setDeviceConstraints",
                                desc: e.message
                            }, this.singInfo.log())), t = {
                                status: l.ResStatus.ERROR,
                                statusMsg: e.message
                            }
                        }
                        return t
                    }
                    async getInstanceMicrophone(e) {
                        try {
                            this.microphone = await c.default.build(e)
                        } catch (e) {
                            throw new Error(e)
                        }
                    }
                    getAudioInputMeter() {
                        return this.microphone ? this.microphone.getMicMeterLevel() : "0"
                    }
                    setAudioInputVolume(e) {
                        e <= 0 ? this.mute() : this.unmute(), this.microphone.setMicVolume(e)
                    }
                    getInstanceLive(e) {
                        this.close(), this.live = new u.default(e)
                    }
                    async startDJ(e) {
                        let t;
                        try {
                            if (!(e.liveID && e.userID && e.userTag)) throw new Error(`sing.getInstanceLive : param : ${JSON.stringify(e)}`);
                            this.getInstanceLive({
                                countryCode: this.singInfo.countryCode,
                                singApi: this.singInfo.singApi,
                                wsUrl: this.singInfo.wsUrl,
                                front: e,
                                isDJ: !0,
                                callback: this.singInfo.callback,
                                mic: this.microphone
                            }), await this.live.createRoom(), await this.live.getIceServers(), await this.live.startPub({
                                person: l.PersonType.OWNER,
                                etype: l.EnterType.NEW
                            }), await this.live.noticeLiveAPIPublish(), this.singInfo.status = l.SingStatus.Active, window.localStorage.setItem(l.LocalStorageKey.SESSIONID, this.live.sessionToken), t = {
                                status: l.ResStatus.OK,
                                statusMsg: "ok"
                            }, o.default.info(Object.assign(Object.assign({}, d.LogInfo.StartDJ), this.singInfo.log()))
                        } catch (e) {
                            this.close(), t = {
                                status: l.ResStatus.ERROR,
                                statusMsg: e.message
                            }, o.default.err(Object.assign(Object.assign(Object.assign({}, d.LogError.StartDJ), {
                                desc: JSON.stringify(t.statusMsg)
                            }), this.singInfo.log()))
                        }
                        return t
                    }
                    async reEnterDJ(e) {
                        let t;
                        try {
                            if (!(e.liveID && e.userID && e.userTag && e.token)) throw new Error(`sing.getInstanceLive : param : ${JSON.stringify(e)}`);
                            this.getInstanceLive({
                                countryCode: this.singInfo.countryCode,
                                singApi: this.singInfo.singApi,
                                wsUrl: this.singInfo.wsUrl,
                                front: e,
                                isDJ: !0,
                                callback: this.singInfo.callback,
                                mic: this.microphone
                            });
                            const s = window.localStorage.getItem(l.LocalStorageKey.SESSIONID);
                            if (!s) throw new Error("Empty sessionid");
                            await this.live.createWebSocket(s), await this.live.getIceServers(), await this.live.startPub({
                                person: l.PersonType.OWNER,
                                etype: l.EnterType.RECONNECT
                            }), await this.live.getPublishers(), await this.live.loopStartSub(), await this.live.noticeLiveAPIPublish();
                            const i = this.live.getPubListByPeers();
                            this.singInfo.status = l.SingStatus.Active, t = {
                                status: l.ResStatus.OK,
                                statusMsg: "ok",
                                result: i
                            }
                        } catch (e) {
                            this.close(), t = {
                                status: l.ResStatus.ERROR,
                                statusMsg: e.message
                            }, o.default.err(Object.assign(Object.assign(Object.assign({}, d.LogError.StartDJ), {
                                desc: JSON.stringify(t.statusMsg)
                            }), this.singInfo.log()))
                        }
                        return t
                    }
                    async play(e) {
                        if (this.live) return {
                            status: l.ResStatus.ING,
                            statusMsg: `live ${this.live.status}`
                        };
                        if (this.singInfo.playProcess) return {
                            status: l.ResStatus.ING,
                            statusMsg: "Play is in progress."
                        };
                        let t;
                        this.singInfo.playProcess = !0;
                        try {
                            if (!(e.userID && e.userTag && e.token)) throw new Error(`sing.getInstanceLive : param : ${JSON.stringify(e)}`);
                            const s = (0, n.default)(e.token).liveid;
                            this.getInstanceLive({
                                countryCode: this.singInfo.countryCode,
                                singApi: this.singInfo.singApi,
                                wsUrl: this.singInfo.wsUrl,
                                front: e,
                                liveID: s,
                                isDJ: !1,
                                callback: this.singInfo.callback
                            }), await this.live.connectRoom(), await this.live.getIceServers(), await this.live.getPublishers(), await this.live.loopStartSub();
                            const i = this.live.getPubListByPeers();
                            this.singInfo.status = l.SingStatus.Active, this.send({
                                type: l.MessageType.MSG,
                                from: e.userID,
                                payload: {
                                    cmd: l.CommandType.ADD_LISTENER,
                                    data: Object.assign(Object.assign({}, e.data), {
                                        userid: e.userID
                                    })
                                },
                                tx: (0, a.v4)()
                            }), t = {
                                status: l.ResStatus.OK,
                                statusMsg: "ok",
                                result: i
                            }, o.default.info(Object.assign(Object.assign({}, d.LogInfo.StartListener), this.singInfo.log()))
                        } catch (e) {
                            this.close(), t = {
                                status: l.ResStatus.ERROR,
                                statusMsg: e.message
                            }, o.default.err(Object.assign(Object.assign(Object.assign({}, d.LogError.StartListener), {
                                desc: JSON.stringify(e.message)
                            }), this.singInfo.log()))
                        }
                        return this.singInfo.playProcess = !1, t
                    }
                    async startGuest() {
                        let e;
                        try {
                            if (!this.microphone.isMicStream()) throw new Error("audioInputStream is undefined");
                            await this.live.startPub({
                                person: l.PersonType.PARTICIPNT,
                                etype: l.EnterType.NEW,
                                mic: this.microphone
                            }), this.mute(), e = {
                                status: l.ResStatus.OK,
                                statusMsg: "ok",
                                result: {
                                    mute: this.live.isSelfMute()
                                }
                            }
                        } catch (t) {
                            e = {
                                status: l.ResStatus.ERROR,
                                statusMsg: t.message
                            }, e = h.default.resultError(t.message), o.default.err({
                                type: l.LogAttr.type.guest,
                                code: "10",
                                loc: "sing.startGuest",
                                desc: `${t.message}`
                            })
                        }
                        return e
                    }
                    close() {
                        this.live && (this.singInfo.status = l.SingStatus.Ready, this.singInfo.playProcess = !1, this.live.close(), this.live = null)
                    }
                    closeWebSocket() {
                        this.live.socket.close()
                    }
                    end() {
                        this.live && (this.singInfo.status = l.SingStatus.Ready, this.singInfo.playProcess = !1, this.live.end(), this.live = null)
                    }
                    endGuest() {
                        this.live.endGuest()
                    }
                    async getAudioLevel() {
                        const e = [];
                        if (!this.live) return e;
                        if (!this.live.peers) return e;
                        for (let [t, s] of this.live.peers.entries()) {
                            let i = await this.live.getAudioLevelPeer(s);
                            e.push({
                                type: s.role,
                                userID: t,
                                level: (100 * i).toFixed(0)
                            })
                        }
                        return e
                    }
                    getDjID() {
                        return this.live.getDjID()
                    }
                    isDJ() {
                        return this.live.user.isDJ()
                    }
                    askGuest(e) {
                        this.live && this.live.askGuest(e)
                    }
                    cancelAskGuest() {
                        this.live && this.live.cancelAskGuest()
                    }
                    permissionGuest(e) {
                        this.live && this.live.permissionGuest(e)
                    }
                    rejectGuest(e) {
                        this.live && this.live.rejectGuest(e)
                    }
                    send(e) {
                        this.live && this.live.send(e)
                    }
                    mute() {
                        this.live && this.live.mute()
                    }
                    unmute() {
                        this.live && this.live.unmute()
                    }
                    muteGuest(e) {
                        this.live && this.live.muteGuest(e)
                    }
                    unmuteGuest(e) {
                        this.live && this.live.unmuteGuest(e)
                    }
                    async checkMicrophone() {
                        try {
                            await navigator.mediaDevices.getUserMedia({
                                audio: !0
                            })
                        } catch (e) {
                            return !1
                        }
                        return !0
                    }
                    offMicrophone() {
                        this.microphone.offMic()
                    }
                    invite(e) {
                        return !!this.live && (this.live.invite(e), !0)
                    }
                    cancelInvite(e) {
                        return !!this.live && (this.live.cancelInvite(e), !0)
                    }
                    rejectInvite(e) {
                        return !!this.live && (this.live.rejectInvite(e), !0)
                    }
                    getInviteList() {
                        this.live && this.live.getInviteList()
                    }
                    kickGuest(e) {
                        this.live && this.live.kickGuest(e)
                    }
                    getAskGuestList(e, t) {
                        this.live && this.live.getAskGuestList(e, t)
                    }
                    getGuestList() {
                        this.live && this.live.getGuestList()
                    }
                    getSingInfo() {
                        return this.live ? {
                            socketID: this.live.socketID,
                            liveID: this.live.getLiveID(),
                            roomToken: this.live.getRoomToken(),
                            userID: this.live.user.id(),
                            userTag: this.live.user.tag(),
                            djID: this.live.getDjID(),
                            countryCode: this.singInfo.countryCode,
                            playProcessStatus: this.singInfo.playProcess,
                            liveStatus: this.live.status,
                            isLiveCall: this.live.isLiveCall
                        } : (o.default.trace({
                            type: "",
                            loc: "sing.getSingInfo",
                            desc: "live null"
                        }), {
                            playProcessStatus: this.singInfo.playProcess,
                            statusMsg: this.singInfo.playProcess ? "play is in progress" : "play is stop"
                        })
                    }
                }
            },
            444: (e, t) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                class s {
                    log() {
                        return {
                            exec_mode: s.ExecMode,
                            signal_ver: s.SING_VERSION,
                            sdk_ver: s.SDK_VERSION,
                            country_code: this.countryCode
                        }
                    }
                }
                s.SING_VERSION = "v0.5.1.24", s.SDK_VERSION = "v1.55.1", s.ROOM_URL = "/v1/room/create", s.SESSION_URL = "/v1/session/create", t.default = s
            },
            567: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const i = s(831);
                t.default = class {
                    constructor(e, t, s) {
                        const n = (0, i.v4)();
                        this._id = e, this._tag = t, this._transactionID = n, this._isDJ = s
                    }
                    id() {
                        return this._id
                    }
                    tag() {
                        return this._tag
                    }
                    transactionID() {
                        return this._transactionID
                    }
                    isDJ() {
                        return this._isDJ
                    }
                }
            },
            518: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                });
                const i = s(630);
                t.default = class {
                    constructor() {}
                    static randomString(e) {
                        let t = "";
                        for (let s = 0; s < e; s++) {
                            let e = Math.floor(62 * Math.random());
                            t += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".substring(e, e + 1)
                        }
                        return t
                    }
                    static time() {
                        const e = new Date;
                        let t = e.getHours(),
                            s = e.getMinutes(),
                            i = e.getSeconds();
                        return `${t<10?"0"+t:t}:${s<10?"0"+s:s}:${i<10?"0"+i:i}`
                    }
                    static sleep(e) {
                        const t = Date.now() + e;
                        for (; Date.now() < t;);
                    }
                    static waitSleep(e) {
                        return new Promise((t => {
                            setTimeout(t, e)
                        }))
                    }
                    static removeArray(e, t) {
                        const s = e.indexOf(t);
                        s > -1 && e.splice(s, 1)
                    }
                    static textLengthOverCut(e) {
                        if (e) return e.length > 195 && (e = e.substr(0, 195) + "..."), e
                    }
                    static resultError(e) {
                        let t;
                        switch (e) {
                            case i.ResStatusMsg.SING_COMPLETED:
                                t = {
                                    status: i.ResStatus.OK,
                                    statusMsg: i.ResStatusMsg.SING_COMPLETED
                                };
                                break;
                            case i.ResStatusMsg.SING_COMPLETED_TIMEOUT:
                                t = {
                                    status: i.ResStatus.TIMEOUT,
                                    statusMsg: i.ResStatusMsg.SING_COMPLETED_TIMEOUT
                                };
                                break;
                            case i.ResStatusMsg.MAXPUB:
                                t = {
                                    status: i.ResStatus.MAXPUB,
                                    statusMsg: i.ResStatusMsg.MAXPUB
                                };
                                break;
                            default:
                                t = {
                                    status: i.ResStatus.ERROR,
                                    statusMsg: e
                                }
                        }
                        return t
                    }
                }
            },
            831: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), Object.defineProperty(t, "NIL", {
                    enumerable: !0,
                    get: function() {
                        return a.default
                    }
                }), Object.defineProperty(t, "parse", {
                    enumerable: !0,
                    get: function() {
                        return d.default
                    }
                }), Object.defineProperty(t, "stringify", {
                    enumerable: !0,
                    get: function() {
                        return l.default
                    }
                }), Object.defineProperty(t, "v1", {
                    enumerable: !0,
                    get: function() {
                        return i.default
                    }
                }), Object.defineProperty(t, "v3", {
                    enumerable: !0,
                    get: function() {
                        return n.default
                    }
                }), Object.defineProperty(t, "v4", {
                    enumerable: !0,
                    get: function() {
                        return o.default
                    }
                }), Object.defineProperty(t, "v5", {
                    enumerable: !0,
                    get: function() {
                        return r.default
                    }
                }), Object.defineProperty(t, "validate", {
                    enumerable: !0,
                    get: function() {
                        return u.default
                    }
                }), Object.defineProperty(t, "version", {
                    enumerable: !0,
                    get: function() {
                        return c.default
                    }
                });
                var i = h(s(137)),
                    n = h(s(948)),
                    o = h(s(73)),
                    r = h(s(186)),
                    a = h(s(808)),
                    c = h(s(775)),
                    u = h(s(37)),
                    l = h(s(910)),
                    d = h(s(792));

                function h(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                }
            },
            311: (e, t) => {
                function s(e) {
                    return 14 + (e + 64 >>> 9 << 4) + 1
                }

                function i(e, t) {
                    const s = (65535 & e) + (65535 & t);
                    return (e >> 16) + (t >> 16) + (s >> 16) << 16 | 65535 & s
                }

                function n(e, t, s, n, o, r) {
                    return i((a = i(i(t, e), i(n, r))) << (c = o) | a >>> 32 - c, s);
                    var a, c
                }

                function o(e, t, s, i, o, r, a) {
                    return n(t & s | ~t & i, e, t, o, r, a)
                }

                function r(e, t, s, i, o, r, a) {
                    return n(t & i | s & ~i, e, t, o, r, a)
                }

                function a(e, t, s, i, o, r, a) {
                    return n(t ^ s ^ i, e, t, o, r, a)
                }

                function c(e, t, s, i, o, r, a) {
                    return n(s ^ (t | ~i), e, t, o, r, a)
                }
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                t.default = function(e) {
                    if ("string" == typeof e) {
                        const t = unescape(encodeURIComponent(e));
                        e = new Uint8Array(t.length);
                        for (let s = 0; s < t.length; ++s) e[s] = t.charCodeAt(s)
                    }
                    return function(e) {
                        const t = [],
                            s = 32 * e.length,
                            i = "0123456789abcdef";
                        for (let n = 0; n < s; n += 8) {
                            const s = e[n >> 5] >>> n % 32 & 255,
                                o = parseInt(i.charAt(s >>> 4 & 15) + i.charAt(15 & s), 16);
                            t.push(o)
                        }
                        return t
                    }(function(e, t) {
                        e[t >> 5] |= 128 << t % 32, e[s(t) - 1] = t;
                        let n = 1732584193,
                            u = -271733879,
                            l = -1732584194,
                            d = 271733878;
                        for (let t = 0; t < e.length; t += 16) {
                            const s = n,
                                h = u,
                                g = l,
                                p = d;
                            n = o(n, u, l, d, e[t], 7, -680876936), d = o(d, n, u, l, e[t + 1], 12, -389564586), l = o(l, d, n, u, e[t + 2], 17, 606105819), u = o(u, l, d, n, e[t + 3], 22, -1044525330), n = o(n, u, l, d, e[t + 4], 7, -176418897), d = o(d, n, u, l, e[t + 5], 12, 1200080426), l = o(l, d, n, u, e[t + 6], 17, -1473231341), u = o(u, l, d, n, e[t + 7], 22, -45705983), n = o(n, u, l, d, e[t + 8], 7, 1770035416), d = o(d, n, u, l, e[t + 9], 12, -1958414417), l = o(l, d, n, u, e[t + 10], 17, -42063), u = o(u, l, d, n, e[t + 11], 22, -1990404162), n = o(n, u, l, d, e[t + 12], 7, 1804603682), d = o(d, n, u, l, e[t + 13], 12, -40341101), l = o(l, d, n, u, e[t + 14], 17, -1502002290), u = o(u, l, d, n, e[t + 15], 22, 1236535329), n = r(n, u, l, d, e[t + 1], 5, -165796510), d = r(d, n, u, l, e[t + 6], 9, -1069501632), l = r(l, d, n, u, e[t + 11], 14, 643717713), u = r(u, l, d, n, e[t], 20, -373897302), n = r(n, u, l, d, e[t + 5], 5, -701558691), d = r(d, n, u, l, e[t + 10], 9, 38016083), l = r(l, d, n, u, e[t + 15], 14, -660478335), u = r(u, l, d, n, e[t + 4], 20, -405537848), n = r(n, u, l, d, e[t + 9], 5, 568446438), d = r(d, n, u, l, e[t + 14], 9, -1019803690), l = r(l, d, n, u, e[t + 3], 14, -187363961), u = r(u, l, d, n, e[t + 8], 20, 1163531501), n = r(n, u, l, d, e[t + 13], 5, -1444681467), d = r(d, n, u, l, e[t + 2], 9, -51403784), l = r(l, d, n, u, e[t + 7], 14, 1735328473), u = r(u, l, d, n, e[t + 12], 20, -1926607734), n = a(n, u, l, d, e[t + 5], 4, -378558), d = a(d, n, u, l, e[t + 8], 11, -2022574463), l = a(l, d, n, u, e[t + 11], 16, 1839030562), u = a(u, l, d, n, e[t + 14], 23, -35309556), n = a(n, u, l, d, e[t + 1], 4, -1530992060), d = a(d, n, u, l, e[t + 4], 11, 1272893353), l = a(l, d, n, u, e[t + 7], 16, -155497632), u = a(u, l, d, n, e[t + 10], 23, -1094730640), n = a(n, u, l, d, e[t + 13], 4, 681279174), d = a(d, n, u, l, e[t], 11, -358537222), l = a(l, d, n, u, e[t + 3], 16, -722521979), u = a(u, l, d, n, e[t + 6], 23, 76029189), n = a(n, u, l, d, e[t + 9], 4, -640364487), d = a(d, n, u, l, e[t + 12], 11, -421815835), l = a(l, d, n, u, e[t + 15], 16, 530742520), u = a(u, l, d, n, e[t + 2], 23, -995338651), n = c(n, u, l, d, e[t], 6, -198630844), d = c(d, n, u, l, e[t + 7], 10, 1126891415), l = c(l, d, n, u, e[t + 14], 15, -1416354905), u = c(u, l, d, n, e[t + 5], 21, -57434055), n = c(n, u, l, d, e[t + 12], 6, 1700485571), d = c(d, n, u, l, e[t + 3], 10, -1894986606), l = c(l, d, n, u, e[t + 10], 15, -1051523), u = c(u, l, d, n, e[t + 1], 21, -2054922799), n = c(n, u, l, d, e[t + 8], 6, 1873313359), d = c(d, n, u, l, e[t + 15], 10, -30611744), l = c(l, d, n, u, e[t + 6], 15, -1560198380), u = c(u, l, d, n, e[t + 13], 21, 1309151649), n = c(n, u, l, d, e[t + 4], 6, -145523070), d = c(d, n, u, l, e[t + 11], 10, -1120210379), l = c(l, d, n, u, e[t + 2], 15, 718787259), u = c(u, l, d, n, e[t + 9], 21, -343485551), n = i(n, s), u = i(u, h), l = i(l, g), d = i(d, p)
                        }
                        return [n, u, l, d]
                    }(function(e) {
                        if (0 === e.length) return [];
                        const t = 8 * e.length,
                            i = new Uint32Array(s(t));
                        for (let s = 0; s < t; s += 8) i[s >> 5] |= (255 & e[s / 8]) << s % 32;
                        return i
                    }(e), 8 * e.length))
                }
            },
            140: (e, t) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var s = {
                    randomUUID: "undefined" != typeof crypto && crypto.randomUUID && crypto.randomUUID.bind(crypto)
                };
                t.default = s
            },
            808: (e, t) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0, t.default = "00000000-0000-0000-0000-000000000000"
            },
            792: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i, n = (i = s(37)) && i.__esModule ? i : {
                    default: i
                };
                t.default = function(e) {
                    if (!(0, n.default)(e)) throw TypeError("Invalid UUID");
                    let t;
                    const s = new Uint8Array(16);
                    return s[0] = (t = parseInt(e.slice(0, 8), 16)) >>> 24, s[1] = t >>> 16 & 255, s[2] = t >>> 8 & 255, s[3] = 255 & t, s[4] = (t = parseInt(e.slice(9, 13), 16)) >>> 8, s[5] = 255 & t, s[6] = (t = parseInt(e.slice(14, 18), 16)) >>> 8, s[7] = 255 & t, s[8] = (t = parseInt(e.slice(19, 23), 16)) >>> 8, s[9] = 255 & t, s[10] = (t = parseInt(e.slice(24, 36), 16)) / 1099511627776 & 255, s[11] = t / 4294967296 & 255, s[12] = t >>> 24 & 255, s[13] = t >>> 16 & 255, s[14] = t >>> 8 & 255, s[15] = 255 & t, s
                }
            },
            656: (e, t) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0, t.default = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i
            },
            858: (e, t) => {
                let s;
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = function() {
                    if (!s && (s = "undefined" != typeof crypto && crypto.getRandomValues && crypto.getRandomValues.bind(crypto), !s)) throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
                    return s(i)
                };
                const i = new Uint8Array(16)
            },
            42: (e, t) => {
                function s(e, t, s, i) {
                    switch (e) {
                        case 0:
                            return t & s ^ ~t & i;
                        case 1:
                        case 3:
                            return t ^ s ^ i;
                        case 2:
                            return t & s ^ t & i ^ s & i
                    }
                }

                function i(e, t) {
                    return e << t | e >>> 32 - t
                }
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                t.default = function(e) {
                    const t = [1518500249, 1859775393, 2400959708, 3395469782],
                        n = [1732584193, 4023233417, 2562383102, 271733878, 3285377520];
                    if ("string" == typeof e) {
                        const t = unescape(encodeURIComponent(e));
                        e = [];
                        for (let s = 0; s < t.length; ++s) e.push(t.charCodeAt(s))
                    } else Array.isArray(e) || (e = Array.prototype.slice.call(e));
                    e.push(128);
                    const o = e.length / 4 + 2,
                        r = Math.ceil(o / 16),
                        a = new Array(r);
                    for (let t = 0; t < r; ++t) {
                        const s = new Uint32Array(16);
                        for (let i = 0; i < 16; ++i) s[i] = e[64 * t + 4 * i] << 24 | e[64 * t + 4 * i + 1] << 16 | e[64 * t + 4 * i + 2] << 8 | e[64 * t + 4 * i + 3];
                        a[t] = s
                    }
                    a[r - 1][14] = 8 * (e.length - 1) / Math.pow(2, 32), a[r - 1][14] = Math.floor(a[r - 1][14]), a[r - 1][15] = 8 * (e.length - 1) & 4294967295;
                    for (let e = 0; e < r; ++e) {
                        const o = new Uint32Array(80);
                        for (let t = 0; t < 16; ++t) o[t] = a[e][t];
                        for (let e = 16; e < 80; ++e) o[e] = i(o[e - 3] ^ o[e - 8] ^ o[e - 14] ^ o[e - 16], 1);
                        let r = n[0],
                            c = n[1],
                            u = n[2],
                            l = n[3],
                            d = n[4];
                        for (let e = 0; e < 80; ++e) {
                            const n = Math.floor(e / 20),
                                a = i(r, 5) + s(n, c, u, l) + d + t[n] + o[e] >>> 0;
                            d = l, l = u, u = i(c, 30) >>> 0, c = r, r = a
                        }
                        n[0] = n[0] + r >>> 0, n[1] = n[1] + c >>> 0, n[2] = n[2] + u >>> 0, n[3] = n[3] + l >>> 0, n[4] = n[4] + d >>> 0
                    }
                    return [n[0] >> 24 & 255, n[0] >> 16 & 255, n[0] >> 8 & 255, 255 & n[0], n[1] >> 24 & 255, n[1] >> 16 & 255, n[1] >> 8 & 255, 255 & n[1], n[2] >> 24 & 255, n[2] >> 16 & 255, n[2] >> 8 & 255, 255 & n[2], n[3] >> 24 & 255, n[3] >> 16 & 255, n[3] >> 8 & 255, 255 & n[3], n[4] >> 24 & 255, n[4] >> 16 & 255, n[4] >> 8 & 255, 255 & n[4]]
                }
            },
            910: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0, t.unsafeStringify = r;
                var i, n = (i = s(37)) && i.__esModule ? i : {
                    default: i
                };
                const o = [];
                for (let e = 0; e < 256; ++e) o.push((e + 256).toString(16).slice(1));

                function r(e, t = 0) {
                    return o[e[t + 0]] + o[e[t + 1]] + o[e[t + 2]] + o[e[t + 3]] + "-" + o[e[t + 4]] + o[e[t + 5]] + "-" + o[e[t + 6]] + o[e[t + 7]] + "-" + o[e[t + 8]] + o[e[t + 9]] + "-" + o[e[t + 10]] + o[e[t + 11]] + o[e[t + 12]] + o[e[t + 13]] + o[e[t + 14]] + o[e[t + 15]]
                }
                t.default = function(e, t = 0) {
                    const s = r(e, t);
                    if (!(0, n.default)(s)) throw TypeError("Stringified UUID is invalid");
                    return s
                }
            },
            137: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i, n = (i = s(858)) && i.__esModule ? i : {
                        default: i
                    },
                    o = s(910);
                let r, a, c = 0,
                    u = 0;
                t.default = function(e, t, s) {
                    let i = t && s || 0;
                    const l = t || new Array(16);
                    let d = (e = e || {}).node || r,
                        h = void 0 !== e.clockseq ? e.clockseq : a;
                    if (null == d || null == h) {
                        const t = e.random || (e.rng || n.default)();
                        null == d && (d = r = [1 | t[0], t[1], t[2], t[3], t[4], t[5]]), null == h && (h = a = 16383 & (t[6] << 8 | t[7]))
                    }
                    let g = void 0 !== e.msecs ? e.msecs : Date.now(),
                        p = void 0 !== e.nsecs ? e.nsecs : u + 1;
                    const y = g - c + (p - u) / 1e4;
                    if (y < 0 && void 0 === e.clockseq && (h = h + 1 & 16383), (y < 0 || g > c) && void 0 === e.nsecs && (p = 0), p >= 1e4) throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");
                    c = g, u = p, a = h, g += 122192928e5;
                    const f = (1e4 * (268435455 & g) + p) % 4294967296;
                    l[i++] = f >>> 24 & 255, l[i++] = f >>> 16 & 255, l[i++] = f >>> 8 & 255, l[i++] = 255 & f;
                    const v = g / 4294967296 * 1e4 & 268435455;
                    l[i++] = v >>> 8 & 255, l[i++] = 255 & v, l[i++] = v >>> 24 & 15 | 16, l[i++] = v >>> 16 & 255, l[i++] = h >>> 8 | 128, l[i++] = 255 & h;
                    for (let e = 0; e < 6; ++e) l[i + e] = d[e];
                    return t || (0, o.unsafeStringify)(l)
                }
            },
            948: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i = o(s(25)),
                    n = o(s(311));

                function o(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                }
                var r = (0, i.default)("v3", 48, n.default);
                t.default = r
            },
            25: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.URL = t.DNS = void 0, t.default = function(e, t, s) {
                    function i(e, i, r, a) {
                        var c;
                        if ("string" == typeof e && (e = function(e) {
                                e = unescape(encodeURIComponent(e));
                                const t = [];
                                for (let s = 0; s < e.length; ++s) t.push(e.charCodeAt(s));
                                return t
                            }(e)), "string" == typeof i && (i = (0, o.default)(i)), 16 !== (null === (c = i) || void 0 === c ? void 0 : c.length)) throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");
                        let u = new Uint8Array(16 + e.length);
                        if (u.set(i), u.set(e, i.length), u = s(u), u[6] = 15 & u[6] | t, u[8] = 63 & u[8] | 128, r) {
                            a = a || 0;
                            for (let e = 0; e < 16; ++e) r[a + e] = u[e];
                            return r
                        }
                        return (0, n.unsafeStringify)(u)
                    }
                    try {
                        i.name = e
                    } catch (e) {}
                    return i.DNS = r, i.URL = a, i
                };
                var i, n = s(910),
                    o = (i = s(792)) && i.__esModule ? i : {
                        default: i
                    };
                const r = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
                t.DNS = r;
                const a = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
                t.URL = a
            },
            73: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i = r(s(140)),
                    n = r(s(858)),
                    o = s(910);

                function r(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                }
                t.default = function(e, t, s) {
                    if (i.default.randomUUID && !t && !e) return i.default.randomUUID();
                    const r = (e = e || {}).random || (e.rng || n.default)();
                    if (r[6] = 15 & r[6] | 64, r[8] = 63 & r[8] | 128, t) {
                        s = s || 0;
                        for (let e = 0; e < 16; ++e) t[s + e] = r[e];
                        return t
                    }
                    return (0, o.unsafeStringify)(r)
                }
            },
            186: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i = o(s(25)),
                    n = o(s(42));

                function o(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    }
                }
                var r = (0, i.default)("v5", 80, n.default);
                t.default = r
            },
            37: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i, n = (i = s(656)) && i.__esModule ? i : {
                    default: i
                };
                t.default = function(e) {
                    return "string" == typeof e && n.default.test(e)
                }
            },
            775: (e, t, s) => {
                Object.defineProperty(t, "__esModule", {
                    value: !0
                }), t.default = void 0;
                var i, n = (i = s(37)) && i.__esModule ? i : {
                    default: i
                };
                t.default = function(e) {
                    if (!(0, n.default)(e)) throw TypeError("Invalid UUID");
                    return parseInt(e.slice(14, 15), 16)
                }
            }
        },
        t = {};

    function s(i) {
        var n = t[i];
        if (void 0 !== n) return n.exports;
        var o = t[i] = {
            exports: {}
        };
        return e[i].call(o.exports, o, o.exports, s), o.exports
    }
    s.d = (e, t) => {
        for (var i in t) s.o(t, i) && !s.o(e, i) && Object.defineProperty(e, i, {
            enumerable: !0,
            get: t[i]
        })
    }, s.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t), s.r = e => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }), Object.defineProperty(e, "__esModule", {
            value: !0
        })
    };
    var i = s(303);
    SING_SDK = i
})();