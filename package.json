{"name": "tamm-v1", "version": "0.9.4", "description": "A Spooncast manager <PERSON><PERSON><PERSON>", "author": {"name": "tamm"}, "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:prod": "NODE_ENV=production vue-cli-service build", "window:build": "vue-cli-service.cmd build", "window:build:prod": "set NODE_ENV=production && vue-cli-service.cmd build", "lint": "vue-cli-service lint", "electron:build": "vue-cli-service electron:build", "electron:build:prod": "NODE_ENV=production vue-cli-service electron:build", "electron:window:build": "vue-cli-service.cmd electron:build -p always", "electron:window:build:prod": "set NODE_ENV=production && vue-cli-service.cmd electron:build -p always", "electron:bm:serve": "vue-cli-service electron:serve --mode bundle-manager", "electron:serve": "vue-cli-service electron:serve", "start": "vue-cli-service electron:serve", "electron:icon-build": "electron-icon-builder --input=public/icon.png --output=build --flatten", "electron:window:icon-build": "electron-icon-builder.cmd --input=public/icon.png --output=build --flatten", "electron:mac:build": "export PYTHON_PATH=\"$(which python)\"; vue-cli-service electron:build", "electron:mac:build:prod": "export PYTHON_PATH=\"$(which python)\"; NODE_ENV=production vue-cli-service electron:build", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps", "rebuild:sqlite": "electron-rebuild -f -w better-sqlite3"}, "main": "background.js", "dependencies": {"@arkiv/zip": "^1.2.0", "@prisma/client": "^6.5.0", "@sopia-bot/api-dto": "^1.0.1", "@sopia-bot/core": "^2.4.0", "@types/hls.js": "^1.0.0", "@types/html2canvas": "^1.0.0", "adm-zip": "^0.5.16", "axios": "^0.19.2", "better-sqlite3": "^11.6.0", "cfg-lite": "^1.1.3", "cors": "^2.8.5", "electron-log": "^4.4.6", "electron-updater": "^4.6.5", "express": "^4.19.2", "firebase": "^11.10.0", "highlight.js": "^11.4.0", "html2canvas": "^1.4.1", "iconv-lite": "^0.6.3", "liquor-tree": "^0.2.70", "lottie-web-vue": "1.2.1", "marked": "^4.0.12", "mime-types": "^2.1.35", "npkgi": "^1.0.7", "prisma": "^5.22.0", "rimraf": "^3.0.2", "supertest": "^5.0.0", "tslib": "^2.3.0", "uuid": "^8.3.0", "vue": "^2.7.16", "vue-carousel-3d": "^1.0.1", "vue-class-component": "^7.2.6", "vue-cli-plugin-electron-builder": "^2.1.1", "vue-monaco": "^1.2.1", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.1", "vue-sweetalert2": "^5.0.2", "vuescroll": "^4.16.1", "vuetify": "^2.7.2", "vuex": "^3.6.2"}, "devDependencies": {"@electron/typescript-definitions": "^9.1.2", "@types/adm-zip": "^0.5.7", "@types/electron-devtools-installer": "^2.2.5", "@types/node": "^22.10.1", "@types/supertest": "^6.0.2", "@types/uuid": "^8.0.0", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-plugin-typescript": "^4.5.15", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "^4.5.19", "babel-plugin-transform-remove-console": "^6.9.4", "electron": "^33.2.1", "electron-builder": "^25.1.8", "electron-devtools-installer": "^3.2.1", "electron-icon-builder": "^2.0.1", "electron-rebuild": "^3.2.9", "monaco-editor-webpack-plugin": "^1.9.0", "sass": "~1.32.12", "sass-loader": "^10.2.0", "typescript": "~4.3.2", "vue-cli-plugin-vuetify": "~2.4.1", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.7.2", "webpack-bundle-analyzer": "^4.10.2"}, "build": {"appId": "com.tamm.v1", "productName": "TAMM-V1", "icon": "./public/app.png", "win": {"icon": "./public/app.ico", "target": ["nsis"]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": false}, "mac": {"icon": "./public/app.png"}, "linux": {"icon": "./public/app.png"}}}