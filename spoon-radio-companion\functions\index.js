const functions = require('firebase-functions');
const cors = require('cors')({ origin: true });

// 스푼 API 프록시 함수 - 전체 라이브 방송용
exports.spoonLivesProxy = functions.https.onRequest((request, response) => {
  cors(request, response, async () => {
    try {
      // 요청 파라미터 추출
      const { page_size = 20, sort = 1, is_adult = 0, cursor } = request.query;
      
      let apiUrl;
      if (cursor) {
        // cursor가 있으면 해당 URL 사용
        apiUrl = cursor;
      } else {
        // 첫 페이지 로드
        const timestamp = new Date().getTime();
        apiUrl = `https://kr-api.spooncast.net/lives/?is_adult=${is_adult}&page_size=${page_size}&sort=${sort}&_t=${timestamp}`;
      }
      
      console.log('스푼 API 호출:', apiUrl);
      
      // 스푼 API 호출
      const fetch = require('node-fetch');
      const apiResponse = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Referer': 'https://www.spooncast.net/',
          'Origin': 'https://www.spooncast.net'
        }
      });

      if (!apiResponse.ok) {
        throw new Error(`HTTP error! status: ${apiResponse.status}`);
      }

      const data = await apiResponse.json();
      
      // CORS 헤더 설정
      response.set('Access-Control-Allow-Origin', '*');
      response.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      response.set('Access-Control-Allow-Headers', 'Content-Type');
      response.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.set('Pragma', 'no-cache');
      response.set('Expires', '0');
      
      // 응답 반환
      response.status(200).json(data);
      
    } catch (error) {
      console.error('스푼 API 프록시 오류:', error);
      response.status(500).json({ 
        error: '스푼 API 호출 실패', 
        message: error.message 
      });
    }
  });
});

// 스푼 토큰 처리 함수
exports.spoonTokenProxy = functions.https.onRequest((request, response) => {
  cors(request, response, async () => {
    try {
      if (request.method === 'OPTIONS') {
        // CORS preflight 요청 처리
        response.set('Access-Control-Allow-Origin', '*');
        response.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        response.status(204).send('');
        return;
      }

      if (request.method !== 'POST') {
        response.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const { sessionId, action, tokenData } = request.body;

      if (!sessionId) {
        response.status(400).json({ error: 'Session ID required' });
        return;
      }

      // 임시 저장소 (실제로는 Firebase Realtime Database 사용 권장)
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const db = admin.database();
      const tokenRef = db.ref(`spoon_tokens/${sessionId}`);

      if (action === 'store') {
        // 토큰 저장
        if (!tokenData) {
          response.status(400).json({ error: 'Token data required' });
          return;
        }

        await tokenRef.set({
          ...tokenData,
          timestamp: admin.database.ServerValue.TIMESTAMP,
          expires: Date.now() + (5 * 60 * 1000) // 5분 후 만료
        });

        console.log('토큰 저장 완료:', sessionId);
        response.status(200).json({ success: true, message: 'Token stored' });

      } else if (action === 'retrieve') {
        // 토큰 조회
        const snapshot = await tokenRef.once('value');
        const data = snapshot.val();

        if (!data) {
          response.status(404).json({ error: 'Token not found' });
          return;
        }

        if (data.expires < Date.now()) {
          // 만료된 토큰 삭제
          await tokenRef.remove();
          response.status(404).json({ error: 'Token expired' });
          return;
        }

        // 토큰 반환 후 삭제 (일회용)
        await tokenRef.remove();
        
        console.log('토큰 조회 완료:', sessionId);
        response.status(200).json({ 
          success: true, 
          data: {
            id: data.id,
            nickname: data.nickname,
            profile_url: data.profile_url,
            tag: data.tag,
            token: data.token,
            refresh_token: data.refresh_token
          }
        });

      } else {
        response.status(400).json({ error: 'Invalid action' });
      }

      // CORS 헤더 설정
      response.set('Access-Control-Allow-Origin', '*');
      response.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    } catch (error) {
      console.error('스푼 토큰 처리 오류:', error);
      response.status(500).json({ 
        error: '토큰 처리 실패', 
        message: error.message 
      });
    }
  });
}); 

// IP 기반 스푼 토큰 관리 API
exports.spoonToken = functions.https.onRequest((request, response) => {
  cors(request, response, async () => {
    try {
      // CORS preflight 요청 처리
      if (request.method === 'OPTIONS') {
        response.set('Access-Control-Allow-Origin', '*');
        response.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        response.status(204).send('');
        return;
      }

      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const db = admin.database();

      if (request.method === 'POST') {
        // 토큰 저장
        const { ip, token, user } = request.body;

        if (!ip || !token || !user) {
          response.status(400).json({ error: 'IP, token, and user data required' });
          return;
        }

        // IP를 키로 사용하여 토큰 저장
        const sanitizedIP = ip.replace(/[.#$\[\]]/g, '_'); // Firebase 키 규칙에 맞게 변환
        const tokenRef = db.ref(`spoon_tokens_by_ip/${sanitizedIP}`);

        await tokenRef.set({
          ip: ip,
          token: token,
          user: user,
          timestamp: admin.database.ServerValue.TIMESTAMP,
          expires: Date.now() + (24 * 60 * 60 * 1000) // 24시간 후 만료
        });

        console.log('IP 기반 토큰 저장 완료:', ip);
        response.status(200).json({ success: true, message: 'Token stored' });

      } else if (request.method === 'GET') {
        // 토큰 조회 - URL 경로에서 IP 추출
        const pathParts = request.path.split('/');
        const ip = pathParts[pathParts.length - 1]; // 마지막 부분이 IP

        if (!ip) {
          response.status(400).json({ error: 'IP required in URL path' });
          return;
        }

        const sanitizedIP = ip.replace(/[.#$\[\]]/g, '_');
        const tokenRef = db.ref(`spoon_tokens_by_ip/${sanitizedIP}`);
        
        const snapshot = await tokenRef.once('value');
        const data = snapshot.val();

        if (!data) {
          response.status(404).json({ error: 'Token not found' });
          return;
        }

        if (data.expires < Date.now()) {
          // 만료된 토큰 삭제
          await tokenRef.remove();
          response.status(404).json({ error: 'Token expired' });
          return;
        }

        console.log('IP 기반 토큰 조회 완료:', ip);
        response.status(200).json({ 
          token: data.token,
          user: data.user
        });

      } else {
        response.status(405).json({ error: 'Method not allowed' });
      }

      // CORS 헤더 설정
      response.set('Access-Control-Allow-Origin', '*');
      response.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    } catch (error) {
      console.error('IP 기반 스푼 토큰 처리 오류:', error);
      response.status(500).json({ 
        error: '토큰 처리 실패', 
        message: error.message 
      });
    }
  });
}); 