// tailwind.config.js
const { heroui } = require('@heroui/react')

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // ...
    '../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
    './src/renderer/src/**/*.{js,ts,jsx,tsx}',
    './src/renderer/index.html'
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#6B8EDB',
          light: '#8BA5E5',
          dark: '#5273C0'
        }
      }
    }
  },
  darkMode: 'class',
  plugins: [heroui()]
}
