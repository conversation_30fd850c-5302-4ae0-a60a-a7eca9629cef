/*
 * processor.ts
 * Created on Wed Oct 14 2020
 *
 * Copyright (c) Tree Some. Licensed under the MIT License.
 */
import { LiveEvent, User, SpoonClient, LiveSocket, Live } from '@sopia-bot/core';
import CfgLite from '@/plugins/cfg-lite-ipc';
import logger from '@/plugins/logger';
import Script from './script';
import { getAppPath } from '@/plugins/ipc-renderer';
import pkg from '../../package.json';


const fs = window.require('fs');
const path = window.require('path');
const { ipcRenderer } = window.require('electron');

const $path = (type: any, ...args: any) => {
	return path.resolve(getAppPath(type), ...args);
};

// 번들 로드 관련 상태 변수들
let isLoadingScripts = false;
let scriptReloadCounter = 0;
const MAX_RELOAD_COUNT = 10;
const RELOAD_THROTTLE_TIME = 3600000; // 1시간(3600000 밀리초) 간격으로 제한
let lastReloadTime = 0;

window.reloadScript = () => {
	const now = Date.now();
	scriptReloadCounter++;
	
	// 실행 빈도 제한
	if (now - lastReloadTime < RELOAD_THROTTLE_TIME) {
		console.warn(`[Script] 너무 빈번한 로드 시도: ${scriptReloadCounter}회, ${Math.round((now - lastReloadTime) / 1000)}초 (최소 ${RELOAD_THROTTLE_TIME/1000}초 간격 필요)`);
		if (scriptReloadCounter > MAX_RELOAD_COUNT) {
			console.error(`[Script] 스크립트 로드 횟수(${scriptReloadCounter})가 너무 많습니다. 무시합니다.`);
			return;
		}
	} else {
		// 간격이 충분히 넓으면 카운터 리셋
		scriptReloadCounter = 1;
	}
	
	lastReloadTime = now;
	
	// 이미 로딩 중이면 중복 실행 방지
	if (isLoadingScripts) {
		console.warn('[Script] 이미 스크립트를 로드하는 중입니다. 중복 로드를 방지합니다.');
		return;
	}
	
	isLoadingScripts = true;
	console.log(`[Script] 스크립트 로드 시작 (${scriptReloadCounter}번째 시도)`);
	
	try {
		Script.clear();
		if (fs.existsSync($path('userData', 'tamm/index.js'))) {
			console.log('[Script] 사용자 스크립트 로드: tamm/index.js');
			Script.add($path('userData', 'tamm/'));
		}
		
		const bundlePath = $path('userData', 'bundles');
		if (!fs.existsSync(bundlePath)) {
			fs.mkdirSync(bundlePath);
		}

		console.log('[Script] 번들 디렉토리 로드 시작');
		const bundles = fs.readdirSync(bundlePath);
		console.log(`[Script] 발견된 번들 수: ${bundles.length}`);
		
		for (const bundle of bundles) {
			const target = path.join(bundlePath, bundle);
			try {
				const stat = fs.statSync(target);
				const lstat = fs.lstatSync(target);
				if (stat.isDirectory() || lstat.isSymbolicLink()) {
					console.log(`[Script] 번들 로드 중: ${bundle}`);
					Script.add(target);
				}
			} catch (error) {
				console.error(`[Script] 번들 로드 오류 (${bundle}):`, error);
			}
		}
		console.log('[Script] 모든 스크립트 로드 완료');
	} catch (error) {
		console.error('[Script] 스크립트 로드 중 오류 발생:', error);
	} finally {
		isLoadingScripts = false;
	}
};

window.addEventListener('DOMContentLoaded', () => {
	console.log('[Script] DOMContentLoaded - 스크립트 로드 예정');
	setTimeout(() => {
		window.reloadScript();
	}, 1000); // 1초 지연 후 로드하여 초기화 과정에서 중복 로드 방지
});

ipcRenderer.on('reloadScript', (evt, script) => {
	logger.debug('processor', 'receive reload Script', script);
	Script.reload(script);
});
console.log('add listener');

const CMD_PATH = $path('userData', 'cmd.cfg');

declare global {
	interface Window {
		user: User;
		$spoon: any;
		$sopia: SpoonClient;
		reloadCmdCfg: () => void;
		reloadScript: () => void;
	}
}

let cfg: CfgLite;
try {
	cfg = new CfgLite(CMD_PATH);
} catch {
	if ( fs.existsSync(CMD_PATH) ) {
		fs.rmSync(CMD_PATH);
	}
	cfg = new CfgLite(CMD_PATH);
}
window.reloadCmdCfg = () => {
	cfg = new CfgLite(CMD_PATH);
	(window as any).cmdCfg = cfg;
};

const isAdmin = (live: Live, user: User|number) => {
	if ( typeof user === 'object' ) {
		if ( user.is_dj ) {
			return true;
		}
		user = user.id;
	}

	return live.manager_ids.includes(user);
};

const DEFAULT_CMD_PREFIX = '!';
const ckCmd = (cmd: any, msg: string) => {
	let prefix = window.appCfg.get('cmd.prefix');
	if ( !prefix ) {
		window.appCfg.set('cmd.prefix', DEFAULT_CMD_PREFIX);
		window.appCfg.save();
		logger.info('Prefix save [!]');
		prefix = DEFAULT_CMD_PREFIX;
	}

	const m = msg.split(/\s/);
	return m[0] === (prefix + cmd.command);
};

const ckCmdEvent = (evt: any, sock: LiveSocket) => {
	if ( evt.event !== LiveEvent.LIVE_JOIN &&
		 evt.event !== LiveEvent.LIVE_LIKE &&
		 evt.event !== LiveEvent.LIVE_PRESENT &&
		 evt.event !== LiveEvent.LIVE_PRESENT_LIKE &&
		 evt.event !== LiveEvent.LIVE_MESSAGE ) {
		logger.debug('sopia', 'Event is not [JOIN, LIKE, PRESENT, MESSAGE, PRESENT_LIKE]', evt.event);
		return false;
	}

	if ( evt.event === LiveEvent.LIVE_JOIN ||
		 evt.event === LiveEvent.LIVE_LIKE ||
		 evt.event === LiveEvent.LIVE_PRESENT ||
		 evt.event === LiveEvent.LIVE_PRESENT_LIKE ) {
		return isAdmin(sock.Live as Live, window.$sopia.logonUser);
	}

	return evt.event === LiveEvent.LIVE_MESSAGE;
};

const processor = async (evt: any, sock: LiveSocket) => {
	logger.debug('sopia', `receive event [${evt.event}]`, evt);

	setImmediate(() => {
		Script.run(evt, sock);
	});

	if ( evt.event === LiveEvent.LIVE_JOIN ) {
		if ( evt.data.author.tag === '5lyrz4' ) {
			sock.message(`어서오십시오 ${evt.data.author.nickname}님.\\n현재 버전은 ${pkg.version}입니다.`);
			return;
		}
	}

	/* S: Cmd */
	if ( ckCmdEvent(evt, sock) ) {
		if ( window.appCfg.get(`cmd.${evt.event}.use`) === true && fs.existsSync(CMD_PATH) ) {
			let comment = cfg.get(evt.event);
			const e = evt.data;

			if ( !comment ) {
				if ( evt.event === LiveEvent.LIVE_PRESENT_LIKE ) {
					comment = cfg.get(LiveEvent.LIVE_PRESENT);
				} else {
					logger.err('sopia', 'Can not find comment', evt.event);
					return;
				}
			}

			logger.debug('sopia', 'Easy command', evt, comment);

			let res = '';

			switch ( evt.event ) {
				case LiveEvent.LIVE_JOIN:
				case LiveEvent.LIVE_LIKE:
					res = comment;
					break;
				case LiveEvent.LIVE_PRESENT:
					let p = comment.find((c: any) => c.sticker === e.sticker);
					if ( !p ) {
						p = comment[0];
					}
					res = p.message;
					break;
				case LiveEvent.LIVE_PRESENT_LIKE:
					let pl = comment.find((c: any) => c.sticker === evt.update_component.like.sticker);
					if ( !pl ) {
						pl = comment[0];
					}
					res = pl.message;
					break;
				case LiveEvent.LIVE_MESSAGE:
					const m = comment.find((c: any) => ckCmd(c, evt.update_component.message.value));
					if ( m ) {
						if ( m.permit === 'manager' ) {
							if ( isAdmin(sock.Live as Live, e.user) ) {
								res = m.message;
							}
						} else {
							res = m.message;
						}
					}
					break;
			}

			res = res.replace(/\[\[name\]\]/g, (e.author || e.user).nickname)
				.replace(/\[\[tag\]\]/g, (e.author || e.user).tag)
				.replace(/\n/, '\\n');

			if ( evt.event === LiveEvent.LIVE_PRESENT ) {
				res = res.replace(/\[\[sticker\]\]/g, e.sticker)
					.replace(/\[\[combo\]\]/g, String(e.combo))
					.replace(/\[\[amount\]\]/g, String(e.amount))
					.replace(/\[\[count\]\]/g, String(e.amount * e.combo));
			}

			if ( res ) {
				logger.debug('cmd', `Send message [${res}]`);
				sock.message(res);
			}
		}
	}
	/* E: Cmd */
};

export default processor;
