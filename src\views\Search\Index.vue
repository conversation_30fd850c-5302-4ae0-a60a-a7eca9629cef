<!--
 * Index.vue
 * Created on Tue Oct 06 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
-->
<template>
	<v-main class="custom white" style="min-height: 100vh;">
		<vue-scroll @handle-scroll="scrollEvent" style="max-height: calc(100vh - 68px);">
		<v-container style="max-height: calc(100vh - 58px);">
			<!-- 상단 검색창과 프로필 -->
			<div class="d-flex align-center justify-center px-3 py-2 home-search-container">
				<div class="search-width mr-3">
					<search-box :value="searchQuery" @search="performSearch"></search-box>
				</div>
				<div class="home-profile-container">
					<v-menu
						v-model="avatarMenu"
						:close-on-content-click="false"
						offset-y
						left
						transition="slide-y-transition"
						:nudge-width="250"
						:nudge-bottom="10">
						<template v-slot:activator="{ on, attrs }">
							<v-avatar size="36" class="no-drag" v-bind="attrs" v-on="on">
								<img :src="$store.getters.user.profile_url">
							</v-avatar>
						</template>
						<v-card color="white" elevation="3" class="profile-popup">
							<v-list-item class="px-2">
								<v-list-item-avatar size="56" color="grey lighten-3">
									<v-img :src="$store.getters.user.profile_url"></v-img>
								</v-list-item-avatar>

								<v-list-item-content class="ml-2">
									<v-list-item-title class="title font-weight-bold" style="font-size: 1.1rem !important;">
										{{ $store.getters.user.nickname }}
									</v-list-item-title>
									<v-list-item-subtitle class="mt-1" style="font-size: 0.8rem !important; color: #666;">
										@{{ $store.getters.user.tag }}
									</v-list-item-subtitle>
								</v-list-item-content>
							</v-list-item>
							
							<v-divider class="mx-4 my-2"></v-divider>
							
							<v-list dense class="profile-action-list">
								<v-list-item link @click="$assign(userLink)" class="profile-action-item">
									<v-list-item-icon class="mr-3">
										<v-icon size="20" color="primary">mdi-account</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>프로필 보기</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								
								<v-list-item link @click="openFollowingDialog" class="profile-action-item">
									<v-list-item-icon class="mr-3">
										<v-icon size="20" color="purple">mdi-account-multiple</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>내 팔로잉</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								
								<v-list-item link @click="spoonLogout" class="profile-action-item">
									<v-list-item-icon class="mr-3">
										<v-icon size="20" color="red darken-1">mdi-logout</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>{{ $t('spoon-logout') }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</v-list>
						</v-card>
					</v-menu>
				</div>
			</div>
			
			<!-- 검색 유형 선택기 (별도로 표시) -->
			<div class="search-type-selector d-flex justify-center mt-3">
				<v-chip-group
					v-model="selectedSearchTypeIndex"
					mandatory
					active-class="primary--text font-weight-bold"
					@change="changeSearchType">
					<v-chip
						v-for="(type, index) in searchTypes"
						:key="index"
						small
						outlined
						:value="index"
						class="search-type-chip">
						{{ type.text }}
					</v-chip>
				</v-chip-group>
			</div>
		
			<!-- 사용자 검색 결과 -->
			<div v-if="searchType === 'user'" class="user-search-results mt-3">
				<div v-if="searchQuery.trim() && searchList.length === 0" class="text-center py-5">
					<v-icon large color="grey lighten-1">mdi-account-search-outline</v-icon>
					<div class="grey--text text--darken-1 mt-2">검색 결과가 없습니다.</div>
				</div>
				<div v-else class="d-flex flex-wrap justify-center">
					<div
						v-for="(search, idx) of searchList"
						:key="searchType + idx"
						class="ma-2 text-center user-profile-wrapper">
						
						<div class="user-profile-container">
							<!-- 방송 중인 경우 LIVE 표시 -->
							<div v-if="search.current_live_id" class="live-user-container">
								<div class="live-border">
									<v-avatar
										@click="$evt.$emit('live-join', search.current_live_id)"
										size="64"
										class="user-avatar"
										style="cursor: pointer;">
										<v-img :src="search.profile_url" aspect-ratio="1"></v-img>
									</v-avatar>
								</div>
								<div class="live-badge">LIVE</div>
							</div>
							<v-avatar
								v-else
								@click="$assign(`/user/${search.id}/`)"
								size="64"
								class="user-avatar mb-2"
								style="cursor: pointer;">
								<v-img :src="search.profile_url" aspect-ratio="1"></v-img>
							</v-avatar>
							
							<div class="user-nickname text-center text-truncate">
								{{ search.nickname }}
							</div>
							<div class="user-tag text-center text-truncate caption">
								@{{ search.tag }}
							</div>
							<div class="followers text-center caption grey--text">
								<v-icon x-small color="grey">mdi-account</v-icon> {{ search.follower_count || 0 }}
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<!-- 라이브 검색 결과 -->
			<div v-else class="mt-3">
				<div v-if="searchQuery.trim() && searchList.length === 0" class="text-center py-5">
					<v-icon large color="grey lighten-1">mdi-magnify-off</v-icon>
					<div class="grey--text text--darken-1 mt-2">검색 결과가 없습니다.</div>
				</div>
				<div v-else class="d-flex flex-wrap justify-center">
					<div 
						v-for="(search, idx) of searchList"
						:key="searchType + idx"
						class="ma-2">
						<live-item :live="search"></live-item>
					</div>
				</div>
			</div>
		</v-container>
		</vue-scroll>
	</v-main>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { User, Live, Cast, HttpRequest } from '@sopia-bot/core';
import SearchBox from '@/views/Search/SearchBox.vue';
import LiveItem from '../Home/LiveItem.vue';

enum ContentType {
	USER = 'user',
	LIVE = 'live',
	CAST = 'cast',
	TALK = 'talk',
}

const sleep = (msec: number) => {
	return new Promise((resolve, reject) => {
		setTimeout(resolve, msec);
	});
};

@Component({
	components: {
		SearchBox,
		LiveItem,
	},
})
export default class Search extends Mixins(GlobalMixins) {
	public searchManager!: any;
	public searchList: Array<Cast|Live|User> = [];
	public userSearchList: Array<User> = [];
	public liveSearchList: Array<Live> = [];
	public asyncMutex: boolean = false;
	public loadComplete: boolean = false;
	public avatarMenu: boolean = false;
	public selectedSearchTypeIndex: number = 0;

	public searchTypes: any[] = [
		{
			text: "User",
			value: ContentType.USER,
		},
		{
			text: "Live",
			value: ContentType.LIVE,
		}
	];
	public searchType: ContentType = ContentType.USER;
	public searchQuery: string = '';

	public get userLink() {
		return `/user/${this.$store.getters.user.id}`;
	}

	public spoonLogout() {
		window.logout();
	}

	public async mounted() {
		const params = this.$route.params;
		const { type, query } = params;
		
		// URL에서 검색 유형과 검색어 가져오기
		this.searchType = decodeURI(type) as ContentType;
		this.searchQuery = decodeURI(query).replace(/\/$/, '');
		
		// 검색 유형에 따라 선택된 탭 인덱스 설정
		this.selectedSearchTypeIndex = this.searchTypes.findIndex(item => item.value === this.searchType);
		
		// 검색 결과 초기화
		this.searchList = [];
		this.userSearchList = [];
		this.liveSearchList = [];
		this.searchManager = null;
		this.loadComplete = false;
		
		// 검색 수행
		if (this.searchQuery) {
			this.getNextSearchList();
		}
	}

	public async getNextSearchList() {
		if (this.loadComplete || this.asyncMutex) {
			return;
		}
		this.asyncMutex = true;
		
		if (this.searchManager && this.searchManager[this.searchType]) {
			if (this.searchManager[this.searchType].res.next) {
				const req = await this.searchManager[this.searchType].next();
				console.log('다음 페이지 결과:', req);
				
				if (this.searchType === ContentType.USER) {
					this.userSearchList = this.userSearchList.concat(this.searchManager[this.searchType].res.results);
				} else if (this.searchType === ContentType.LIVE) {
					this.liveSearchList = this.liveSearchList.concat(this.searchManager[this.searchType].res.results);
				}
			}
		} else {
			// 검색 매니저 초기화
			if (!this.searchManager) {
				this.searchManager = {};
			}
			
			// 사용자 검색 결과 가져오기
			try {
				this.searchManager[ContentType.USER] = await this.$sopia.api.search.user({
					params: {
						keyword: this.searchQuery,
					},
				});
				this.userSearchList = this.searchManager[ContentType.USER].res.results;
			} catch (error) {
				console.error('사용자 검색 오류:', error);
				this.userSearchList = [];
			}
			
			// 방송 검색 결과 가져오기
			try {
				console.log('방송 검색 시작:', this.searchQuery);
				
				// 검색어가 있는 경우에만 검색 실행
				if (this.searchQuery.trim()) {
					// fetch API를 직접 사용하여 search/live/ 엔드포인트 호출
					console.log('fetch API를 사용하여 직접 요청');
					
					// 스푼라디오 API 기본 URL과 검색 엔드포인트
					const baseUrl = 'https://kr-api.spooncast.net';
					const endpoint = '/search/live/';
					const url = `${baseUrl}${endpoint}?keyword=${encodeURIComponent(this.searchQuery)}`;
					
					// 인증 토큰과 헤더 설정
					const headers: HeadersInit = {
						'Content-Type': 'application/json',
					};
					
					// 인증 토큰이 있으면 추가
					if (this.$sopia && this.$sopia.token) {
						headers['Authorization'] = `Token ${this.$sopia.token}`;
					} else if (this.$store && this.$store.getters.token) {
						headers['Authorization'] = `Token ${this.$store.getters.token}`;
					}
					
					// fetch 요청 보내기
					const response = await fetch(url, {
						method: 'GET',
						headers,
					});
					
					// 응답 처리
					if (response.ok) {
						const result = await response.json();
						console.log('방송 검색 API 응답:', result);
						
						// HttpRequest 형식으로 응답 가공
						this.searchManager[ContentType.LIVE] = {
							res: result,
							url: endpoint,
							next: function() {
								return Promise.resolve(null);
							}
						};
						
						console.log('방송 검색 응답 전체 (직접 HTTP 요청):', this.searchManager[ContentType.LIVE]);
						console.log('API URL:', endpoint);
						
						if (this.searchManager[ContentType.LIVE] && this.searchManager[ContentType.LIVE].res) {
							console.log('방송 검색 결과 구조:', this.searchManager[ContentType.LIVE].res);
							
							if (this.searchManager[ContentType.LIVE].res.results) {
								// LiveItem 컴포넌트 형식에 맞게 변환
								this.liveSearchList = this.searchManager[ContentType.LIVE].res.results.map((item: any) => 
									this.convertToLiveItem(item)
								).filter((item: any) => item !== null);
								
								console.log('방송 목록 개수:', this.liveSearchList.length);
								
								if (this.liveSearchList.length > 0) {
									console.log('첫번째 방송 항목:', this.liveSearchList[0]);
								} else {
									console.log('방송 항목이 없습니다.');
								}
							} else {
								console.error('방송 검색 결과에 results 필드가 없습니다:', this.searchManager[ContentType.LIVE].res);
								this.liveSearchList = [];
							}
						} else {
							console.error('방송 검색 응답에 res 필드가 없습니다.');
							this.liveSearchList = [];
						}
					} else {
						console.error('방송 검색 API 오류:', response.status, response.statusText);
						// 기존 방식으로 폴백
						this.searchManager[ContentType.LIVE] = await this.$sopia.api.search.content({
							params: {
								keyword: this.searchQuery,
								content_type: 'live',
							}
						});
						
						if (this.searchManager[ContentType.LIVE] && this.searchManager[ContentType.LIVE].res && 
							this.searchManager[ContentType.LIVE].res.results) {
							this.liveSearchList = this.searchManager[ContentType.LIVE].res.results;
						} else {
							this.liveSearchList = [];
						}
					}
				} else {
					// 검색어가 없는 경우 빈 결과 설정
					this.liveSearchList = [];
				}
			} catch (error) {
				console.error('방송 검색 오류:', error);
				this.liveSearchList = [];
			}
		}
		
		// 현재 선택된 탭에 맞는 검색 결과 설정
		this.updateSearchList();
		
		// 현재 선택된 검색 타입의 결과가 더 이상 없으면 완료 표시
		if (this.searchManager && this.searchManager[this.searchType] && 
			this.searchManager[this.searchType].res.next === '') {
			this.loadComplete = true;
		} else {
			this.loadComplete = false;
		}
		
		this.asyncMutex = false;
	}

	// 현재 선택된 탭에 맞는 검색 결과 업데이트
	public updateSearchList() {
		console.log('업데이트 검색 목록 - 현재 탭:', this.searchType);
		console.log('사용자 검색 결과 수:', this.userSearchList.length);
		console.log('방송 검색 결과 수:', this.liveSearchList.length);
		
		if (this.searchType === ContentType.USER) {
			this.searchList = [...this.userSearchList];
			console.log('사용자 탭으로 설정됨, 결과 수:', this.searchList.length);
		} else if (this.searchType === ContentType.LIVE) {
			this.searchList = [...this.liveSearchList];
			console.log('방송 탭으로 설정됨, 결과 수:', this.searchList.length);
		}
	}

	public changeSearchType(index: number) {
		// 현재 선택된 유형 가져오기
		const selectedType = this.searchTypes[index].value;
		console.log('검색 유형 변경:', this.searchType, '->', selectedType);
		
		// 검색 유형이 변경된 경우에만 처리
		if (this.searchType !== selectedType) {
			this.searchType = selectedType;
			
			// 검색 타입 변경 시 URL 업데이트 (검색어가 있는 경우)
			if (this.searchQuery) {
				this.$assign(`/search/${this.searchType}/${encodeURI(this.searchQuery.trim())}`);
			}
			
			// 완료 상태 초기화 및 현재 탭에 맞는 검색 결과 업데이트
			this.loadComplete = false;
			this.updateSearchList();
		}
	}

	public performSearch(query: string) {
		if (query.trim()) {
			// 검색어가 변경되면 검색 결과와 상태를 초기화
			this.searchList = [];
			this.userSearchList = [];
			this.liveSearchList = [];
			this.searchManager = null;
			this.loadComplete = false;
			this.searchQuery = query.trim();
			
			// 현재 선택된 검색 유형과 검색어로 URL 업데이트
			this.$assign(`/search/${this.searchType}/${encodeURI(query.trim())}`);
			
			// URL 업데이트 후 실제 검색 수행 (중요!)
			this.$nextTick(() => {
				this.getNextSearchList();
			});
		}
	}

	public scrollEvent(vertical: any) {
		if ( vertical.process >= 0.9 ) {
			this.getNextSearchList();
		}
	}

	// 방송 검색 결과를 LiveItem 컴포넌트에 맞게 변환하는 함수
	public convertToLiveItem(item: any): any {
		// 기본 필수 필드가 없으면 null 반환
		if (!item || !item.id || !item.title) {
			return null;
		}
		
		// 기본 Live 객체 구조 생성
		const liveItem = {
			id: item.id,
			title: item.title,
			img_url: item.img_url || item.thumbnail_url || item.image_url || '',
			member_count: item.member_count || item.viewers || item.view_count || 0,
			like_count: item.like_count || item.likes || 0,
			author: {
				id: 0,
				nickname: '',
				profile_url: ''
			}
		};
		
		// author 정보 처리
		if (item.author) {
			liveItem.author.id = item.author.id || 0;
			liveItem.author.nickname = item.author.nickname || item.author.name || '알 수 없음';
			liveItem.author.profile_url = item.author.profile_url || '';
		} else if (item.user) {
			// 일부 API는 user 필드로 반환할 수 있음
			liveItem.author.id = item.user.id || 0;
			liveItem.author.nickname = item.user.nickname || item.user.name || '알 수 없음';
			liveItem.author.profile_url = item.user.profile_url || '';
		}
		
		return liveItem;
	}

	public openFollowingDialog() {
		this.$store.commit('setFollowingDialogOpen', true);
		this.avatarMenu = false;
	}
}
</script>
<style>
.home-search-container {
	background-color: transparent;
	border-radius: 4px;
	margin: 50px 12px 12px 12px;
	display: flex;
	justify-content: center;
}

.home-profile-container {
	margin-left: 12px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.search-width {
	width: 50%;
	max-width: 300px;
}

.no-drag {
	-webkit-app-region: no-drag;
}

.user-profile-wrapper {
	width: 120px;
}

.user-profile-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8px;
	border-radius: 12px;
	margin-bottom: 15px;
	transition: all 0.3s;
}

.user-profile-container:hover {
	background-color: rgba(0, 0, 0, 0.03);
}

.user-avatar {
	border: 2px solid #fff;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.user-nickname {
	font-size: 0.9rem;
	font-weight: 500;
	width: 100%;
	margin-top: 4px;
}

.user-tag {
	font-size: 0.75rem;
	color: #777;
	width: 100%;
}

.followers {
	margin-top: 2px;
}

.search-type-selector {
	margin-top: 8px;
}

.search-type-chip {
	margin: 0 4px;
}

.profile-popup {
	border-radius: 12px;
	overflow: hidden;
}

.profile-action-list {
	padding: 0;
}

.profile-action-item {
	transition: background-color 0.2s;
	border-radius: 0;
	min-height: 42px;
}

.profile-action-item:hover {
	background-color: rgba(0, 0, 0, 0.04);
}

/* 방송 중 사용자 스타일 */
.live-user-container {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 8px;
}

.live-border {
	padding: 2px;
	border-radius: 50%;
	border: 2px solid #f44336;
	background-color: transparent;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.live-badge {
	position: relative;
	top: -5px;
	background-color: #f44336;
	color: white;
	font-size: 10px;
	font-weight: bold;
	padding: 1px 5px;
	border-radius: 8px;
	letter-spacing: 0.5px;
}
</style>
