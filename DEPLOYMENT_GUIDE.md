# 🚀 TAMM 배포 가이드

## 📋 배포 시나리오

### 👨‍💻 **개발자 (당신)가 해야 할 일**
✅ GitHub 토큰 생성 및 설정  
✅ 앱 빌드 및 배포  
✅ GitHub 저장소 관리  

### 👥 **일반 사용자가 해야 할 일**
✅ 앱 다운로드 및 설치  
✅ 회원가입/로그인  
❌ **토큰 설정 불필요!**

---

## 🔧 개발자 배포 단계

### **1단계: GitHub 설정**
```bash
# 1. GitHub 저장소 생성
Repository: leekyuk/tamm-users (Private 권장)

# 2. Personal Access Token 생성
권한: repo (Full control of private repositories)
```

### **2단계: 빌드 환경 설정**

#### **Windows 배포:**
```cmd
# 환경변수 설정
set GITHUB_TOKEN=your_github_token_here

# 앱 빌드
npm run build
npm run electron:build
```

#### **macOS/Linux 배포:**
```bash
# 환경변수 설정
export GITHUB_TOKEN=your_github_token_here

# 앱 빌드
npm run build
npm run electron:build
```

### **3단계: 배포 파일 생성**
- 빌드된 앱에는 GitHub 토큰이 안전하게 내장됨
- 일반 사용자는 추가 설정 없이 바로 사용 가능

---

## 👥 일반 사용자 사용법

### **설치:**
1. 개발자가 배포한 앱 다운로드
2. 설치 프로그램 실행
3. 앱 실행

### **사용:**
1. **회원가입**: 이메일, 비밀번호, 닉네임 입력
2. **승인 대기**: 관리자 승인까지 대기
3. **로그인**: 승인 후 이메일/비밀번호로 로그인
4. **스푼라디오 연동**: 기존 스푼 계정으로 로그인

### **주의사항:**
- ❌ GitHub 계정 필요 없음
- ❌ 토큰 설정 필요 없음
- ❌ 기술적 지식 불필요
- ✅ 단순히 앱 사용만 하면 됨

---

## 🔒 보안 구조

```
📱 TAMM 앱 (일반 사용자)
    ↓ (HTTP 요청)
🔐 GitHub API (내장된 토큰으로 인증)
    ↓ (데이터 저장/조회)
📁 leekyuk/tamm-users (개발자 소유)
    ↓ (사용자 데이터)
📊 approved-users.json
    pending-users.json
```

### **보안 특징:**
- ✅ 사용자는 GitHub API에 직접 접근하지 않음
- ✅ 토큰은 앱에 안전하게 내장됨  
- ✅ 사용자 데이터는 개발자 GitHub에만 저장
- ✅ 개인정보는 암호화되어 전송

---

## 🚨 자주 묻는 질문

### **Q: 사용자도 GitHub 토큰을 만들어야 하나요?**
**A: 아니요! 개발자만 토큰을 설정하면 됩니다.**

### **Q: 사용자가 GitHub 계정이 없어도 되나요?**
**A: 네! GitHub 계정이 전혀 필요하지 않습니다.**

### **Q: 앱을 재설치하면 회원정보가 사라지나요?**
**A: 아니요! 모든 데이터는 GitHub에 저장되어 안전합니다.**

### **Q: 여러 컴퓨터에서 같은 계정을 쓸 수 있나요?**
**A: 네! 어디서든 이메일/비밀번호로 로그인 가능합니다.**

---

## 📞 기술 지원

배포 관련 문제가 있으면:
1. GitHub 저장소 권한 확인
2. 토큰 유효성 검사  
3. 네트워크 연결 상태 확인

일반 사용자 지원:
1. 회원가입/로그인 문제
2. 승인 대기 관련 문의
3. 앱 사용법 문의 