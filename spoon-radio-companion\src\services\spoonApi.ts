// 스푼 API 관련 타입 정의
interface SpoonBroadcast {
  id: number;
  author: {
    id: number;
    nickname: string;
    profile_url: string;
    is_active: boolean;
    country: string;
    tag: string;
  };
  title: string;
  type: number;
  url_hls: string;
  img_url: string;
  like_count: number;
  member_count: number;
  total_member_count: number;
  is_editors: boolean;
  tier: {
    name: string;
    title: string;
  } | null;
  is_vip: boolean;
  is_adult: boolean;
  created: string;
  engine_name: string;
  live_call: {
    guests: Array<{
      id: number;
      nick_name: string;
      profile_url: string;
      guest_status: number;
    }>;
    version: number;
  } | null;
  is_live_call: boolean;
  categories: string[];
  is_verified: boolean;
  room_token: string | null;
  tags: string[];
  hashtags: Array<{
    id: number;
    name: string;
  }>;
  badge_style_ids: string[];
}

interface SpoonApiResponse {
  status_code: number;
  detail: string;
  next: string | null;
  previous: string | null;
  results: SpoonBroadcast[];
}

// 요청 제한을 위한 변수
let lastRequestTime = 0;
const REQUEST_DELAY = 500; // 500ms 간격

// 딜레이 함수
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 전체 라이브 방송 API 호출 함수 (cursor 기반 페이지네이션)
export const fetchAllBroadcasts = async (pageSize: number = 20, cursor?: string): Promise<{ broadcasts: SpoonBroadcast[]; nextCursor: string | null }> => {
  try {
    // 요청 제한 (500ms 간격)
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < REQUEST_DELAY) {
      await delay(REQUEST_DELAY - timeSinceLastRequest);
    }
    lastRequestTime = Date.now();

    let spoonApiUrl: string;
    if (cursor) {
      // cursor URL 그대로 사용
      spoonApiUrl = cursor;
    } else {
      // 첫 페이지 로드 - 올바른 API 엔드포인트 사용
      const timestamp = new Date().getTime();
      spoonApiUrl = `https://kr-api.spooncast.net/lives/?is_adult=0&page_size=${pageSize}&sort=1&_t=${timestamp}`;
    }
    
    console.log('🔄 직접 API 호출:', spoonApiUrl);
    
    const response = await fetch(spoonApiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      signal: AbortSignal.timeout(8000)
    });

    if (!response.ok) {
      if (response.status === 429) {
        console.warn('⏰ 요청 제한 (429) - 2초 후 재시도');
        await delay(2000);
        return fetchAllBroadcasts(pageSize, cursor);
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: SpoonApiResponse = await response.json();
    
    if (data.status_code === 200 && data.results) {
      console.log(`✅ API 호출 성공: ${data.results.length}개 방송 데이터`);
      return {
        broadcasts: data.results,
        nextCursor: data.next
      };
    } else {
      throw new Error('Invalid API response structure');
    }
  } catch (error) {
    console.error('스푼 API 호출 실패:', error);
    throw error;
  }
};

// 기존 함수명 유지 (호환성) - 첫 페이지만 반환
export const fetchPopularBroadcasts = async (pageSize: number = 20): Promise<SpoonBroadcast[]> => {
  const result = await fetchAllBroadcasts(pageSize);
  return result.broadcasts;
};

// 타입 내보내기
export type { SpoonBroadcast, SpoonApiResponse }; 