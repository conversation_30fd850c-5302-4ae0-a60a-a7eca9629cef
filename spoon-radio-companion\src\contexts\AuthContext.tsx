import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, onAuthStateChanged } from 'firebase/auth';
import { auth } from '../firebaseConfig';
import { signOut } from 'firebase/auth';

interface CustomAdminUser {
  uid: string;
  email: string;
  displayName: string;
  isAdmin: boolean;
}

interface AuthContextType {
  user: User | CustomAdminUser | null;
  loading: boolean;
  logout: () => Promise<void>;
  setUser: (user: User | CustomAdminUser | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};

// 기존 useAuth도 유지 (호환성)
export const useAuth = useAuthContext;

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUserState] = useState<User | CustomAdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  const setUser = (newUser: User | CustomAdminUser | null) => {
    setUserState(newUser);
  };

  const logout = async () => {
    try {
      await signOut(auth);
      // 관리자 세션도 제거
      localStorage.removeItem('tamm_admin_session');
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        setUser(firebaseUser);
      } else {
        // Firebase 인증이 없는 경우 로컬 관리자 세션 확인
        const adminSession = localStorage.getItem('tamm_admin_session');
        if (adminSession) {
          try {
            const adminUser = JSON.parse(adminSession);
            setUser(adminUser);
          } catch (error) {
            console.error('관리자 세션 복원 오류:', error);
            localStorage.removeItem('tamm_admin_session');
            setUser(null);
          }
        } else {
          setUser(null);
        }
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const value = {
    user,
    loading,
    logout,
    setUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 