# Core Api

스푼의 직접적인 정보를 가져오려면 [sopia-core](https://github.com/sopia-bot/sopia-core) 를 사용해 API를 호출할 수 있습니다.

생성된 클래스 객체는 `window.$sopia` 에 담겨있으며, 모든 객체 정보는 `window.$spoon`에 담겨있습니다.
<br><br>
언/팔로우 예제
```js
exports.live_message = async (evt /* LiveMessageSocket */, sock /* LiveSocket */)  => {
	const message = evt.update_component.message.value;
	if ( message === '.팔로우' ) {
		await window.$sopia.api.users.follow(evt.data.author);
		sock.message(`${evt.data.author.nickname} 님을 팔로우했습니다.`);
	} else if ( message === '.언팔로우' ) {
		await window.$sopia.api.users.unfollow(evt.data.author);
		sock.message(`${evt.data.author.nickname} 님을 언팔로우했습니다.`);
	}
}
```
