<!--
 * Index.vue
 * Created on Thu Oct 08 2020
 *
 * Copyright (c) Tree Some. Licensed under the MIT License.
-->
<template>
	<v-main class="custom white" v-if="user">
		<!-- S:Image Dialog -->
		<v-dialog
			v-model="image.show"
			width="80%"
			max-width="1000px"
			content-class="custom">
			<v-img
				:src="image.src"
				aspect-ratio="1.7"
				contain
				width="100%"></v-img>
		</v-dialog>
		<!-- E:Image Dialog -->
		<v-container class="pa-0 pa-md-4">
			<!-- S:PROFILE -->
			<v-card flat class="white rounded-0 rounded-md-lg overflow-hidden">
				<!-- 커버 이미지 컨테이너 -->
				<div 
					class="user-cover-container"
					:style="{ backgroundImage: `url(${backgroundImage || user.profile_url})` }">
					<!-- 그라데이션 효과 레이어 (큰 화면) -->
					<div class="gradient-overlay d-none d-md-block"></div>
					<!-- 비네트 효과 레이어 (작은 화면) -->
					<div class="vignette-overlay d-md-none"></div>
					
					<div class="overlay-top">
						<v-col cols="12" align="center" style="min-height: 72px; z-index: 2;">
							<!-- Choice 뱃지 (큰 화면) - 제거 -->
							<!-- 기존 큰 화면 뱃지 제거 -->
							
							<v-chip
								v-if="isLive"
								class="ma-2"
								@click="joinLive"
								color="red darken-2"
								dark
								style="cursor: pointer;">ON AIR</v-chip>
						</v-col>
					</div>
					
					<!-- 큰 화면에서만 프로필 정보 표시 -->
					<div class="overlay-bottom d-none d-md-flex">
						<v-col cols="12" style="z-index: 2; position: relative;">
							<div class="profile-container">
								<div class="profile-avatar-container">
									<div class="avatar-border-wrapper"
										:class="{
											'yellow-border': user.tier && user.tier.name === 'Yellow_Choice' && !user.profile_ring,
											'orange-border': user.tier && (user.tier.name === 'orange_choice' || user.tier.name === 'Orange_Choice') && !user.profile_ring,
											'red-border': user.tier && (user.tier.name === 'red_choice' || user.tier.name === 'Red_Choice') && !user.profile_ring
										}">
										<v-avatar 
											size="120" 
											class="profile-avatar" 
											:class="{'no-border': user.profile_ring}"
											style="border: 3px solid white; box-sizing: border-box; overflow: hidden !important; background-color: transparent !important;">
											<div style="width: 100%; height: 100%; overflow: hidden; border-radius: 50%; position: relative;">
												<img 
													:src="user.profile_url" 
													@click="image.src = user.profile_url; image.show = true;"
													style="width: 100%; height: 100%; object-fit: cover; display: block; position: absolute; top: 0; left: 0;"
													alt="프로필 이미지" />
											</div>
										</v-avatar>
									</div>
									
									<!-- 프로필 링 별도 처리 -->
									<div v-if="user.profile_ring" class="profile-ring-container">
										<!-- JSON 파일인 경우 Lottie 애니메이션으로 표시 -->
										<div v-if="user.profile_ring.type === 'LOTTIE'" ref="lottieContainer" class="profile-ring-lottie"></div>
										<!-- 이미지 파일인 경우 일반 이미지로 표시 -->
										<img v-else :src="user.profile_ring.url" class="profile-ring" alt="프로필 링" />
									</div>
								</div>

								<div class="profile-info">
									<div class="d-flex align-center">
										<h2 class="white--text custom-font mb-1">{{ user.nickname }}</h2>
										<span v-if="user.membership && user.membership.grade === 'PREMIUM'" class="membership-badge ml-2">Premium</span>
										<!-- Choice 뱃지 (큰 화면 이름 옆) -->
										<span 
											v-if="user.tier && user.tier.name === 'Yellow_Choice'" 
											class="choice-badge yellow-choice ml-2">Choice</span>
										<span 
											v-if="user.tier && (user.tier.name === 'Orange_Choice' || user.tier.name === 'Orange_Choice')"
											class="choice-badge orange-choice ml-2">Choice</span>
										<span 
											v-if="user.tier && (user.tier.name === 'Red_Choice' || user.tier.name === 'Red_Choice')"
											class="choice-badge red-choice ml-2">Choice</span>
									</div>
									<p class="white--text custom-font mb-3">@{{ user.tag }}</p>
									
									<!-- 팬/팔로잉 정보 -->
									<div class="d-flex">
										<div class="mr-5 text-center">
											<div class="white--text font-weight-bold text-h6">{{ user.follower_count }}</div>
											<div class="white--text text-caption">{{ $t('users.fan') }}</div>
										</div>
										<div class="text-center" @click="openUserFollowingList" style="cursor: pointer;">
											<div class="white--text font-weight-bold text-h6">{{ user.following_count }}</div>
											<div class="white--text text-caption">{{ $t('users.following') }}</div>
										</div>
									</div>
								</div>
							</div>
							
							<!-- 큰 화면에서 팬 버튼을 커버 이미지 좌측 하단으로 이동 -->
							<div class="cover-button-container">
								<v-btn
									rounded
									width="100"
									:color="isFollowing() ? 'purple' : 'purple darken-1'"
									:outlined="isFollowing()"
									:dark="!isFollowing()"
									@click="toggleFollow"
									:loading="user.isLoading"
									:disabled="user.isLoading"
								>
									<v-icon left size="16">{{ isFollowing() ? 'mdi-account-check' : 'mdi-account-plus' }}</v-icon>
									<span class="custom-font">팬</span>
								</v-btn>
							</div>
						</v-col>
					</div>
				</div>
				
				<!-- 큰 화면에서 description 섹션으로 이동 -->
				<div class="d-none d-md-block pa-4 text-left">
					<!-- 자기소개 텍스트 (큰 화면) -->
					<div 
						class="mb-4 px-2 py-3 self-intro-container"
						:class="{ 'expanded': expandedSelfIntro, 'has-gradient': !expandedSelfIntro && user.self_introduction && user.self_introduction.length > 100 }"
						@click="expandedSelfIntro = !expandedSelfIntro">
						<p class="text-body-1 custom-font" v-if="user.self_introduction" v-html="formatLineBreaks(user.self_introduction)"></p>
						<p class="text-body-1 custom-font grey--text" v-else>자기소개가 없습니다.</p>
						<div v-if="!expandedSelfIntro && user.self_introduction && user.self_introduction.length > 100" class="more-button">더보기..</div>
					</div>
					
					<!-- 설명 텍스트 (큰 화면) -->
					<div 
						class="mt-4 px-2 py-3 description-container"
						:class="{ 'expanded': expandedDescription, 'has-gradient': !expandedDescription && user.description && user.description.length > 100 }"
						@click="expandedDescription = !expandedDescription"
						v-if="user.description">
						<div class="d-flex align-center mb-2">
							<v-icon class="mr-2" color="black" size="20">mdi-bullhorn-outline</v-icon>
							<p class="text-subtitle-1 font-weight-medium mb-0">Notice</p>
						</div>
						<p class="text-body-1 custom-font" v-html="formatLineBreaks(user.description)"></p>
						<div v-if="!expandedDescription && user.description && user.description.length > 100" class="more-button">더보기..</div>
					</div>
					
					<!-- Fan Board (큰 화면) -->
					<FanBoard :userId="user.id" v-if="user.id" class="mt-4" />
					
					<!-- 팬 유저 목록 (큰 화면) -->
					<div class="mt-4" v-if="topFans && topFans.length > 0">
						<div class="d-flex align-center justify-space-between mb-3">
							<div class="d-flex align-center">
								<v-icon class="mr-2" color="black" size="20">mdi-account-group-outline</v-icon>
								<p class="text-subtitle-1 font-weight-medium mb-0">Fans</p>
							</div>
							<v-btn
								text
								small
								color="grey darken-1"
								class="text-caption"
								style="text-transform: none;"
								@click="openFanRankingModal"
							>
								전체보기
							</v-btn>
						</div>
						
						<div class="d-flex flex-wrap">
							<div v-for="(fan, index) in topFans" :key="index" class="fan-item" @click="goToUserPage(fan.user.id)">
								<v-avatar 
									size="100" 
									class="mb-1"
									:class="{
										'gold-border': index === 0,
										'silver-border': index === 1,
										'bronze-border': index === 2
									}">
									<v-img :src="fan.user.profile_url" cover></v-img>
								</v-avatar>
								<p class="text-caption mb-0 text-center fan-nickname">{{ fan.user.nickname }}</p>
							</div>
						</div>
					</div>
					
					<!-- Cast 목록 (큰 화면) -->
					<div class="mt-5">
						<div class="d-flex align-center mb-3">
							<v-icon class="mr-2" color="black" size="20">mdi-microphone-variant</v-icon>
							<p class="text-subtitle-1 font-weight-medium mb-0">Cast</p>
						</div>
						
						<div v-if="castList.length > 0" class="cast-grid">
							<div v-for="cast in castList" :key="cast.id" class="cast-item" @click="openCast(cast.contentData.id)">
								<div class="cast-thumbnail-container">
									<div class="cast-thumbnail" :style="{backgroundImage: `url(${cast.contentData.media[0]})`}">
										<div class="cast-duration">{{ formatDuration(cast.contentData.duration) }}</div>
									</div>
								</div>
								<div class="cast-info">
									<p class="cast-title">{{ cast.contentData.title }}</p>
									<div class="cast-stats">
										<div class="cast-likes">
											<v-icon size="14" color="grey">mdi-heart</v-icon>
											<span>{{ cast.contentData.likeCount }}</span>
										</div>
										<div class="cast-comments">
											<v-icon size="14" color="grey">mdi-message-outline</v-icon>
											<span>{{ cast.contentData.commentCount }}</span>
										</div>
										<div class="cast-date">{{ formatCastDate(cast.created) }}</div>
									</div>
								</div>
							</div>
						</div>
						<!-- 초기 로딩 상태 표시 -->
						<div v-else-if="castLoading" class="d-flex justify-center align-center pa-6">
							<v-progress-circular indeterminate color="purple"></v-progress-circular>
							<span class="ml-3 text-subtitle-1 grey--text">캐스트 로딩 중...</span>
						</div>
						<!-- 캐스트가 없는 경우 표시 -->
						<div v-else class="d-flex justify-center align-center pa-6">
							<span class="text-subtitle-1 grey--text">캐스트가 없습니다</span>
						</div>
						<!-- 추가 로딩 표시기 -->
						<div v-if="isLoadingMore" class="text-center py-4 loading-more">
							<v-progress-circular indeterminate color="purple" size="24"></v-progress-circular>
							<div class="mt-2 grey--text text-body-2">더 많은 캐스트 로딩 중...</div>
						</div>
						<!-- 모든 캐스트를 로드한 경우 표시 -->
						<div v-if="castList.length > 0 && !hasMoreCasts && !isLoadingMore" class="text-center py-3 grey--text text-body-2">
							모든 캐스트를 로드했습니다
						</div>
					</div>
				</div>
				
				<!-- 작은 화면에서만 프로필 정보 표시 (커버 이미지 아래) -->
				<div class="d-md-none pa-4 mobile-profile">
					<v-row>
						<v-col cols="12" class="d-flex align-center">
							<!-- Choice 뱃지 (작은 화면) - 제거 -->
							<!-- 기존 작은 화면 뱃지 제거 -->
							
							<div class="profile-avatar-container mr-4">
								<div class="avatar-border-wrapper"
									:class="{
										'yellow-border': user.tier && user.tier.name === 'Yellow_Choice' && !user.profile_ring,
										'orange-border': user.tier && (user.tier.name === 'orange_choice' || user.tier.name === 'Orange_Choice') && !user.profile_ring,
										'red-border': user.tier && (user.tier.name === 'red_choice' || user.tier.name === 'Red_Choice') && !user.profile_ring
									}">
									<v-avatar 
										size="120" 
										class="profile-avatar" 
										:class="{'no-border': user.profile_ring}"
										style="border: 3px solid #f0f0f0; cursor: pointer; box-sizing: border-box; overflow: hidden !important; background-color: transparent !important;">
										<div style="width: 100%; height: 100%; overflow: hidden; border-radius: 50%; position: relative;">
											<img 
												:src="user.profile_url" 
												@click="image.src = user.profile_url; image.show = true;"
												style="width: 100%; height: 100%; object-fit: cover; display: block; position: absolute; top: 0; left: 0;"
												alt="프로필 이미지" />
										</div>
									</v-avatar>
								</div>
								
								<!-- 프로필 링 별도 처리 (작은 화면) -->
								<div v-if="user.profile_ring" class="profile-ring-container">
									<!-- JSON 파일인 경우 Lottie 애니메이션으로 표시 -->
									<div v-if="user.profile_ring.type === 'LOTTIE'" ref="lottieMobileContainer" class="profile-ring-lottie"></div>
									<!-- 이미지 파일인 경우 일반 이미지로 표시 -->
									<img v-else :src="user.profile_ring.url" class="profile-ring" alt="프로필 링" />
								</div>
							</div>
							<div class="flex-grow-1">
								<div class="d-flex justify-space-between align-center">
									<div>
										<div class="d-flex align-center">
											<h2 class="text-h5 mb-1 custom-font">{{ user.nickname }}</h2>
											<span v-if="user.membership && user.membership.grade === 'PREMIUM'" class="membership-badge-mobile ml-2">Premium</span>
											<!-- Choice 뱃지 (작은 화면 이름 옆) -->
											<span 
												v-if="user.tier && user.tier.name === 'Yellow_Choice'" 
												class="choice-badge-mobile yellow-choice ml-2">Choice</span>
											<span 
												v-if="user.tier && (user.tier.name === 'orange_choice' || user.tier.name === 'Orange_Choice')"
												class="choice-badge-mobile orange-choice ml-2">Choice</span>
											<span 
												v-if="user.tier && (user.tier.name === 'red_choice' || user.tier.name === 'Red_Choice')"
												class="choice-badge-mobile red-choice ml-2">Choice</span>
										</div>
										<p class="text-subtitle-2 grey--text custom-font">@{{ user.tag }}</p>
										
										<!-- 팬/팔로잉 정보 -->
										<div class="d-flex mt-2">
											<div class="mr-5 text-center">
												<div class="font-weight-bold text-body-1">{{ user.follower_count }}</div>
												<div class="text-caption grey--text text--darken-1">팬</div>
											</div>
											<div class="text-center" @click="openUserFollowingList" style="cursor: pointer;">
												<div class="font-weight-bold text-body-1">{{ user.following_count }}</div>
												<div class="text-caption grey--text text--darken-1">팔로잉</div>
											</div>
										</div> 
									</div>
									
									<!-- 작은 화면에서 팬 버튼을 오른쪽으로 이동하고 너비 줄임 -->
									<div>
										<v-btn
											rounded
											width="80"
											small
											:color="isFollowing() ? 'purple' : 'purple darken-1'"
											:outlined="isFollowing()"
											:dark="!isFollowing()"
											@click="toggleFollow"
											:loading="user.isLoading"
											:disabled="user.isLoading"
										>
											<v-icon left size="16">{{ isFollowing() ? 'mdi-account-check' : 'mdi-account-plus' }}</v-icon>
											팬
										</v-btn>
									</div>
								</div>
							</div>
						</v-col>
					</v-row>
					
					<!-- 자기소개 텍스트 (작은 화면) -->
					<v-row>
						<v-col cols="12" class="pt-3 pb-3">
							<div 
								class="self-intro-container-mobile pa-3"
								:class="{ 'expanded': expandedSelfIntro, 'has-gradient': !expandedSelfIntro && user.self_introduction && user.self_introduction.length > 100 }"
								@click="expandedSelfIntro = !expandedSelfIntro">
								<p class="text-body-2 custom-font" v-if="user.self_introduction" v-html="formatLineBreaks(user.self_introduction)"></p>
								<p class="text-body-2 custom-font grey--text" v-else>자기소개가 없습니다.</p>
								<div v-if="!expandedSelfIntro && user.self_introduction && user.self_introduction.length > 100" class="more-button">더보기..</div>
							</div>
						</v-col>
					</v-row>
					
					<!-- 설명 텍스트 (작은 화면) -->
					<v-row v-if="user.description">
						<v-col cols="12" class="pt-3 pb-3">
							<div 
								class="description-container-mobile pa-3"
								:class="{ 'expanded': expandedDescription, 'has-gradient': !expandedDescription && user.description && user.description.length > 100 }"
								@click="expandedDescription = !expandedDescription">
								<div class="d-flex align-center mb-2">
									<v-icon class="mr-2" color="black" size="18">mdi-bullhorn-outline</v-icon>
									<p class="text-subtitle-2 font-weight-medium mb-0">Notice</p>
								</div>
								<p class="text-body-2 custom-font" v-html="formatLineBreaks(user.description)"></p>
								<div v-if="!expandedDescription && user.description && user.description.length > 100" class="more-button">더보기..</div>
							</div>
						</v-col>
					</v-row>
					
					<!-- Fan Board (작은 화면) -->
					<v-row v-if="user.id">
						<v-col cols="12" class="pt-3 pb-3">
							<FanBoard :userId="user.id" />
						</v-col>
					</v-row>
					
					<!-- 팬 유저 목록 (작은 화면) -->
					<v-row v-if="topFans && topFans.length > 0">
						<v-col cols="12" class="pt-3 pb-3">
							<div class="fan-container-mobile pa-3">
								<div class="d-flex align-center justify-space-between mb-2">
									<div class="d-flex align-center">
										<v-icon class="mr-2" color="black" size="18">mdi-account-group-outline</v-icon>
										<p class="text-subtitle-2 font-weight-medium mb-0">Fans</p>
									</div>
									<v-btn
										text
										x-small
										color="grey darken-1"
										class="text-caption"
										style="text-transform: none;"
										@click="openFanRankingModal"
									>
										전체보기
									</v-btn>
								</div>
								
								<div class="d-flex flex-wrap">
									<div v-for="(fan, index) in topFans" :key="index" class="fan-item" @click="goToUserPage(fan.user.id)">
										<v-avatar 
											size="90" 
											class="mb-1"
											:class="{
												'gold-border': index === 0,
												'silver-border': index === 1,
												'bronze-border': index === 2
											}">
											<v-img :src="fan.user.profile_url" cover></v-img>
										</v-avatar>
										<p class="text-caption mb-0 text-center fan-nickname">{{ fan.user.nickname }}</p>
									</div>
								</div>
							</div>
						</v-col>
					</v-row>
					
					<!-- Cast 목록 (작은 화면) -->
					<v-row>
						<v-col cols="12" class="pt-3 pb-3">
							<div class="cast-container-mobile pa-3">
								<div class="d-flex align-center mb-3">
									<v-icon class="mr-2" color="black" size="18">mdi-microphone-variant</v-icon>
									<p class="text-subtitle-2 font-weight-medium mb-0">Cast</p>
								</div>
								
								<div v-if="castList.length > 0" class="cast-grid-mobile">
									<div v-for="cast in castList" :key="cast.id" class="cast-item-mobile" @click="openCast(cast.contentData.id)">
										<div class="cast-thumbnail-container">
											<div class="cast-thumbnail" :style="{backgroundImage: `url(${cast.contentData.media[0]})`}">
												<div class="cast-duration">{{ formatDuration(cast.contentData.duration) }}</div>
											</div>
										</div>
										<div class="cast-info">
											<p class="cast-title">{{ cast.contentData.title }}</p>
											<div class="cast-stats">
												<div class="cast-likes">
													<v-icon size="12" color="grey">mdi-heart</v-icon>
													<span>{{ cast.contentData.likeCount }}</span>
												</div>
												<div class="cast-comments">
													<v-icon size="12" color="grey">mdi-message-outline</v-icon>
													<span>{{ cast.contentData.commentCount }}</span>
												</div>
												<div class="cast-date">{{ formatCastDate(cast.created) }}</div>
											</div>
										</div>
									</div>
								</div>
								<!-- 초기 로딩 상태 표시 (작은 화면) -->
								<div v-else-if="castLoading" class="d-flex justify-center align-center pa-4">
									<v-progress-circular indeterminate color="purple" size="24"></v-progress-circular>
									<span class="ml-3 text-body-2 grey--text">캐스트 로딩 중...</span>
								</div>
								<!-- 캐스트가 없는 경우 표시 (작은 화면) -->
								<div v-else class="d-flex justify-center align-center pa-4">
									<span class="text-body-2 grey--text">캐스트가 없습니다</span>
								</div>
								<!-- 추가 로딩 표시기 (작은 화면) -->
								<div v-if="isLoadingMore" class="text-center py-3 loading-more">
									<v-progress-circular indeterminate color="purple" size="20"></v-progress-circular>
									<div class="mt-1 grey--text text-caption">더 많은 캐스트 로딩 중...</div>
								</div>
								<!-- 모든 캐스트를 로드한 경우 표시 (작은 화면) -->
								<div v-if="castList.length > 0 && !hasMoreCasts && !isLoadingMore" class="text-center py-2 grey--text text-caption">
									모든 캐스트를 로드했습니다
								</div>
							</div>
						</v-col>
					</v-row>
				</div>
			</v-card>
			<!-- E:PROFILE -->
		</v-container>
		
		<!-- 팬 랭킹 모달 -->
		<FanRankingModal
			v-if="user && user.id"
			v-model="fanRankingModalOpen"
			:userId="user.id"
			:userNickname="user.nickname"
		/>
	</v-main>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { User } from '@sopia-bot/core';
import SearchHeader from '../Search/Header.vue';
import FanBoard from '../Components/FanBoard.vue';
import FanRankingModal from '../Components/FanRankingModal.vue';
import lottie from 'lottie-web';

// User에서 필요한 속성만 가져와서 새로운 인터페이스 정의
interface ExtendedUser {
	id: number;
	nickname: string;
	tag: string;
	profile_url: string;
	follower_count: number;
	following_count: number;
	follow_status: number;
	current_live?: any;
	self_introduction?: string;
	description: string;
	membership?: {
		cast_id: number;
		color_code: number;
		grade: string;
	};
	tier?: {
		name: string;
		title: string;
	};
	profile_ring?: {
		type: string;
		url: string;
	};
	isLoading?: boolean;
	
	// User 클래스의 메소드 (필요한 경우)
	follow: () => Promise<any>;
	unfollow: () => Promise<any>;
}

@Component({
	components: {
		SearchHeader,
		FanBoard,
		FanRankingModal,
	},
})
export default class UserPage extends Mixins(GlobalMixins) {
	public user: ExtendedUser = {} as ExtendedUser;
	public image = {
		show: false,
		src: '',
	};
	public backgroundImage: string = '';
	public topFans: any[] = [];
	public expandedDescription = false;
	public expandedSelfIntro = false;
	
	// 캐스트 목록 관련 데이터 추가
	public castList: any[] = [];
	public castLoading: boolean = false;
	public castError: string = '';
	public castOffset: string = ''; // 다음 페이지 로드를 위한 offset 값
	public isLoadingMore: boolean = false; // 추가 로드 중인지 표시
	public hasMoreCasts: boolean = true; // 더 로드할 캐스트가 있는지 여부
	public castScrollContainer: any = null; // 스크롤 컨테이너 참조
	public boundHandleScroll: any = null;
	public _lastLoadTime: number = 0;
	public _lastScrollTime: number = 0; // 스크롤 이벤트 스로틀링을 위한 마지막 스크롤 시간
	
	// Lottie 애니메이션 인스턴스 저장 변수
	private lottieAnim: any = null;
	private lottieMobileAnim: any = null;

	// 팬 랭킹 모달 관련
	public fanRankingModalOpen: boolean = false;

	get isLive() {
		return !!this.user?.current_live?.id;
	}

	// 팔로우 상태를 확인하는 메서드
	isFollowing(): boolean {
		// follow_status가 1 또는 3이면 팔로우 중인 상태
		return this.user.follow_status === 1 || this.user.follow_status === 3;
	}

	// 줄바꿈 처리를 위한 메서드
	formatLineBreaks(text: string | undefined): string {
		if (!text) return '';
		// \n을 <br> 태그로 변환 (이스케이프된 \n과 실제 줄바꿈 모두 처리)
		return text.replace(/\\n/g, '<br>').replace(/\n/g, '<br>');
	}

	public async created() {
		const { id } = this.$route.params;
		if ( id ) {
			try {
				// 기존 방식 제거하고 직접 API 호출만 사용
				const response = await fetch(`https://kr-api.spooncast.net/users/${id}/`);
				const data = await response.json();
				
				// 사용자 정보 설정
				if (data?.result?.channel) {
					// 크롬 콘솔 방식의 응답 구조 사용
					this.user = {
						id: data.result.channel.id,
						nickname: data.result.channel.nickname,
						tag: data.result.channel.tag,
						profile_url: data.result.channel.profileUrl,
						follower_count: data.result.channel.followerCount,
						following_count: data.result.channel.subscriberCount || 0,
						follow_status: data.result.channel.followStatus,
						current_live: data.result.channel.currentLiveId ? { id: data.result.channel.currentLiveId } : null,
						self_introduction: data.result.channel.selfIntroduction || '',
						description: data.result.channel.description || '',
						membership: data.result.channel.membership,
						tier: data.result.channel.tier,
						profile_ring: data.result.channel.profileRing
					} as ExtendedUser;
					
					// 커버 이미지 설정
					if (data.result.channel.profileCoverUrl) {
						this.backgroundImage = data.result.channel.profileCoverUrl;
					} else {
						this.backgroundImage = data.result.channel.profileUrl;
					}
					
					// 사용자 캐스트 목록 로드
					this.loadUserCasts(data.result.channel.id);
				} else if (data?.results && data.results[0]) {
					// 기존 API 응답 구조도 처리
					const userResult = data.results[0];
					this.user = userResult as ExtendedUser;
					
					// 커버 이미지 설정
					if (userResult.profile_cover_url) {
						this.backgroundImage = userResult.profile_cover_url;
					} else {
						this.backgroundImage = userResult.profile_url;
					}
					
					// 팬 유저 목록 설정
					if (data.results[0].top_fans && data.results[0].top_fans.length > 0) {
						this.topFans = data.results[0].top_fans;
					}
					
					// 사용자 캐스트 목록 로드
					this.loadUserCasts(userResult.id);
				}
				
				// 스크롤 이벤트 감지를 위한 타이머 설정
				this.$nextTick(() => {
					// 초기 설정 지연 (DOM이 완전히 렌더링된 후 설정)
					setTimeout(() => {
						this.setupScrollListeners();
						
						// 초기 스크롤 테스트 (5초 후)
						setTimeout(() => {
							this.checkInitialLoadMore();
						}, 3000);
					}, 500);
				});
				
			} catch (error) {
				// 오류 발생 시 조용히 처리
			}
		}
	}

	public async toggleFollow() {
		// 이미 로딩 중이면 중복 요청 방지
		if (this.user.isLoading) return;
		
		// 로딩 상태 설정
		this.user.isLoading = true;
		
		try {
			const isCurrentlyFollowing = this.isFollowing();
			const action = isCurrentlyFollowing ? 'unfollow' : 'follow';
			const url = `/users/${this.user.id}/${action}/`;
			
			const response = await this.$sopia.api.request({
				url,
				method: 'POST',
			});
			
			if (response && response.res && response.res.status_code === 200) {
				// 팔로우 상태 변경 성공
				if (isCurrentlyFollowing) {
					// 언팔로우 성공 - follow_status를 0으로 변경
					this.user.follow_status = 0;
					
					// 팔로워 수 감소
					if (this.user.follower_count > 0) {
						this.user.follower_count -= 1;
					}
				} else {
					// 팔로우 성공 - follow_status를 1로 변경
					this.user.follow_status = 1;
					
					// 팔로워 수 증가
					this.user.follower_count += 1;
				}
			}
		} catch (error) {
			// 오류 발생 시 조용히 처리
		} finally {
			// 로딩 상태 해제
			this.user.isLoading = false;
		}
	}

	// 사용자의 팔로잉 목록 보기
	public openUserFollowingList() {
		if (this.user && this.user.id) {
			// 팔로잉 다이얼로그에 현재 사용자 ID와 닉네임 전달
			this.$store.commit('setUserFollowingInfo', {
				userId: this.user.id,
				nickname: this.user.nickname
			});
			
			// 팔로잉 다이얼로그 열기
			this.$store.commit('setUserFollowingDialogOpen', true);
		}
	}

	public async joinLive() {
		this.$evt.$emit('live-join', this.user.current_live.id);
	}

	public goToUserPage(userId: number) {
		this.$router.push(`/user/${userId}`);
	}
	
	// 사용자의 캐스트 목록을 가져오는 메서드
	public async loadUserCasts(userId: number, isLoadingMore = false) {
		// 첫 로드시 초기화
		if (!isLoadingMore) {
			this.castLoading = true;
			this.castList = [];
			this.castOffset = '';
			this.hasMoreCasts = true;
		} else {
			// 이미 로딩 중이거나 더 로드할 항목이 없으면 중지
			if (this.isLoadingMore || !this.hasMoreCasts) {
				return;
			}
			this.isLoadingMore = true;
		}
		
		this.castError = '';
		
		try {
			// API 호출 URL 구성
			let apiUrl = `https://kr-gw.spooncast.net/feed/${userId}/DJ?contentType=CAST&contentSource=GENERAL&excludeContentType=TALK`;
			
			if (isLoadingMore && this.castOffset) {
				apiUrl += `&isNext=true&offset=${this.castOffset}`;
			} else {
				apiUrl += '&isNext=false';
			}
			
			const response = await fetch(apiUrl);
			
			if (!response.ok) {
				throw new Error(`API 호출 실패: ${response.status} ${response.statusText}`);
			}
			
			const data = await response.json();
			
			if (data && data.results && Array.isArray(data.results)) {
				// offset 값 저장
				this.castOffset = data.offset || '';
				
				// 결과가 없거나 빈 배열이면 더 이상 로드할 데이터가 없음
				if (data.results.length === 0) {
					this.hasMoreCasts = false;
				} else {
					// 기존 목록에 추가
					if (isLoadingMore) {
						this.castList = [...this.castList, ...data.results];
					} else {
						this.castList = data.results;
					}
					
					// 첫 로드 후 화면 크기에 따라 추가 로드 필요한지 확인
					if (!isLoadingMore) {
						this.$nextTick(() => {
							this.checkPageNeedsMoreContent();
						});
					}
				}
			} else {
				this.hasMoreCasts = false;
			}
		} catch (error) {
			this.castError = error instanceof Error ? error.message : '알 수 없는 오류가 발생했습니다.';
			this.hasMoreCasts = false;
		} finally {
			this.castLoading = false;
			this.isLoadingMore = false;
		}
	}
	
	// 캐스트 재생 시간을 포맷팅하는 메서드
	public formatDuration(durationInSeconds: number): string {
		if (!durationInSeconds) return '00:00';
		
		const minutes = Math.floor(durationInSeconds / 60);
		const seconds = Math.floor(durationInSeconds % 60);
		return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
	}
	
	// 캐스트 날짜를 포맷팅하는 메서드
	public formatCastDate(dateStr: string): string {
		if (!dateStr) return '';
		
		const date = new Date(dateStr);
		const year = date.getFullYear();
		const month = date.getMonth() + 1;
		const day = date.getDate();
		
		return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
	}
	
	// 캐스트 열기
	public openCast(castId: number) {
		// 캐스트 페이지로 이동
		this.$router.push(`/cast/${castId}`);
	}

	// 스크롤 이벤트 리스너 설정 메서드 추가
	public setupScrollListeners() {
		// 디버그 로그 제거
		
		// vue-scroll이 있는지 확인하고 스크롤 컨테이너 찾기
		const vueScrollContainer = document.querySelector('.v-main .vue-scroll');
		const mainContainer = document.querySelector('.v-main');
		
		// 스크롤 컨테이너를 찾아서 이벤트 리스너 추가
		if (vueScrollContainer) {
			this.boundHandleScroll = this.throttledHandleScroll.bind(this);
			vueScrollContainer.addEventListener('scroll', this.boundHandleScroll, { passive: true });
			this.castScrollContainer = vueScrollContainer;
		} else if (mainContainer) {
			this.boundHandleScroll = this.throttledHandleScroll.bind(this);
			mainContainer.addEventListener('scroll', this.boundHandleScroll, { passive: true });
			this.castScrollContainer = mainContainer;
		} else {
			this.boundHandleScroll = this.throttledHandleScroll.bind(this);
			window.addEventListener('scroll', this.boundHandleScroll, { passive: true });
			this.castScrollContainer = window;
		}
		
		// 수동 스크롤 이벤트 트리거 (한 번)
		window.addEventListener('resize', this.handleResize, { passive: true });
	}

	public removeScrollListeners() {
		if (this.boundHandleScroll) {
			if (this.castScrollContainer) {
				if (this.castScrollContainer === window) {
					window.removeEventListener('scroll', this.boundHandleScroll);
				} else {
					(this.castScrollContainer as HTMLElement).removeEventListener('scroll', this.boundHandleScroll);
				}
			}
			this.boundHandleScroll = null;
		}
		
		window.removeEventListener('resize', this.handleResize);
	}

	// 스로틀링된 스크롤 핸들러 개선
	public throttledHandleScroll(event: Event) {
		// 현재 시간 확인
		const now = Date.now();
		// 마지막 실행 시간과 비교하여 200ms 이상 지났는지 확인 (스로틀링)
		if (!this._lastScrollTime || now - this._lastScrollTime >= 200) {
			this._lastScrollTime = now;
			// 로딩 상태 및 데이터 상태 확인은 handleScroll 내부로 이동
			this.handleScroll(event);
		}
	}

	// 스크롤 핸들러 개선
	public handleScroll(event: Event) {
		// 스크롤 위치 계산
		let scrollHeight = 0;
		let scrollTop = 0; 
		let clientHeight = 0;

		// 스크롤 컨테이너 확인
		if (this.castScrollContainer === window) {
			// window 스크롤인 경우
			scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
			scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop;
			clientHeight = window.innerHeight;
		} else if (event.target) {
			// 이벤트 타겟이 있는 경우
			const target = event.target as HTMLElement;
			scrollHeight = target.scrollHeight;
			scrollTop = target.scrollTop;
			clientHeight = target.clientHeight;
		} else if (this.castScrollContainer) {
			// 직접 컨테이너 참조
			const container = this.castScrollContainer as HTMLElement;
			scrollHeight = container.scrollHeight;
			scrollTop = container.scrollTop;
			clientHeight = container.clientHeight;
		}
		
		// 스크롤 위치 디버깅을 제거하고 로직 개선
		// 스크롤이 하단에서 300px 이내일 때 다음 페이지 로드 (값을 크게 증가)
		const remainingDistance = scrollHeight - (scrollTop + clientHeight);
		if (remainingDistance < 300) {
			this.loadMoreCasts();
		}
	}

	// 다음 페이지 로드 메서드 추가
	public loadMoreCasts() {
		// 디바운싱 - 이미 로딩 중이거나 데이터가 없으면 무시
		if (this.isLoadingMore || !this.hasMoreCasts || !this.user || !this.user.id) {
			return;
		}
		
		// 디바운싱 - 최근 호출 시간 확인 (1000ms 이내 중복 호출 방지)
		const now = Date.now();
		if (this._lastLoadTime && now - this._lastLoadTime < 1000) {
			return;
		}
		
		this._lastLoadTime = now;
		this.loadUserCasts(this.user.id, true);
	}

	// 초기 로드 추가 확인
	public checkInitialLoadMore() {
		// 스크롤이 필요 없이 화면에 모든 콘텐츠가 표시되는 경우 자동으로 다음 페이지 로드
		if (!this.castScrollContainer) return;
		
		let scrollHeight, clientHeight;
		
		if (this.castScrollContainer === window) {
			scrollHeight = document.documentElement.scrollHeight;
			clientHeight = window.innerHeight;
		} else {
			scrollHeight = (this.castScrollContainer as HTMLElement).scrollHeight;
			clientHeight = (this.castScrollContainer as HTMLElement).clientHeight;
		}
		
		// 컨텐츠가 화면보다 작거나 거의 같을 때 더 로드 (여유 공간 추가)
		if (scrollHeight - clientHeight < 200 && this.hasMoreCasts && this.castOffset) {
			this.loadMoreCasts();
		}
	}

	// mounted 메서드 개선: 초기 로드 후 스크롤 이벤트 강제 발생
	public mounted() {
		// 초기 설정은 created 메서드에서 처리됨
		// 컴포넌트가 마운트된 후 스크롤 이벤트 테스트
		setTimeout(() => {
			if (!this.boundHandleScroll || !this.castScrollContainer) {
				this.setupScrollListeners();
			}
			
			// 초기 스크롤 이벤트 다중 트리거 추가
			for (let i = 0; i < 3; i++) {
				setTimeout(() => {
					// 더 로드할 콘텐츠가 있는지 확인
					this.checkPageNeedsMoreContent();
					
					// 스크롤 이벤트 강제 발생 (더 강력하게)
					if (this.hasMoreCasts) {
						const event = new Event('scroll');
						if (this.castScrollContainer === window) {
							window.dispatchEvent(event);
						} else if (this.castScrollContainer instanceof HTMLElement) {
							this.castScrollContainer.dispatchEvent(event);
						}
					}
				}, 800 * (i + 1)); // 800ms, 1600ms, 2400ms에 각각 발생
			}
		}, 500);
		
		// 프로필 링이 Lottie 타입인 경우 애니메이션 로드
		this.loadLottieAnimation();
	}

	public beforeDestroy() {
		// 컴포넌트 제거 시 이벤트 해제
		this.removeScrollListeners();
		
		// Lottie 애니메이션 정리
		if (this.lottieAnim) {
			this.lottieAnim.destroy();
		}
		if (this.lottieMobileAnim) {
			this.lottieMobileAnim.destroy();
		}
	}

	// handleResize 메서드
	public handleResize() {
		// 화면 크기가 변경될 때마다 스크롤 컨테이너 다시 참조
		this.$nextTick(() => {
			// 화면 크기 변경 시 처리
		});
	}

	// Lottie 애니메이션 로드 메서드
	loadLottieAnimation() {
		// 타이밍 이슈를 방지하기 위해 setTimeout 사용
		setTimeout(() => {
			if (this.user?.profile_ring?.type === 'LOTTIE') {
				// 큰 화면 Lottie 애니메이션
				const container = this.$refs.lottieContainer as HTMLElement;
				if (container) {
					this.lottieAnim = lottie.loadAnimation({
						container: container,
						renderer: 'svg',
						loop: true,
						autoplay: true,
						path: this.user.profile_ring.url
					});
				}
				
				// 작은 화면 Lottie 애니메이션
				const mobileContainer = this.$refs.lottieMobileContainer as HTMLElement;
				if (mobileContainer) {
					this.lottieMobileAnim = lottie.loadAnimation({
						container: mobileContainer,
						renderer: 'svg',
						loop: true,
						autoplay: true,
						path: this.user.profile_ring.url
					});
				}
			}
		}, 300);
	}

	// 화면 내용에 따라 추가 콘텐츠 필요한지 확인하는 새 메서드
	public checkPageNeedsMoreContent() {
		// 이미 충분한 콘텐츠가 있는지 확인
		if (!this.castScrollContainer || !this.hasMoreCasts || this.castList.length === 0) return;
		
		let scrollHeight, clientHeight;
		
		if (this.castScrollContainer === window) {
			scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
			clientHeight = window.innerHeight;
		} else {
			scrollHeight = (this.castScrollContainer as HTMLElement).scrollHeight;
			clientHeight = (this.castScrollContainer as HTMLElement).clientHeight;
		}
		
		// 현재 화면에 표시된 콘텐츠가 충분하지 않으면 더 로드
		// 여유 공간을 500px로 크게 설정하여 스크롤 없이도 더 많은 콘텐츠 로드
		if (scrollHeight - clientHeight < 500) {
			// 로드 지연 시간을 짧게 설정하여 빠르게 콘텐츠 채우기
			setTimeout(() => {
				this.loadMoreCasts();
			}, 300);
		}
	}

	// 팬 랭킹 모달 열기
	public openFanRankingModal() {
		this.fanRankingModalOpen = true;
	}
}
</script>
<style scoped>
.custom .v-parallax__content {
	padding: 0;
}
.custom.v-dialog {
	box-shadow: none !important;
}

.user-cover-container {
	position: relative;
	height: 300px;
	width: 100%;
	background-size: cover;
	background-position: center 40%; /* 상단이 잘리지 않도록 조정 */
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	border-radius: 0;
	background-color: white; /* 배경색을 흰색으로 명시적 설정 */
	overflow: hidden;
}

/* 그라데이션 오버레이 (큰 화면) */
.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 60%;
	height: 100%;
	background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.5) 30%, rgba(0,0,0,0) 100%);
	z-index: 1;
}

/* 비네트 오버레이 (작은 화면) */
.vignette-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, 
		rgba(0,0,0,0.6) 0%, 
		rgba(0,0,0,0.3) 30%, 
		rgba(0,0,0,0.1) 60%, 
		rgba(0,0,0,0) 100%);
	z-index: 1;
}

.overlay-top, .overlay-bottom, .overlay-actions {
	width: 100%;
	position: relative;
}

.overlay-top {
	display: flex;
	justify-content: center;
}

.overlay-bottom {
	display: flex;
	flex-direction: column;
}

.overlay-actions {
	padding-bottom: 8px;
}

/* 반응형 스타일 */
@media (min-width: 960px) {
	.user-cover-container {
		border-radius: 24px; /* 더 둥글게 변경 */
		height: 340px; /* 높이 조정 */
		background-position: center 25%; /* 상단이 잘리지 않도록 조정 */
	}
}

.mobile-profile {
	border-bottom: 1px solid #f0f0f0;
}

/* Vuetify 헬퍼 클래스 */
.rounded-md-lg {
	border-radius: 0;
}

@media (min-width: 960px) {
	.rounded-md-lg {
		border-radius: 24px !important; /* 더 둥글게 변경 */
		overflow: hidden;
		background-color: white !important; /* 배경색 명시적 설정 */
	}
}

/* 전체 폰트 변경 */
.custom-font {
	font-family: 'Pretendard', 'SUIT', 'Noto Sans KR', sans-serif !important;
	letter-spacing: -0.02em;
}

/* 전역 폰트 설정 */
:root {
	--font-family-sans-serif: 'Pretendard', 'SUIT', 'Noto Sans KR', sans-serif;
}

/* v-application 전체 폰트 설정 */
.v-application {
	font-family: 'Pretendard', 'SUIT', 'Noto Sans KR', sans-serif !important;
	letter-spacing: -0.02em;
}

/* 폰트 임포트 */
@import url('https://cdn.jsdelivr.net/gh/orioncactus/pretendard/dist/web/static/pretendard.css');

/* 자기소개 컨테이너 스타일 */
.self-intro-container {
	background-color: #f8f8f8;
	border-radius: 12px;
	max-height: 200px;
	overflow-y: hidden;
	transition: max-height 0.3s ease;
	cursor: pointer;
	position: relative;
}

.self-intro-container-mobile {
	background-color: #f8f8f8;
	border-radius: 12px;
	max-height: 120px;
	overflow-y: hidden;
	transition: max-height 0.3s ease;
	cursor: pointer;
	position: relative;
}

.self-intro-container.expanded,
.description-container.expanded,
.self-intro-container-mobile.expanded,
.description-container-mobile.expanded {
	max-height: none;
	overflow-y: visible;
}

/* 그라데이션 효과 */
.has-gradient::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 60px;
	background: linear-gradient(to bottom, rgba(248, 248, 248, 0) 0%, rgba(248, 248, 248, 1) 100%);
	pointer-events: none;
}

/* 자기소개 및 설명 컨테이너 스타일 */
.description-container, .description-container-mobile {
	background-color: #f8f8f8;
	border-radius: 12px;
	max-height: 200px;
	overflow-y: hidden;
	transition: max-height 0.3s ease;
	cursor: pointer;
	position: relative;
}

/* 프로필 컨테이너 스타일 */
.profile-container {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
	padding-left: 24px;
	height: 150px;
}

.profile-avatar-container {
	position: relative;
	width: 120px;
	height: 120px;
	margin-right: 24px;
	flex-shrink: 0;
}

.avatar-border-wrapper {
	position: relative;
	width: 130px;
	height: 130px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 5px solid transparent;
	z-index: 1;
}

.profile-avatar {
	cursor: pointer;
	flex-shrink: 0;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 2;
	overflow: hidden !important;
	padding: 0 !important;
}

.profile-avatar .v-image, 
.profile-avatar .v-responsive, 
.profile-avatar .v-responsive__content {
	width: 100% !important;
	height: 100% !important;
	padding: 0 !important;
	margin: 0 !important;
	border-radius: 50% !important;
	overflow: hidden !important;
}

.profile-avatar .v-image__image {
	background-size: cover !important;
	transform: scale(1.02) !important;
	background-position: center center !important;
}

.profile-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

/* 커버 이미지 내 버튼 컨테이너 (큰 화면) */
.cover-button-container {
	position: absolute;
	bottom: 20px;
	right: 24px;
	z-index: 3;
}

/* 팬 아이템 스타일 */
.fan-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 16px;
	margin-bottom: 16px;
	width: 100px;
	cursor: pointer;
}

/* 팬 랭킹 테두리 */
.gold-border {
	border: 4px solid gold !important;
}

.silver-border {
	border: 4px solid silver !important;
}

.bronze-border {
	border: 4px solid #cd7f32 !important; /* 청동색 */
}

.fan-nickname {
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.fan-container-mobile {
	background-color: #f8f8f8;
	border-radius: 12px;
	padding: 12px;
}

/* 멤버십 뱃지 스타일 */
.membership-badge {
	background-color: #f1f1f1;
	color: #4e7fe2;
	font-size: 0.75rem;
	padding: 2px 8px;
	border-radius: 50px;
	font-weight: 800;
	display: inline-block;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.membership-badge-mobile {
	background-color: #f1f1f1;
	color: #4e7fe2;
	font-size: 0.65rem;
	padding: 1px 6px;
	border-radius: 50px;
	font-weight: 800;
	display: inline-block;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	vertical-align: middle;
}

/* Choice 뱃지 스타일 */
.choice-badge {
	display: inline-block;
	padding: 3px 12px;
	border-radius: 50px;
	font-size: 0.8rem;
	font-weight: 800;
	letter-spacing: 0.5px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
	margin-bottom: 0;
}

.choice-badge-mobile {
	display: inline-block;
	padding: 2px 10px;
	border-radius: 50px;
	font-size: 0.7rem;
	font-weight: 800;
	letter-spacing: 0.5px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
	margin-bottom: 0;
	vertical-align: middle;
}

.choice-badge-container {
	position: relative;
	width: 100%;
}
/* choice 뱃지 색상 절대 바꾸지 말것 */
.yellow-choice {
	background-color: #ffcc00;
	color: white;
}

.orange-choice {
	background-color: #ff3300;
	color: white;
}

.red-choice {
	background-color: #b61010;
	color: white;
	position: relative;
	overflow: hidden;
	border: 1px solid rgba(255, 255, 255, 0.2);
	box-shadow: 0 0 2px #ff0000;
}

/* 사선으로 지나가는 빛 효과 */
.red-choice::after {
	content: '';
	position: absolute;
	top: -50%;
	left: -100%;
	width: 60%;
	height: 200%;
	background: linear-gradient(
		to right,
		rgba(255, 255, 255, 0) 0%,
		rgba(255, 255, 255, 0.3) 50%,
		rgba(255, 255, 255, 0) 100%
	);
	transform: rotate(25deg);
	animation: shine 2s infinite;
}

/* 외곽선을 따라 도는 빛 효과 - 얇고 길게 수정 */
.red-choice::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 50px;
	box-shadow: 0 0 5px 1px rgba(255, 64, 64, 0.8);
	clip-path: inset(0 0 0 0);
	animation: border-glow 2s linear infinite;
}

@keyframes border-glow {
	0% {
		clip-path: inset(0 0 92% 0);
	}
	25% {
		clip-path: inset(0 0 0 92%);
	}
	50% {
		clip-path: inset(92% 0 0 0);
	}
	75% {
		clip-path: inset(0 92% 0 0);
	}
	100% {
		clip-path: inset(0 0 92% 0);
	}
}

@keyframes shine {
	0% {
		left: -100%;
	}
	100% {
		left: 200%;
	}
}

/* 프로필 이미지 테두리 */
.yellow-border {
	border: 5px solid #ffcc00 !important;
}

.orange-border {
	border: 5px solid #ff6600 !important;
}

.red-border {
	border: 5px solid #e61919 !important;
}

.no-border {
	border: none !important;
}

/* 프로필 링 스타일 (수정됨) */
.profile-ring-container {
	position: absolute;
	top: -30px;
	left: -20px;
	width: 170px;
	height: 170px;
	pointer-events: none;
	z-index: 3;
}

.profile-ring {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 3;
}

.profile-ring-lottie {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 3;
}

/* 더보기 버튼 */
.more-button {
	position: absolute;
	right: 10px;
	bottom: 5px;
	font-size: 0.75rem;
	color: #666;
	z-index: 2;
}

/* 프로필 컨테이너에 항상 스크롤 허용 */
.v-main {
	overflow-y: auto;
	max-height: 100vh;
	height: 100vh;
}

/* 전역 스크롤바 스타일 (홈 방송목록과 동일) */
::-webkit-scrollbar {
	width: 4px !important;
}

::-webkit-scrollbar-track {
	background: transparent !important;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(100, 100, 100, 0.2) !important;
	border-radius: 4px !important;
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(100, 100, 100, 0.4) !important;
}

::-webkit-scrollbar-button {
	display: none !important;
	height: 0 !important;
	width: 0 !important;
}

/* Firefox용 스크롤바 스타일 */
* {
	scrollbar-width: thin !important;
	scrollbar-color: rgba(100, 100, 100, 0.2) transparent !important;
}

/* 컨테이너 확장 후 스크롤 자연스럽게 처리 */
.v-main > .v-container {
	overflow-y: visible;
	height: auto;
	padding-bottom: 60px;
}

/* 확장된 컨테이너가 겹치지 않게 처리 */
.description-container.expanded,
.self-intro-container.expanded,
.description-container-mobile.expanded,
.self-intro-container-mobile.expanded {
	max-height: none;
	overflow-y: visible;
	z-index: 1;
}

/* Cast 목록 관련 스타일 */
.cast-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16px;
}

.cast-grid-mobile {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 10px;
}

.cast-item, .cast-item-mobile {
	cursor: pointer;
	border-radius: 8px;
	overflow: hidden;
	transition: transform 0.2s ease;
	position: relative;
	background-color: white;
	box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cast-item:hover, .cast-item-mobile:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.cast-thumbnail-container {
	position: relative;
	width: 100%;
	overflow: hidden;
	border-radius: 8px 8px 0 0;
}

.cast-thumbnail {
	width: 100%;
	height: 0;
	padding-bottom: 100%;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	position: relative;
}

.cast-duration {
	position: absolute;
	top: 8px;
	left: 8px;
	background-color: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 0.7rem;
	font-weight: 500;
}

.cast-info {
	padding: 8px 12px;
}

.cast-title {
	font-size: 0.95rem;
	font-weight: 500;
	margin-bottom: 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.cast-stats {
	display: flex;
	align-items: center;
	color: #888;
	font-size: 0.75rem;
}

.cast-likes, .cast-comments {
	display: flex;
	align-items: center;
	margin-right: 10px;
}

.cast-likes span, .cast-comments span {
	margin-left: 2px;
}

.cast-date {
	margin-left: auto;
	font-size: 0.7rem;
}

.cast-container-mobile {
	background-color: #f8f8f8;
	border-radius: 12px;
	padding: 12px;
}

/* 로딩 중 애니메이션 스타일 */
@keyframes pulse {
	0% { opacity: 0.6; }
	50% { opacity: 1; }
	100% { opacity: 0.6; }
}

.v-progress-circular--indeterminate {
	animation: pulse 1.5s infinite;
}

.loading-more {
	margin-top: 16px;
	margin-bottom: 16px;
}

/* 프로필 이미지 내 여백 문제 해결 */
.profile-avatar .v-image {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
	overflow: hidden !important;
}

.profile-avatar .v-image__image {
	background-size: cover !important;
	transform: scale(1.02 ) !important;
	background-position: center center !important;
}
</style>


