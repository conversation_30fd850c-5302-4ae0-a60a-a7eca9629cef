﻿<template>
  <v-card class="mx-auto mt-10 modern-card" max-width="800" elevation="12">
    <div class="card-header">
      <div class="header-icon">
        <v-icon size="32" color="white">mdi-database-sync</v-icon>
      </div>
      <h2 class="header-title">GitHub → Firebase 마이그레이션</h2>
      <p class="header-subtitle">사용자 데이터를 마이그레이션합니다</p>
      
      <!-- 뒤로가기 버튼 -->
      <v-btn
        @click="$emit('back')"
        icon
        color="white"
        class="back-button"
        title="돌아가기"
      >
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
    </div>
    
    <v-card-text class="pt-6 px-8">
      <v-alert
        v-if="message"
        :type="messageType"
        dense
        text
        class="mb-4"
      >
        {{ message }}
      </v-alert>

      <!-- 마이그레이션 단계 진행 상황 -->
      <v-stepper v-model="currentStep" vertical class="elevation-0 mb-4">
        <v-stepper-step 
          :complete="currentStep > 1" 
          step="1"
          :editable="!loading"
        >
          Firebase 초기화
          <small>Firebase 서비스 초기화 및 설정</small>
        </v-stepper-step>
        
        <v-stepper-content step="1">
          <v-card flat class="mb-4 pa-3 bg-light rounded">
            <p>Firebase 서비스가 초기화되었습니다. 아래 내용을 확인하세요:</p>
            <ul class="pl-4">
              <li>Firebase Authentication이 설정되었습니다.</li>
              <li>Firestore 데이터베이스가 연결되었습니다.</li>
              <li>보안 규칙은 Firebase 콘솔에서 설정해야 합니다.</li>
            </ul>
            <v-btn
              color="primary"
              @click="currentStep = 2"
              :disabled="loading"
            >
              계속
            </v-btn>
          </v-card>
        </v-stepper-content>

        <v-stepper-step 
          :complete="currentStep > 2" 
          step="2"
          :editable="!loading"
        >
          관리자 계정 마이그레이션
          <small>기본 관리자 계정을 Firebase로 마이그레이션합니다</small>
        </v-stepper-step>
        
        <v-stepper-content step="2">
          <v-card flat class="mb-4 pa-3 bg-light rounded">
            <p>
              <code>src/data/approved-users.json</code> 파일에서 관리자 계정 정보를 가져와 Firebase로 마이그레이션합니다.
              마이그레이션할 관리자 계정의 임시 비밀번호를 입력하세요.
            </p>
            
            <v-text-field
              v-model="adminPassword"
              label="관리자 임시 비밀번호"
              :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :type="showPassword ? 'text' : 'password'"
              @click:append="showPassword = !showPassword"
              :rules="[v => !!v || '비밀번호를 입력하세요.', v => v.length >= 6 || '최소 6자 이상이어야 합니다.']"
              :disabled="loading"
              outlined
              dense
            ></v-text-field>
            
            <div class="d-flex mt-4">
              <v-btn
                color="primary"
                @click="migrateAdmin"
                :loading="loading"
                :disabled="!adminPassword || adminPassword.length < 6"
              >
                관리자 마이그레이션
              </v-btn>
              <v-btn
                text
                @click="currentStep = 3"
                class="ml-4"
                :disabled="loading"
              >
                건너뛰기
              </v-btn>
            </div>
          </v-card>
        </v-stepper-content>

        <v-stepper-step 
          :complete="currentStep > 3" 
          step="3"
          :editable="!loading"
        >
          일반 사용자 마이그레이션
          <small>승인된 모든 사용자를 Firebase로 마이그레이션합니다</small>
        </v-stepper-step>
        
        <v-stepper-content step="3">
          <v-card flat class="mb-4 pa-3 bg-light rounded">
            <p>
              <code>src/data/approved-users.json</code> 파일의 모든 일반 사용자 계정을 Firebase로 마이그레이션합니다.
              마이그레이션할 사용자 계정의 임시 비밀번호를 입력하세요.
            </p>
            
            <v-text-field
              v-model="userPassword"
              label="사용자 임시 비밀번호"
              :append-icon="showUserPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :type="showUserPassword ? 'text' : 'password'"
              @click:append="showUserPassword = !showUserPassword"
              :rules="[v => !!v || '비밀번호를 입력하세요.', v => v.length >= 6 || '최소 6자 이상이어야 합니다.']"
              :disabled="loading"
              outlined
              dense
            ></v-text-field>
            
            <div class="d-flex mt-4">
              <v-btn
                color="primary"
                @click="migrateUsers"
                :loading="loading"
                :disabled="!userPassword || userPassword.length < 6"
              >
                사용자 마이그레이션
              </v-btn>
              <v-btn
                text
                @click="currentStep = 4"
                class="ml-4"
                :disabled="loading"
              >
                건너뛰기
              </v-btn>
            </div>
          </v-card>
        </v-stepper-content>

        <v-stepper-step step="4">
          마이그레이션 완료
          <small>Firebase로의 마이그레이션이 완료되었습니다</small>
        </v-stepper-step>
        
        <v-stepper-content step="4">
          <v-card flat class="mb-4 pa-3 bg-light rounded">
            <v-alert
              type="success"
              dense
              text
              class="mb-4"
            >
              Firebase로의 마이그레이션이 완료되었습니다!
            </v-alert>
            
            <p>다음 단계:</p>
            <ol class="pl-4">
              <li>Firebase 보안 규칙을 Firebase 콘솔에서 설정하세요.</li>
              <li>마이그레이션한 사용자들에게 비밀번호 재설정 안내를 보내세요.</li>
              <li>기존 GitHub 기반 인증 시스템을 완전히 비활성화하세요.</li>
            </ol>
            
            <v-btn
              color="primary"
              @click="$emit('back')"
              class="mt-4"
            >
              완료
            </v-btn>
          </v-card>
        </v-stepper-content>
      </v-stepper>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { FirebaseInitializer } from '@/plugins/firebase-init';

@Component
export default class MigrationAdmin extends Vue {
  public currentStep = 1;
  public loading = false;
  public message = '';
  public messageType: 'success' | 'error' | 'info' | 'warning' = 'info';
  
  // 비밀번호 필드
  public adminPassword = '';
  public userPassword = '';
  public showPassword = false;
  public showUserPassword = false;
  
  // 마이그레이션 결과
  public adminMigrated = false;
  public usersMigrated = 0;

  /**
   * 관리자 계정 마이그레이션
   */
  public async migrateAdmin() {
    if (!this.adminPassword || this.adminPassword.length < 6) {
      this.message = '유효한 비밀번호를 입력하세요.';
      this.messageType = 'warning';
      return;
    }
    
    this.loading = true;
    this.message = '관리자 계정 마이그레이션 중...';
    this.messageType = 'info';
    
    try {
      const result = await FirebaseInitializer.migrateAdminAccount(this.adminPassword);
      
      if (result.success) {
        this.message = result.message;
        this.messageType = 'success';
        this.adminMigrated = true;
        this.currentStep = 3; // 다음 단계로 자동 이동
      } else {
        this.message = result.message;
        this.messageType = 'error';
      }
    } catch (error: any) {
      this.message = `마이그레이션 오류: ${error.message}`;
      this.messageType = 'error';
    } finally {
      this.loading = false;
    }
  }
  
  /**
   * 일반 사용자 계정 마이그레이션
   */
  public async migrateUsers() {
    if (!this.userPassword || this.userPassword.length < 6) {
      this.message = '유효한 비밀번호를 입력하세요.';
      this.messageType = 'warning';
      return;
    }
    
    this.loading = true;
    this.message = '사용자 계정 마이그레이션 중...';
    this.messageType = 'info';
    
    try {
      const result = await FirebaseInitializer.migrateAllUsers(this.userPassword);
      
      if (result.success) {
        this.message = result.message;
        this.messageType = 'success';
        this.usersMigrated = result.count;
        this.currentStep = 4; // 완료 단계로 자동 이동
      } else {
        this.message = result.message;
        this.messageType = 'error';
      }
    } catch (error: any) {
      this.message = `마이그레이션 오류: ${error.message}`;
      this.messageType = 'error';
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
.card-header {
  background: linear-gradient(135deg, #4a6cf7 0%, #2c3e98 100%);
  padding: 32px 24px 24px;
  text-align: center;
  color: white;
  position: relative;
}

.header-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px;
  letter-spacing: -0.5px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

/* 뒤로가기 버튼 */
.back-button {
  position: absolute !important;
  top: 16px !important;
  left: 16px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.05) !important;
}

.modern-card {
  border-radius: 24px !important;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.bg-light {
  background-color: #f8f9ff !important;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>