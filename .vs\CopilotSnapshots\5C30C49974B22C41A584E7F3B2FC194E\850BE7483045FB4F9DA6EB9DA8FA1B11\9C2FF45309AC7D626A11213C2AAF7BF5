﻿import Vue from 'vue';
import Vuex from 'vuex';

import { User } from '@sopia-bot/core';
import authModule from './modules/auth';

Vue.use(Vuex);

interface State {
	user?: User;
	sideopen: boolean;
	loginDialog: boolean;
	partners: User[];
	liked: boolean;
	followingDialogOpen: boolean;
	userFollowingDialogOpen: boolean;
	userFollowingInfo: {
		userId: number;
		nickname: string;
	};
}

export default new Vuex.Store({
	state: {
		user: {
			id: 0,
			nickname: 'Not Login',
			tag: 'Not Login',
			profile_url: require('assets/default-profile.png'),
		} as User,
		sideopen: false,
		loginDialog: false,
		partners: [],
		liked: false,
		followingDialogOpen: false,
		userFollowingDialogOpen: false,
		userFollowingInfo: {
			userId: 0,
			nickname: ''
		}
	},
	getters: {
		user(state: State) {
			return state.user;
		},
		streamingPartners(state: State): User[] {
			return state.partners.filter((partner) => !!partner.current_live?.id) || [];
		},
	},
	mutations: {
		user(state: State, user: User) {
			state.user = user;
		},
		partners(state: State, list: User[]) {
			state.partners = list;
		},
		setFollowingDialogOpen(state: State, isOpen: boolean) {
			state.followingDialogOpen = isOpen;
		},
		setUserFollowingDialogOpen(state: State, isOpen: boolean) {
			state.userFollowingDialogOpen = isOpen;
		},
		setUserFollowingInfo(state: State, info: { userId: number; nickname: string }) {
			state.userFollowingInfo = info;
		},
		// Firebase 인증 관련 전역 뮤테이션 추가
		setTammUser(state: any, user: any) {
			// auth 모듈에 위임
			state.auth.tammUser = user;
		},
		clearTammUser(state: any) {
			// auth 모듈에 위임
			state.auth.tammUser = null;
		}
	},
	actions: {
	},
	modules: {
		// Firebase 인증 모듈 등록
		auth: authModule,
	},
});
