import { Controller } from '@nestjs/common'
import { type Ipc<PERSON>ontext, IpcH<PERSON>le, IpcOn } from '@doubleshot/nest-electron'
import { Ctx, Payload } from '@nestjs/microservices'

@Controller()
export class AppController {
  @IpcHandle('chat')
  chat(@Payload() msg: string, @Ctx() { ipcEvt: _ }: IpcContext) {
    // you can get ipc event object from @Ctx decorator
    console.log(`Get message from frontend: ${msg}`)
    return 'This is a message to frontend'
  }

  @IpcOn('print-log')
  printLog(@Payload() log: string) {
    console.log(`Get log: ${log}`)
  }

  @IpcOn('multi-params')
  sendMultiParams(@Payload() [param1, param2]) {
    // multi params will be an array from ipc channel
    console.log(`Get param1: ${param1}`)
    console.log(`Get param2: ${param2}`)
  }
}
