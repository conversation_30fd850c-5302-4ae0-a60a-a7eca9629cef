# TAMM (Tamm Audio Member Manager)

스푼라디오 음원 관리 및 방송 보조 도구입니다.

## 프로젝트 디렉토리 구조

```
tamm-v1/
├── public/                  # 정적 파일 디렉토리
├── src/                     # 소스 코드
│   ├── app/                 # 애플리케이션 관련 모듈
│   ├── assets/              # 이미지, 폰트 등 정적 자원
│   │   ├── suit/            # SUIT 폰트 파일
│   │   ├── icon128.png      # 앱 아이콘
│   │   ├── logo.svg         # 로고 이미지
│   │   ├── sopia.png        # 소피아 이미지
│   │   └── ...              # 기타 이미지 파일들
│   ├── interface/           # 인터페이스 정의
│   ├── languages/           # 다국어 지원 파일
│   ├── plugins/             # 플러그인 및 유틸리티
│   │   ├── cfg-lite-ipc.ts  # 설정 관리 모듈
│   │   ├── ipc-renderer.ts  # Electron IPC 렌더러
│   │   ├── logger.ts        # 로깅 유틸리티
│   │   ├── mixins.ts        # Vue 믹스인
│   │   ├── sopia-api.ts     # 스푼라디오 API 래퍼
│   │   ├── ssm-connector.ts # SSM 연결 모듈
│   │   └── vuetify.ts       # Vuetify 설정
│   ├── router/              # Vue Router 설정
│   │   ├── index.ts         # 메인 라우터 설정
│   │   └── bundle.ts        # 번들 관련 라우터 설정
│   ├── sopia/               # 스푼라디오 관련 모듈
│   ├── store/               # Vuex 상태 관리
│   │   └── index.ts         # 스토어 설정
│   ├── views/               # 화면 컴포넌트
│   │   ├── Bundle/          # 번들 관련 컴포넌트
│   │   ├── Cmd/             # 명령어 관련 컴포넌트
│   │   ├── Code/            # 코드 관련 컴포넌트
│   │   ├── Components/      # 공통 컴포넌트
│   │   │   ├── SideMenu.vue         # 사이드 메뉴 컴포넌트
│   │   │   ├── SideMenuItem.vue     # 사이드 메뉴 아이템 컴포넌트
│   │   │   ├── TitleBar.vue         # 타이틀 바 컴포넌트
│   │   │   ├── AgreeLiveInfoDialog.vue # 라이브 정보 동의 다이얼로그
│   │   │   ├── Donation.vue         # 후원 컴포넌트
│   │   │   ├── Notification.vue     # 알림 컴포넌트
│   │   │   └── ...                  # 기타 공통 컴포넌트
│   │   ├── Home/            # 홈 화면 컴포넌트
│   │   │   ├── Index.vue    # 홈 메인 컴포넌트
│   │   │   └── LiveItem.vue # 라이브 아이템 컴포넌트
│   │   ├── Live/            # 라이브 관련 컴포넌트
│   │   │   ├── Menus/       # 라이브 메뉴 컴포넌트
│   │   │   ├── ChatMessage.vue # 채팅 메시지 컴포넌트
│   │   │   ├── Player.vue   # 라이브 플레이어 컴포넌트
│   │   │   ├── player.ts    # 플레이어 로직
│   │   │   ├── PlayerBar.vue # 플레이어 바 컴포넌트
│   │   │   └── PlayerFooter.vue # 플레이어 푸터 컴포넌트
│   │   ├── Login/           # 로그인 관련 컴포넌트
│   │   ├── ReleaseNote/     # 릴리즈 노트 컴포넌트
│   │   ├── Search/          # 검색 관련 컴포넌트
│   │   │   ├── SearchBox.vue # 검색 박스 컴포넌트
│   │   │   ├── Header.vue   # 검색 헤더 컴포넌트
│   │   │   ├── Index.vue    # 검색 메인 컴포넌트
│   │   │   └── PreviewList.vue # 검색 미리보기 컴포넌트
│   │   ├── Setting/         # 설정 관련 컴포넌트
│   │   ├── SideMenu/        # 사이드 메뉴 관련 컴포넌트
│   │   ├── Tutorials/       # 튜토리얼 컴포넌트
│   │   └── User/            # 사용자 관련 컴포넌트
│   ├── App.vue              # 루트 컴포넌트
│   ├── background.ts        # Electron 백그라운드 프로세스
│   └── main.ts              # 앱 진입점
├── build/                   # 빌드 관련 파일
├── custom_script/           # 사용자 정의 스크립트
├── dist_electron/           # Electron 빌드 결과물
├── examples/                # 예제 파일
├── node_modules/            # 의존성 모듈
├── official-bundles/        # 공식 번들
├── refer/                   # 참조 파일
├── trash/                   # 임시 파일
├── package.json             # 프로젝트 의존성 관리
├── tsconfig.json            # TypeScript 설정
├── tslint.json              # TSLint 설정
├── vue.config.js            # Vue 설정
├── electron.js              # Electron 설정
├── build_app.bat            # 앱 빌드 스크립트
├── set_icon.bat             # 아이콘 설정 스크립트
└── start_app.bat            # 앱 실행 스크립트
```

## 주요 기능 구조

### API 요청 처리
- `src/plugins/sopia-api.ts`: 스푼라디오 API와의 통신을 처리하는 래퍼 모듈

### 사용자 인터페이스
- `src/views/Components/`: 공통 UI 컴포넌트 (사이드 메뉴, 타이틀 바 등)
- `src/views/Home/`: 홈 화면 및 라이브 목록 컴포넌트
- `src/views/Live/`: 라이브 스트림 재생 및 채팅 관련 컴포넌트
- `src/views/Search/`: 검색 기능 관련 컴포넌트

### 상태 관리
- `src/store/`: Vuex를 사용한 애플리케이션 상태 관리

### 라우팅
- `src/router/`: Vue Router를 사용한 페이지 내비게이션 설정

### 유틸리티 및 헬퍼
- `src/plugins/`: 다양한 유틸리티 및 플러그인 (설정 관리, 로깅 등)
- `src/mixins.ts`: 컴포넌트 간 공유되는 메서드 및 속성

### 전역 설정
- `src/App.vue`: 애플리케이션의 루트 컴포넌트
- `src/main.ts`: 애플리케이션 진입점 및 전역 설정

```
yarn install
```

### Compiles and hot-reloads for development
```
yarn serve
```

### Compiles and minifies for production
```
yarn build
```

### Lints and fixes files
```
yarn lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
