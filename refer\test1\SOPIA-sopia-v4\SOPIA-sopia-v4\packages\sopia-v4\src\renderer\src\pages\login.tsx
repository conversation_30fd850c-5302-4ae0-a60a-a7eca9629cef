import { <PERSON>, CardBody, Card<PERSON><PERSON>er, Input, Button } from '@heroui/react'
import { useState } from 'react'
import { useSessionStore } from '../stores/sessionStore'
import { useNavigate, Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useLanguageStore } from '../stores/languageStore'
import Slider from 'react-slick'
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'

const slides = [
  {
    titleKey: 'app.title',
    descriptionKey: 'app.description',
    icon: '🎥'
  },
  {
    titleKey: 'features.chat',
    descriptionKey: 'features.chat.description',
    icon: '💬'
  },
  {
    titleKey: 'features.customize',
    descriptionKey: 'features.customize.description',
    icon: '⚙️'
  }
]

export default function LoginPage() {
  const [id, setId] = useState('')
  const [password, setPassword] = useState('')
  const [isLoginEnabled, setIsLoginEnabled] = useState(false)
  const setSession = useSessionStore((state) => state.setSession)
  const navigate = useNavigate()
  const { t } = useTranslation()
  const { language, setLanguage } = useLanguageStore()

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    arrows: false
  }

  const handleLogin = async () => {
    try {
      // TODO: 실제 로그인 API 호출
      const response = await fetch('/api/login', {
        method: 'POST',
        body: JSON.stringify({ id, password })
      })

      if (response.ok) {
        const sessionData = await response.json()
        setSession(sessionData)
        navigate('/')
      }
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  const toggleLanguage = () => {
    setLanguage(language === 'ko' ? 'en' : 'ko')
  }

  return (
    <div className="min-h-screen flex dark:bg-gray-900">
      {/* 왼쪽 슬라이드 섹션 */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary to-primary-dark dark:from-primary-dark dark:to-primary">
        <div className="w-full flex items-center justify-center p-12">
          <div className="w-full max-w-md">
            <Slider {...sliderSettings}>
              {slides.map((slide, index) => (
                <div key={index} className="outline-none">
                  <div className="text-center px-6 py-12">
                    <div className="text-6xl mb-6">{slide.icon}</div>
                    <h3 className="text-3xl font-bold text-white mb-4">{t(slide.titleKey)}</h3>
                    <p className="text-xl text-white">{t(slide.descriptionKey)}</p>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </div>
      </div>

      {/* 오른쪽 로그인 섹션 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
        <div className="w-full max-w-md space-y-8">
          <div className="flex flex-col items-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">{t('login.title')}</h2>
            <p className="mt-2 text-gray-600 dark:text-gray-400">{t('login.description')}</p>
          </div>

          <Card className="w-full shadow-lg dark:bg-gray-800">
            <CardBody className="p-6 space-y-6">
              <div>
                <label
                  htmlFor="id"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t('auth.id')}
                </label>
                <Input
                  id="id"
                  type="text"
                  value={id}
                  onChange={(e) => setId(e.target.value)}
                  className="w-full"
                  placeholder="sopia"
                  size="lg"
                />
              </div>
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t('auth.password')}
                </label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full"
                  placeholder="••••••••"
                  size="lg"
                />
              </div>
              <Button
                onPress={() => {
                  /* TODO: 스푼 로그인 처리 */
                }}
                className="w-full mb-3 text-white transition-colors"
                style={{ backgroundColor: '#FF5500' }}
                size="lg"
                onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#FF6A1F')}
                onMouseOut={(e) => (e.currentTarget.style.backgroundColor = '#FF5500')}
              >
                {t('auth.loginWithSpoon')}
              </Button>
              <Button
                onPress={handleLogin}
                className="w-full bg-primary text-white hover:bg-primary-light dark:bg-primary-light dark:hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                size="lg"
                isDisabled={!isLoginEnabled}
              >
                {t('auth.login')}
              </Button>
            </CardBody>
          </Card>

          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            {t('auth.noAccount')}{' '}
            <Link
              to="/signup"
              className="text-primary hover:text-primary-light dark:text-primary-light dark:hover:text-primary font-medium"
            >
              {t('auth.signup')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
