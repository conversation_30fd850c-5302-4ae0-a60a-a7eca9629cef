<template>
  <div class="fan-board-container">
    <!-- Fan Board 헤더 -->
    <div class="fan-board-header" @click="toggleFanBoard">
      <div class="d-flex align-center">
        <v-icon class="mr-2" color="black" size="20">mdi-message-text-outline</v-icon>
        <p class="text-subtitle-1 font-weight-medium mb-0">Fan Board</p>
        <v-spacer></v-spacer>
        <v-icon :class="{ 'rotate-180': isExpanded }" class="transition-icon">
          mdi-chevron-down
        </v-icon>
      </div>
    </div>

    <!-- Fan Board 내용 -->
    <v-expand-transition>
      <div v-if="isExpanded" class="fan-board-content">
        <!-- 로딩 상태 -->
        <div v-if="loading" class="text-center py-4">
          <v-progress-circular indeterminate color="purple" size="24"></v-progress-circular>
          <div class="mt-2 text-body-2 grey--text">팬 보드를 불러오는 중...</div>
        </div>

        <!-- 에러 상태 -->
        <div v-else-if="error" class="text-center py-4">
          <v-icon color="red" size="32">mdi-alert-circle</v-icon>
          <div class="mt-2 text-body-2 red--text">{{ error }}</div>
          <v-btn small text color="purple" @click="loadFanBoard" class="mt-2">
            다시 시도
          </v-btn>
        </div>

        <!-- 팬 보드 게시물 목록 -->
        <div v-else-if="fanBoardPosts.length > 0" class="fan-posts" ref="fanPostsContainer" @scroll="throttledScroll">
          <div v-for="post in fanBoardPosts" :key="post.id" class="fan-post-item">
            <div class="d-flex">
              <!-- 작성자 프로필 -->
              <v-avatar size="40" class="mr-3 flex-shrink-0" @click="goToUserProfile(post.contentData.authorId)" style="cursor: pointer;">
                <v-img :src="getAuthorProfileUrl(post)"></v-img>
              </v-avatar>
              
              <!-- 게시물 내용 -->
              <div class="flex-grow-1">
                <div class="d-flex align-center mb-1">
                  <span class="font-weight-medium text-body-2" @click="goToUserProfile(post.contentData.authorId)" style="cursor: pointer;">{{ getAuthorNickname(post) }}</span>
                  <span class="ml-2 text-caption grey--text">{{ formatDate(post.created) }}</span>
                  
                  <!-- 공개범위 표시 -->
                  <v-chip
                    x-small
                    :color="getVisibilityColor(post.visibleOption)"
                    text-color="white"
                    class="ml-2"
                  >
                    {{ getVisibilityText(post.visibleOption) }}
                  </v-chip>
                  
                  <v-spacer></v-spacer>
                  
                  <!-- 메뉴 버튼 -->
                  <v-menu offset-y :close-on-content-click="false" v-model="post.menuOpen">
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        icon
                        small
                        v-bind="attrs"
                        v-on="on"
                        class="menu-button"
                        @click.stop="togglePostMenu(post)"
                      >
                        <v-icon size="16" color="grey">mdi-dots-vertical</v-icon>
                      </v-btn>
                    </template>
                    <v-list dense>
                      <v-list-item @click="changeVisibility(post)">
                        <v-list-item-icon>
                          <v-icon size="18">mdi-eye-outline</v-icon>
                        </v-list-item-icon>
                        <v-list-item-content>
                          <v-list-item-title class="text-body-2">공개범위</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                      <v-list-item @click="confirmDeletePost(post)" class="delete-menu-item">
                        <v-list-item-icon>
                          <v-icon size="18" color="red">mdi-delete-outline</v-icon>
                        </v-list-item-icon>
                        <v-list-item-content>
                          <v-list-item-title class="text-body-2 red--text">삭제하기</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
                
                <!-- 게시물 텍스트 -->
                <div class="post-content text-body-2 mb-2" v-html="formatContent(post.contentData.contents)"></div>
                
                <!-- 미디어 (이미지) -->
                <div v-if="post.contentData.media && post.contentData.media.length > 0" class="post-media mb-2">
                  <div v-for="(media, index) in post.contentData.media" :key="index" class="media-item">
                    <v-img 
                      :src="media.url" 
                      max-width="200" 
                      max-height="200" 
                      class="rounded"
                      @click="openImageDialog(media.url)">
                    </v-img>
                  </div>
                </div>
                
                <!-- 좋아요/댓글 수 -->
                <div class="post-stats d-flex align-center">
                  <div class="d-flex align-center mr-4 like-button" @click="toggleLike(post)">
                    <v-icon 
                      size="16" 
                      :color="post.likeStatus ? 'red' : 'grey'"
                      class="like-icon"
                    >
                      {{ post.likeStatus ? 'mdi-heart' : 'mdi-heart-outline' }}
                    </v-icon>
                    <span class="ml-1 text-caption" :class="post.likeStatus ? 'red--text' : 'grey--text'">
                      {{ post.contentData.likeCount || 0 }}
                    </span>
                  </div>
                  <div class="d-flex align-center comment-button" @click="openPostDetail(post)">
                    <v-icon size="16" color="grey">mdi-comment-outline</v-icon>
                    <span class="ml-1 text-caption grey--text">{{ post.contentData.commentCount || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <v-divider class="my-3"></v-divider>
          </div>

          <!-- 로딩 더보기 상태 -->
          <div v-if="loadingMore" class="text-center py-3">
            <v-progress-circular indeterminate color="purple" size="20"></v-progress-circular>
            <div class="mt-1 text-caption grey--text">더 불러오는 중...</div>
          </div>

          <!-- 끝에 도달했을 때 -->
          <div v-else-if="!hasMore && fanBoardPosts.length > 0" class="text-center py-3">
            <div class="text-caption grey--text">모든 게시물을 불러왔습니다</div>
          </div>
        </div>

        <!-- 빈 상태 -->
        <div v-else class="text-center py-4">
          <v-icon color="grey" size="48">mdi-message-text-outline</v-icon>
          <div class="mt-2 text-body-2 grey--text">팬 보드에 게시물이 없습니다</div>
        </div>
      </div>
    </v-expand-transition>

    <!-- 게시물 상세 화면 -->
    <v-dialog v-model="postDetailDialog.show" max-width="900" persistent>
      <v-card class="post-detail-dialog">
        <PostDetail
          v-if="postDetailDialog.postId"
          :post-id="postDetailDialog.postId"
          :current-user-id="currentUserId"
          @close="closePostDetail"
        />
      </v-card>
    </v-dialog>

    <!-- 이미지 다이얼로그 -->
    <v-dialog v-model="imageDialog.show" max-width="80%">
      <v-card>
        <v-img :src="imageDialog.src" contain></v-img>
      </v-card>
    </v-dialog>

    <!-- 삭제 확인 다이얼로그 -->
    <v-dialog v-model="deleteDialog.show" max-width="400">
      <v-card>
        <v-card-title class="text-h6">
          게시물 삭제
        </v-card-title>
        <v-card-text>
          정말로 이 게시물을 삭제하시겠습니까?<br>
          삭제된 게시물은 복구할 수 없습니다.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialog.show = false">
            아니오
          </v-btn>
          <v-btn color="red" text @click="deletePost" :loading="deleteDialog.loading">
            예
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 공개범위 설정 다이얼로그 -->
    <v-dialog v-model="visibilityDialog.show" max-width="400">
      <v-card>
        <v-card-title class="text-h6">
          공개범위 설정
        </v-card-title>
        <v-card-text>
          <div v-if="visibilityDialog.loading" class="text-center py-4">
            <v-progress-circular indeterminate color="purple" size="24"></v-progress-circular>
            <div class="mt-2 text-body-2">현재 공개범위를 확인하는 중...</div>
          </div>
          <div v-else>
            <p class="text-body-2 mb-3">이 게시물의 공개범위를 선택해주세요.</p>
            <v-radio-group v-model="visibilityDialog.selectedOption" :disabled="visibilityDialog.saving">
              <v-radio label="전체보기" value="ALL">
                <template v-slot:label>
                  <div>
                    <strong>전체보기</strong>
                    <div class="text-caption grey--text">모든 사용자가 볼 수 있습니다</div>
                  </div>
                </template>
              </v-radio>
              <v-radio label="팬만보기" value="ONLYFAN">
                <template v-slot:label>
                  <div>
                    <strong>팬만보기</strong>
                    <div class="text-caption grey--text">내 팬들만 볼 수 있습니다</div>
                  </div>
                </template>
              </v-radio>
              <v-radio label="나만보기" value="ONLYME">
                <template v-slot:label>
                  <div>
                    <strong>나만보기</strong>
                    <div class="text-caption grey--text">나만 볼 수 있습니다</div>
                  </div>
                </template>
              </v-radio>
            </v-radio-group>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="visibilityDialog.show = false" :disabled="visibilityDialog.saving">
            취소
          </v-btn>
          <v-btn 
            color="purple" 
            text 
            @click="changePostVisibility" 
            :loading="visibilityDialog.saving"
            :disabled="visibilityDialog.loading"
          >
            확인
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import PostDetail from './PostDetail.vue';

interface FanBoardPost {
  id: string;
  contentData: {
    id: number;
    authorId: number;
    contents: string;
    media: Array<{ url: string; type: string }>;
    likeCount: number;
    commentCount: number;
  };
  contentType: string;
  created: string;
  type: string;
  visibleOption: string;
  isPin: boolean;
  contentSource: string;
  likeStatus: boolean;
  plan: any;
  menuOpen: boolean;
}

interface FanBoardResponse {
  userId: number;
  results: FanBoardPost[];
  offset: string;
}

@Component({
  components: {
    PostDetail
  }
})
export default class FanBoard extends Vue {
  @Prop({ required: true }) readonly userId!: number;

  // 상태 관리
  public isExpanded: boolean = false;
  public loading: boolean = false;
  public loadingMore: boolean = false;
  public error: string = '';
  public fanBoardPosts: FanBoardPost[] = [];
  public hasMore: boolean = true;
  public offset: string = '';
  
  // 사용자 정보 캐시
  private userCache: Map<number, { nickname: string; profile_url: string }> = new Map();

  // 게시물 상세 다이얼로그
  public postDetailDialog = {
    show: false,
    postId: null as number | null
  };

  // 이미지 다이얼로그
  public imageDialog = {
    show: false,
    src: ''
  };

  // 삭제 확인 다이얼로그
  public deleteDialog = {
    show: false,
    loading: false,
    post: null as FanBoardPost | null
  };

  // 공개범위 설정 다이얼로그
  public visibilityDialog = {
    show: false,
    loading: false,
    saving: false,
    selectedOption: 'ALL',
    post: null as FanBoardPost | null
  };

  // 현재 사용자 ID 계산 속성
  get currentUserId(): number {
    const auth = (this as any).$cfg?.get('auth');
    return auth?.spoon?.user_id || 0;
  }

  // 컴포넌트가 마운트되면 초기화
  mounted() {
    // 초기 상태에서는 로드하지 않음 (사용자가 확장했을 때만 로드)
  }

  // userId가 변경되면 데이터 초기화
  @Watch('userId')
  onUserIdChanged(newUserId: number, oldUserId: number) {
    if (newUserId !== oldUserId) {
      this.resetData();
    }
  }

  // Fan Board 토글
  public toggleFanBoard() {
    this.isExpanded = !this.isExpanded;
    
    // 처음 열 때만 데이터 로드
    if (this.isExpanded && this.fanBoardPosts.length === 0) {
      this.loadFanBoard();
    }
  }

  // Fan Board 데이터 로드
  public async loadFanBoard() {
    if (!this.userId) return;

    this.loading = true;
    this.error = '';

    try {
      const url = `https://kr-gw.spooncast.net/feed/${this.userId}/FAN?contentType=POST&excludeContentType=TALK&isNext=false`;
      console.log('API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'ko-KR,ko;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Origin': 'https://www.spooncast.net',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
        console.log('스푼캐스트 인증 토큰 사용:', auth.spoon.token.substring(0, 20) + '...');
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다. 제한된 데이터만 받을 수 있습니다.');
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      console.log('응답 상태:', response.status);
      console.log('응답 헤더:', [...response.headers.entries()]);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log('원본 응답 텍스트 길이:', responseText.length);
      console.log('원본 응답 텍스트 일부:', responseText.substring(0, 500));
      
      const data = JSON.parse(responseText) as FanBoardResponse;
      console.log('Fan Board 초기 로드 응답:', data);
      console.log('받은 게시물 수:', data.results?.length || 0);
      
      this.fanBoardPosts = data.results || [];
      this.offset = data.offset || '';
      this.hasMore = this.fanBoardPosts.length > 0;
      
      // 게시물에 menuOpen 속성 초기화
      this.fanBoardPosts.forEach(post => {
        post.menuOpen = false;
      });
      
      console.log('설정된 상태:', {
        postsCount: this.fanBoardPosts.length,
        offset: this.offset,
        hasMore: this.hasMore
      });
      
      // 사용자 정보 일괄 로드
      await this.loadUsersInfo(this.fanBoardPosts);
    } catch (error) {
      console.error('Fan Board 로드 중 오류:', error);
      this.error = '팬 보드를 불러오는 중 오류가 발생했습니다';
    } finally {
      this.loading = false;
    }
  }

  // 더 많은 게시물 로드
  public async loadMorePosts() {
    if (!this.userId || !this.hasMore || this.loadingMore) return;

    console.log('더 많은 게시물 로드 시작, 현재 offset:', this.offset);
    this.loadingMore = true;

    try {
      const url = `https://kr-gw.spooncast.net/feed/${this.userId}/FAN?contentType=POST&excludeContentType=TALK&isNext=true&offset=${this.offset}`;
      console.log('더 많은 게시물 로드 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'ko-KR,ko;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Origin': 'https://www.spooncast.net',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
        console.log('더 많은 게시물 로드시 스푼캐스트 인증 토큰 사용 중...');
      } else {
        console.warn('더 많은 게시물 로드시 스푼캐스트 인증 토큰이 없습니다.');
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log('더 많은 게시물 원본 응답 길이:', responseText.length);
      
      const data = JSON.parse(responseText) as FanBoardResponse;
      const newPosts = data.results || [];
      
      console.log('더 많은 게시물 로드 응답:', data);
      console.log('새로 받은 게시물 수:', newPosts.length);
      
      if (newPosts.length > 0) {
        // 새 게시물에 menuOpen 속성 초기화
        newPosts.forEach(post => {
          post.menuOpen = false;
        });
        
        this.fanBoardPosts.push(...newPosts);
        this.offset = data.offset || '';
        
        console.log('업데이트된 상태:', {
          totalPosts: this.fanBoardPosts.length,
          newOffset: this.offset
        });
        
        // 새로운 게시물의 사용자 정보 일괄 로드
        await this.loadUsersInfo(newPosts);
      } else {
        // 빈 응답이면 더 이상 게시물이 없음
        console.log('더 이상 게시물이 없음');
        this.hasMore = false;
      }
    } catch (error) {
      console.error('더 많은 게시물 로드 중 오류:', error);
    } finally {
      this.loadingMore = false;
    }
  }

  // 데이터 초기화
  private resetData() {
    this.fanBoardPosts = [];
    this.offset = '';
    this.hasMore = true;
    this.error = '';
    this.isExpanded = false;
  }

  // 작성자 프로필 URL 가져오기
  private getAuthorProfileUrl(post: FanBoardPost): string {
    const cached = this.userCache.get(post.contentData.authorId);
    if (cached) {
      return cached.profile_url;
    }
    
    return 'https://via.placeholder.com/40x40?text=User';
  }

  // 작성자 닉네임 가져오기
  private getAuthorNickname(post: FanBoardPost): string {
    const cached = this.userCache.get(post.contentData.authorId);
    if (cached) {
      return cached.nickname;
    }
    
    return `User${post.contentData.authorId}`;
  }
  
  // 사용자 정보 일괄 로드 (bulk API 사용)
  private async loadUsersInfo(posts: FanBoardPost[]) {
    const userIds = Array.from(new Set(posts.map((post: FanBoardPost) => post.contentData.authorId)));
    console.log('사용자 ID 목록:', userIds);

    if (userIds.length === 0) return;

    try {
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'ko-KR,ko;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Origin': 'https://www.spooncast.net',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
        console.log('스푼캐스트 인증 토큰 사용 중...');
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
      }

      // bulk API로 사용자 정보 요청
      const userIdsParam = userIds.join(',');
      const url = `https://kr-api.spooncast.net/users/meta/?user_ids=${userIdsParam}&include_current_live=false`;
      console.log('Bulk 사용자 정보 요청 URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        console.warn(`Bulk 사용자 정보 로드 실패: ${response.status}`);
        return;
      }

      const data = await response.json();
      console.log('Bulk 사용자 정보 응답:', data);

      if (data.results && Array.isArray(data.results)) {
        data.results.forEach((userInfo: any) => {
          this.userCache.set(userInfo.id, {
            nickname: userInfo.nickname || `User${userInfo.id}`,
            profile_url: userInfo.profile_url || 'https://via.placeholder.com/40x40?text=User'
          });
        });
        console.log(`${data.results.length}명의 사용자 정보를 캐시에 저장했습니다.`);
      }
    } catch (error) {
      console.error('Bulk 사용자 정보 로드 중 오류:', error);
    }
  }

  // 날짜 포맷팅
  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        if (diffHours === 0) {
          const diffMinutes = Math.floor(diffMs / (1000 * 60));
          return `${diffMinutes}분 전`;
        }
        return `${diffHours}시간 전`;
      } else if (diffDays < 7) {
        return `${diffDays}일 전`;
      } else {
        return date.toLocaleDateString('ko-KR', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return dateString;
    }
  }

  // 콘텐츠 포맷팅 (줄바꿈 처리)
  private formatContent(content: string): string {
    if (!content) return '';
    return content.replace(/\n/g, '<br>');
  }

  // 이미지 다이얼로그 열기
  private openImageDialog(src: string) {
    this.imageDialog.src = src;
    this.imageDialog.show = true;
  }

  // 스크롤 이벤트 핸들러
  private onScroll() {
    const container = this.$refs.fanPostsContainer as HTMLElement;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const scrollThreshold = scrollHeight - (scrollTop + clientHeight);

    // 디버깅을 위한 로그
    console.log('스크롤 상태:', {
      scrollTop,
      scrollHeight,
      clientHeight,
      scrollThreshold,
      hasMore: this.hasMore,
      loadingMore: this.loadingMore,
      postsCount: this.fanBoardPosts.length
    });

    if (scrollThreshold <= 100 && this.hasMore && !this.loadingMore) {
      console.log('더 많은 게시물 로드 시작');
      this.loadMorePosts();
    }
  }

  // 스크롤 throttling을 위한 변수
  private scrollThrottleTimer: number | null = null;

  // throttled 스크롤 핸들러
  private throttledScroll() {
    if (this.scrollThrottleTimer) return;
    
    this.scrollThrottleTimer = window.setTimeout(() => {
      this.onScroll();
      this.scrollThrottleTimer = null;
    }, 200);
  }

  // 컴포넌트 파괴 시 타이머 정리
  beforeDestroy() {
    if (this.scrollThrottleTimer) {
      clearTimeout(this.scrollThrottleTimer);
    }
  }

  // 좋아요 토글
  private async toggleLike(post: FanBoardPost) {
    // 현재 상태 저장 (실패시 복원용)
    const originalLikeStatus = post.likeStatus;
    const originalLikeCount = post.contentData.likeCount;
    
    // UI 즉시 업데이트 (낙관적 업데이트)
    post.likeStatus = !post.likeStatus;
    if (post.likeStatus) {
      post.contentData.likeCount++;
    } else {
      post.contentData.likeCount--;
    }

    try {
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Content-Length': '0',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
        // 원래 상태로 복원
        post.likeStatus = originalLikeStatus;
        post.contentData.likeCount = originalLikeCount;
        return;
      }

      const url = `https://kr-gw.spooncast.net/posts/${post.contentData.id}/like`;
      const method = post.likeStatus ? 'POST' : 'DELETE';
      
      console.log(`좋아요 ${post.likeStatus ? '추가' : '취소'} 요청:`, url);

      const response = await fetch(url, {
        method,
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log(`좋아요 ${post.likeStatus ? '추가' : '취소'} 성공`);
    } catch (error) {
      console.error('좋아요 토글 중 오류:', error);
      
      // 실패시 원래 상태로 복원
      post.likeStatus = originalLikeStatus;
      post.contentData.likeCount = originalLikeCount;
      
      // 사용자에게 오류 알림 (선택사항)
      // this.$toast.error('좋아요 처리 중 오류가 발생했습니다.');
    }
  }

  // 게시물 메뉴 토글
  private togglePostMenu(post: FanBoardPost) {
    post.menuOpen = !post.menuOpen;
  }

  // 게시물 공개범위 변경
  private async changeVisibility(post: FanBoardPost) {
    this.visibilityDialog.show = true;
    this.visibilityDialog.post = post;
    this.visibilityDialog.loading = true;
    
    // 현재 게시물의 공개범위를 미리 선택
    this.visibilityDialog.selectedOption = post.visibleOption;
    
    // 현재 공개범위를 서버에서 가져오기
    await this.getCurrentVisibility(post);
  }

  // 현재 공개범위 가져오기
  private async getCurrentVisibility(post: FanBoardPost) {
    try {
      const url = `https://kr-gw.spooncast.net/posts/${post.contentData.id}`;
      console.log('현재 공개범위 확인 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
        this.visibilityDialog.selectedOption = post.visibleOption;
        this.visibilityDialog.loading = false;
        return;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('현재 공개범위 응답:', data);
      
      // 현재 공개범위 설정
      this.visibilityDialog.selectedOption = data.visibleOption || post.visibleOption;
    } catch (error) {
      console.error('현재 공개범위 확인 중 오류:', error);
      this.visibilityDialog.selectedOption = post.visibleOption;
    } finally {
      this.visibilityDialog.loading = false;
    }
  }

  // 게시물 공개범위 변경 확인
  private async changePostVisibility() {
    if (!this.userId || !this.visibilityDialog.selectedOption || !this.visibilityDialog.post) return;

    this.visibilityDialog.saving = true;

    try {
      const url = `https://kr-gw.spooncast.net/posts/${this.visibilityDialog.post.contentData.id}`;
      console.log('게시물 공개범위 변경 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Content-Type': 'application/json',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
        return;
      }

      const response = await fetch(url, {
        method: 'PATCH',
        headers,
        credentials: 'include',
        body: JSON.stringify({
          visibleOption: this.visibilityDialog.selectedOption
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log('게시물 공개범위 변경 성공');

      // 게시물의 공개범위 업데이트
      const postToUpdate = this.visibilityDialog.post;
      this.fanBoardPosts.forEach((post) => {
        if (post.id === postToUpdate.id) {
          post.visibleOption = this.visibilityDialog.selectedOption;
        }
      });
    } catch (error) {
      console.error('게시물 공개범위 변경 중 오류:', error);
    } finally {
      this.visibilityDialog.show = false;
      this.visibilityDialog.saving = false;
      this.visibilityDialog.post = null;
    }
  }

  // 게시물 삭제
  private async deletePost() {
    if (!this.userId || !this.deleteDialog.post) return;

    this.deleteDialog.loading = true;

    try {
      const url = `https://kr-gw.spooncast.net/posts/${this.deleteDialog.post.contentData.id}`;
      console.log('게시물 삭제 API 요청 URL:', url);
      
      // 인증 헤더 설정
      const headers: any = {
        'Accept': 'application/json',
        'Accept-Language': 'ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7',
        'Origin': 'https://www.spooncast.net',
        'Priority': 'u=1, i',
        'Referer': 'https://www.spooncast.net/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      };

      // 스푼캐스트 인증 토큰 추가
      const auth = (this as any).$cfg?.get('auth');
      if (auth && auth.spoon && auth.spoon.token) {
        headers['Authorization'] = `Bearer ${auth.spoon.token}`;
      } else {
        console.warn('스푼캐스트 인증 토큰이 없습니다.');
        return;
      }

      const response = await fetch(url, {
        method: 'DELETE',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log('게시물 삭제 성공');

      // 삭제된 게시물을 리스트에서 제거
      this.fanBoardPosts = this.fanBoardPosts.filter((p) => p.id !== this.deleteDialog.post?.id);
      this.hasMore = this.fanBoardPosts.length > 0;
    } catch (error) {
      console.error('게시물 삭제 중 오류:', error);
    } finally {
      this.deleteDialog.show = false;
      this.deleteDialog.loading = false;
      this.deleteDialog.post = null;
    }
  }

  // 게시물 삭제 확인
  private confirmDeletePost(post: FanBoardPost) {
    this.deleteDialog.show = true;
    this.deleteDialog.post = post;
  }

  // 공개범위에 따른 텍스트와 색상 가져오기
  private getVisibilityText(visibleOption: string): string {
    switch (visibleOption) {
      case 'ALL':
        return '모두볼수있어요';
      case 'ONLYFAN':
        return '팬만볼수있어요';
      case 'ONLYME':
        return '나만볼수있어요';
      default:
        return '모두볼수있어요';
    }
  }

  private getVisibilityColor(visibleOption: string): string {
    switch (visibleOption) {
      case 'ALL':
        return 'green';
      case 'ONLYFAN':
        return 'purple';
      case 'ONLYME':
        return 'orange';
      default:
        return 'green';
    }
  }

  // 게시물 상세 열기
  private openPostDetail(post: FanBoardPost) {
    this.postDetailDialog.postId = post.contentData.id;
    this.postDetailDialog.show = true;
  }

  // 게시물 상세 닫기
  private closePostDetail() {
    this.postDetailDialog.show = false;
    this.postDetailDialog.postId = null;
  }

  // 작성자 프로필 클릭 시 이동
  private goToUserProfile(userId: number) {
    // 사용자 프로필 페이지로 이동
    this.$router.push(`/user/${userId}`);
  }
}
</script>

<style scoped>
.fan-board-container {
  margin-top: 16px;
}

.fan-board-header {
  background-color: #f8f8f8;
  border-radius: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fan-board-header:hover {
  background-color: #f0f0f0;
}

.transition-icon {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

.fan-board-content {
  background-color: #f8f8f8;
  border-radius: 0 0 12px 12px;
  margin-top: -12px;
  padding: 16px;
  padding-top: 12px;
}

.fan-posts {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 8px;
}

.fan-post-item {
  margin-bottom: 16px;
}

.fan-post-item:last-child {
  margin-bottom: 0;
}

.post-content {
  word-break: break-word;
  line-height: 1.4;
}

.post-media {
  margin-top: 8px;
}

.media-item {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.post-stats {
  margin-top: 8px;
}

.like-button {
  cursor: pointer;
  transition: transform 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
}

.like-button:hover {
  transform: scale(1.05);
  background-color: rgba(255, 0, 0, 0.1);
}

.like-icon {
  transition: color 0.2s ease;
}

.menu-button {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.menu-button:hover {
  opacity: 1;
}

.delete-menu-item {
  cursor: pointer;
}

.delete-menu-item:hover {
  background-color: rgba(255, 0, 0, 0.1);
}

.comment-button {
  cursor: pointer;
  transition: transform 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
}

.comment-button:hover {
  transform: scale(1.05);
  background-color: rgba(156, 39, 176, 0.1);
}

.post-detail-dialog {
  height: 80vh;
  max-height: 700px;
}

/* 스크롤바 스타일 */
.fan-posts::-webkit-scrollbar {
  width: 6px;
}

.fan-posts::-webkit-scrollbar-track {
  background: transparent;
}

.fan-posts::-webkit-scrollbar-thumb {
  background-color: rgba(156, 39, 176, 0.3);
  border-radius: 10px;
}

.fan-posts::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 39, 176, 0.5);
}
</style> 