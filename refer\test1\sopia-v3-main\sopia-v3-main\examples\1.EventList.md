# Event List

소피아 3의 코드모듈은 [cjs](https://ko.wikipedia.org/wiki/CommonJS) 형식을 사용합니다.

각 폴더의 `index.js` 파일에서 추출된 함수들을 기반으로 스푼의 소켓을 받게 되면 해당 이벤트 함수를 호출합니다.

<br><br>
이벤트 리스트는 [이 선언체](https://github.com/sopia-bot/sopia-core/blob/v2/src/socket/enum/index.ts#L16-L40)를 참고하십시오.

소켓 구현체는 [이 주소](https://github.com/sopia-bot/sopia-core/blob/v2/src/socket/live-socket.ts)를 확인하세요.
<br><br>

채팅을 받았을 때에 대한 이벤트를 가져오려면 함수를 다음과 같이 추출하면 됩니다.
<br><br>
```js
exports.live_message = (evt /* LiveMessageSocket */, sock /* LiveSocket */)  => {
	const message = evt.update_component.message.value;
	if ( message === 'ping' ) {
		sock.message('pong!');
	}
}
```
<br><br>
누군가 방송에 들어왔을 때 `live_join` 이벤트가 발생합니다.
<br><br>
```js
exports.live_join = (evt /* LiveJoinSocket */, sock /* LiveSocket */)  => {
	sock.message(`Hello. ${evt.data.author.nickname}!`)
}
```
