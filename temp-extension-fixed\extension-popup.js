function isSpoonPage(url) {
    return /https?:\/\/(?:www\.)?spooncast\.net/.test(url);
}

let currentUserInfo = null;

// Firebase 설정
const firebaseConfig = {
    apiKey: "AIzaSyDt1d7J8QS-mrRmVcNZ21AEd8f03ePL3Xs",
    authDomain: "tammapp-4cbb3.firebaseapp.com",
    projectId: "tammapp-4cbb3",
    storageBucket: "tammapp-4cbb3.firebasestorage.app",
    messagingSenderId: "839696613595",
    appId: "1:839696613595:web:cb4545c20a1558fdfeccfe",
    measurementId: "G-2M8HBKPLV4"
};

// Firebase 초기화 (SDK가 이미 로드됨)
let app = null;
let db = null;

const initFirebase = () => {
    try {
        console.log('🔥 Firebase 초기화 시작 (내장 SDK 사용)');
        console.log('🔥 Firebase 설정:', {
            apiKey: firebaseConfig.apiKey.substring(0, 10) + '...',
            authDomain: firebaseConfig.authDomain,
            projectId: firebaseConfig.projectId
        });
        
        // Firebase v9+ 모듈 구조 확인
        if (typeof firebase === 'undefined') {
            throw new Error('Firebase SDK가 로드되지 않았습니다');
        }
        
        console.log('🔥 Firebase 전역 객체 확인:', typeof firebase);
        console.log('🔥 Firebase 사용 가능한 메서드:', Object.keys(firebase));
        
        // Firebase 앱 초기화 (v9+ 방식)
        app = firebase.initializeApp(firebaseConfig);
        db = firebase.getFirestore(app);
        
        console.log('✅ Firebase 초기화 완료');
        console.log('✅ Firestore 인스턴스:', !!db);
        
        return { doc: firebase.doc, setDoc: firebase.setDoc };
    } catch (error) {
        console.error('❌ Firebase 초기화 실패:', error);
        console.error('❌ 오류 상세:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
        return null;
    }
};

window.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 확장프로그램 팝업 로드:', new Date().toLocaleString());
    
    try {
        const [tab] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
        console.log('📱 현재 탭:', tab.url);
        
        if (!isSpoonPage(tab.url)) {
            console.log('❌ 스푼캐스트 페이지가 아님');
            document.querySelector('#wrong-page').classList.remove('hidden');
            return;
        }

        console.log('📬 content script에 메시지 전송');
        chrome.tabs.sendMessage(tab.id, { method: 'getUserInfo' }, function(res) {
            console.log('📬 응답 수신:', res);
            
            if (chrome.runtime.lastError) {
                console.error('❌ 메시지 전송 실패:', chrome.runtime.lastError);
                document.querySelector('#not-login').classList.remove('hidden');
                return;
            }
            
            if (res && res.success) {
                const userInfo = res.data;
                currentUserInfo = userInfo;
                console.log('✅ 사용자 정보 수신:', {
                    id: userInfo.id,
                    nickname: userInfo.nickname,
                    hasToken: !!userInfo.token,
                    tokenLength: userInfo.token ? userInfo.token.length : 0
                });
                
                // UI 업데이트
                document.querySelector('#user-avatar').src = userInfo.profile_url || '/favicon.ico';
                document.querySelector('#user-name').textContent = userInfo.nickname || '사용자';
                document.querySelector('#user-tag').textContent = userInfo.tag ? `@${userInfo.tag}` : '';
                document.querySelector('#login-page').classList.remove('hidden');
                
            } else {
                console.log('❌ 로그인되지 않음:', res?.error);
                document.querySelector('#not-login').classList.remove('hidden');
            }
        });
    } catch (error) {
        console.error('❌ 초기화 실패:', error);
        document.querySelector('#not-login').classList.remove('hidden');
    }
});

// 버튼 클릭 이벤트 리스너
document.addEventListener('DOMContentLoaded', () => {
    const loginBtn = document.querySelector('#login-btn');
    if (loginBtn) {
        console.log('🔘 버튼 이벤트 리스너 등록 중...');
        loginBtn.addEventListener('click', async () => {
            console.log('🔗 TAMM 연동 버튼 클릭됨!');
            
            if (!currentUserInfo) {
                console.error('❌ 사용자 정보가 없습니다');
                alert('사용자 정보를 찾을 수 없습니다. 스푼캐스트에 로그인되어 있는지 확인해주세요.');
                return;
            }
            
            try {
                loginBtn.disabled = true;
                loginBtn.textContent = '연동 중...';
                console.log('🔄 연동 프로세스 시작');
                
                // 1. Firebase 초기화
                console.log('🔥 Firebase 초기화 시작');
                const firebaseResult = initFirebase();
                if (!firebaseResult) {
                    throw new Error('Firebase 초기화 실패');
                }
                const { doc, setDoc } = firebaseResult;
                
                // 2. 사용자 IP 가져오기
                console.log('🌐 사용자 IP 조회 시작');
                let userIP, ipData;
                try {
                    const ipResponse = await fetch('https://api.ipify.org?format=json');
                    console.log('🌐 IP API 응답 상태:', ipResponse.status, ipResponse.statusText);
                    
                    if (!ipResponse.ok) {
                        throw new Error(`IP API 요청 실패: ${ipResponse.status}`);
                    }
                    
                    ipData = await ipResponse.json();
                    userIP = ipData.ip;
                    console.log('🌐 사용자 IP 조회 완료:', userIP);
                    
                    if (!userIP || userIP === 'undefined') {
                        throw new Error('유효하지 않은 IP 주소');
                    }
                } catch (ipError) {
                    console.error('❌ IP 조회 실패:', ipError);
                    throw new Error(`IP 조회 실패: ${ipError.message}`);
                }
                
                const tokenData = {
                    id: currentUserInfo.id,
                    nickname: currentUserInfo.nickname,
                    profile_url: currentUserInfo.profile_url,
                    tag: currentUserInfo.tag,
                    token: currentUserInfo.token,
                    refresh_token: currentUserInfo.refresh_token,
                    userIP: userIP,
                    timestamp: Date.now()
                };
                
                console.log('💾 Firestore에 저장할 토큰 데이터:', {
                    id: tokenData.id,
                    nickname: tokenData.nickname,
                    userIP: tokenData.userIP,
                    tokenLength: tokenData.token ? tokenData.token.length : 0
                });
                
                // 3. Firestore에 IP별 토큰 저장
                console.log('💾 Firestore에 토큰 저장 시작');
                console.log('💾 저장 대상 IP:', userIP);
                console.log('💾 DB 인스턴스 확인:', !!db);
                
                try {
                    const saveData = {
                        // tamm-v1 호환 형식으로 저장
                        token: tokenData.token,
                        refreshToken: tokenData.refresh_token,
                        username: tokenData.nickname,
                        userId: tokenData.id.toString(),
                        timestamp: tokenData.timestamp,
                        userIP: userIP,  // IP 추가
                        user: {
                            id: tokenData.id.toString(),  // 문자열로 저장
                            nickname: tokenData.nickname,
                            tag: tokenData.tag,
                            profile_url: tokenData.profile_url
                        },
                        // 원본 데이터 저장 (tamm-v1 호환성)
                        originalData: currentUserInfo.originalData || currentUserInfo,
                        // spoon-radio-companion 호환성을 위한 추가 필드
                        createdAt: new Date(),
                        lastUpdated: new Date()
                    };
                    
                    console.log('💾 저장할 데이터 구조:', {
                        tokenLength: saveData.token?.length || 0,
                        userId: saveData.user.id,
                        nickname: saveData.user.nickname,
                        timestamp: saveData.timestamp
                    });
                    
                    console.log('💾 Firestore doc() 실행 시작...');
                    const tokenDocRef = doc(db, 'spoon_tokens', userIP);
                    console.log('💾 Document Reference 생성:', !!tokenDocRef);
                    
                    console.log('💾 setDoc() 실행 시작...');
                    await setDoc(tokenDocRef, saveData);
                    console.log('✅ Firestore에 토큰 저장 성공');
                    
                } catch (firestoreError) {
                    console.error('❌ Firestore 저장 상세 오류:', {
                        name: firestoreError.name,
                        message: firestoreError.message,
                        code: firestoreError.code,
                        stack: firestoreError.stack
                    });
                    throw new Error(`Firestore 저장 실패: ${firestoreError.message}`);
                }
                
                // 4. TAMM 웹앱에 메시지 전송 (선택적)
                console.log('📡 TAMM 웹앱에 메시지 전송');
                try {
                    const [tab] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
                    chrome.tabs.sendMessage(tab.id, { 
                        method: 'sendToTamm',
                        tokenData: tokenData
                    }, function(response) {
                        if (chrome.runtime.lastError) {
                            console.log('⚠️ TAMM 메시지 전송 실패 (정상):', chrome.runtime.lastError.message);
                        } else {
                            console.log('✅ TAMM 메시지 전송 성공');
                        }
                    });
                } catch (messageError) {
                    console.log('⚠️ TAMM 메시지 전송 실패 (정상):', messageError);
                }
                
                // 성공 UI 표시
                document.querySelector('#success-message').classList.remove('hidden');
                loginBtn.textContent = '연동 완료';
                
                // 3초 후 팝업 닫기
                setTimeout(() => {
                    window.close();
                }, 3000);
                
            } catch (error) {
                console.error('❌ 연동 실패:', error);
                alert('연동에 실패했습니다: ' + error.message);
                loginBtn.disabled = false;
                loginBtn.textContent = 'TAMM에 연동하기';
            }
        });
        console.log('✅ 버튼 이벤트 리스너 등록 완료');
    } else {
        console.error('❌ 로그인 버튼을 찾을 수 없음');
    }
}); 