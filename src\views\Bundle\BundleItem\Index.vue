<template>
	<div class="mt-3">
		<v-hover>
			<template v-slot:default="{ hover }">
				<v-card
					:elevation="hover ? 3 : 1"
					@click="detail = true;"
					:min-height="hover ? '160px' : '120px'"
					:max-height="hover ? '160px' : '120px'"
					class="pa-0 bundle-card">
					<v-card-text class="py-2 px-4 d-flex flex-column">
						<div class="mb-2">
							<v-chip
								v-if="pkg.is_official"
								color="purple"
								small
								outlined
								class="text-caption py-0">
								Official
							</v-chip>
						</div>
						
						<v-row align="start" class="ma-0" :style="`height: ${hover ? '130px' : '90px'};`">
							<v-col cols="9" align="" class="pt-0 pb-0">
								<h3 class="bundle-title">
									{{ name }}
									<span class="text-caption mb-1 ml-1 text--secondary">^{{ pkg.version }}</span>
								</h3>
								
								<p v-if="hover" class="ma-0 mt-2 description-text">{{ description }}</p>
							</v-col>
							<v-col cols="3" align="right" v-if="hover">
								<div class="text-caption grey--text mb-2">{{ pkg.owner_name }}</div>
								<v-btn
									v-if="pkg.page && isPackageUsing"
									small
									text
									color="primary"
									@click.stop="$assign(`/bundle/${pkg.name}/`)">
									<v-icon small left>mdi-open-in-new</v-icon>
									{{ $t('bundle.store.move-bundle-page') }}
								</v-btn>
							</v-col>
						</v-row>
					</v-card-text>
				</v-card>
			</template>
		</v-hover>
		<v-dialog v-model="detail" flat fullscreen persistent>
			<Detail :pkg="pkg" @close="detail = false;"/>
		</v-dialog>
	</div>
</template>
<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator';
import BundleMixins from '../bundle-mixin';
import EmptyBundlePackage, { BundlePackage } from '@/interface/bundle';
import Detail from './Detail.vue';
import path from 'path';
const fs = window.require('fs');
const { ipcRenderer } = window.require('electron');

@Component({
	components: {
		Detail,
	},
})
export default class BundleItem extends Mixins(BundleMixins) {

	@Prop(Object) public pkg!: BundlePackage;
	@Prop(Boolean) public isLocal!: Boolean;

	public isPackageUsing = false;
	public loading = false;
	public detail: boolean = false;
	public canUpdate = false;

	public created() {
		console.log(`[BundleItem] Created for package: ${this.pkg.name}`);
		this.updatePackageUsing();
	}

	public async install() {
		await this.$swal({
			icon: 'info',
			html: '<h3>번들 관리는 번들 매니저에서 진행해주세요.</h3>',
			confirmButtonText: '확인',
		});

		await ipcRenderer.invoke('open-bundle-manager');
	}

	public async localUninstall() {
		await this.$swal({
			icon: 'info',
			html: '<h3>번들 관리는 번들 매니저에서 진행해주세요.</h3>',
			confirmButtonText: '확인',
		});
		
		await ipcRenderer.invoke('open-bundle-manager');
	}

	public async uninstall() {
		await this.$swal({
			icon: 'info',
			html: '<h3>번들 관리는 번들 매니저에서 진행해주세요.</h3>',
			confirmButtonText: '확인',
		});

		await ipcRenderer.invoke('open-bundle-manager');
	}

	private updatePackageUsing() {
		console.log(`[BundleItem] updatePackageUsing for: ${this.pkg.name}`);
		this.isPackageUsing = fs.existsSync(this.getBundlePath(this.pkg));
	}

	private updateCanUpdate() {
		console.log(`[BundleItem] updateCanUpdate for: ${this.pkg.name}`);
		this.canUpdate = false;
	}

}
</script>

<style scoped>
.description-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.bundle-title {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 1.1rem;
  line-height: 1.4;
}

.bundle-card {
  transition: all 0.3s ease;
  cursor: pointer;
}
</style>

