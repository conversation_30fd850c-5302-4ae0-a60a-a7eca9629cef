import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuthContext } from '../contexts/AuthContext';
import { collection, addDoc, query, orderBy, onSnapshot } from 'firebase/firestore';
import { db } from '../firebaseConfig';

export interface BroadcastRoomProps {
  broadcast: {
    id: string;
    title: string;
    djName: string;
    thumbnail?: string;
    listeners: number;
    likes: number;
    tags: string[];
    isLive: boolean;
    category: string;
  };
  onBack: () => void;
}

interface ChatMessage {
  id: string;
  message: string;
  user: string;
  timestamp: any;
}

const RoomContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f7f7f7;
`;

const RoomHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 16px;
  border-radius: 50%;
  
  &:hover {
    background-color: #f7f7f7;
  }
`;

const RoomInfo = styled.div`
  flex: 1;
`;

const RoomTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 4px 0;
`;

const RoomStats = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #666;
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`;

const BroadcastArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: #fff;
  margin: 24px 0 24px 24px;
  border-radius: 12px;
`;

const BroadcastThumbnail = styled.div`
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: #ff4100;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 24px;
`;

const DjName = styled.h3`
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
`;

const LiveStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #ff4100;
  font-weight: 600;
`;

const LiveDot = styled.div`
  width: 8px;
  height: 8px;
  background-color: #ff4100;
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const ChatArea = styled.div`
  width: 320px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin: 24px 24px 24px 0;
  border-radius: 12px;
  overflow: hidden;
`;

const ChatHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f7f7f7;
`;

const ChatTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
`;

const ChatMessages = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  max-height: 400px;
`;

const ChatMessageContainer = styled.div`
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f7f7f7;
  border-radius: 8px;
`;

const MessageUser = styled.div`
  font-size: 12px;
  color: #666;
  font-weight: 600;
  margin-bottom: 4px;
`;

const MessageText = styled.div`
  font-size: 14px;
  color: #1a1a1a;
  line-height: 1.4;
`;

const ChatInput = styled.div`
  display: flex;
  padding: 16px;
  border-top: 1px solid #e6e6e6;
`;

const MessageInput = styled.input`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e6e6e6;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  
  &:focus {
    border-color: #ff4100;
  }
`;

const SendButton = styled.button`
  margin-left: 8px;
  padding: 8px 16px;
  background-color: #ff4100;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  
  &:hover {
    background-color: #e63a00;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const BroadcastRoom: React.FC<BroadcastRoomProps> = ({ broadcast, onBack }) => {
  const { user } = useAuthContext();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 실시간 채팅 메시지 구독
    const q = query(
      collection(db, 'chatRooms', broadcast.id, 'messages'),
      orderBy('timestamp', 'asc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messagesData: ChatMessage[] = [];
      snapshot.forEach((doc) => {
        messagesData.push({
          id: doc.id,
          ...doc.data()
        } as ChatMessage);
      });
      setMessages(messagesData);
    });

    return () => unsubscribe();
  }, [broadcast.id]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user) return;

    setLoading(true);
    try {
      await addDoc(collection(db, 'chatRooms', broadcast.id, 'messages'), {
        message: newMessage,
        user: user.displayName || user.email?.split('@')[0] || 'Anonymous',
        timestamp: new Date()
      });
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <RoomContainer>
      <RoomHeader>
        <BackButton onClick={onBack}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5m7-7l-7 7 7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </BackButton>
        <RoomInfo>
          <RoomTitle>{broadcast.title}</RoomTitle>
          <RoomStats>
            <span>👤 {broadcast.listeners}명 청취 중</span>
            <span>❤️ {broadcast.likes}</span>
          </RoomStats>
        </RoomInfo>
      </RoomHeader>

      <MainContent>
        <BroadcastArea>
          <BroadcastThumbnail>
            {broadcast.djName.charAt(0)}
          </BroadcastThumbnail>
          <DjName>{broadcast.djName}</DjName>
          <LiveStatus>
            <LiveDot />
            LIVE
          </LiveStatus>
        </BroadcastArea>

        <ChatArea>
          <ChatHeader>
            <ChatTitle>실시간 채팅</ChatTitle>
          </ChatHeader>
          <ChatMessages>
            {messages.map(message => (
              <ChatMessageContainer key={message.id}>
                <MessageUser>{message.user}</MessageUser>
                <MessageText>{message.message}</MessageText>
              </ChatMessageContainer>
            ))}
            {messages.length === 0 && (
              <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
                아직 채팅이 없습니다. 첫 번째 메시지를 보내보세요!
              </div>
            )}
          </ChatMessages>
          <ChatInput>
            <MessageInput
              type="text"
              placeholder={user ? "메시지를 입력하세요..." : "로그인 후 채팅할 수 있습니다"}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={!user || loading}
            />
            <SendButton 
              onClick={handleSendMessage}
              disabled={!user || loading || !newMessage.trim()}
            >
              전송
            </SendButton>
          </ChatInput>
        </ChatArea>
      </MainContent>
    </RoomContainer>
  );
};

export default BroadcastRoom; 