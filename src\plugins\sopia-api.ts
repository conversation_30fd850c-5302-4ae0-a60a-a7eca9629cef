import axios from 'axios';
import { UserDto } from '@sopia-bot/api-dto';

export class SopiaAPI {

	public user: any = {};

	public protocol: string = 'https';
	public host: string = 'api.sopia.dev';

	get ApiURL() {
		return `${this.protocol}://${this.host}`;
	}

	public async login(id: string, pw: string) {
		// 소피아 로그인은 항상 성공하도록 더미 데이터 반환
		const dummyUser = {
			id: 'auto',
			user_id: Date.now(),
			name: id || 'Auto User',
			gender: 'M',
			spoon_id: '0',
			token: 'dummy_token',
			refresh_token: 'dummy_refresh_token',
			agree_live_info: false
		};
		
		this.user = dummyUser;
		return dummyUser;
	}

	public async setUserInfo(data: UserDto) {
		// 소피아 사용자 정보 설정 성공 처리
		this.user = { ...this.user, ...data, agree_live_info: false };
		
		// 더미 응답 반환
		return {
			error: false,
			status: 200,
			data: [this.user]
		};
	}

	public async activityLog(tag: string, data: string = '') {
		// 활동 로그는 성공한 것으로 간주
		return {
			error: false,
			status: 200,
			data: [{ success: true }]
		};
	}

	public async req(method: string, url: string, data: any = {}): Promise<any> {
		// 소피아 관련 API 요청은 더미 데이터로 처리
		if (url.includes('/auth/login') || url.includes('/auth/sign')) {
			return {
				error: false,
				status: 200,
				data: [this.user || {
					id: 'auto',
					user_id: Date.now(),
					name: '자동사용자',
					gender: 'M',
					spoon_id: '0',
					agree_live_info: false
				}]
			};
		}

		if ( !data['headers'] ) {
			data = { data, headers: {} };
		}
		if ( url[0] !== '/' ) {
			url = '/' + url;
		}

		data['url'] = this.ApiURL + url;
		data['method'] = method;

		if ( this.user?.token ) {
			data['headers']['authorization'] = 'Bearer ' + this.user.token;
		}

		try {
			const res = await axios(data);
			return res.data;
		} catch (err: any) {
			if ( err.response ) {
				if ( err.response.data.error ) {
					if ( err.response.data.msg === 'jwt_expired' ) {
						await this.refreshToken.call(this);
						return await this.req(method, url, data);
					}
				}
			}
			
			// API 요청 실패 시 에러를 던지지 않고 더미 응답을 반환
			if (url.includes('/user/info') || url.includes('/user/')) {
				return {
					error: false,
					status: 200,
					data: [this.user || {
						id: 'auto',
						user_id: Date.now(),
						name: '자동사용자',
						gender: 'M',
						spoon_id: '0',
						agree_live_info: false
					}]
				};
			}
			
			throw err;
		}
	}

	private async refreshToken() {
		try {
			const res = await axios({
				url: this.ApiURL + '/auth/refresh',
				method: 'post',
				data: {
					refresh_token: this.user.refresh_token,
				},
			});
			if ( res.data.error ) {
				switch ( res.data.msg ) {
					case 'expired':
						window.logout();
						return;
				}
			}
			this.user.token = res.data.data[0].token;
			this.user.refresh_token = res.data.data[0].refresh_token;
			this.user.agree_live_info = false;

			window.appCfg.set('auth.sopia', this.user);
			window.appCfg.save();
		} catch (error) {
			console.error('Token refresh failed:', error);
		}
	}

}
