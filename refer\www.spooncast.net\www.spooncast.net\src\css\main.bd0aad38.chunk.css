.alert-container {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    z-index: 8
}

.alert-container,
.alert-container .alert-backdrop {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0
}

.alert-container .alert-backdrop {
    background-color: rgba(0, 0, 0, .5)
}

.alert-container .alert-contents {
    position: relative;
    overflow-y: auto;
    width: 100%;
    max-width: 283px;
    max-height: 80vh;
    border-radius: 15px;
    background-color: #fff;
    -webkit-overflow-scrolling: touch
}

.alert-container .alert-contents .alert-header {
    position: relative;
    text-align: left
}

.alert-container .alert-contents .alert-header .title {
    padding: 8px 16px 11px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    border-bottom: 1px solid #d9d9d9;
    color: #4d4d4d
}

.alert-container .alert-contents .alert-header .sub-title {
    padding: 8px 16px 11px;
    line-height: 43px;
    font-size: 12px;
    border-bottom: 1px solid #e6e6e6;
    color: #4d4d4d
}

.alert-container .alert-contents .alert-body {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32px 20px 24px;
    letter-spacing: -.5px;
    color: #4d4d4d;
    white-space: pre-wrap
}

.alert-container .alert-contents .alert-body .alert-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    grid-gap: 20px;
    gap: 20px;
    width: 100%;
    min-height: 24px;
    color: #333
}

.alert-container .alert-contents .alert-body .alert-text .icon-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background-color: #ffefee
}

.alert-container .alert-contents .alert-body .alert-text .body-text {
    font-size: 14px;
    font-weight: 700
}

.alert-container .alert-contents .alert-body .alert-text .body-text .title {
    line-height: 150%
}

.alert-container .alert-contents .alert-body .alert-text .body-text ul {
    padding-top: 5px
}

.alert-container .alert-contents .alert-body .alert-text .body-text li {
    list-style-type: circle;
    list-style: inside
}

.alert-container .alert-contents .alert-body .alert-text .body-text .list {
    text-align: start
}

.alert-container .alert-contents .alert-body .alert-text .body-text .sub-text {
    color: #999;
    font-size: 12px;
    font-weight: 400;
    line-height: 150%
}

.alert-container .alert-contents .alert-body .alert-text .sub-text {
    color: #999;
    font-size: 12px;
    font-weight: 400;
    white-space: pre-wrap
}

.alert-container .alert-contents .alert-body .account {
    color: grey
}

.alert-container .alert-contents .alert-body .nick-name {
    font-weight: 600
}

.alert-container .alert-contents .alert-body a {
    text-decoration: underline
}

.alert-container .alert-contents .btns {
    display: flex;
    justify-content: center;
    padding: 0 16px 20px;
    margin: 0 auto;
    width: 251px;
    height: 40px;
    font-weight: 600
}

.alert-container .alert-contents .btns button {
    position: relative;
    width: 100%;
    min-height: 40px;
    border-radius: 8px;
    padding: 3px 0;
    font-size: 14px;
    text-align: center;
    color: #fff;
    background-color: #1a1a1a
}

.alert-container .alert-contents .btns .cover {
    border-radius: 8px
}

.alert-container .alert-contents .btns .line-btn {
    border: 1px solid #e6e6e6;
    color: #1a1a1a;
    background-color: #fff
}

.alert-container .alert-contents .btns .line-btn.red {
    color: #ff2e21
}

.alert-container .alert-contents .btns .line-btn.aliveorange {
    color: #ff4100
}

.alert-container .alert-contents.small {
    max-width: 300px
}

.alert-container .alert-contents.small .alert-body {
    padding: 20px
}

.alert-container .alert-contents.small .btns {
    padding: 10px 60px 30px
}

.alert-container .alert-contents.mini {
    max-width: 300px
}

.alert-container .alert-contents.mini .alert-body {
    padding: 20px;
    min-height: auto
}

.alert-container .alert-contents.mini .btns {
    padding: 10px 60px 30px
}

.alert-container .alert-contents.align-left {
    text-align: left
}

.alert-container .alert-contents.align-left .alert-body {
    justify-content: left
}

.alert-container .alert-contents.align-center {
    text-align: center
}

.alert-container .alert-contents.align-right {
    text-align: right
}

.alert-container.select .alert-contents .alert-body,
.alert-container.title .alert-contents .alert-body {
    min-height: auto
}

.alert-container.select .alert-contents .btns button {
    width: 100%
}

.alert-container.select .alert-contents .btns button:first-child {
    margin-right: 8px;
    border: 1px solid #e6e6e6;
    color: #333;
    background-color: #fff
}

.alert-container.sticker .alert-contents .alert-body {
    flex-direction: column;
    padding: 32px 0 20px;
    margin: 0 20px;
    box-sizing: border-box
}

.alert-container.sticker .alert-contents .alert-body * {
    box-sizing: border-box
}

.alert-container.sticker .alert-contents .alert-body span {
    font-weight: 600;
    color: #ff4100
}

.alert-container.sticker .alert-contents .alert-body .sticker-thumbnail {
    width: 160px;
    height: 160px;
    margin-bottom: 16px;
    border-radius: 50%;
    background: #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 36px;
    overflow: hidden;
    box-sizing: border-box
}

.alert-container.sticker .alert-contents .alert-body .sticker-thumbnail img {
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: 50%
}

.alert-container.sticker .alert-contents .alert-body .sticker-thumbnail img.item-image {
    height: 112px
}

.alert-container.sticker .alert-contents .btns {
    padding: 0 0 16px
}

.alert-container.rtl {
    direction: rtl
}

.alert-container.rtl.select .alert-contents .alert-header {
    text-align: right
}

.alert-container.rtl.select .alert-contents .btns button:first-child {
    margin-left: 8px;
    margin-right: 0
}

.initialize-loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
    background-color: #fff
}

.initialize-loader-container img {
    width: 60px
}

.initialize-loader-container pre {
    margin-top: 23px;
    font-size: 16px;
    font-weight: 600;
    line-height: 26px;
    text-align: center;
    color: #333
}

.initialize-loader-container.dark-mode {
    background-color: #121212
}

.initialize-loader-container.dark-mode pre {
    color: #cfcfcf
}

.initialize-loader-container.rtl {
    direction: rtl
}

.initialize-loader-container.show-header {
    top: 52px;
    height: calc(100% - 52px)
}

.modal-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    height: 100%
}

.modal-contents {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    border: 1px solid #4d4d4d;
    background-color: #fff;
    box-shadow: 5px 5px 10px 0 rgba(0, 0, 0, .3)
}

.modal-contents a,
.modal-contents button {
    text-align: center
}

.modal-header {
    display: flex;
    align-items: center;
    flex: none;
    position: relative;
    padding: 10px 46px 10px 16px;
    border-bottom: 1px solid #d9d9d9
}

.modal-header .title {
    overflow: hidden;
    min-height: 24px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #4d4d4d;
    word-break: break-all
}

.modal-header .btn-close {
    flex: none;
    position: absolute;
    width: 30px;
    height: 30px;
    top: 7px;
    right: 12px;
    z-index: 1
}

.modal-header .btn-close img {
    width: 24px
}

.modal-body {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.ReactModal__Overlay {
    background-color: rgba(0, 0, 0, .8) !important
}

.modal-container.rtl {
    direction: rtl
}

.modal-container.rtl .modal-header {
    padding: 10px 16px 10px 46px
}

.modal-container.rtl .modal-header .btn-close {
    left: 12px;
    right: auto
}

.view-container.channel,
.view-container.notice {
    display: flex
}

.app-container.rtl .view-container.side-menu-open {
    margin-left: 0;
    margin-right: 240px
}

.app-container.rtl .view-container.side-menu-open.detail-view {
    margin-right: 0
}

@media screen and (max-width:1365px) {
    .app-container.rtl .view-container.side-menu-open,
    .view-container,
    .view-container.side-menu-open {
        width: 100%;
        margin-left: 0;
        margin-right: 0
    }
}

@media screen and (max-width:767px) {
    .view-container {
        width: 100%;
        transition-duration: 0s
    }
    .view-container:not {
        min-height: auto
    }
    .view-container.on-top {
        padding-top: 0
    }
    .view-container.on-top.mobile-post-detail {
        margin-top: 0;
        position: -webkit-sticky;
        position: sticky
    }
    .view-container.creator {
        padding-top: 0
    }
}

.yarl__root {
    --yarl__thumbnails_thumbnail_padding: 0;
    --yarl__thumbnails_thumbnail_border: transparent;
    --yarl__color_backdrop: $black_a80;
    --yarl__slide_captions_container_background: $black_a80
}

.yarl__container {
    background-color: rgba(0, 0, 0, .8)
}

.yarl__button {
    filter: "unset"
}

.yarl__thumbnails_thumbnail {
    opacity: .48
}

.yarl__thumbnails_thumbnail .yarl__thumbnails_thumbnail_active {
    opacity: 1
}

.tier-badge-list-container {
    display: flex;
    position: relative
}

.tier-badge-list-container .award-badge-img,
.tier-badge-list-container .tier-badge {
    margin-inline-start: 4px;
    -moz-margin-start: 4px;
    -webkit-margin-start: 4px
}

.tier-badge-list-container .award-badge-img:first-child,
.tier-badge-list-container .tier-badge:first-child {
    margin-inline-start: 0;
    -moz-margin-start: 0;
    -webkit-margin-start: 0
}

.create-list-wrap {
    position: relative
}

.create-list-wrap .empty-space {
    width: 3px
}

.create-list-wrap .btn-create-list {
    background-color: #ff4100;
    border: 2px solid #ff4100;
    border-radius: 20px;
    color: #fff;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    align-items: center;
    position: relative;
    padding: 4px 12px;
    min-height: 40px
}

.create-list-wrap .btn-create-list img {
    width: 16px;
    margin-inline-end: 4px;
    -moz-margin-end: 4px;
    -webkit-margin-end: 4px
}

.create-list-wrap .btn-create-list:hover {
    background-color: #f53e00
}

@media screen and (max-width:930px) {
    .create-list-wrap.off-air {
        display: none
    }
}

.thumbnail-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-color: #d9d9d9;
    background-size: 36px 36px
}

.thumbnail-container,
.thumbnail-container.default-img-s {
    background-image: url(/src/images/profile/logo_spoon_small.7b0a90d2.png)
}

.thumbnail-container.default-img-s {
    background-size: 50px 50px
}

.thumbnail-container.default-img-m {
    background-size: 100px 100px;
    background-image: url(/src/images/profile/logo_spoon_medium.acdb4949.png)
}

.thumbnail-container.default-img-l {
    background-size: 150px 150px
}

.thumbnail-container.default-img-l,
.thumbnail-container.default-img-xl {
    background-image: url(/src/images/profile/logo_spoon_large.376cd9d7.png)
}

.thumbnail-container.default-img-xl {
    background-size: 300px 300px
}

.thumbnail-container.loaded {
    background-color: transparent;
    background-image: none
}

.thumbnail-container.loaded img {
    width: 100%;
    height: 100%;
    opacity: 1
}

.thumbnail-container.loaded img.cover {
    object-fit: cover
}

.thumbnail-container.loaded img.contain {
    object-fit: contain
}

.thumbnail-container.loaded img.fill {
    object-fit: fill
}

.thumbnail-container.loaded img.scale-down {
    object-fit: scale-down
}

@media screen and (max-width:767px) {
    .thumbnail-container.mobile-default-img-s {
        background-size: 50px 50px;
        background-image: url(/src/images/profile/logo_spoon_small.7b0a90d2.png)
    }
    .thumbnail-container.mobile-default-img-m {
        background-size: 100px 100px;
        background-image: url(/src/images/profile/logo_spoon_medium.acdb4949.png)
    }
    .thumbnail-container.mobile-default-img-l {
        background-size: 150px 150px;
        background-image: url(/src/images/profile/logo_spoon_large.376cd9d7.png)
    }
    .thumbnail-container.mobile-default-img-xl {
        background-size: 300px 300px;
        background-image: url(/src/images/profile/logo_spoon_large.376cd9d7.png)
    }
}

.user-thumbnail-container {
    display: flex;
    flex: none;
    width: 50px;
    height: 50px;
    z-index: 1
}

.user-thumbnail-container a,
.user-thumbnail-container button {
    display: block;
    flex: 1 1;
    overflow: hidden;
    border-radius: 50%;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover
}

.user-thumbnail-container .badge-live {
    display: flex;
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    pointer-events: none
}

.user-thumbnail-container .badge-live p {
    flex: none;
    position: relative;
    min-width: 36px;
    padding: 0 5px;
    left: 50%;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    border: 1px solid #fff;
    border-radius: 8px;
    color: #fff;
    background-color: #81d4a8;
    transform: translateY(2px) translateX(-50%)
}

.user-thumbnail-container .badge-tier {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -4px;
    pointer-events: none
}

.user-thumbnail-container .badge-tier p {
    background-color: #ff4100;
    border: 1px solid transparent;
    border-radius: 9px;
    color: #fff;
    position: relative;
    padding: 2px 4px;
    font-size: 10px;
    font-weight: 600;
    line-height: 10px;
    text-align: center;
    text-transform: capitalize
}

.user-thumbnail-container .badge-tier.original p {
    border-color: #ff275c;
    background-color: #ff275c
}

.user-thumbnail-container .badge-tier.red-choice p {
    border-color: #fff;
    background-color: #b71c1c
}

.user-thumbnail-container .badge-tier.yellow-choice p {
    border-color: #fff;
    background-color: #ff9800
}

.user-thumbnail-container .badge-tier.voice p {
    border-color: #fff;
    background-color: #536dfe
}

.user-thumbnail-container .btn-live-badge {
    background-color: #fff;
    border: 1px solid #ff571e !important;
    border-radius: 8px;
    color: #ff4100;
    display: block;
    flex: 1 1;
    font-size: 10px;
    font-stretch: normal;
    font-style: normal;
    font-weight: 600;
    line-height: 15px;
    text-align: center;
    padding: 0;
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 48px;
    z-index: 2
}

.user-thumbnail-container .btn-live-badge .icon {
    display: none
}

.user-thumbnail-container .btn-live-badge .text {
    margin: 0;
    font-weight: 500
}

.user-thumbnail-container.on-air {
    border-color: transparent
}

.user-thumbnail-container.no-border {
    border: 0
}

.user-thumbnail-container:hover+.nick-name-wrap .name-wrap a,
.user-thumbnail-container:hover+.nick-name-wrap .name-wrap button {
    text-decoration: underline
}

.user-thumbnail-container:hover+.nick-name-wrap .name-wrap a.disabled,
.user-thumbnail-container:hover+.nick-name-wrap .name-wrap button.disabled {
    text-decoration: none
}

.user-thumbnail-container.default-img-xss {
    width: 24px;
    height: 24px
}

.user-thumbnail-container.default-img-xs {
    width: 36px;
    height: 36px
}

.user-thumbnail-container.default-img-s {
    width: 48px;
    height: 48px
}

.user-thumbnail-container.default-img-m {
    width: 120px;
    height: 120px
}

.user-thumbnail-container.default-img-l {
    width: 320px;
    height: 320px
}

.app-container.rtl .user-thumbnail-container .badge-live p {
    left: auto;
    right: 50%;
    transform: translateY(2px) translateX(50%)
}

@media screen and (max-width:767px) {
    .user-thumbnail-container .badge-tier {
        display: none
    }
    .user-thumbnail-container.default-img-m {
        width: 52px;
        height: 52px
    }
}

.noti-list-item-container {
    display: flex;
    white-space: pre-wrap
}

.noti-list-item-container button {
    flex: 1 1;
    padding: 12px 16px;
    text-align: left
}

.noti-list-item-container button .btn-inner {
    justify-content: flex-start;
    align-items: flex-start
}

.noti-list-item-container button .btn-inner .user-info {
    position: relative
}

.noti-list-item-container button .btn-inner .user-info .thumbnail-wrap {
    position: relative;
    overflow: hidden;
    width: 36px;
    height: 36px;
    border-radius: 50%
}

.noti-list-item-container button .btn-inner .user-info .icon-wrap {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid #fff;
    background-color: #ff4100
}

.noti-list-item-container button .btn-inner .user-info .icon-wrap>img {
    width: 16px
}

.noti-list-item-container button .btn-inner .noti-info {
    flex: 1 1;
    overflow: hidden;
    margin-left: 12px
}

.noti-list-item-container button .btn-inner .noti-info .text-box {
    -webkit-line-clamp: inherit;
    line-clamp: inherit
}

.noti-list-item-container button .btn-inner .noti-info p {
    position: relative;
    font-size: 12px;
    line-height: 19px
}

.noti-list-item-container button .btn-inner .noti-info p.text {
    color: #4d4d4d
}

.noti-list-item-container button .btn-inner .noti-info p.text span {
    font-weight: 600
}

.noti-list-item-container button .btn-inner .noti-info p.text span.contents {
    font-weight: 400
}

.noti-list-item-container button .btn-inner .noti-info p.date {
    color: grey
}

.noti-list-item-container.staff .noti-info p.text span {
    color: #ff4100
}

.noti-list-item-container.confirmed {
    background-color: #e6e6e6
}

.app-container.rtl .noti-list-item-container .noti-info {
    margin-left: 0;
    margin-right: 12px;
    text-align: right
}

@media screen and (max-width:767px) {
    .noti-list-item-container button {
        padding: 12px
    }
}

.portal-container.notificationNotice {
    background-color: rgba(0, 0, 0, .5);
    display: flex;
    align-items: center;
    justify-content: center
}

.portal-container.notificationNotice .notification-notice-container {
    background-color: #fff;
    width: 680px;
    height: 612px;
    border-radius: 6px
}

.portal-container.notificationNotice .notification-notice-header {
    padding: 22px 0;
    text-align: center;
    position: relative
}

.portal-container.notificationNotice .notification-notice-header h2 {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #333
}

.portal-container.notificationNotice .notification-notice-header button {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px
}

.portal-container.notificationNotice .notification-notice-header button>img {
    width: 100%
}

.portal-container.notificationNotice .notification-notice-content .user-info {
    padding: 20px;
    display: flex;
    align-items: center
}

.portal-container.notificationNotice .notification-notice-content .user-info h3 {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 600;
    color: #333
}

.portal-container.notificationNotice .notification-notice-content pre {
    padding: 12px 20px;
    font-size: 14px;
    color: #333
}

.portal-container.notificationNotice.rtl .notification-notice-header button {
    right: auto;
    left: 16px
}

.portal-container.notificationNotice.rtl .notification-notice-content .user-info h3 {
    margin-left: 0;
    margin-right: 10px
}

@media(max-width:767px) {
    .portal-container.notificationNotice {
        z-index: 5
    }
    .portal-container.notificationNotice .notification-notice-container {
        width: 100%;
        height: 100%;
        border-radius: 0
    }
    .portal-container.notificationNotice .notification-notice-header {
        padding: 18px 0
    }
    .portal-container.notificationNotice .notification-notice-header button {
        right: auto;
        left: 16px
    }
    .portal-container.notificationNotice .notification-notice-content .user-info {
        padding: 14px 16px
    }
    .portal-container.notificationNotice .notification-notice-content pre {
        padding: 10px 16px
    }
}

.notifications-simple-menu {
    position: relative
}

.notifications-simple-menu .notification-menu-contents {
    position: absolute;
    width: 320px;
    height: 80vh;
    max-height: 478px;
    top: 31px;
    right: -19px;
    background-color: transparent;
    z-index: 1
}

.notifications-simple-menu .notification-menu-contents .dimmed {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, .5);
    background-size: cover;
    cursor: auto
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
    min-height: 400px;
    background-color: #fff;
    border: 1px solid #4d4d4d;
    z-index: 1
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    padding: 0 16px;
    border-bottom: 1px solid #d9d9d9
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .nav-arrow {
    position: absolute;
    width: 8px;
    height: 8px;
    top: -5px;
    right: 24px;
    border-color: #4d4d4d;
    border-style: solid;
    border-width: 1px 0 0 1px;
    background-color: #fff;
    transform: rotate(45deg)
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #4d4d4d;
    cursor: default
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .btn-menu-header {
    display: flex;
    justify-content: flex-end
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .btn-menu-header button {
    font-size: 13px;
    color: #ff4100
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .btn-menu-header button:hover {
    text-decoration: underline
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .btn-menu-header button svg {
    width: 24px;
    height: 24px
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .btn-menu-header button img {
    width: 24px
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .btn-menu-header.off button {
    color: #d9d9d9;
    pointer-events: none
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .noti-list {
    overflow-x: hidden;
    overflow-y: auto;
    min-height: 51px;
    -webkit-overflow-scrolling: touch
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .noti-list ul {
    display: block
}

.notifications-simple-menu .notification-menu-contents .menu-item-wrap .noti-list .empty-text {
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -8px;
    font-size: 13px;
    text-align: center;
    color: grey
}

.app-container.rtl .notifications-simple-menu .notification-menu-contents {
    position: absolute;
    width: 320px;
    height: 80vh;
    max-height: 478px;
    top: 41px;
    left: -13px;
    background-color: transparent;
    z-index: 1
}

.app-container.rtl .notifications-simple-menu .notification-menu-contents .menu-item-wrap .menu-header .nav-arrow {
    left: 24px;
    right: auto
}

@media screen and (max-width:767px) {
    .notifications-simple-menu {
        display: none
    }
}

/*# sourceMappingURL=main.bd0aad38.chunk.css.map */