@echo off
echo TAMM-V1 앱을 시작합니다...
echo 아이콘 파일을 설정합니다...
cd /d %~dp0

:: 필요한 디렉토리 생성
if not exist "build" mkdir build
if not exist "build\icons" mkdir build\icons

:: trash 폴더의 아이콘 복사
copy /Y trash\icon16.png public\icon16.png
copy /Y trash\icon48.png public\icon48.png
copy /Y trash\icon128.png public\icon128.png
copy /Y trash\icon128.png public\icon.png

:: 특별한 이름의 아이콘 파일 생성 (Electron 자동 인식)
copy /Y trash\icon128.png public\app.ico
copy /Y trash\icon128.png public\app.png
copy /Y trash\icon16.png public\favicon.ico

:: 빌드 폴더에도 아이콘 복사
copy /Y trash\icon16.png build\icons\16x16.png
copy /Y trash\icon48.png build\icons\48x48.png
copy /Y trash\icon128.png build\icons\128x128.png
copy /Y trash\icon128.png build\icons\256x256.png
copy /Y trash\icon128.png build\icons\512x512.png
copy /Y trash\icon128.png build\icons\1024x1024.png
copy /Y trash\icon128.png build\icons\icon.png
copy /Y trash\icon128.png build\icons\app.png
copy /Y trash\icon128.png build\icons\app.ico

:: 운영체제별 아이콘 설정
copy /Y trash\icon128.png public\icon.ico
copy /Y trash\icon128.png build\icons\icon.ico

echo 아이콘 설정 완료!
npm start
pause 