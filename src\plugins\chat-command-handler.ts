/**
 * chat-command-handler.ts
 * 
 * 채팅 명령어 처리 서비스
 * 시청자 채팅 명령어를 처리하고 유저 데이터를 관리합니다.
 */
import userDataManager, { UserData } from './user-data-ipc';

export interface ChatAuthor {
  nickname: string;
  tag: string;
  [key: string]: any;
}

export interface ChatMessage {
  message: string;
  author: ChatAuthor;
  [key: string]: any;
}

export class ChatCommandHandler {
  private djTag: string = '';
  private sendMessageCallback: ((message: string) => void) | null = null;
  private attendanceCheckedUsers: Set<string> = new Set(); // 오늘 출석 체크한 유저 목록
  private lastAttendanceDate: string = ''; // 마지막 출석 체크 날짜 (YYYY-MM-DD 형식)

  constructor(djTag: string = '', sendMessageCallback: ((message: string) => void) | null = null) {
    this.djTag = djTag;
    this.sendMessageCallback = sendMessageCallback;
    this.initAttendanceCheck();
  }

  /**
   * 출석 체크 데이터 초기화
   */
  private initAttendanceCheck(): void {
    // 현재 날짜 가져오기 (YYYY-MM-DD 형식)
    const today = this.getFormattedDate(new Date());
    this.lastAttendanceDate = today;
    this.attendanceCheckedUsers = new Set();
    
    // 날짜 변경 체크를 위한 타이머 설정 (10분마다 체크)
    setInterval(() => this.checkDateChange(), 10 * 60 * 1000);
  }

  /**
   * 날짜 변경 체크 및 처리
   */
  private checkDateChange(): void {
    const today = this.getFormattedDate(new Date());
    if (today !== this.lastAttendanceDate) {
      console.log(`날짜 변경 감지: ${this.lastAttendanceDate} -> ${today}`);
      this.lastAttendanceDate = today;
      this.attendanceCheckedUsers.clear();
    }
  }

  /**
   * 날짜 포맷팅 (YYYY-MM-DD)
   */
  private getFormattedDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * DJ 태그 설정
   * @param djTag DJ 고유 태그
   */
  setDjTag(djTag: string): void {
    this.djTag = djTag;
  }

  /**
   * 메시지 전송 콜백 설정
   * @param callback 메시지 전송 함수
   */
  setSendMessageCallback(callback: (message: string) => void): void {
    this.sendMessageCallback = callback;
  }

  /**
   * 채팅 메시지 전송
   * @param message 전송할 메시지
   */
  sendMessage(message: string): void {
    if (this.sendMessageCallback) {
      this.sendMessageCallback(message);
    } else {
      console.log('메시지 전송 콜백이 설정되지 않았습니다:', message);
    }
  }

  /**
   * 채팅 메시지 처리
   * @param data 채팅 메시지 데이터
   */
  async handleChatMessage(data: ChatMessage): Promise<void> {
    try {
      if (!data || !data.message || !data.author) return;

      const { message, author } = data;
      const command = message.trim().toLowerCase();

      // 명령어 처리
      if (command === '!내정보') {
        await this.processUserInfoCommand(author);
      } else if (command === '!내정보 생성') {
        await this.processCreateUserCommand(author);
      } else if (command.startsWith('!복권 ')) {
        await this.processLotteryNumbers(message, author);
      }

      // 출석 체크 (첫 채팅 시)
      await this.checkAttendance(author);

      // 채팅 카운트 증가 (모든 채팅에 적용)
      await this.updateUserChatCount(author);
    } catch (err) {
      console.error('채팅 메시지 처리 중 오류 발생:', err);
    }
  }

  /**
   * 좋아요(하트) 이벤트 처리
   * @param data 이벤트 데이터
   */
  async handleLikeEvent(data: any): Promise<void> {
    try {
      if (!data || !data.author) return;
      
      const { author } = data;
      await this.updateUserHeartCount(author);
    } catch (err) {
      console.error('좋아요 이벤트 처리 중 오류 발생:', err);
    }
  }

  /**
   * 출석 체크 처리
   * @param author 시청자 정보
   */
  private async checkAttendance(author: ChatAuthor): Promise<void> {
    try {
      if (!author || !author.tag) return;
      
      // 오늘 이미 출석 체크한 유저인지 확인
      if (this.attendanceCheckedUsers.has(author.tag)) {
        return;
      }
      
      // 유저 찾기
      let user = userDataManager.getUserByNickname(author.nickname);
      
      // 유저가 없으면 무시 (명시적으로 !내정보 생성 명령어를 사용해야 함)
      if (!user) return;
      
      // 출석 카운트 증가
      user.attend_count += 1;
      
      // 경험치 증가 (출석 보너스: 10 포인트)
      user.point += 10;
      
      // 출석 일자 업데이트
      user.last_attend = Date.now();
      
      // 레벨 계산
      this.calculateUserLevel(user);
      
      // 유저 업데이트
      await userDataManager.updateUser(user);
      
      // 출석 체크한 유저 목록에 추가
      this.attendanceCheckedUsers.add(author.tag);
      
      console.log(`${author.nickname} 출석 체크 완료 (${user.attend_count}회)`);
    } catch (err) {
      console.error('출석 체크 중 오류 발생:', err);
    }
  }

  /**
   * !내정보 명령어 처리
   * @param author 시청자 정보
   */
  private async processUserInfoCommand(author: ChatAuthor): Promise<void> {
    try {
      if (!author || !author.tag) return;
      
      // 유저 찾기
      const user = userDataManager.getUserByNickname(author.nickname);
      
      // 유저가 없으면 안내 메시지 전송
      if (!user) {
        this.sendMessage(`@${author.nickname} 님의 정보가 없습니다. '!내정보 생성' 명령어로 정보를 생성해주세요.`);
        return;
      }
      
      // 경험치 퍼센트 계산
      const expPercentage = this.calculateExpPercentage(user.point, user.level);
      
      // 응답 메시지 생성 (줄바꿈 포맷)
      const message = [
        `@${author.nickname} 님의 정보`,
        `Lv. ${user.level} (${expPercentage}%)`,
        `채팅 : ${user.chat_count}`,
        `출석 : ${user.attend_count}`,
        `하트 : ${user.heart_count}`,
        `복권 : ${user.spoon ? user.spoon[2] : 0}`,
        `보유금 : ${user.money ? user.money.toLocaleString() : 0}원`
      ].join('\n');
      
      // 메시지 전송
      this.sendMessage(message);
    } catch (err) {
      console.error('내정보 명령어 처리 중 오류 발생:', err);
    }
  }

  /**
   * !내정보 생성 명령어 처리
   * @param author 시청자 정보
   */
  private async processCreateUserCommand(author: ChatAuthor): Promise<void> {
    try {
      if (!author || !author.tag) return;
      
      // 이미 존재하는 유저인지 확인
      const existingUser = userDataManager.getUserByNickname(author.nickname);
      
      if (existingUser) {
        // 이미 정보가 있는 경우, 정보 생성 대신 정보 표시
        this.sendMessage(`@${author.nickname} 님은 이미 등록된 정보가 있습니다.`);
        await this.processUserInfoCommand(author);
        return;
      }
      
      // 새 유저 생성
      const newUser = this.createNewUser(author);
      await userDataManager.addUser(newUser);
      
      // 성공 메시지
      this.sendMessage(`@${author.nickname} 님의 정보가 생성되었습니다.`);
      
      // 바로 정보 표시
      await this.processUserInfoCommand(author);
    } catch (err) {
      console.error('내정보 생성 명령어 처리 중 오류 발생:', err);
    }
  }

  /**
   * !복권 X X X 명령어 처리
   * @param message 채팅 메시지
   * @param author 시청자 정보
   */
  private async processLotteryNumbers(message: string, author: ChatAuthor): Promise<void> {
    try {
      if (!author || !author.tag) return;
      
      // 유저 찾기
      const user = userDataManager.getUserByNickname(author.nickname);
      
      // 유저가 없으면 안내 메시지 전송
      if (!user) {
        this.sendMessage(`@${author.nickname} 님의 정보가 없습니다. '!내정보 생성' 명령어로 정보를 생성해주세요.`);
        return;
      }
      
      // 복권 개수 확인
      if (!user.spoon || user.spoon[2] <= 0) {
        this.sendMessage(`@${author.nickname} 님은 사용할 수 있는 복권이 없습니다.`);
        return;
      }
      
      // 메시지 파싱
      const msg_split = message.split(' ');
      if (msg_split.length === 4) {
        // !복권 X X X 형식인 경우 (3개의 숫자 선택)
        const numbers = msg_split.slice(1);
        const selectedNumbers: number[] = [];
        
        // 숫자 유효성 검사
        for (const num of numbers) {
          if (isNaN(+num) || num.length > 1 || +num < 0 || +num > 9) {
            this.sendMessage('복권은 0~9 사이의 숫자 세개를 선택하세요\n(예)!복권 2 5 7');
            return;
          }
          
          selectedNumbers.push(+num);
        }
        
        // 복권 사용
        user.spoon[2]--;
        
        // 랜덤 숫자 3개 생성
        const winningNumbers: number[] = [];
        while (winningNumbers.length < 3) {
          const randomNum = Math.floor(Math.random() * 10);
          if (!winningNumbers.includes(randomNum)) {
            winningNumbers.push(randomNum);
          }
        }
        
        // 당첨 결과 계산
        let matchCount = 0;
        let multiplier = 1;
        
        for (const num of winningNumbers) {
          if (selectedNumbers.includes(num)) {
            multiplier *= 10;
            matchCount++;
          }
        }
        
        // 보유금 계산 (복권은 기본 1000원으로 설정)
        const baseReward = 1000;
        const earnedMoney = multiplier * baseReward;
        
        // money 필드 초기화 (없는 경우)
        if (user.money === undefined) {
          user.money = 0;
        }
        
        // 보유금 증가
        user.money += earnedMoney;
        
        // 레벨 계산 (경험치는 그대로 유지하되 레벨 계산은 실행)
        this.calculateUserLevel(user);
        
        // 유저 업데이트
        await userDataManager.updateUser(user);
        
        // 결과 메시지 전송
        this.sendMessage(`[당첨숫자] ${winningNumbers.join(' , ')}\n[당첨결과] ${author.nickname}님 ${matchCount}개 적중\n보유금 ${earnedMoney.toLocaleString()}원 지급`);
      } else if (msg_split.length === 3) {
        // !복권 자동 N 형식인 경우 (자동으로 N개의 복권 사용)
        if (msg_split[1].toLowerCase() === '자동' && !isNaN(+msg_split[2])) {
          const count = Math.min(+msg_split[2], user.spoon[2]);
          
          if (count <= 0) {
            this.sendMessage(`@${author.nickname} 님은 사용할 수 있는 복권이 없습니다.`);
            return;
          }
          
          // 복권 자동 결과
          const results = [0, 0, 0, 0]; // 0개 적중, 1개 적중, 2개 적중, 3개 적중 횟수
          let totalMoney = 0;
          
          // 기본 선택 숫자 (0, 1, 2)
          const selectedNumbers = [0, 1, 2];
          
          // money 필드 초기화 (없는 경우)
          if (user.money === undefined) {
            user.money = 0;
          }
          
          // N번 복권 사용
          for (let i = 0; i < count; i++) {
            // 복권 개수 감소
            user.spoon[2]--;
            
            // 랜덤 숫자 3개 생성
            const winningNumbers: number[] = [];
            while (winningNumbers.length < 3) {
              const randomNum = Math.floor(Math.random() * 10);
              if (!winningNumbers.includes(randomNum)) {
                winningNumbers.push(randomNum);
              }
            }
            
            // 당첨 결과 계산
            let matchCount = 0;
            let multiplier = 1;
            
            for (const num of winningNumbers) {
              if (selectedNumbers.includes(num)) {
                multiplier *= 10;
                matchCount++;
              }
            }
            
            // 결과 기록
            results[matchCount]++;
            
            // 보유금 계산 (복권은 기본 1000원으로 설정)
            const baseReward = 1000;
            const earnedMoney = multiplier * baseReward;
            totalMoney += earnedMoney;
          }
          
          // 보유금 증가
          user.money += totalMoney;
          
          // 레벨 계산 (경험치는 그대로 유지하되 레벨 계산은 실행)
          this.calculateUserLevel(user);
          
          // 유저 업데이트
          await userDataManager.updateUser(user);
          
          // 결과 메시지 전송
          this.sendMessage(`[자동결과] ${author.nickname}님 ${count}개 사용\n🥇3개적중:: ${results[3]}회\n🥈2개적중:: ${results[2]}회\n🥉1개적중:: ${results[1]}회\n🏅0개적중:: ${results[0]}회\n💎보유금 ${totalMoney.toLocaleString()}원 지급`);
        } else {
          this.sendMessage('복권 자동 사용법: !복권 자동 개수');
        }
      } else {
        this.sendMessage('복권은 0~9 사이의 숫자 세개를 선택하세요\n(예)!복권 2 5 7\n또는 자동 사용: !복권 자동 개수');
      }
    } catch (err) {
      console.error('복권 번호 처리 중 오류 발생:', err);
    }
  }

  /**
   * 복권 지급
   * @param nickname 대상 닉네임
   * @param count 지급할 복권 개수
   */
  async giveLottery(nickname: string, count: number): Promise<boolean> {
    try {
      if (!nickname || count <= 0) return false;
      
      // 유저 찾기
      const user = userDataManager.getUserByNickname(nickname);
      
      // 유저가 없으면 실패
      if (!user) {
        console.error(`복권 지급 실패: ${nickname} 유저를 찾을 수 없음`);
        return false;
      }
      
      // 복권 객체 초기화 (없는 경우)
      if (!user.spoon) {
        user.spoon = [0, 0, 0];
      }
      
      // 복권 개수 증가
      user.spoon[2] += count;
      
      // 유저 업데이트
      await userDataManager.updateUser(user);
      
      console.log(`${nickname}에게 복권 ${count}개 지급 완료`);
      return true;
    } catch (err) {
      console.error('복권 지급 중 오류 발생:', err);
      return false;
    }
  }

  /**
   * 채팅 카운트 증가
   * @param author 시청자 정보
   */
  private async updateUserChatCount(author: ChatAuthor): Promise<void> {
    try {
      if (!author || !author.tag) return;
      
      // 유저 찾기
      let user = userDataManager.getUserByNickname(author.nickname);
      
      // 유저가 없으면 무시 (명시적으로 !내정보 생성 명령어를 사용해야 함)
      if (!user) return;
      
      // 경험치 및 채팅 카운트 증가
      user.point += 1;
      user.chat_count += 1;
      
      // 레벨 계산
      this.calculateUserLevel(user);
      
      // 유저 업데이트
      await userDataManager.updateUser(user);
    } catch (err) {
      console.error('유저 채팅 카운트 업데이트 중 오류 발생:', err);
    }
  }

  /**
   * 하트 카운트 증가
   * @param author 시청자 정보
   */
  private async updateUserHeartCount(author: ChatAuthor): Promise<void> {
    try {
      if (!author || !author.tag) return;
      
      // 유저 찾기
      const user = userDataManager.getUserByNickname(author.nickname);
      
      // 유저가 없으면 무시 (명시적으로 !내정보 생성 명령어를 사용해야 함)
      if (!user) return;
      
      // 경험치 및 하트 카운트 증가
      user.point += 5; // 하트는 5경험치
      user.heart_count += 1;
      
      // 레벨 계산
      this.calculateUserLevel(user);
      
      // 유저 업데이트
      await userDataManager.updateUser(user);
    } catch (err) {
      console.error('유저 하트 카운트 업데이트 중 오류 발생:', err);
    }
  }

  /**
   * 유저 레벨 계산
   * @param user 유저 데이터
   */
  private calculateUserLevel(user: UserData): void {
    try {
      // 새로운 레벨 계산 로직: 경험치가 (현재 레벨 * 100)을 넘으면 레벨업
      const requiredExp = user.level * 100;
      
      if (user.point >= requiredExp) {
        // 레벨업
        user.level += 1;
        console.log(`${user.nickname} 레벨업! ${user.level-1} -> ${user.level}`);
      }
    } catch (err) {
      console.error('유저 레벨 계산 중 오류 발생:', err);
    }
  }

  /**
   * 경험치 퍼센트 계산
   * @param exp 현재 경험치
   * @param level 현재 레벨
   * @returns 경험치 퍼센트 (0-100)
   */
  private calculateExpPercentage(exp: number, level: number): number {
    try {
      const requiredExp = level * 100;
      const currentLevelExp = exp % requiredExp; // 현재 레벨 내에서의 경험치
      return Math.floor((currentLevelExp / requiredExp) * 100);
    } catch (err) {
      console.error('경험치 퍼센트 계산 중 오류 발생:', err);
      return 0;
    }
  }

  /**
   * 새 유저 생성
   * @param author 시청자 정보
   */
  private createNewUser(author: ChatAuthor): UserData {
    return {
      nickname: author.nickname || '',
      tag: author.tag || '',
      last_attend: Date.now(),
      level: 1,
      point: 0,
      attend_count: 0,
      heart_count: 0,
      chat_count: 0,
      is_double: false,
      spoon: [0, 0, 0],
      money: 0 // 초기 보유금
    };
  }
}

// 싱글톤 인스턴스 생성
const chatCommandHandler = new ChatCommandHandler();
export default chatCommandHandler; 