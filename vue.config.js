const path = require('path');
const MonacoEditorPlugin = require('monaco-editor-webpack-plugin');
const webpack = require('webpack');
const fs = require('fs');

module.exports = {
	pluginOptions: {
		electronBuilder: {
			mainProcessWatch: [
				'src/app/**/*',
			],
			chainWebpackMainProcess: (config) => {
				// supertest의 formidable 패키지에서 발생하는 문제 수정
				config.plugin('gently')
					.use(new webpack.DefinePlugin({ "global.GENTLY": false }))
					.end();
				
				// 프로덕션에서 console.log 제거
				if (process.env.NODE_ENV === 'production') {
					config.plugin('remove-console')
						.use(new webpack.DefinePlugin({
							'console.log': 'function(){}'
						}))
						.end();
				}
				
				return config;
			},
			builderOptions: {
				afterPack: (context) => {
					const target = context.targets[0];
					const filename = target.packager.appInfo.productFilename;
					let ext = '';
					if (process.platform === 'win32') {
						ext = '.exe';
					}
					fs.cpSync(path.join(context.appOutDir, `${filename}${ext}`), path.join(context.appOutDir, `TAMMBundleManager${ext}`), { recursive: true });
				},
				publish: [
					{
						"provider": "s3",
						"bucket": "sopia-v3",
						"region": "ap-northeast-2",
					},
				],
				productName: 'TAMM-V1',
				appId: 'com.tamm.v1',
				icon: "./public/app.png",
				win: {
					icon: "./public/app.ico",
					target: ["nsis"]
				},
				nsis: {
					oneClick: false,
					allowToChangeInstallationDirectory: true,
					perMachine: false
				},
				mac: {
					icon: "public/icon.png"
				},
				linux: {
					icon: "public/icon.png"
				},
				// 번들 크기 최적화: 필요한 파일만 포함
				files: [
					"dist/**/*",
					"!dist/js/*.map",
					"!dist/css/*.map",
					"node_modules/axios/**/*",
					"node_modules/follow-redirects/**/*",
					"node_modules/better-sqlite3/**/*",
					"node_modules/bindings/**/*",
					"node_modules/file-uri-to-path/**/*",
					"node_modules/rimraf/**/*",
					"node_modules/@prisma/client/**/*",
					"package.json",
					"src/app/**/*",
					"public/app.ico",
					"public/app.png",
					"public/icon.png",
					"public/bundle-manager.html",
					"!**/*.ts",
					"!**/*.map",
					"!**/node_modules/**/test/**/*",
					"!**/node_modules/**/tests/**/*",
					"!**/node_modules/**/*.md",
					"!**/node_modules/**/README*",
					"!**/node_modules/**/CHANGELOG*",
					"!**/node_modules/**/LICENSE*",
					"!**/node_modules/**/*.d.ts",
					"!src/**/*.ts",
					"!trash/**/*",
					"!refer/**/*",
					"!examples/**/*",
					"!custom_script/**/*",
					"!.git/**/*",
					"!*.md",
					"!*.log",
					"!**/*.log"
				],
				extraFiles: [
					{
						from: "node_modules/follow-redirects/",
						to: "resources/node_modules/follow-redirects",
					},
					{
						from: "node_modules/axios/",
						to: "resources/node_modules/axios"
					},
					{
						from: "node_modules/better-sqlite3/",
						to: "resources/node_modules/better-sqlite3"
					},
					{
						from: "node_modules/bindings/",
						to: "resources/node_modules/bindings"
					},
					{
						from: "node_modules/file-uri-to-path/",
						to: "resources/node_modules/file-uri-to-path"
					},
					{
						from: "node_modules/rimraf",
						to: "resources/node_modules/rimraf"
					},
					{
						from: "node_modules/@prisma/client/",
						to: "resources/node_modules/@prisma/client"
					},
					{
						from: 'bun-binary',
						to: '.bun',
					},
				],
			},
			mac: {
				target: "dmg",
				arch: [
					"x64",
					"arm64",
					"universal",
				],
			},
		},
	},
	configureWebpack: config => {
		// 프로덕션 환경에서만 적용되는 최적화
		if (process.env.NODE_ENV === 'production') {
			config.plugins.push(
				// 프로덕션에서 console.log 제거
				new webpack.DefinePlugin({
					'process.env.NODE_ENV': '"production"'
				})
			);
			
			// 소스맵 비활성화로 번들 크기 감소
			config.devtool = false;
			
			// 최적화 설정
			config.optimization = {
				...config.optimization,
				minimize: true,
				usedExports: true,
				sideEffects: false,
				splitChunks: {
					chunks: 'all',
					cacheGroups: {
						vendor: {
							test: /[\\/]node_modules[\\/]/,
							name: 'vendors',
							chunks: 'all',
						},
					},
				},
			};
		}
		
		// 기존 resolve 설정 유지
		config.resolve = config.resolve || {};
		config.resolve.alias = {
			...config.resolve.alias,
			"assets": path.join(__dirname, "src/assets"),
			"@": path.join(__dirname, "src"),
		};
		
		// 기존 module 설정 유지
		config.module = config.module || {};
		config.module.exprContextCritical = false;
		
		// 기존 플러그인에 MonacoEditorPlugin 추가
		config.plugins.push(
			new MonacoEditorPlugin({
				languages: ['javascript', 'css', 'html', 'typescript', 'json', 'markdown'],
				features: ['!gotoSymbol'],
			})
		);
	},
	transpileDependencies: [
		"vuetify"
	],
	// 프로덕션에서 런타임 컴파일러 비활성화
	runtimeCompiler: process.env.NODE_ENV !== 'production',
}
