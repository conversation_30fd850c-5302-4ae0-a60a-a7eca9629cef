import { Navigate } from 'react-router-dom'
import { useSessionStore } from '../stores/sessionStore'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
}

export function ProtectedRoute({ children, requireAuth = true }: ProtectedRouteProps) {
  const session = useSessionStore((state) => state.session)

  if (requireAuth && !session) {
    return <Navigate to="/login" replace />
  }

  if (!requireAuth && session) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}
