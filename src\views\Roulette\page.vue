<template>
	<v-container fluid class="pa-0">
		<v-card class="mx-auto" max-width="800" flat>
			<v-card-title class="pa-4">
				<v-row align="center" justify="space-between" no-gutters>
					<v-col cols="4"></v-col>
					<v-col cols="4" class="text-center">
						<span class="headline">룰렛 관리</span>
					</v-col>
					<v-col cols="4" class="text-right">
						<v-btn
							color="deep-purple accent-3"
							dark
							small
							class="rounded-pill px-4 elevation-3 font-weight-bold mr-2"
							style="background: linear-gradient(45deg, #9c27b0, #f50057); text-transform: none;">
							<v-icon left small>mdi-package-variant-closed</v-icon>
							인벤토리
						</v-btn>
						<v-btn 
							color="purple darken-1" 
							dark 
							small
							class="rounded-pill px-4 elevation-3"
							@click="save">
							<v-icon left small>mdi-content-save</v-icon>
							저장
						</v-btn>
					</v-col>
				</v-row>
			</v-card-title>
			
			<!-- S: Present List Dialog -->
			<v-dialog
				v-model="present"
				max-width="600"
				width="80%">
				<v-card class="rounded-xl">
					<v-card-title>
						{{ $t('cmd.sticker.list') }}
					</v-card-title>
					<v-card-text>
						<v-row class="ma-0">
							<v-col
								cols="6" md="4"
								v-for="(sticker, idx) in validStickers"
								:key="sticker.name"
								@click="selectPresent(sticker, idx); present = false;">
								<v-hover>
									<template v-slot:default="{ hover }">
										<v-card
											style="cursor: pointer;"
											class="rounded-pill"
											:elevation="hover ? 12 : 3">
											<v-img
												:src="sticker.image_thumbnail"
												class="white--text align-center"
												:gradient="hover ? 'to bottom, rgba(0,0,0,.7), rgba(0,0,0,.7)' : ''"
												width="100%">
											<v-row v-if="hover" align="center">
												<v-col cols="12" class="pb-0" align="center">
													<h3>{{ sticker.title }}</h3>
												</v-col>
												<v-col cols="12" class="pt-0" align="center">
													<v-chip color="transparent">
														<v-img width="20px" :src="gift_coin"/>
														<span class="ml-2 white--text">{{ sticker.price }}</span>
													</v-chip>
												</v-col>
											</v-row>
											</v-img>
										</v-card>
									</template>
								</v-hover>
							</v-col>
						</v-row>
					</v-card-text>
				</v-card>
			</v-dialog>
			<!-- E: Present List Dialog -->
			
			<v-card-text>
				<!-- 기본 설정 카드 -->
				<v-card outlined class="rounded-xl mb-4">
					<v-card-title class="subtitle-1 d-flex">
						<span class="text-h6 purple--text text--darken-2">기본 설정</span>
						<v-spacer></v-spacer>
						<v-switch
							v-model="enable"
							color="purple"
							inset
							class="mt-0"
							hide-details
							label="사용">
						</v-switch>
					</v-card-title>
					
					<v-card-text>
						<!-- S: options.type -->
						<v-row class="ma-0 mt-2" align="center">
							<v-col cols="6" align="left" class="pa-0">
								작동 방식
							</v-col>
							<v-col cols="6" align="right" class="pa-0">
								<div style="position: relative;">
									<v-select
										v-model="options.type"
										color="purple darken-1"
										:items="type"
										dense
										solo
										single-line
										flat
										rounded
										hide-details
										class="custom-select"
										append-icon="mdi-chevron-down">
									</v-select>
								</div>
							</v-col>
						</v-row>
						<!-- E: options.type -->
						
						<!-- S: options.min -->
						<v-row v-if="options.type === 'min'" class="ma-0 mt-3" align="center">
							<v-col cols="6" align="left" class="pa-0">
								최소 스푼 개수
							</v-col>
							<v-col cols="6" align="right" class="pa-0">
								<v-text-field
									v-model="options.min"
									color="purple darken-1"
									type="number"
									suffix="개"
									dense
									outlined
									hide-details
									class="rounded-pill-field">
								</v-text-field>
							</v-col>
						</v-row>
						<!-- E: options.min -->
						
						<!-- S: options.select -->
						<v-row v-if="options.type === 'select'" class="ma-0 mt-3" align="center">
							<v-col cols="6" align="left" class="pa-0">
								스푼 선택
							</v-col>
							<v-col cols="6" align="right" class="pa-0">
								<v-btn
									block
									outlined
									color="purple"
									:elevation="2"
									@click="present = true"
									class="rounded-pill">
									<div class="d-flex align-center justify-center w-100">
										<img
											v-if="options.present?.image_thumbnail"
											:src="options.present?.image_thumbnail"
											width="30px"
											class="mr-2"
											:alt="options.present?.title"/>
										<span>{{ substr(options.present?.title) || '스푼을 선택해 주세요.' }}</span>
									</div>
								</v-btn>
							</v-col>
						</v-row>
						<!-- E: options.select -->
						
						<!-- S: options.auto -->
						<v-row class="ma-0 mt-3" align="center">
							<v-col cols="6" align="left" class="pa-0">
								자동 룰렛 시작
								<v-tooltip right>
									<template v-slot:activator="{ on, attrs }">
										<v-icon small v-bind="attrs" v-on="on" color="grey">mdi-help-circle</v-icon>
									</template>
									<div>
										<span class="tooltip-title">자동 룰렛 시작 기능</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">사용 시:</span>
											<span class="tooltip-desc">스푼을 보낸 사용자가 자동으로 룰렛을 시작합니다.</span>
										</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">미사용 시:</span>
											<span class="tooltip-desc">스푼을 보낸 사용자가 <code>!룰렛</code> 명령어를 직접 입력해야 합니다.</span>
										</span>
									</div>
								</v-tooltip>
							</v-col>
							<v-col cols="6" align="right" class="pa-0">
								<v-switch
									v-model="options.auto"
									color="purple"
									inset
									hide-details
									:label="options.auto ? '사용 중' : '사용 안 함'">
								</v-switch>
							</v-col>
						</v-row>
						<!-- E: options.auto -->
						
						<!-- S: options.simple -->
						<v-row class="ma-0 mt-3" align="center">
							<v-col cols="6" align="left" class="pa-0">
								룰렛 안내 제거
								<v-tooltip right>
									<template v-slot:activator="{ on, attrs }">
										<v-icon small v-bind="attrs" v-on="on" color="grey">mdi-help-circle</v-icon>
									</template>
									<div>
										<span class="tooltip-title">룰렛 안내 메시지 제거</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">사용 시:</span>
											<span class="tooltip-desc">룰렛 결과만 즉시 출력합니다.</span>
										</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">미사용 시:</span>
											<span class="tooltip-desc">룰렛이 돌아가는 효과 안내를 함께 출력합니다.</span>
										</span>
									</div>
								</v-tooltip>
							</v-col>
							<v-col cols="6" align="right" class="pa-0">
								<v-switch
									v-model="options.simple"
									color="purple"
									inset
									hide-details
									:label="options.simple ? '사용 중' : '사용 안 함'">
								</v-switch>
							</v-col>
						</v-row>
						<!-- E: options.simple -->
						
						<!-- S: Rule -->
						<v-row class="ma-0 mt-3" align="center">
							<v-col cols="6" align="left" class="pa-0">
								룰렛 규칙 설정
								<v-tooltip right max-width="320">
									<template v-slot:activator="{ on, attrs }">
										<v-icon small v-bind="attrs" v-on="on" color="grey">mdi-help-circle</v-icon>
									</template>
									<div>
										<span class="tooltip-title">룰렛 횟수 계산 방식</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">기본:</span>
											<span class="tooltip-desc">설정 가격 이상의 스푼에 대해 항상 1회만 룰렛이 동작합니다.</span>
										</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">콤보:</span>
											<span class="tooltip-desc">설정 가격 이상의 스푼에서 n회 이상의 연속 스푼이 발생하면, 콤보 수만큼 룰렛이 동작합니다.</span>
										</span>
										<span class="tooltip-item">
											<span class="tooltip-highlight">배분:</span>
											<span class="tooltip-desc">설정 가격 이상의 스푼에서 (총 스푼 개수 ÷ 설정 스푼)의 몫만큼 룰렛이 동작합니다.</span>
										</span>
									</div>
								</v-tooltip>
							</v-col>
							<v-col cols="6" align="right" class="pa-0">
								<div style="position: relative;">
									<v-select
										v-model="options.rule"
										color="purple darken-1"
										:items="rules"
										dense
										solo
										single-line
										flat
										rounded
										hide-details
										class="custom-select"
										append-icon="mdi-chevron-down">
									</v-select>
								</div>
							</v-col>
						</v-row>
						<!-- E: Rule -->
						
						<!-- S: 설정 파일 관리 버튼 -->
						<!-- 
						<v-row align="center" justify="center" class="ma-0 mt-4">
							<v-col cols="auto" class="pa-1">
								<v-btn
									color="green darken-1"
									dark
									small
									class="rounded-pill"
									@click="exportConfigFile">
									<v-icon x-small left>mdi-file-export</v-icon>
									아이템설정 내보내기
								</v-btn>
							</v-col>
							<v-col cols="auto" class="pa-1">
								<v-btn
									color="purple"
									dark
									small
									class="rounded-pill"
									@click="importConfigFile">
									<v-icon x-small left>mdi-file-import</v-icon>
									아이템설정 가져오기
								</v-btn>
							</v-col>
						</v-row>
						-->
					</v-card-text>
				</v-card>
				
				<!-- 이력 카드: 접었다 펼 수 있게 변경 -->
				<v-expansion-panels class="mb-4">
					<v-expansion-panel>
						<v-expansion-panel-header class="purple--text text--darken-2 rounded-xl">
							<span class="text-h6">룰렛 동작 이력</span>
						</v-expansion-panel-header>
						<v-expansion-panel-content class="rounded-xl">
							<pre
								class="grey lighten-4 pa-3 rounded-xl my-2"
								style="overflow:auto; min-height: 150px; max-height: 1200px; font-size: 13px;">{{ history }}</pre>
						</v-expansion-panel-content>
					</v-expansion-panel>
				</v-expansion-panels>
				
				<!-- 아이템 설정 카드 -->
				<v-card outlined class="rounded-xl mb-4">
					<v-card-title class="subtitle-1 d-flex align-center">
						<span class="text-h6 purple--text text--darken-2">아이템 설정</span>
						
						<!-- 설정 아이콘과 호버 메뉴 -->
						<v-menu
							open-on-hover
							offset-y
							transition="slide-y-transition"
							bottom
							right
							open-delay="0"
							close-delay="300"
							content-class="settings-menu-rounded"
							:nudge-width="20"
							min-width="200"
							rounded
							eager>
							<template v-slot:activator="{ on, attrs }">
								<v-btn
									icon
									small
									color="purple lighten-1"
									class="ml-2 hover-area"
									v-bind="attrs"
									v-on="on">
									<v-icon>mdi-cog</v-icon>
								</v-btn>
							</template>
							<v-list dense rounded class="py-2">
								<v-list-item @click="exportConfigFile" class="menu-item" rounded>
									<v-list-item-icon>
										<v-icon color="green darken-1">mdi-playlist-edit</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>아이템설정 내보내기</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-list-item @click="importConfigFile" class="menu-item" rounded>
									<v-list-item-icon>
										<v-icon color="purple">mdi-playlist-plus</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>아이템설정 가져오기</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</v-list>
						</v-menu>
						
						<!-- 검색 기능 추가 -->
						<v-menu
							v-model="searchMenuOpen"
							:close-on-content-click="false"
							offset-x
							transition="slide-x-transition"
							right
							:nudge-width="220"
							:nudge-top="20"
							min-width="220"
							content-class="search-menu search-menu-custom-rounded"
							:open-on-hover="false"
							rounded
							eager>
							<template v-slot:activator="{ on: menuOn, attrs: menuAttrs }">
								<v-tooltip bottom>
									<template v-slot:activator="{ on: tooltipOn, attrs: tooltipAttrs }">
										<v-btn
											icon
											small
											color="purple lighten-1"
											class="ml-2 circle-icon-btn"
											v-bind="{ ...tooltipAttrs, ...menuAttrs }"
											v-on="{ ...tooltipOn, ...menuOn }"
											@click="toggleSearchMenu">
											<v-icon>mdi-magnify</v-icon>
										</v-btn>
									</template>
									<span>항목 검색</span>
								</v-tooltip>
							</template>
							<v-card flat class="pa-2 search-card" transparent elevation="0">
								<div class="d-flex align-center">
									<v-text-field
										v-model="searchQuery"
										label="항목 검색"
										placeholder="검색어를 입력하세요"
										prepend-inner-icon="mdi-magnify"
										clearable
										dense
										hide-details
										rounded
										outlined
										color="purple darken-1"
										class="mb-0"
										@input="filterItems"
										ref="searchInput"
										@click:clear="clearSearch">
									</v-text-field>
								</div>
								<div class="mt-2 text-caption text-center grey--text" v-if="searchActive && filteredList.length > 0">
									{{ filteredList.length }}개 항목 검색됨
								</div>
							</v-card>
						</v-menu>
						
						<v-spacer></v-spacer>
						
						<!-- 아이템 추가 버튼 -->
						<v-btn
							icon
							small
							color="purple lighten-1"
							class="mr-2 circle-icon-btn"
							@click="addNewItem">
							<v-icon>mdi-plus</v-icon>
						</v-btn>
						
						<v-chip color="purple" text-color="white" small class="rounded-pill">
							당첨 확률: {{ totalItemPercentage }}%
						</v-chip>
					</v-card-title>
					
					<!-- 컬럼 헤더 추가 -->
					<div class="px-4 pt-2 pb-1 d-flex">
						<div style="width: 58.33%; position: relative; text-align: center;">
							<v-chip small color="purple lighten-4" class="rounded-pill label-chip" @click="switchToNameSort">
								<span class="purple--text text--darken-2 font-weight-medium">항목</span>
								<v-btn v-if="sortBy === 'name'" icon x-small class="pa-0 ml-1">
									<v-icon x-small color="purple darken-1">{{ nameSort === 1 ? 'mdi-arrow-up' : 'mdi-arrow-down' }}</v-icon>
								</v-btn>
							</v-chip>
						</div>
						<div style="width: 33.33%; position: relative; text-align: center;">
							<v-chip small color="purple lighten-4" class="rounded-pill label-chip" @click="switchToPercentageSort">
								<span class="purple--text text--darken-2 font-weight-medium">확률</span>
								<v-btn v-if="sortBy === 'percentage'" icon x-small class="pa-0 ml-1">
									<v-icon x-small color="purple darken-1">{{ percentageSort === 1 ? 'mdi-arrow-up' : 'mdi-arrow-down' }}</v-icon>
								</v-btn>
							</v-chip>
						</div>
						<div style="width: 8.33%"></div>
					</div>
					
					<v-card-text class="pt-0">
						<!-- 항목 아이템: 외곽선 없이 입력 필드만 캡슐 형태로 -->
						<div
							v-for="(item, idx) of displayedList" 
							:key="idx + '-' + item.value"
							class="mb-1 pa-1">
							<v-row align="center" class="ma-0">
								<v-col cols="7" class="py-0">
									<v-text-field
										:value="item.value"
										@input="keyInput(this, $event, getOriginalIndex(idx), 'value')"
										placeholder="항목을 입력하세요"
										hide-details
										outlined
										rounded
										color="purple darken-1"
										class="rounded-pill-field item-field"
										style="font-weight: 900;">
									</v-text-field>
								</v-col>
								<v-col cols="4" class="py-0">
									<v-text-field
										:value="item.percentage"
										@input="keyInput(this, $event, getOriginalIndex(idx), 'percentage')"
										type="number"
										placeholder="확률"
										suffix="%"
										hide-details
										outlined
										rounded
										color="purple darken-1" 
										class="rounded-pill-field item-field"
										style="font-weight: 900;">
									</v-text-field>
								</v-col>
								<v-col cols="1" class="pa-0 d-flex justify-center">
									<v-btn
										icon
										color="red darken-1"
										@click="deleteItem(getOriginalIndex(idx))">
										<v-icon>mdi-delete</v-icon>
									</v-btn>
								</v-col>
							</v-row>
						</div>
						
						<v-alert
							v-if="displayedList.length === 0 && list.length === 0"
							type="info"
							outlined
							dense
							class="mt-3 rounded-pill">
							아이템이 없습니다. '+' 버튼을 클릭하여 룰렛 항목을 추가하세요.
						</v-alert>
						
						<v-alert
							v-else-if="displayedList.length === 0 && searchActive"
							type="info"
							outlined
							dense
							class="mt-3 rounded-pill">
							검색 결과가 없습니다. 다른 검색어를 입력해보세요.
						</v-alert>
					</v-card-text>
				</v-card>
			</v-card-text>
		</v-card>
		
		<!-- 알림 스낵바 -->
		<v-snackbar
			v-model="snackbar.show"
			:color="snackbar.color"
			:timeout="3000"
			bottom
			right
			class="rounded-pill">
			{{ snackbar.text }}
			<template v-slot:action="{ attrs }">
				<v-btn
					text
					v-bind="attrs"
					@click="snackbar.show = false">
					닫기
				</v-btn>
			</template>
		</v-snackbar>
	</v-container>
</template>
<script>
const fs = window.require('fs');
const path = window.require('path');
const CfgLite = window.appCfg.__proto__.constructor;
const cfgPath = path.join(__dirname, 'config.cfg');
let cfgRemoved = false;
let cfg;
try {
	cfg = new CfgLite(cfgPath);
} catch {
	if ( fs.existsSync(cfgPath) ) {
		fs.rmSync(cfgPath);
		cfgRemoved = true;
	}
	cfg = new CfgLite(cfgPath);
}
const { ipcRenderer } = window.require('electron');
const ctx = window.bctx.get('roulette');

const copy = (obj) => JSON.parse(JSON.stringify(obj));

export default {
	data: () => ({
		enable: cfg.get('enable') ?? false,
		options: cfg.get('options') || {   
			'min': '1',
			'useEffect': false,
			'effectVolume': 50,
			'type': 'min',
			'auto': true,
			'rule': 'default',
			'simple': false,
        },
		rules: [
			{ text: '기본', value: 'default' },
			{ text: '콤보', value: 'combo' },
			{ text: '배분', value: 'division' },
		],
		ruleMenu: false,
		simpleMenu: false,
		list: [],
		listCopy: [],
		leftCol: 7,
		rightCol: 5,
		history: '',
		type: [
			{
				text: '지정 스푼',
				value: 'select',
			},
			{
				text: '최소 스푼',
				value: 'min',
			},
		],
		validStickers: [],
		present: false,
		gift_coin: '',
		menu: false,
		// 정렬 상태 추가
		nameSort: 1, // 1: 오름차순, -1: 내림차순
		percentageSort: -1, // 1: 오름차순, -1: 내림차순 (초기값 높은순)
		sortBy: 'percentage', // 'name' 또는 'percentage'
		// 검색 관련 상태 추가
		searchQuery: '',
		searchMenuOpen: false,
		searchActive: false,
		filteredList: [],
		filteredIndices: [],
		// 스낵바 상태
		snackbar: {
			show: false,
			text: '',
			color: 'success'
		}
	}),
	async mounted() {
		if ( cfgRemoved ) {
			this.showSnackbar('설정파일을 불러올 수 없습니다. 새로운 설정파일을 초기화합니다.', 'error');
		}
		await this.init();
		this.listRefresh();
		
		// 초기 정렬 적용 (확률 높은순)
		this.applySort();
	},
	methods: {
		async init() {
			this.list = cfg.get('list') || [];
			this.listCopy = copy(this.list);
			const p = path.join(__dirname, 'gift_coin.png');
			this.gift_coin = 'data:image/png;base64,' + fs.readFileSync(p, 'base64');

			ctx.ipc.register('history:set', (str) => {
				this.history = str;
			});

			if ( !this.$sopia.sticker.stickers ) {
				await this.asleep(2000);
			}
			this.$sopia.sticker.stickers.categories.forEach((category) => {
				if ( !category.is_used ) {
					return;
				}

				category.stickers.forEach((sticker) => {
					if ( sticker.is_used ) {
						this.validStickers.push(sticker);
					}
				});
			});
		},
		asleep(ms) {
			return new Promise((resolve) => {
				setTimeout(resolve, ms);
			});
		},
		addNewItem() {
			this.list.unshift({
				value: '',
				percentage: 100,
			});
			this.listCopy = copy(this.list);
			// 검색이 활성화되어 있다면 필터링 다시 적용
			if (this.searchActive) {
				this.filterItems();
			}
		},
		keyInput(node, value, idx, key) {
			this.listCopy[idx][key] = value;
		},
		listRefresh() {
			const tmp = this.list;
			tmp.forEach((l, idx) => {
				if ( this.listCopy[idx] ) {
					l.value = this.listCopy[idx].value;
					l.percentage = +this.listCopy[idx].percentage;
				}
			});
			this.listCopy = copy(tmp);
			this.list = tmp;
		},
		deleteItem(idx) {
			this.listCopy.splice(idx, 1);
			this.list = copy(this.listCopy);
			// 검색이 활성화되어 있다면 필터링 다시 적용
			if (this.searchActive) {
				this.filterItems();
			}
		},
		selectPresent(sticker, idx) {
			this.options.present = sticker;
		},
		substr(str) {
			if ( str ) {
				if ( str.length > 15 ) {
					return str.substr(0, 15) + '...';
				}
			}
			return str;
		},
		save() {
			this.listRefresh();
			
			let sum = 0;
			this.list.forEach((item) => sum += item.percentage);
			if ( sum > 100 ) {
				this.showSnackbar(`아이템들의 총 확률은 100을 넘길 수 없습니다. (현재: ${sum}%)`, 'error');
				return;
			}

			if ( this.options.type === 'select' && !this.options.present ) {
				this.showSnackbar('지정 스푼 옵션을 사용할 경우, 스푼을 선택해 주세요.', 'error');
				return;
			}
			
			cfg.set('options', this.options);
			cfg.set('enable', this.enable);
			cfg.set('list', this.list);
			cfg.save();
			
			this.showSnackbar('설정이 저장되었습니다.', 'success');
		},
		copy(src, dst) {
			fs.writeFileSync(dst, fs.readFileSync(src));
		},
		async exportConfigFile() {
			const res = await ipcRenderer.invoke('open-dialog', {
				title: '저장할 경로',
				defaultPath: __dirname,
				properties: [
					'openDirectory',
				],
			});
			if ( !res.canceled ) {
				const [ folder ] = res.filePaths;
				if ( !fs.existsSync(folder) ) {
					this.showSnackbar('폴더 경로가 존재하지 않습니다.', 'error');
					return;
				}
				this.copy(path.join(__dirname, 'config.cfg'), path.join(folder, 'config.cfg'));
				this.showSnackbar('설정 파일이 성공적으로 내보내졌습니다.', 'success');
			}
		},
		async importConfigFile() {
			const res = await ipcRenderer.invoke('open-dialog', {
				title: '설정 파일 선택',
				defaultPath: __dirname,
				properties: [
					'openFile',
				],
			});
			if ( !res.canceled ) {
				const [ file ] = res.filePaths;
				if ( !fs.existsSync(file) ) {
					this.showSnackbar('파일이 존재하지 않습니다.', 'error');
					return;
				}
				try {
					// check valid config file
					new CfgLite(file);
				} catch {
					console.log('Cannot load config file', file);
					this.showSnackbar('잘못된 설정파일입니다.', 'error');
					return;
				}
				this.copy(file, path.join(__dirname, 'config.cfg'));
				cfg = new CfgLite(path.join(__dirname, 'config.cfg'));
				this.list = cfg.get('list') || [];
				this.listCopy = copy(this.list);
				this.options = cfg.get('options') || {   
					'min': '1',
					'useEffect': false,
					'effectVolume': 50,
					'type': 'min',
					'auto': true,
				};
				this.enable = cfg.get('enable') ?? false;
				this.save();
				this.showSnackbar('설정 파일을 성공적으로 가져왔습니다.', 'success');
			}
		},
		// 스낵바 표시 메소드 추가
		showSnackbar(text, color = 'success') {
			this.snackbar.text = text;
			this.snackbar.color = color;
			this.snackbar.show = true;
		},
		// 항목 정렬로 전환
		switchToNameSort() {
			if (this.sortBy === 'name') {
				// 이미 항목 정렬 상태면 정렬 방향만 토글
				this.nameSort = -this.nameSort;
			} else {
				// 확률 정렬 상태면 항목 정렬로 전환
				this.sortBy = 'name';
			}
			this.applySort();
		},
		
		// 확률 정렬로 전환
		switchToPercentageSort() {
			if (this.sortBy === 'percentage') {
				// 이미 확률 정렬 상태면 정렬 방향만 토글
				this.percentageSort = -this.percentageSort;
			} else {
				// 항목 정렬 상태면 확률 정렬로 전환
				this.sortBy = 'percentage';
			}
			this.applySort();
		},
		
		// 현재 정렬 설정에 따라 정렬 적용
		applySort() {
			if (this.sortBy === 'name') {
				if (this.nameSort === 1) {
					// 항목 오름차순
					this.list.sort((a, b) => a.value.localeCompare(b.value));
				} else {
					// 항목 내림차순
					this.list.sort((a, b) => b.value.localeCompare(a.value));
				}
			} else {
				if (this.percentageSort === 1) {
					// 확률 오름차순 (낮은순)
					this.list.sort((a, b) => +a.percentage - +b.percentage);
				} else {
					// 확률 내림차순 (높은순)
					this.list.sort((a, b) => +b.percentage - +a.percentage);
				}
			}
			this.listCopy = copy(this.list);
		},
		
		// 검색어에 따라 항목 필터링
		filterItems() {
			if (!this.searchQuery || this.searchQuery.trim() === '') {
				this.searchActive = false;
				this.filteredList = [];
				this.filteredIndices = [];
				return;
			}
			
			this.searchActive = true;
			const query = this.searchQuery.toLowerCase().trim();
			
			// 원본 인덱스를 추적하면서 필터링
			this.filteredIndices = [];
			this.filteredList = this.list.filter((item, index) => {
				const match = item.value.toLowerCase().includes(query);
				if (match) {
					this.filteredIndices.push(index);
				}
				return match;
			});
		},
		
		// 원본 배열에서의 인덱스 가져오기
		getOriginalIndex(filteredIndex) {
			return this.searchActive 
				? this.filteredIndices[filteredIndex] 
				: filteredIndex;
		},
		
		// 검색 초기화
		clearSearch() {
			this.searchQuery = '';
			this.searchActive = false;
			this.filteredList = [];
			this.filteredIndices = [];
		},
		toggleSearchMenu() {
			this.searchMenuOpen = !this.searchMenuOpen;
			if (this.searchMenuOpen) {
				this.$nextTick(() => {
					if (this.$refs.searchInput) {
						this.$refs.searchInput.focus();
					}
				});
			}
		},
	},
	computed: {
		totalItemPercentage() {
			return (this.list || []).reduce((p=0, c) => p + +c.percentage, 0);
		},
		// 표시할 항목 목록 (검색 결과 또는 전체 목록)
		displayedList() {
			return this.searchActive ? this.filteredList : this.list;
		}
	},
	watch: {
		// 검색 메뉴가 열릴 때 입력 필드에 포커스
		searchMenuOpen(val) {
			if (val) {
				// $nextTick을 사용하여 DOM 업데이트 후 포커스
				this.$nextTick(() => {
					if (this.$refs.searchInput) {
						this.$refs.searchInput.focus();
					}
				});
			}
		}
	}
}
</script>

<style>
/* 글로벌 스타일 - 검색 메뉴만 위한 스타일 */
.v-application .search-menu-custom-rounded.v-menu__content {
  border-radius: 50px !important;
  overflow: hidden !important;
  background-color: white !important;
}

/* 글로벌 스타일 - 설정 메뉴만 위한 스타일 */
.v-application .settings-menu-rounded.v-menu__content {
  border-radius: 24px !important;
  overflow: hidden !important;
  background-color: white !important;
}
</style>

<style scoped>
.rounded-xl {
  border-radius: 16px !important;
}

.rounded-pill {
  border-radius: 50px !important;
}

.rounded-pill-field .v-input__slot {
  border-radius: 50px !important;
  overflow: hidden;
}

.v-text-field .v-input__slot {
  min-height: 40px !important;
}

/* 캡슐형 선택 필드 스타일 */
.pill-select-container .v-input__slot {
  border-radius: 50px !important;
}

.pill-select-container .v-select__slot input {
  margin-left: 8px;
}

.pill-select-container .v-input__append-inner {
  margin-top: 8px !important;
}

.pill-select-container .v-icon {
  color: #9c27b0 !important;
}

/* 확장 패널 스타일 */
.v-expansion-panels {
  border-radius: 16px !important;
}

.v-expansion-panel {
  background: white !important;
  border-radius: 16px !important;
}

.v-expansion-panel-header {
  padding: 16px;
  border-radius: 16px !important;
  box-shadow: 0 0 0 1px rgba(156, 39, 176, 0.2);
}

.v-expansion-panel-content__wrap {
  padding: 12px;
}

/* 캡슐형 선택 필드 스타일 */
.custom-select .v-input__slot {
  border-radius: 50px !important;
  overflow: hidden;
  border: 1px solid #e0e0e0 !important;
}

.custom-select .v-select__selection {
  margin-left: 15px;
}

.custom-select .v-input__append-inner {
  margin-right: 10px !important;
}

.custom-select .v-icon {
  color: #9c27b0 !important;
}

/* 드롭다운 메뉴 개선 스타일 */
.v-menu__content {
  border-radius: 20px !important;
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.15) !important;
  overflow: hidden;
  border: 1px solid rgba(156, 39, 176, 0.1) !important;
  margin-top: 5px !important;
}

.v-list {
  padding: 8px !important;
  background-color: white !important;
}

.v-list-item {
  border-radius: 15px !important;
  margin-bottom: 2px !important;
  transition: all 0.2s ease;
}

.v-list-item:hover {
  background-color: rgba(156, 39, 176, 0.05) !important;
}

.v-list-item--active {
  background-color: rgba(156, 39, 176, 0.1) !important;
}

.v-list-item__title {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #424242 !important;
}

.v-list-item--active .v-list-item__title {
  color: #9c27b0 !important;
  font-weight: 600 !important;
}

/* 커스텀 선택 필드에 대한 추가 스타일 */
.custom-select .v-menu__content {
  margin-top: 5px !important;
}

.custom-select .v-input__append-inner .v-input__icon--append .v-icon {
  transition: transform 0.3s ease;
}

.custom-select.v-input--is-focused .v-input__append-inner .v-input__icon--append .v-icon {
  transform: rotate(180deg);
}

/* 툴팁 스타일 개선 */
.v-tooltip__content {
  background-color: rgba(33, 33, 33, 0.95) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
  max-width: 320px !important;
  line-height: 1.5 !important;
}

/* 툴팁 내부 강조 텍스트 */
.tooltip-title {
  font-size: 15px !important;
  font-weight: 700 !important;
  color: #e1bee7 !important;
  margin-bottom: 10px !important;
  display: block !important;
  border-bottom: 1px solid rgba(225, 190, 231, 0.3) !important;
  padding-bottom: 5px !important;
}

/* 툴팁 내부 강조 요소 */
.tooltip-highlight {
  color: #ce93d8 !important;
  font-weight: 600 !important;
  display: inline-block !important;
  margin: 2px 0 !important;
}

/* 툴팁 항목 여백 */
.tooltip-item {
  margin-bottom: 8px !important;
  display: block !important;
}

/* 툴팁 마지막 항목은 여백 없음 */
.tooltip-item:last-child {
  margin-bottom: 0 !important;
}

/* 툴팁 설명 텍스트 */
.tooltip-desc {
  display: block !important;
  padding-left: 8px !important;
  border-left: 2px solid rgba(156, 39, 176, 0.5) !important;
  margin-top: 2px !important;
  color: #e0e0e0 !important;
}

/* 작은 버튼 스타일 */
.small-button {
  font-size: 12px !important;
  padding: 0 12px !important;
  height: 32px !important;
  min-height: 0 !important;
  text-transform: none !important;
}

.small-button .v-icon {
  font-size: 16px !important;
  margin-right: 4px !important;
}

/* 호버 영역 확장 */
.hover-area {
  position: relative;
}

.hover-area::after {
  content: '';
  position: absolute;
  top: -10px;
  right: -10px;
  bottom: -10px;
  left: -10px;
  z-index: -1;
}

/* 동그란 아이콘 버튼 스타일 */
.circle-icon-btn {
  background-color: rgba(156, 39, 176, 0.08) !important;
  transition: all 0.2s ease;
}

.circle-icon-btn:hover {
  background-color: rgba(156, 39, 176, 0.15) !important;
}

/* 항목 필드 스타일 개선 */
.item-field.v-text-field--outlined >>> fieldset {
  border: 2px solid rgba(156, 39, 176, 0.3) !important;
  border-radius: 50px !important;
  transition: all 0.2s ease;
}

.item-field.v-text-field--outlined:hover >>> fieldset {
  border-color: rgba(156, 39, 176, 0.5) !important;
}

.item-field.v-text-field--outlined.v-input--is-focused >>> fieldset {
  border-color: #9c27b0 !important;
  border-width: 2px !important;
  box-shadow: 0 0 0 1px rgba(156, 39, 176, 0.1) !important;
}

.item-field.v-text-field--outlined .v-text-field__slot {
  min-height: 44px !important;
}

.item-field.v-text-field--outlined .v-label {
  transform: translateY(-24px) scale(0.75) !important;
  height: auto !important;
  background-color: white !important;
  padding: 0 4px !important;
}

.item-field.v-text-field--outlined input {
  padding-top: 10px !important;
  font-weight: 900 !important;
}

/* 라벨 칩 스타일 */
.label-chip {
  height: 31px !important;
  min-width: 78px !important;
  justify-content: center !important;
  padding: 0 16px !important;
}

.label-chip .v-icon {
  font-size: 18px !important;
}

.label-chip span {
  font-size: 15px !important;
}

.label-chip .v-btn {
  margin: 0 !important;
  height: 22px !important;
  width: 22px !important;
  min-width: 0 !important;
}

.label-center {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, 0) !important;
  z-index: 1 !important;
}

/* 항목 필드 스타일 개선 */
.item-field.v-text-field--solo {
  border: 1px solid rgba(156, 39, 176, 0.2) !important;
  border-radius: 50px !important;
  box-shadow: none !important;
  transition: all 0.2s ease;
  overflow: hidden !important;
  background-color: white !important;
}

.item-field.v-text-field--solo:hover {
  border-color: rgba(156, 39, 176, 0.4) !important;
  box-shadow: 0 1px 5px rgba(156, 39, 176, 0.1) !important;
}

.item-field.v-text-field--solo.v-input--is-focused {
  border-color: #9c27b0 !important;
  border-width: 2px !important;
  box-shadow: 0 0 0 1px rgba(156, 39, 176, 0.2), 0 2px 8px rgba(156, 39, 176, 0.3) !important;
}

.item-field.v-text-field--solo .v-input__slot {
  box-shadow: none !important;
  min-height: 38px !important;
  padding: 0 8px 0 16px !important;
}

.item-field.v-text-field--solo .v-text-field__slot input {
  padding: 0 !important;
  margin: 0 !important;
  height: 38px !important;
  line-height: 38px !important;
  font-size: 17px !important;
  font-weight: 900 !important;
  margin-top: 3px !important; /* 텍스트 위치 아래로 조정 */
}

.item-field.v-text-field--solo .v-text-field__slot input::placeholder {
  color: rgba(0, 0, 0, 0.3) !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  padding-top: 3px !important; /* 플레이스홀더도 같이 아래로 */
}

.item-field.v-text-field--solo .v-input__append-inner {
  margin-top: 8px !important;
}

.item-field.v-text-field--solo .v-text-field__suffix {
  font-size: 17px !important;
  font-weight: 900 !important;
  padding-right: 4px !important;
  color: rgba(0, 0, 0, 0.8) !important;
}

/* 입력 필드 끝 부분 */
.rounded-pill-field.v-text-field--solo .v-input__control > .v-input__slot {
  border-radius: 50px !important;
  overflow: hidden !important;
}

/* 정렬 아이콘 애니메이션 */
.sort-icon {
  transition: transform 0.3s ease;
}

.sort-icon-desc {
  transform: rotate(180deg);
}

/* 검색 메뉴 스타일 */
::v-deep .search-menu {
  border-radius: 50px !important;
  box-shadow: 0 4px 20px rgba(156, 39, 176, 0.2) !important;
  overflow: hidden !important;
  margin-right: 10px !important; /* 검색 버튼과의 간격 조정 */
  background-color: transparent !important;
  border: none !important;
}

::v-deep .search-menu .v-card {
  border-radius: 50px !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

::v-deep .search-card {
  width: 220px !important;
  border: none !important;
  border-radius: 50px !important;
  overflow: hidden !important;
}

::v-deep .search-card .v-text-field {
  border-radius: 50px !important;
  overflow: hidden !important;
}

::v-deep .v-menu__content.search-menu {
  border-radius: 50px !important;
  overflow: hidden !important;
}

/* 추가: 글꼴 두께를 위한 스타일 */
::v-deep input {
  font-weight: 900 !important;
}

/* 설정 메뉴 스타일 */
.settings-menu {
  border-radius: 24px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(156, 39, 176, 0.2) !important;
}

.settings-menu .v-list {
  border-radius: 20px !important;
  padding: 12px 8px !important;
}

.settings-menu .v-list-item {
  border-radius: 18px !important;
  margin-bottom: 4px !important;
  transition: all 0.2s ease;
}

/* 설정 메뉴 둥근 모서리 스타일 */
::v-deep .settings-menu-rounded {
  border-radius: 24px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(156, 39, 176, 0.2) !important;
}

::v-deep .settings-menu-rounded .v-list {
  border-radius: 20px !important;
  padding: 12px 8px !important;
  background-color: white !important;
}

::v-deep .settings-menu-rounded .v-card {
  border-radius: 24px !important;
  overflow: hidden !important;
}

::v-deep .settings-menu-rounded .v-list-item {
  border-radius: 18px !important;
  margin-bottom: 4px !important;
  transition: all 0.2s ease;
}

/* 메뉴 컨텐츠 전체 스타일 강화 */
::v-deep .v-menu__content.settings-menu-rounded {
  border-radius: 24px !important;
  overflow: hidden !important;
}
</style>