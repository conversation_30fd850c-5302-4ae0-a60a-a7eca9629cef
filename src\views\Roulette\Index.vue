<template>
  <div class="roulette-tab-container roulette-namespace">
    <component v-if="dynamicComponent" :is="dynamicComponent"></component>
    <v-alert v-else-if="error" type="error" outlined>
      {{ error }}
    </v-alert>
    <v-skeleton-loader v-else type="article" class="mx-auto"></v-skeleton-loader>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import * as VuetifyComponents from 'vuetify/lib/components';

const fs = window.require('fs');
const vm = window.require('vm');
const path = window.require('path');
const os = window.require('os');

@Component
export default class RouletteIndex extends Mixins(GlobalMixins) {
  dynamicComponent: any = null;
  error: string | null = null;
  
  mounted() {
    // 타이틀바 요소 숨기기
    this.$evt.$emit('hide-titlebar-elements', true);
    // 외부 Vue 파일 로드
    this.loadExternalVueFile();
  }
  
  beforeDestroy() {
    // 타이틀바 요소 복원
    this.$evt.$emit('hide-titlebar-elements', false);
    // 추가한 스타일 시트 제거
    const styles = document.querySelectorAll('style[data-roulette-tab-style]');
    styles.forEach(styleEl => {
      if (styleEl) {
        document.head.removeChild(styleEl);
      }
    });
  }
  
  getInnerString(txt: string, tag: string): string | null {
    const regx = new RegExp(`<${tag}>((?:.|\r|\n)*)?</${tag}>`);
    const m = txt.match(regx);
    if (m) {
      return m[1];
    }
    return null;
  }
  
  async loadExternalVueFile() {
    try {
      // 사용자 홈 디렉토리 가져오기
      const homedir = os.homedir();
      const filePath = path.join(homedir, 'AppData', 'Roaming', 'tamm-v1', 'bundles', 'roulette', 'page.vue');
      
      console.log('로드할 파일 경로:', filePath);
      
      // 파일 존재 확인
      if (!fs.existsSync(filePath)) {
        this.error = `파일을 찾을 수 없습니다: ${filePath}`;
        console.error(this.error);
        return;
      }
      
      // Vue 파일 읽기
      const vueFileContent = fs.readFileSync(filePath, 'utf8');
      
      // template 및 script 부분 추출
      const template = this.getInnerString(vueFileContent, 'template');
      let script = this.getInnerString(vueFileContent, 'script');
      const style = this.getInnerString(vueFileContent, 'style');
      
      if (!template || !script) {
        this.error = 'Vue 파일의 template 또는 script 태그를 찾을 수 없습니다.';
        console.error(this.error);
        return;
      }
      
      // script 내용 수정: export default { ... } -> module = { ... }
      script = script.replace(/export\s+default\s+{/, 'module = {');
      
      // VM 컨텍스트 생성 및 스크립트 실행
      const vmScript = new vm.Script(script);
      
      // 포괄적인 컨텍스트 구성
      const context: any = {};
      context.module = {};
      
      // 필수 전역 객체 및 함수들 복사
      context.window = window;
      context.require = window.require;
      context.console = console;
      context.setTimeout = setTimeout;
      context.clearTimeout = clearTimeout;
      context.setInterval = setInterval;
      context.clearInterval = clearInterval;
      context.requestAnimationFrame = requestAnimationFrame;
      context.cancelAnimationFrame = cancelAnimationFrame;
      context.__dirname = path.dirname(filePath);
      context.document = document;
      context.localStorage = localStorage;
      context.Date = Date;
      context.Math = Math;
      context.JSON = JSON;
      context.RegExp = RegExp;
      context.Array = Array;
      context.Object = Object;
      context.String = String;
      context.Number = Number;
      context.Boolean = Boolean;
      context.Error = Error;
      context.Promise = Promise;
      
      // Vue 관련 컨텍스트
      context.Vue = window.Vue;
      
      // TAMM 앱 관련 값들 복사
      if (window.$sopia) context.$sopia = window.$sopia;
      if (window.appCfg) context.appCfg = window.appCfg;
      // @ts-ignore - window.bctx는 타입 정의가 없지만 실제로 존재
      if (window.bctx) context.bctx = window.bctx;
      
      // 스크립트 실행
      vmScript.runInNewContext(context);
      
      // 컴포넌트 생성
      const component: any = {
        template,
        ...context.module,
        mixins: [Mixins(GlobalMixins)],
      };
      
      // 필요한 메서드 및 컴포넌트 추가
      if (!component.methods) {
        component.methods = {};
      }
      
      // 리로드 메서드 추가
      component.methods.reload = () => {
        console.log('컴포넌트 리로드');
        this.loadExternalVueFile();
      };
      
      // Vuetify 컴포넌트 추가
      if (!component.components) {
        component.components = {};
      }
      
      // 모든 Vuetify 컴포넌트를 등록
      for (const [name, comp] of Object.entries(VuetifyComponents)) {
        component.components[name] = comp;
      }
      
      // 스타일 추가 (있는 경우)
      if (style && style.trim()) {
        // 이전 스타일 제거
        const oldStyles = document.querySelectorAll('style[data-roulette-tab-style]');
        oldStyles.forEach(el => el && document.head.removeChild(el));
        
        // 모든 스타일에 네임스페이스 추가
        const processedStyle = this.processStyle(style);
        
        const styleEl = document.createElement('style');
        styleEl.textContent = processedStyle;
        styleEl.setAttribute('data-roulette-tab-style', '');
        document.head.appendChild(styleEl);
        
        console.log('네임스페이스가 적용된 스타일이 추가되었습니다.');
      }
      
      // 컴포넌트 설정 완료
      this.dynamicComponent = component;
      
      console.log('Vue 컴포넌트가 성공적으로 로드되었습니다.', component);
    } catch (error: any) {
      console.error('Vue 컴포넌트 로드 오류:', error);
      this.error = `컴포넌트 로드 중 오류가 발생했습니다: ${error.message}`;
    }
  }
  
  // 스타일에 네임스페이스 추가하는 메서드
  processStyle(styleContent: string): string {
    // CSS 규칙을 분석하기 위한 정규식
    const cssRuleRegex = /([^{]+)({[^}]*})/g;
    let match;
    let result = '';
    
    // 현재 위치를 저장하여 처리되지 않은 부분(주석 등)도 보존
    let lastIndex = 0;
    
    while ((match = cssRuleRegex.exec(styleContent)) !== null) {
      // 매칭 전 비매칭 부분을 결과에 추가 (주석, 공백 등 보존)
      if (match.index > lastIndex) {
        result += styleContent.substring(lastIndex, match.index);
      }
      
      const selectors = match[1];
      const ruleBody = match[2];
      
      // 미디어 쿼리 및 키프레임 처리
      if (selectors.trim().startsWith('@media') || selectors.trim().startsWith('@keyframes')) {
        result += selectors + ruleBody;
      } 
      // v-application으로 시작하는 전역 스타일 - 네임스페이스 추가
      else if (selectors.includes('.v-application')) {
        // .v-application을 .roulette-namespace .v-application으로 변경
        const namespacedSelectors = selectors.replace(
          /\.v-application/g, 
          '.roulette-namespace .v-application'
        );
        result += namespacedSelectors + ruleBody;
      } 
      // 일반 선택자들에 네임스페이스 추가
      else {
        const selectorArray = selectors.split(',').map(s => {
          s = s.trim();
          // html, body 같은 루트 요소는 그대로 유지
          if (s === 'html' || s === 'body') {
            return s;
          }
          // 이미 .roulette-namespace로 시작하면 그대로 유지
          if (s.startsWith('.roulette-namespace')) {
            return s;
          }
          // 네임스페이스 추가
          return '.roulette-namespace ' + s;
        });
        
        result += selectorArray.join(', ') + ruleBody;
      }
      
      lastIndex = cssRuleRegex.lastIndex;
    }
    
    // 마지막 비매칭 부분 추가
    if (lastIndex < styleContent.length) {
      result += styleContent.substring(lastIndex);
    }
    
    return result;
  }
}
</script>

<style scoped>
.roulette-tab-container {
  padding: 0;
  height: calc(100vh - 48px);
  overflow-y: auto;
}
</style> 