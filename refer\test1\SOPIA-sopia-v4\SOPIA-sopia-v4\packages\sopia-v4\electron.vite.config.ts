import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react-swc'
import tsConfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    build: {
      minify: false
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    build: {
      minify: false
    }
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src')
      }
    },
    plugins: [react(), tsConfigPaths()],
    css: {
      postcss: './postcss.config.js'
    },
    build: {
      minify: false
    }
  }
})
