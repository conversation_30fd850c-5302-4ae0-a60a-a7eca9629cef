# Node Module

Node.JS가 지원하는 모듈을 사용할 수 있습니다.

지원하는 모듈 정보는 [이곳](https://nodejs.org/docs/latest-v16.x/api/index.html)을 확인하세요.


<br><br>

파일을 읽고 쓰는 예제
```js
const fs = window.require('fs');
const path = window.require('path');

const TARGET_PATH = path.join(__dirname, 'file.txt');

exports.live_message = (evt /* LiveMessageSocket */, sock /* LiveSocket */)  => {
	const message = evt.update_component.message.value;
	if ( message === '.쓰기' ) {
		const nickname = evt.data.author.nickname;
		fs.writeFileSync(TARGET_PATH, nickname, 'utf8');
		sock.message(`파일을 썼습니다. 작성자: ${nickname}`);
	} else if ( message === '.읽기' ) {
		if ( fs.existsSync(TARGET_PATH) ) {
			const nickname = fs.readFileSync(TARGET_PATH, 'utf8');
			sock.message(`파일을 읽었습니다. 내용: ${nickname}`);
		} else {
			sock.message('읽을 파일이 없습니다.');
		}
	}
}
```