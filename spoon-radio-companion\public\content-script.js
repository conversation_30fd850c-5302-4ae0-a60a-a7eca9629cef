console.log('🔧 Tamm 스푼 연동 확장프로그램 로드됨');

// 안전한 객체 복사 함수 (순환 참조 및 함수 제거)
function safeClone(obj, seen = new WeakSet()) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (seen.has(obj)) {
        return {}; // 순환 참조 방지
    }
    
    seen.add(obj);
    
    if (Array.isArray(obj)) {
        return obj.map(item => safeClone(item, seen));
    }
    
    const cloned = {};
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            // 함수, undefined, symbol 제외
            if (typeof value !== 'function' && typeof value !== 'undefined' && typeof value !== 'symbol') {
                try {
                    cloned[key] = safeClone(value, seen);
                } catch (error) {
                    // 직렬화할 수 없는 값은 무시
                    console.warn('직렬화 불가능한 속성 무시:', key);
                }
            }
        }
    }
    
    seen.delete(obj);
    return cloned;
}

// Chrome extension 메시지 전송을 위한 안전한 직렬화
function safeSendResponse(data) {
    try {
        // JSON 직렬화 테스트
        const testSerialization = JSON.stringify(data);
        return data;
    } catch (error) {
        console.warn('직렬화 오류, 안전한 복사본 생성:', error);
        return safeClone(data);
    }
}

// 확장프로그램 감지를 위한 메시지 리스너
window.addEventListener('message', (event) => {
    if (event.data.type === 'CHECK_EXTENSION') {
        console.log('📡 확장프로그램 감지 요청 수신');
        window.postMessage({ type: 'EXTENSION_RESPONSE' }, '*');
    }
});

// 확장프로그램에서 메시지 수신
chrome.runtime.onMessage.addListener(
    function(request, sender, sendResponse) {
        console.log('📬 메시지 수신:', request);
        
        if (request.method === 'getUserInfo') {
            console.log('🔍 토큰 추출 시작');
            
            try {
                // 국가 코드 확인
                const countryCode = window.location.hostname.includes('spooncast.net') ? 'kr' : 'jp';
                console.log('🌍 국가 코드:', countryCode);
                
                // localStorage 키 설정
                const userInfoKey = `SPOONCAST_${countryCode.toUpperCase()}_userInfo`;
                const refreshTokenKey = `SPOONCAST_${countryCode.toUpperCase()}_refreshToken`;
                
                console.log('🔑 확인할 키들:', userInfoKey, refreshTokenKey);
                
                // 사용자 정보 가져오기
                const userInfoStr = localStorage.getItem(userInfoKey);
                
                if (!userInfoStr) {
                    throw new Error('스푼캐스트에 로그인되어 있지 않습니다.');
                }
                
                const refreshToken = localStorage.getItem(refreshTokenKey) || '';
                
                // 사용자 정보 파싱
                const userInfo = JSON.parse(userInfoStr);
                userInfo.refresh_token = refreshToken;
                
                // 원본 데이터를 안전하게 복사 (tamm-v1 호환성)
                const originalData = safeClone(userInfo);
                userInfo.originalData = originalData;
                
                console.log('✅ 토큰 추출 성공:', {
                    id: userInfo.id,
                    nickname: userInfo.nickname,
                    hasToken: !!userInfo.token,
                    tokenLength: userInfo.token ? userInfo.token.length : 0,
                    hasOriginalData: !!userInfo.originalData
                });
                
                // 안전한 응답 데이터 생성
                const responseData = safeSendResponse({
                    success: true,
                    data: userInfo
                });
                
                sendResponse(responseData);
                
            } catch (error) {
                console.error('❌ 토큰 추출 실패:', error);
                
                // 에러 응답도 안전하게 직렬화
                const errorResponse = safeSendResponse({
                    success: false,
                    error: error.message
                });
                
                sendResponse(errorResponse);
            }
        }
        
        return true; // 비동기 응답을 위해 true 반환
    }
);

// 페이지 로드 시 토큰 확인
window.addEventListener('load', () => {
    console.log('📄 스푼캐스트 페이지 로드 완료');
    
    // localStorage 변경 감지
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        originalSetItem.apply(this, arguments);
        if (key.includes('SPOONCAST') && key.includes('userInfo')) {
            console.log('💾 스푼캐스트 로그인 감지:', key);
        }
    };
}); 