import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { auth, db } from '../firebaseConfig';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword, updateProfile, signOut } from 'firebase/auth';
import { doc, setDoc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { useAuthContext } from '../contexts/AuthContext';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 32px;
  width: 400px;
  max-width: 90vw;
  position: relative;
`;

const ModalHeader = styled.h2`
  text-align: center;
  margin-bottom: 24px;
  color: #333;
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 12px;
  background: none;
  border: none;
  font-size: 16px;
  color: ${props => props.active ? '#8b5cf6' : '#666'};
  border-bottom: ${props => props.active ? '2px solid #8b5cf6' : 'none'};
  cursor: pointer;
  transition: color 0.3s;
`;

const FormContainer = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  
  &:focus {
    border-color: #8b5cf6;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
`;

const CheckboxLabel = styled.label`
  font-size: 14px;
  color: #666;
  cursor: pointer;
`;

const Button = styled.button`
  background-color: #8b5cf6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #7c3aed;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  
  &:hover {
    color: #333;
  }
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 14px;
  margin-top: 16px;
  text-align: center;
  white-space: pre-line;
`;

const SuccessMessage = styled.div`
  color: #16a34a;
  font-size: 14px;
  margin-top: 16px;
  text-align: center;
  white-space: pre-line;
`;

const InfoText = styled.div`
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  text-align: center;
`;

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAuthSuccess: (user: any) => void;
}

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, onAuthSuccess }) => {
  const { setUser } = useAuthContext();
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  // 간단한 암호화/복호화 함수
  const encryptData = (data: string): string => {
    return btoa(encodeURIComponent(data));
  };

  const decryptData = (encryptedData: string): string => {
    try {
      return decodeURIComponent(atob(encryptedData));
    } catch {
      return '';
    }
  };

  // 이메일 중복 검사
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const userQuery = query(collection(db, 'users'), where('email', '==', email));
      const querySnapshot = await getDocs(userQuery);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('이메일 중복 검사 오류:', error);
      return false;
    }
  };

  // 이름 중복 검사
  const checkNameExists = async (displayName: string): Promise<boolean> => {
    try {
      const userQuery = query(collection(db, 'users'), where('name', '==', displayName));
      const querySnapshot = await getDocs(userQuery);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('이름 중복 검사 오류:', error);
      return false;
    }
  };

  // 저장된 로그인 정보 불러오기
  useEffect(() => {
    if (activeTab === 'login') {
      const savedEmail = localStorage.getItem('tamm_saved_email');
      const savedPassword = localStorage.getItem('tamm_saved_password');
      const savedRemember = localStorage.getItem('tamm_remember_me');

      if (savedEmail && savedPassword && savedRemember === 'true') {
        setEmail(decryptData(savedEmail));
        setPassword(decryptData(savedPassword));
        setRememberMe(true);
      }
    }
  }, [activeTab]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    console.log('로그인 시도:', { email, activeTab });
    
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      if (activeTab === 'login') {
        console.log('로그인 탭에서 처리 중...');
        
        // Firebase 인증 시도
        console.log('Firebase 인증 시도...');
        try {
          const result = await signInWithEmailAndPassword(auth, email, password);
          console.log('✅ Firebase 로그인 성공:', result.user);
          
          // 관리자 계정 확인
          const isAdmin = email === '<EMAIL>';
          
          if (isAdmin) {
            // 관리자는 바로 로그인 허용
            const userWithAdmin = {
              ...result.user,
              isAdmin: true
            };
            
            // 아이디/비밀번호 저장 처리
            if (rememberMe) {
              localStorage.setItem('tamm_saved_email', encryptData(email));
              localStorage.setItem('tamm_saved_password', encryptData(password));
              localStorage.setItem('tamm_remember_me', 'true');
            } else {
              localStorage.removeItem('tamm_saved_email');
              localStorage.removeItem('tamm_saved_password');
              localStorage.removeItem('tamm_remember_me');
            }

            // 관리자 로컬 세션도 저장 (백업용)
            localStorage.setItem('tamm_admin_session', JSON.stringify({
              uid: result.user.uid,
              email: result.user.email,
              displayName: result.user.displayName || '관리자',
              isAdmin: true
            }));

            // AuthContext 상태 즉시 업데이트
            setUser(userWithAdmin);
            onAuthSuccess(userWithAdmin);
            onClose();
            return;
          } else {
            // 일반 사용자는 승인 상태 확인
            console.log('일반 사용자 승인 상태 확인 중...');
            
            try {
              const userDocRef = doc(db, 'users', result.user.uid);
              const userDocSnap = await getDoc(userDocRef);
              
              if (userDocSnap.exists()) {
                const userData = userDocSnap.data();
                console.log('사용자 데이터:', userData);
                
                if (userData.isApproved === true && userData.status === 'active') {
                  // 승인된 사용자만 로그인 허용
                  const approvedUser = {
                    ...result.user,
                    isAdmin: false
                  };
                  
                  // 아이디/비밀번호 저장 처리
                  if (rememberMe) {
                    localStorage.setItem('tamm_saved_email', encryptData(email));
                    localStorage.setItem('tamm_saved_password', encryptData(password));
                    localStorage.setItem('tamm_remember_me', 'true');
                  } else {
                    localStorage.removeItem('tamm_saved_email');
                    localStorage.removeItem('tamm_saved_password');
                    localStorage.removeItem('tamm_remember_me');
                  }

                  // AuthContext 상태 즉시 업데이트
                  setUser(approvedUser);
                  onAuthSuccess(approvedUser);
                  onClose();
                  return;
                } else {
                  // 승인되지 않은 사용자
                  await signOut(auth); // Firebase 로그아웃
                  setError('아직 관리자 승인이 완료되지 않았습니다.\n승인 완료 후 다시 로그인해주세요.');
                  return;
                }
              } else {
                // Firestore에 사용자 정보가 없는 경우
                await signOut(auth); // Firebase 로그아웃
                setError('사용자 정보를 찾을 수 없습니다.\n관리자에게 문의해주세요.');
                return;
              }
            } catch (firestoreError) {
              console.error('Firestore 조회 오류:', firestoreError);
              await signOut(auth); // Firebase 로그아웃
              setError('사용자 정보 확인 중 오류가 발생했습니다.\n다시 시도해주세요.');
              return;
            }
          }
        } catch (firebaseError: any) {
          console.error('❌ Firebase 인증 오류:', firebaseError);
          
          // 관리자 계정인 경우에만 로컬 인증 시도 (백업)
          if (email === '<EMAIL>' && password === 'tamm1234') {
            console.log('🔄 관리자 계정 로컬 인증 시도 (백업)');
            
            // 관리자 계정 로컬 인증 성공
            const adminUser = {
              uid: 'admin-tamm',
              email: '<EMAIL>',
              displayName: '관리자',
              isAdmin: true
            };
            
            // 아이디/비밀번호 저장 처리
            if (rememberMe) {
              localStorage.setItem('tamm_saved_email', encryptData(email));
              localStorage.setItem('tamm_saved_password', encryptData(password));
              localStorage.setItem('tamm_remember_me', 'true');
            } else {
              localStorage.removeItem('tamm_saved_email');
              localStorage.removeItem('tamm_saved_password');
              localStorage.removeItem('tamm_remember_me');
            }

            // 로컬 스토리지에 관리자 세션 저장
            localStorage.setItem('tamm_admin_session', JSON.stringify(adminUser));
            
            console.log('✅ 관리자 로컬 인증 성공 (백업):', adminUser);
            
            // AuthContext 상태 즉시 업데이트
            setUser(adminUser);
            onAuthSuccess(adminUser);
            onClose();
            return;
          }
          
          // Firebase 오류 메시지를 한국어로 변환
          let errorMessage = '로그인에 실패했습니다.';
          
          if (firebaseError.code === 'auth/user-not-found') {
            errorMessage = '등록되지 않은 이메일입니다.';
          } else if (firebaseError.code === 'auth/wrong-password') {
            errorMessage = '비밀번호가 올바르지 않습니다.';
          } else if (firebaseError.code === 'auth/invalid-email') {
            errorMessage = '올바르지 않은 이메일 형식입니다.';
          } else if (firebaseError.code === 'auth/too-many-requests') {
            errorMessage = '너무 많은 로그인 시도가 있었습니다. 잠시 후 다시 시도해주세요.';
          } else if (firebaseError.code === 'auth/network-request-failed') {
            errorMessage = '네트워크 연결을 확인해주세요.';
          } else if (firebaseError.code === 'auth/invalid-credential') {
            errorMessage = '이메일 또는 비밀번호가 올바르지 않습니다.';
          }
          
          setError(errorMessage);
        }
      } else {
        console.log('회원가입 처리 중...');
        
        try {
          // 이메일 중복 검사
          const emailExists = await checkEmailExists(email);
          if (emailExists) {
            setError('이미 사용 중인 이메일입니다.');
            return;
          }

          // 이름 중복 검사
          const nameExists = await checkNameExists(displayName);
          if (nameExists) {
            setError('이미 사용 중인 이름입니다.');
            return;
          }

          // Firebase Authentication에 사용자 생성
          const userCredential = await createUserWithEmailAndPassword(auth, email, password);
          const user = userCredential.user;
          
          // 사용자 프로필 업데이트
          await updateProfile(user, {
            displayName: displayName
          });
          
          // Firestore에 사용자 정보 저장 (signOut 전에 수행)
          await setDoc(doc(db, 'users', user.uid), {
            uid: user.uid,
            email: email,
            name: displayName,  // displayName 대신 name 필드 사용
            displayName: displayName,  // 호환성을 위해 displayName도 유지
            isApproved: false,
            createdAt: new Date().toISOString(),
            status: 'pending',
            role: 'user'
          });
          
          console.log('Firestore 사용자 정보 저장 성공');
          
          // 즉시 로그아웃하여 자동 로그인 방지
          await signOut(auth);
          
          console.log('Firebase 회원가입 성공:', user);
          
          setSuccess('회원가입이 성공적으로 완료되었습니다.\n관리자 승인 후 이용하실 수 있습니다.');
          
          // 폼 초기화
          setEmail('');
          setPassword('');
          setDisplayName('');
          
        } catch (firebaseError: any) {
          console.error('Firebase 회원가입 오류:', firebaseError);
          
          // Firebase 실패 시 로컬 저장소에 백업
          console.log('로컬 저장소에 백업 저장...');
          const newUser = {
            id: Date.now().toString(),
            email: email,
            name: displayName,  // displayName 대신 name 필드 사용
            displayName: displayName,  // 호환성을 위해 displayName도 유지
            isApproved: false,
            createdAt: new Date().toISOString(),
            status: 'pending',
            role: 'user'
          };
          
          const existingUsers = JSON.parse(localStorage.getItem('pendingUsers') || '[]');
          const emailExists = existingUsers.some((user: any) => user.email === email);
          const nameExists = existingUsers.some((user: any) => user.name === displayName);
          
          if (emailExists) {
            setError('이미 사용 중인 이메일입니다.');
          } else if (nameExists) {
            setError('이미 사용 중인 이름입니다.');
          } else {
            existingUsers.push(newUser);
            localStorage.setItem('pendingUsers', JSON.stringify(existingUsers));
            setSuccess('회원가입이 성공적으로 완료되었습니다.\n관리자 승인 후 이용하실 수 있습니다.');
            setEmail('');
            setPassword('');
            setDisplayName('');
          }
        }
      }
    } catch (error: any) {
      console.error('로그인 처리 오류:', error);
      setError('로그인 처리 중 오류가 발생했습니다. 다시 시도해주세요.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setDisplayName('');
    setError('');
    setSuccess('');
    setRememberMe(false);
  };

  const handleTabChange = (tab: 'login' | 'register') => {
    setActiveTab(tab);
    resetForm();
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleModalClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // 모달 바깥 클릭 시 닫히지 않도록 수정
  const handleOverlayClick = (e: React.MouseEvent) => {
    // 바깥 클릭 시 모달이 닫히지 않도록 제거
    // if (e.target === e.currentTarget) {
    //   handleClose();
    // }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={handleOverlayClick}>
      <ModalContent onClick={handleModalClick}>
        <CloseButton onClick={handleClose}>×</CloseButton>
        <ModalHeader>
          {activeTab === 'login' ? '로그인' : '회원가입 신청'}
        </ModalHeader>
        
        <TabContainer>
          <Tab 
            active={activeTab === 'login'}
            onClick={() => handleTabChange('login')}
          >
            로그인
          </Tab>
          <Tab 
            active={activeTab === 'register'}
            onClick={() => handleTabChange('register')}
          >
            회원가입
          </Tab>
        </TabContainer>

        {activeTab === 'register' && (
          <InfoText>
            회원가입은 관리자 승인이 필요합니다.<br />
            승인 완료 후 이메일로 알림을 받으실 수 있습니다.
          </InfoText>
        )}
        
        <FormContainer onSubmit={handleSubmit}>
          {activeTab === 'register' && (
            <Input
              type="text"
              placeholder="이름"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              required
            />
          )}
          <Input
            type="email"
            placeholder="이메일"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
          <Input
            type="password"
            placeholder="비밀번호"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
          {activeTab === 'login' && (
            <CheckboxContainer>
              <Checkbox
                type="checkbox"
                id="rememberMe"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <CheckboxLabel htmlFor="rememberMe">
                아이디/비밀번호 저장
              </CheckboxLabel>
            </CheckboxContainer>
          )}
          <Button type="submit" disabled={loading}>
            {loading ? '처리 중...' : (activeTab === 'login' ? '로그인' : '가입 신청')}
          </Button>
        </FormContainer>
        
        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
      </ModalContent>
    </ModalOverlay>
  );
};

export default AuthModal; 