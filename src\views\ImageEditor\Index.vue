<template>
  <div class="image-editor-container">
    <v-container fluid class="content-container">
      <v-row>
        <v-col cols="12">
          <h1 class="text-center purple--text mb-5">{{ $t('page.ImageEditor') }}</h1>
        </v-col>
      </v-row>
      
      <v-row>
        <v-col cols="12" md="3">
          <!-- 사이드 패널: 스티커 목록 -->
          <v-card class="mb-5 rounded-xl">
            <v-card-title class="d-flex align-center">
              <span>스티커 선택</span>
              <v-spacer></v-spacer>
              <v-btn 
                small 
                color="primary" 
                @click="openStickerGallery" 
                class="rounded-pill"
              >
                <v-icon left small>mdi-magnify</v-icon>
                크게보기
              </v-btn>
            </v-card-title>
            <v-card-text class="stickers-container">
              <v-list v-if="validStickers.length > 0" class="sticker-list rounded-lg">
                <v-tooltip bottom v-for="(sticker, idx) in validStickers" :key="sticker.name">
                  <template v-slot:activator="{ on, attrs }">
                    <v-list-item
                      @click="selectSticker(sticker, idx)"
                      :class="selectedStickerIndex === idx ? 'selected-sticker' : ''"
                      class="mb-2 sticker-item"
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item-avatar>
                        <v-img :src="sticker.image_thumbnail" contain></v-img>
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-subtitle class="sticker-price">{{ sticker.price }} 스푼</v-list-item-subtitle>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                  <span>{{ sticker.title }}</span>
                </v-tooltip>
              </v-list>
              <v-alert v-else type="info" text class="rounded-lg">
                스티커를 불러오는 중입니다...
              </v-alert>
            </v-card-text>
          </v-card>
          
          <v-card class="rounded-xl">
            <v-card-title class="d-flex align-center">
              <span>{{ editMode ? '스티커 편집' : '텍스트 정보' }}</span>
              <v-spacer></v-spacer>
              <v-btn 
                v-if="editMode"
                small 
                color="error" 
                @click="cancelEdit" 
                class="rounded-pill mr-2"
              >
                <v-icon left small>mdi-close</v-icon>
                편집 취소
              </v-btn>
            </v-card-title>
            <v-card-text>
              <v-text-field
                v-model="textInfo.nickname"
                label="닉네임"
                outlined
                dense
                class="rounded-lg"
              ></v-text-field>
              <v-text-field
                v-model="textInfo.spoonCount"
                label="갯수"
                type="number"
                outlined
                dense
                class="rounded-lg"
              ></v-text-field>
              <v-radio-group v-model="textInfo.type" row>
                <v-radio label="스푼" value="spoon"></v-radio>
                <v-radio label="좋아요" value="like"></v-radio>
              </v-radio-group>
            </v-card-text>
          </v-card>
        </v-col>
        
        <v-col cols="12" md="9">
          <!-- 메인 편집 영역 -->
          <v-card class="rounded-xl">
            <v-card-text class="editor-canvas-container">
              <div v-if="!imageLoaded" class="text-center pa-10 image-placeholder" @click="openImagePicker">
                <v-icon size="100" color="grey lighten-1">mdi-image-plus</v-icon>
                <p class="mt-4 grey--text">클릭하여 이미지 선택하기</p>
                <input
                  type="file"
                  ref="imageInput"
                  accept="image/*"
                  class="d-none"
                  @change="onImageSelected"
                >
              </div>
              
              <div v-else ref="editorCanvas" class="editor-canvas">
                <img ref="loadedImage" :src="imageUrl" class="loaded-image" />
                
                <!-- 이미 적용된 스티커들 -->
                <div
                  v-for="(sticker, index) in appliedStickers"
                  :key="'applied-' + index"
                  class="sticker-overlay applied-sticker"
                  :class="{ 
                    'editing-sticker': editMode && editingStickerIndex === index,
                    'resizing-sticker': resizing && resizingIndex === index 
                  }"
                  :style="{
                    transform: `translate(${sticker.position.x}px, ${sticker.position.y}px) scale(${editMode && editingStickerIndex === index ? stickerPosition.scale : sticker.position.scale})`,
                    transformOrigin: 'center'
                  }"
                  @mousedown="startDragAppliedSticker($event, index)"
                  @dblclick="editAppliedSticker(index)"
                >
                  <div class="sticker-container" 
                    :style="{ 
                      backgroundColor: editMode && editingStickerIndex === index ? getBackgroundWithOpacity(stickerBackgroundColor, backgroundOpacity) : (sticker.backgroundColor || 'white'),
                      border: editMode && editingStickerIndex === index 
                        ? (useBorder ? `2px solid ${borderColor}` : 'none') 
                        : (sticker.useBorder ? `2px solid ${sticker.borderColor}` : 'none')
                    }"
                  >
                    <div class="sticker-body">
                      <div class="sticker-image-container">
                        <v-img 
                          :src="getEditingImageSrc(index, sticker)"
                          contain 
                          width="62" 
                          height="62"
                          class="sticker-image"
                        ></v-img>
                      </div>
                      <span class="sticker-title ml-2">{{ editMode && editingStickerIndex === index ? textInfo.nickname : sticker.nickname }}</span>
                      <span class="sticker-count ml-2">
                        {{ editMode && editingStickerIndex === index ? textInfo.spoonCount : sticker.spoonCount }} 
                        {{ editMode && editingStickerIndex === index ? (textInfo.type === 'spoon' ? '스푼' : '개') : (sticker.type === 'spoon' ? '스푼' : '개') }}
                      </span>
                    </div>
                  </div>
                  
                  <!-- 삭제 버튼 (편집 중이 아닐 때만 표시) -->
                  <v-btn
                    x-small
                    color="error"
                    fab
                    class="delete-sticker-btn"
                    @click="deleteSticker(index)"
                    v-if="!editMode || editingStickerIndex !== index"
                  >
                    <v-icon small>mdi-close</v-icon>
                  </v-btn>
                  
                  <!-- 편집 취소 버튼 (편집 중일 때 표시) -->
                  <v-btn
                    v-if="editMode && editingStickerIndex === index"
                    x-small
                    color="error"
                    fab
                    class="cancel-edit-btn"
                    @click="cancelEdit"
                  >
                    <v-icon small>mdi-close</v-icon>
                  </v-btn>
                  
                  <!-- 편집 중인 스티커에 적용 버튼 추가 -->
                  <v-btn
                    v-if="editMode && editingStickerIndex === index"
                    x-small
                    color="success"
                    fab
                    class="apply-sticker-btn"
                    @click="applySticker"
                  >
                    <v-icon small>mdi-check</v-icon>
                  </v-btn>
                  
                  <!-- 크기 조절 버튼 -->
                  <v-btn
                    x-small
                    color="primary"
                    fab
                    class="resize-sticker-btn"
                    @mousedown="startResizing($event, index)"
                  >
                    <v-icon small>mdi-resize</v-icon>
                  </v-btn>
                </div>
                
                <!-- 현재 편집 중인 스티커 -->
                <div
                  v-if="selectedSticker && !editMode && creatingSticker"
                  class="sticker-overlay current-sticker"
                  :style="{
                    transform: `translate(${stickerPosition.x}px, ${stickerPosition.y}px) scale(${stickerPosition.scale})`,
                    transformOrigin: 'center'
                  }"
                  @mousedown="startDragSticker"
                >
                  <!-- 심플한 스티커 디자인 (한 줄) -->
                  <div class="sticker-container" 
                    :style="{ 
                      backgroundColor: getBackgroundWithOpacity(stickerBackgroundColor, backgroundOpacity),
                      border: useBorder ? `2px solid ${borderColor}` : 'none'
                    }"
                  >
                    <div class="sticker-body">
                      <div class="sticker-image-container">
                        <v-img 
                          :src="selectedSticker.image_thumbnail" 
                          contain 
                          width="62" 
                          height="62"
                          class="sticker-image"
                        ></v-img>
                      </div>
                      <span class="sticker-title ml-2">{{ textInfo.nickname }}</span>
                      <span class="sticker-count ml-2">{{ textInfo.spoonCount }} {{ textInfo.type === 'spoon' ? '스푼' : '개' }}</span>
                    </div>
                  </div>
                  
                  <!-- 생성 취소 버튼 -->
                  <v-btn
                    x-small
                    color="error"
                    fab
                    class="cancel-create-btn"
                    @click="toggleCreatingSticker"
                  >
                    <v-icon small>mdi-close</v-icon>
                  </v-btn>
                  
                  <!-- 적용 버튼 -->
                  <v-btn
                    x-small
                    color="success"
                    fab
                    class="apply-sticker-btn"
                    @click="applySticker"
                  >
                    <v-icon small>mdi-check</v-icon>
                  </v-btn>
                  
                  <!-- 크기 조절 버튼 -->
                  <v-btn
                    x-small
                    color="primary"
                    fab
                    class="resize-sticker-btn"
                    @mousedown="startResizingCurrent($event)"
                  >
                    <v-icon small>mdi-resize</v-icon>
                  </v-btn>
                </div>
              </div>
            </v-card-text>
            
            <v-card-actions>
              <v-btn 
                v-if="!editMode && selectedSticker && imageLoaded"
                color="indigo"
                class="rounded-pill mr-3"
                @click="toggleCreatingSticker"
              >
                <v-icon left>{{ creatingSticker ? 'mdi-close' : 'mdi-plus' }}</v-icon>
                {{ creatingSticker ? '추가 취소' : '스티커 추가' }}
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn 
                color="primary"
                outlined 
                @click="imageLoaded ? cancelEdit() : openImagePicker()" 
                class="rounded-pill mr-3"
              >
                <v-icon left>{{ imageLoaded ? 'mdi-close' : 'mdi-image-plus' }}</v-icon>
                {{ imageLoaded ? '취소' : '이미지 선택' }}
              </v-btn>
              <v-btn 
                color="primary" 
                :disabled="!imageLoaded" 
                @click="saveImage"
                class="rounded-pill"
              >
                <v-icon left>mdi-content-save</v-icon>
                저장하기
              </v-btn>
            </v-card-actions>
          </v-card>
          
          <v-card class="mt-5 rounded-xl" v-if="imageLoaded">
            <v-card-title>스티커 설정</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <v-slider
                    v-model="stickerPosition.scale"
                    min="0.5"
                    max="3"
                    step="0.01"
                    label="크기"
                    thumb-label="always"
                  ></v-slider>
                </v-col>
                <v-col cols="12" md="6">
                  <p class="mb-2">배경 색상</p>
                  <v-color-picker
                    v-model="stickerBackgroundColor"
                    hide-canvas
                    hide-inputs
                    show-swatches
                    swatches-max-height="100px"
                    class="rounded-lg"
                  ></v-color-picker>
                </v-col>
              </v-row>
              
              <v-row class="mt-3">
                <v-col cols="12" md="6">
                  <p class="mb-2">배경 투명도</p>
                  <v-slider
                    v-model="backgroundOpacity"
                    min="0"
                    max="1"
                    step="0.01"
                    label="투명도"
                    thumb-label="always"
                    :thumb-size="24"
                  ></v-slider>
                </v-col>
                <v-col cols="12" md="6">
                  <v-checkbox
                    v-model="useBorder"
                    label="외곽선 사용"
                    color="primary"
                  ></v-checkbox>
                </v-col>
                <v-col cols="12" md="6" v-if="useBorder">
                  <p class="mb-2">외곽선 색상</p>
                  <v-color-picker
                    v-model="borderColor"
                    hide-canvas
                    hide-inputs
                    show-swatches
                    swatches-max-height="100px"
                    class="rounded-lg"
                  ></v-color-picker>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- 스티커 크게보기 다이얼로그 -->
    <v-dialog v-model="stickerGalleryOpen" max-width="800" scrollable>
      <v-card class="rounded-xl">
        <v-card-title class="headline">
          스티커 갤러리
          <v-spacer></v-spacer>
          <v-btn icon @click="stickerGalleryOpen = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <!-- 카테고리 탭 추가 -->
        <v-tabs
          v-model="selectedCategoryIndex"
          background-color="purple lighten-5"
          color="purple darken-1"
          centered
          slider-color="purple"
          class="rounded-t-xl"
        >
          <v-tab
            v-for="(category, index) in stickerCategories"
            :key="'cat-' + index"
            @click="selectCategory(index)"
          >
            {{ category.title }}
          </v-tab>
          
          <!-- 모든 스티커 탭 -->
          <v-tab
            :key="'cat-all'"
            @click="selectedCategoryIndex = -1"
          >
            전체
          </v-tab>
        </v-tabs>
        
        <v-card-text style="height: 600px;">
          <v-row>
            <v-col
              v-for="(sticker, idx) in selectedCategoryIndex === -1 ? validStickers : getCategoryStickers(selectedCategoryIndex)"
              :key="sticker.name"
              cols="6"
              sm="4"
              md="3"
              class="pa-2"
            >
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-card
                    @click="selectStickerFromGallery(sticker, validStickers.findIndex(s => s.name === sticker.name))"
                    :class="selectedStickerIndex === validStickers.findIndex(s => s.name === sticker.name) ? 'selected-gallery-sticker' : ''"
                    elevation="2"
                    class="rounded-xl sticker-gallery-card"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-img
                      :src="sticker.image_thumbnail"
                      height="100"
                      contain
                      class="pa-2"
                    ></v-img>
                    <v-card-text class="py-2 text-center">
                      <div class="sticker-price">{{ sticker.price }} 스푼</div>
                    </v-card-text>
                  </v-card>
                </template>
                <span>{{ sticker.title }}</span>
              </v-tooltip>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn 
            color="primary" 
            @click="stickerGalleryOpen = false"
            class="rounded-pill"
          >
            닫기
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Vue, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { Sticker, StickerCategory } from '@sopia-bot/core';
// @ts-ignore - html2canvas 타입 정의 문제 해결
import html2canvas from 'html2canvas';

@Component
export default class ImageEditor extends Mixins(GlobalMixins) {
  // 이미지 관련 상태
  private imageFile: File | null = null;
  private imageUrl: string = '';
  private imageLoaded: boolean = false;
  
  // 스티커 관련 상태
  private validStickers: Sticker[] = [];
  private stickerCategories: StickerCategory[] = [];
  private selectedCategoryIndex: number = -1;
  private selectedSticker: Sticker | null = null;
  private selectedStickerIndex: number = -1;
  private stickerGalleryOpen: boolean = false;
  private stickerPosition = {
    x: 50,
    y: 50,
    scale: 1
  };
  private dragging = {
    active: false,
    offsetX: 0,
    offsetY: 0,
    target: 'current' // 'current' 또는 'applied'
  };
  private activeStickerIndex: number = -1;
  
  // 텍스트 관련 상태
  private textInfo = {
    nickname: '닉네임',
    spoonCount: '100',
    type: 'spoon',
    color: '#FFFFFF'
  };
  
  // 스티커 배경 색상
  private stickerBackgroundColor: string = '#FFFFFF';
  
  // 스티커 배경 투명도
  private backgroundOpacity: number = 1;
  
  // 외곽선 관련 상태
  private useBorder: boolean = false;
  private borderColor: string = '#FF0000';
  
  // 적용된 스티커 관련 상태
  private appliedStickers: { 
    position: { x: number; y: number; scale: number }; 
    backgroundColor?: string; 
    backgroundOpacity?: number;
    borderColor?: string; 
    nickname: string; 
    spoonCount: string; 
    type: string;
    image: string;
    useBorder: boolean;
  }[] = [];
  
  // 편집 모드 상태
  private editMode: boolean = false;
  private editingStickerIndex: number = -1;
  private previousState: {
    selectedSticker: Sticker | null;
    selectedStickerIndex: number;
    textInfo: {
      nickname: string;
      spoonCount: string;
      type: string;
      color: string;
    };
    stickerBackgroundColor: string;
    useBorder: boolean;
    borderColor: string;
    stickerPosition: {
      x: number;
      y: number;
      scale: number;
    };
  } | null = null;
  
  // 편집 중인 스티커의 원본 정보
  private editingOriginalSticker: {
    image: string;
    stickerId?: string;
  } | null = null;
  
  // 편집 모드에서 스티커 변경 여부
  private hasChangedStickerInEditMode: boolean = false;
  
  // 스티커 생성 모드 상태
  private creatingSticker: boolean = false;
  
  // 크기 조절 모드
  private resizing: boolean = false;
  private resizingIndex: number = -1;
  private initialScale: number = 1;
  private initialMouseY: number = 0;
  
  // 컴포넌트가 마운트될 때 스티커 데이터 로드
  mounted() {
    this.loadStickers();
    window.addEventListener('mousemove', this.moveSticker);
    window.addEventListener('mouseup', this.stopDragSticker);
    window.addEventListener('mousemove', this.resizeSticker);
    window.addEventListener('mouseup', this.stopResizing);
    
    // 타이틀바 요소 숨기기
    this.$evt.$emit('hide-titlebar-elements', true);
  }
  
  // 컴포넌트가 제거될 때 이벤트 리스너 제거
  beforeDestroy() {
    window.removeEventListener('mousemove', this.moveSticker);
    window.removeEventListener('mouseup', this.stopDragSticker);
    window.removeEventListener('mousemove', this.resizeSticker);
    window.removeEventListener('mouseup', this.stopResizing);
    
    // 타이틀바 요소 다시 표시
    this.$evt.$emit('hide-titlebar-elements', false);
  }
  
  // 스티커 배경 필터 계산
  get stickerBackgroundFilter(): string {
    // 이 메소드는 더 이상 사용하지 않지만, 향후 활용 가능성을 위해 남겨둡니다.
    return 'none';
  }
  
  // 스푼 API에서 스티커 데이터 로드
  async loadStickers() {
    if (!this.$sopia.sticker.stickers) {
      await this.$sopia.sticker.initSticker();
    }
    
    if (this.$sopia.sticker.stickers) {
      // 카테고리 정보 저장
      this.stickerCategories = this.$sopia.sticker.stickers.categories.filter(
        (category: StickerCategory) => category.is_used
      );
      
      // 모든 사용 가능한 스티커를 validStickers에 저장 (기존 기능 유지)
      this.validStickers = [];
      this.stickerCategories.forEach((category: StickerCategory) => {
        if (category.is_used) {
          category.stickers.forEach((sticker: Sticker) => {
            if (sticker.is_used) {
              this.validStickers.push(sticker);
            }
          });
        }
      });
      
      // 첫 번째 카테고리 선택
      if (this.stickerCategories.length > 0) {
        this.selectedCategoryIndex = 0;
      }
    }
  }
  
  // 이미지 선택 다이얼로그 열기
  openImagePicker() {
    const fileInput = this.$refs.imageInput as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }
  
  // 이미지가 선택되었을 때
  onImageSelected(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
      this.loadImage(target.files[0]);
    }
  }
  
  // 이미지 로드
  loadImage(file: File) {
    if (!file) {
      this.imageUrl = '';
      this.imageLoaded = false;
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      this.imageUrl = e.target?.result as string;
      this.imageLoaded = true;
    };
    reader.readAsDataURL(file);
  }
  
  // 스티커 갤러리 열기
  openStickerGallery() {
    this.stickerGalleryOpen = true;
  }
  
  // 스티커 갤러리에서 스티커 선택
  selectStickerFromGallery(sticker: Sticker, idx: number) {
    this.selectSticker(sticker, idx);
    this.stickerGalleryOpen = false;
  }
  
  // 스티커 선택
  selectSticker(sticker: Sticker, idx: number) {
    // 편집 모드일 때는 스티커 변경 플래그 설정
    if (this.editMode) {
      this.hasChangedStickerInEditMode = true;
    }
    
    this.selectedSticker = sticker;
    this.selectedStickerIndex = idx;
    
    // 스티커 선택 시 텍스트 정보 업데이트
    if (sticker && sticker.price) {
      this.textInfo.spoonCount = sticker.price.toString();
    }
    
    // 생성 모드가 아닌 상태에서 스티커 선택 시 자동으로 생성 모드 활성화
    if (!this.editMode && !this.creatingSticker) {
      this.creatingSticker = true;
    }
  }
  
  // 스티커 드래그 시작
  startDragSticker(event: MouseEvent) {
    if (!this.selectedSticker) return;
    
    const canvas = this.$refs.editorCanvas as HTMLElement;
    const rect = canvas.getBoundingClientRect();
    
    this.dragging.active = true;
    this.dragging.offsetX = event.clientX - rect.left - this.stickerPosition.x;
    this.dragging.offsetY = event.clientY - rect.top - this.stickerPosition.y;
    this.dragging.target = 'current';
  }
  
  // 적용된 스티커 드래그 시작
  startDragAppliedSticker(event: MouseEvent, index: number) {
    // Ctrl 키가 눌러져 있으면 편집 모드로 전환
    if (event.ctrlKey || event.metaKey) {
      this.editAppliedSticker(index);
      return;
    }
    
    const canvas = this.$refs.editorCanvas as HTMLElement;
    const rect = canvas.getBoundingClientRect();
    
    this.dragging.active = true;
    this.dragging.offsetX = event.clientX - rect.left - this.appliedStickers[index].position.x;
    this.dragging.offsetY = event.clientY - rect.top - this.appliedStickers[index].position.y;
    this.dragging.target = 'applied';
    this.activeStickerIndex = index;
    
    // 이벤트 버블링 방지
    event.stopPropagation();
  }
  
  // 적용된 스티커 편집
  editAppliedSticker(index: number) {
    // 이미 편집 모드면 이전 편집 상태 저장
    if (this.editMode) {
      this.cancelEdit();
    }
    
    const sticker = this.appliedStickers[index];
    
    // 편집 중인 스티커의 원본 이미지 저장
    this.editingOriginalSticker = {
      image: sticker.image
    };
    
    // 스티커 변경 플래그 초기화
    this.hasChangedStickerInEditMode = false;
    
    // 현재 상태 저장
    this.previousState = {
      selectedSticker: this.selectedSticker,
      selectedStickerIndex: this.selectedStickerIndex,
      textInfo: { ...this.textInfo },
      stickerBackgroundColor: this.stickerBackgroundColor,
      useBorder: this.useBorder,
      borderColor: this.borderColor,
      stickerPosition: { ...this.stickerPosition }
    };
    
    // 편집 모드로 전환
    this.editMode = true;
    this.editingStickerIndex = index;
    
    // 스티커 정보를 편집 UI에 로드
    this.stickerPosition.scale = sticker.position.scale;
    this.textInfo.nickname = sticker.nickname;
    this.textInfo.spoonCount = sticker.spoonCount;
    this.textInfo.type = sticker.type;
    this.stickerBackgroundColor = sticker.backgroundColor || '#FFFFFF';
    this.backgroundOpacity = sticker.backgroundOpacity !== undefined ? sticker.backgroundOpacity : 1;
    this.useBorder = sticker.useBorder;
    this.borderColor = sticker.borderColor || '#FF0000';
  }
  
  // 스티커 드래그 중
  moveSticker(event: MouseEvent) {
    if (!this.dragging.active) return;
    
    const canvas = this.$refs.editorCanvas as HTMLElement;
    const rect = canvas.getBoundingClientRect();
    
    if (this.dragging.target === 'current') {
      this.stickerPosition.x = event.clientX - rect.left - this.dragging.offsetX;
      this.stickerPosition.y = event.clientY - rect.top - this.dragging.offsetY;
    } else if (this.dragging.target === 'applied' && this.activeStickerIndex >= 0) {
      this.appliedStickers[this.activeStickerIndex].position.x = event.clientX - rect.left - this.dragging.offsetX;
      this.appliedStickers[this.activeStickerIndex].position.y = event.clientY - rect.top - this.dragging.offsetY;
    }
  }
  
  // 스티커 드래그 종료
  stopDragSticker() {
    this.dragging.active = false;
    this.activeStickerIndex = -1;
  }
  
  // 이미지 저장
  async saveImage() {
    if (!this.imageLoaded || !this.$refs.editorCanvas) return;
    
    try {
      const canvas = await html2canvas(this.$refs.editorCanvas as HTMLElement, {
        useCORS: true,
        scale: 2
      });
      
      const link = document.createElement('a');
      link.download = `tamm_edited_image_${new Date().getTime()}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
      
      this.$swal({
        icon: 'success',
        title: '저장 완료',
        text: '이미지가 저장되었습니다!'
      });
    } catch (error) {
      console.error('이미지 저장 실패:', error);
      this.$swal({
        icon: 'error',
        title: '저장 실패',
        text: '이미지 저장에 실패했습니다.'
      });
    }
  }
  
  // 편집 취소
  cancelEdit() {
    if (this.editMode) {
      // 스티커 편집 모드 취소
      this.editMode = false;
      this.editingStickerIndex = -1;
      this.editingOriginalSticker = null;
      this.hasChangedStickerInEditMode = false;
      
      // 이전 상태로 복원
      if (this.previousState) {
        this.selectedSticker = this.previousState.selectedSticker;
        this.selectedStickerIndex = this.previousState.selectedStickerIndex;
        this.textInfo = { ...this.previousState.textInfo };
        this.stickerBackgroundColor = this.previousState.stickerBackgroundColor;
        this.useBorder = this.previousState.useBorder;
        this.borderColor = this.previousState.borderColor;
        this.stickerPosition = { ...this.previousState.stickerPosition };
        
        // 복원 후 이전 상태 초기화
        this.previousState = null;
      } else {
        // 이전 상태가 없는 경우 기본값으로 리셋
        this.textInfo = {
          nickname: '닉네임',
          spoonCount: '100',
          type: 'spoon',
          color: '#FFFFFF'
        };
        this.stickerBackgroundColor = '#FFFFFF';
        this.useBorder = false;
        this.borderColor = '#FF0000';
        this.stickerPosition = {
          x: 50,
          y: 50,
          scale: 1
        };
      }
      
      return;
    }
    
    // 이미지 편집 취소 (기존 코드)
    this.imageFile = null;
    this.imageUrl = '';
    this.imageLoaded = false;
    this.selectedSticker = null;
    this.selectedStickerIndex = -1;
  }
  
  // 스티커 적용
  applySticker() {
    // 편집 모드일 경우 기존 스티커 업데이트
    if (this.editMode && this.editingStickerIndex >= 0) {
      const sticker = this.appliedStickers[this.editingStickerIndex];
      
      // 스티커 정보 업데이트
      sticker.position.scale = this.stickerPosition.scale;
      sticker.nickname = this.textInfo.nickname;
      sticker.spoonCount = this.textInfo.spoonCount;
      sticker.type = this.textInfo.type;
      sticker.backgroundColor = this.stickerBackgroundColor;
      sticker.backgroundOpacity = this.backgroundOpacity;
      sticker.useBorder = this.useBorder;
      sticker.borderColor = this.useBorder ? this.borderColor : undefined;
      
      // 스티커 이미지 업데이트 (명시적으로 스티커가 변경된 경우에만)
      if (this.hasChangedStickerInEditMode && this.selectedSticker) {
        sticker.image = this.selectedSticker.image_thumbnail;
      }
      
      // 편집 모드 종료
      this.editMode = false;
      this.editingStickerIndex = -1;
      this.editingOriginalSticker = null;
      this.hasChangedStickerInEditMode = false;
      
      return;
    }
    
    // 신규 스티커 추가 (기존 코드)
    if (!this.selectedSticker) return;
    
    this.appliedStickers.push({
      position: {
        x: this.stickerPosition.x,
        y: this.stickerPosition.y,
        scale: this.stickerPosition.scale
      },
      backgroundColor: this.stickerBackgroundColor,
      backgroundOpacity: this.backgroundOpacity,
      borderColor: this.useBorder ? this.borderColor : undefined,
      nickname: this.textInfo.nickname,
      spoonCount: this.textInfo.spoonCount,
      type: this.textInfo.type,
      image: this.selectedSticker.image_thumbnail,
      useBorder: this.useBorder
    });
    
    // 스티커를 초기 위치로 리셋
    this.stickerPosition = {
      x: 50,
      y: 50,
      scale: 1
    };
    
    // 스티커 추가 후 생성 모드 비활성화
    this.creatingSticker = false;
  }
  
  // 적용된 스티커 삭제
  deleteSticker(index: number) {
    this.appliedStickers.splice(index, 1);
  }
  
  // 스티커 생성 모드 토글
  toggleCreatingSticker() {
    this.creatingSticker = !this.creatingSticker;
  }
  
  // 편집 중인 스티커 이미지 소스 가져오기
  getEditingImageSrc(index: number, sticker: any): string {
    // 편집 중인 스티커인지 확인
    if (this.editMode && this.editingStickerIndex === index) {
      // 스티커가 명시적으로 변경된 경우만 새 스티커 이미지 사용
      if (this.hasChangedStickerInEditMode && this.selectedSticker) {
        return this.selectedSticker.image_thumbnail;
      }
      // 그렇지 않으면 원본 이미지 사용
      return this.editingOriginalSticker ? this.editingOriginalSticker.image : sticker.image;
    }
    // 편집 중이 아닌 경우 기존 이미지 사용
    return sticker.image;
  }
  
  // 적용된 스티커 크기 조절 시작
  startResizing(event: MouseEvent, index: number) {
    event.stopPropagation();
    event.preventDefault();
    
    this.resizing = true;
    this.resizingIndex = index;
    this.initialScale = this.appliedStickers[index].position.scale;
    this.initialMouseY = event.clientY;
  }
  
  // 현재 편집 중인 스티커 크기 조절 시작
  startResizingCurrent(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();
    
    this.resizing = true;
    this.resizingIndex = -1; // 현재 편집 중인 스티커를 의미
    this.initialScale = this.stickerPosition.scale;
    this.initialMouseY = event.clientY;
  }
  
  // 스티커 크기 조절 중 (Vue 반응형 시스템 활용)
  resizeSticker(event: MouseEvent) {
    if (!this.resizing) return;
    
    const deltaY = this.initialMouseY - event.clientY;
    const newScale = Math.max(0.5, Math.min(3, this.initialScale + deltaY * 0.002)); // 감도 조정
    
    // Vue 반응형 시스템을 통한 부드러운 업데이트
    if (this.resizingIndex === -1) {
      this.stickerPosition.scale = newScale;
    } else if (this.resizingIndex >= 0 && this.resizingIndex < this.appliedStickers.length) {
      this.appliedStickers[this.resizingIndex].position.scale = newScale;
    }
  }
  
  // 스티커 크기 조절 종료
  stopResizing() {
    this.resizing = false;
    this.resizingIndex = -1;
  }
  
  // 색상과 투명도를 조합하여 backgroundColor 값 생성
  getBackgroundWithOpacity(color: string, opacity: number): string {
    // color가 없거나 opacity가 1인 경우 그대로 반환
    if (!color || opacity === 1) {
      return color || 'white';
    }
    
    // HEX 색상 코드를 RGB로 변환
    let r, g, b;
    if (color.startsWith('#')) {
      const hex = color.substring(1);
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    } else if (color.startsWith('rgb')) {
      // rgb 또는 rgba 형식인 경우
      const matches = color.match(/(\d+),\s*(\d+),\s*(\d+)/);
      if (matches) {
        r = parseInt(matches[1]);
        g = parseInt(matches[2]);
        b = parseInt(matches[3]);
      } else {
        return color;
      }
    } else {
      return color;
    }
    
    // rgba 형식으로 반환
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  // 카테고리별 스티커 가져오기
  getCategoryStickers(categoryIndex: number): Sticker[] {
    if (categoryIndex < 0 || categoryIndex >= this.stickerCategories.length) {
      return this.validStickers; // 선택된 카테고리가 없으면 모든 스티커 반환
    }
    
    const category = this.stickerCategories[categoryIndex];
    return category.stickers.filter(sticker => sticker.is_used);
  }
  
  // 카테고리 선택 처리
  selectCategory(index: number) {
    this.selectedCategoryIndex = index;
  }
}
</script>

<style scoped>
.image-editor-container {
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
}

.content-container {
  height: 100%;
  overflow-y: auto;
}

.editor-canvas-container {
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.editor-canvas {
  position: relative;
  display: inline-block;
}

.loaded-image {
  max-width: 100%;
  max-height: 70vh;
  display: block;
}

.sticker-overlay {
  position: absolute;
  top: 0;
  left: 0;
  cursor: move;
  display: flex;
  flex-direction: column;
  align-items: center;
  will-change: transform;
  transform-origin: center;
}

/* 심플한 스티커 디자인 */
.sticker-container {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 0px 8px 0px 4px;
  min-width: 180px;
}

.sticker-body {
  display: flex;
  align-items: center;
  width: 100%;
  margin: -3px 0;
}

.sticker-image-container {
  position: relative;
  width: 62px;
  height: 62px;
  flex-shrink: 0;
}

.sticker-image {
  flex-shrink: 0;
  image-rendering: auto;
}

.sticker-title {
  font-size: 14px;
  font-weight: 700;
  color: #333;
  margin-left: 4px;
  line-height: 1;
}

.sticker-count {
  font-size: 13px;
  font-weight: bold;
  color: #FF6B00;
  margin-left: auto;
  white-space: nowrap;
  line-height: 1;
}

.selected-sticker {
  background-color: rgba(156, 39, 176, 0.1);
  border-radius: 16px;
}

.stickers-container {
  max-height: 500px;
  overflow-y: auto;
}

.sticker-list {
  max-height: 450px;
  overflow-y: auto;
}

.sticker-item {
  border-radius: 24px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.sticker-item:hover {
  background-color: rgba(156, 39, 176, 0.05);
}

.sticker-name {
  font-weight: normal;
  font-size: 14px;
  color: #666;
}

.sticker-price {
  font-weight: bold !important;
  font-size: 16px !important;
  color: #9c27b0 !important;
}

.image-placeholder {
  cursor: pointer;
  width: 100%;
  height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e0e0e0;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  border-color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.05);
}

.selected-gallery-sticker {
  border: 2px solid #9c27b0 !important;
  box-shadow: 0 0 8px rgba(156, 39, 176, 0.5) !important;
}

.sticker-gallery-card {
  cursor: pointer;
  transition: all 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sticker-gallery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.apply-sticker-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.applied-sticker {
  z-index: 1;
}

.applied-sticker:hover {
  z-index: 3;
}

.current-sticker {
  z-index: 10 !important;
}

.edit-preview-sticker {
  opacity: 0.9;
  pointer-events: none;
}

.delete-sticker-btn {
  position: absolute;
  top: -10px;
  left: -10px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.applied-sticker:hover .delete-sticker-btn {
  opacity: 1;
}

.editing-sticker {
  z-index: 5 !important;
}

.cancel-edit-btn {
  position: absolute;
  top: -10px;
  left: -10px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.cancel-create-btn {
  position: absolute;
  top: -10px;
  left: -10px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resize-sticker-btn {
  position: absolute;
  bottom: -10px;
  right: -10px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.applied-sticker:hover .resize-sticker-btn,
.applied-sticker:hover .delete-sticker-btn,
.applied-sticker:hover .cancel-edit-btn,
.current-sticker:hover .resize-sticker-btn,
.current-sticker:hover .cancel-create-btn {
  opacity: 1;
}

.resizing-sticker {
  z-index: 6 !important;
  cursor: nesw-resize !important;
  transition: none !important;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}
</style> 