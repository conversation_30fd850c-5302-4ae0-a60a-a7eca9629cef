(window["webpackJsonpspooncast-web-next"] = window["webpackJsonpspooncast-web-next"] || []).push([
    [279], {
        2406: function(e, t, n) {
            "use strict";
            var i = n(10),
                r = n(72);

            function s(e, t) {
                var n = {};
                for (var i in e) Object.prototype.hasOwnProperty.call(e, i) && t.indexOf(i) < 0 && (n[i] = e[i]);
                if (null != e && "function" === typeof Object.getOwnPropertySymbols) {
                    var r = 0;
                    for (i = Object.getOwnPropertySymbols(e); r < i.length; r++) t.indexOf(i[r]) < 0 && Object.prototype.propertyIsEnumerable.call(e, i[r]) && (n[i[r]] = e[i[r]])
                }
                return n
            }
            Object.create;
            Object.create;
            var o = n(179),
                a = n(136);

            function c() {
                return {
                    "dependent-sdk-initialized-before-auth": "Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."
                }
            }
            const u = c,
                l = new i.b("auth", "Firebase", {
                    "dependent-sdk-initialized-before-auth": "Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."
                }),
                h = new o.b("@firebase/auth");

            function d(e) {
                if (h.logLevel <= o.a.ERROR) {
                    for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++) n[i - 1] = arguments[i];
                    h.error("Auth (".concat(r.a, "): ").concat(e), ...n)
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function p(e) {
                for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++) n[i - 1] = arguments[i];
                throw g(e, ...n)
            }

            function f(e) {
                for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++) n[i - 1] = arguments[i];
                return g(e, ...n)
            }

            function m(e, t, n) {
                const r = Object.assign(Object.assign({}, u()), {
                    [t]: n
                });
                return new i.b("auth", "Firebase", r).create(t, {
                    appName: e.name
                })
            }

            function g(e) {
                for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), i = 1; i < t; i++) n[i - 1] = arguments[i];
                if ("string" !== typeof e) {
                    const t = n[0],
                        i = [...n.slice(1)];
                    return i[0] && (i[0].appName = e.name), e._errorFactory.create(t, ...i)
                }
                return l.create(e, ...n)
            }

            function v(e, t) {
                if (!e) {
                    for (var n = arguments.length, i = new Array(n > 2 ? n - 2 : 0), r = 2; r < n; r++) i[r - 2] = arguments[r];
                    throw g(t, ...i)
                }
            }

            function _(e) {
                const t = "INTERNAL ASSERTION FAILED: " + e;
                throw d(t), new Error(t)
            }

            function I(e, t) {
                e || _(t)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const y = new Map;

            function w(e) {
                I(e instanceof Function, "Expected a class definition");
                let t = y.get(e);
                return t ? (I(t instanceof e, "Instance stored in cache mismatched with class"), t) : (t = new e, y.set(e, t), t)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function T(e, t) {
                const n = Object(r.b)(e, "auth");
                if (n.isInitialized()) {
                    const e = n.getImmediate(),
                        r = n.getOptions();
                    if (Object(i.q)(r, null !== t && void 0 !== t ? t : {})) return e;
                    p(e, "already-initialized")
                }
                return n.initialize({
                    options: t
                })
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function k() {
                var e;
                return "undefined" !== typeof self && (null === (e = self.location) || void 0 === e ? void 0 : e.href) || ""
            }

            function b() {
                return "http:" === O() || "https:" === O()
            }

            function O() {
                var e;
                return "undefined" !== typeof self && (null === (e = self.location) || void 0 === e ? void 0 : e.protocol) || null
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class R {
                constructor(e, t) {
                    this.shortDelay = e, this.longDelay = t, I(t > e, "Short delay should be less than long delay!"), this.isMobile = Object(i.A)() || Object(i.C)()
                }
                get() {
                    return "undefined" !== typeof navigator && navigator && "onLine" in navigator && "boolean" === typeof navigator.onLine && (b() || Object(i.w)() || "connection" in navigator) && !navigator.onLine ? Math.min(5e3, this.shortDelay) : this.isMobile ? this.longDelay : this.shortDelay
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function E(e, t) {
                I(e.emulator, "Emulator should always be set here");
                const {
                    url: n
                } = e.emulator;
                return t ? "".concat(n).concat(t.startsWith("/") ? t.slice(1) : t) : n
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class S {
                static initialize(e, t, n) {
                    this.fetchImpl = e, t && (this.headersImpl = t), n && (this.responseImpl = n)
                }
                static fetch() {
                    return this.fetchImpl ? this.fetchImpl : "undefined" !== typeof self && "fetch" in self ? self.fetch : void _("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")
                }
                static headers() {
                    return this.headersImpl ? this.headersImpl : "undefined" !== typeof self && "Headers" in self ? self.Headers : void _("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")
                }
                static response() {
                    return this.responseImpl ? this.responseImpl : "undefined" !== typeof self && "Response" in self ? self.Response : void _("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const P = {
                    CREDENTIAL_MISMATCH: "custom-token-mismatch",
                    MISSING_CUSTOM_TOKEN: "internal-error",
                    INVALID_IDENTIFIER: "invalid-email",
                    MISSING_CONTINUE_URI: "internal-error",
                    INVALID_PASSWORD: "wrong-password",
                    MISSING_PASSWORD: "internal-error",
                    EMAIL_EXISTS: "email-already-in-use",
                    PASSWORD_LOGIN_DISABLED: "operation-not-allowed",
                    INVALID_IDP_RESPONSE: "invalid-credential",
                    INVALID_PENDING_TOKEN: "invalid-credential",
                    FEDERATED_USER_ID_ALREADY_LINKED: "credential-already-in-use",
                    MISSING_REQ_TYPE: "internal-error",
                    EMAIL_NOT_FOUND: "user-not-found",
                    RESET_PASSWORD_EXCEED_LIMIT: "too-many-requests",
                    EXPIRED_OOB_CODE: "expired-action-code",
                    INVALID_OOB_CODE: "invalid-action-code",
                    MISSING_OOB_CODE: "internal-error",
                    CREDENTIAL_TOO_OLD_LOGIN_AGAIN: "requires-recent-login",
                    INVALID_ID_TOKEN: "invalid-user-token",
                    TOKEN_EXPIRED: "user-token-expired",
                    USER_NOT_FOUND: "user-token-expired",
                    TOO_MANY_ATTEMPTS_TRY_LATER: "too-many-requests",
                    INVALID_CODE: "invalid-verification-code",
                    INVALID_SESSION_INFO: "invalid-verification-id",
                    INVALID_TEMPORARY_PROOF: "invalid-credential",
                    MISSING_SESSION_INFO: "missing-verification-id",
                    SESSION_EXPIRED: "code-expired",
                    MISSING_ANDROID_PACKAGE_NAME: "missing-android-pkg-name",
                    UNAUTHORIZED_DOMAIN: "unauthorized-continue-uri",
                    INVALID_OAUTH_CLIENT_ID: "invalid-oauth-client-id",
                    ADMIN_ONLY_OPERATION: "admin-restricted-operation",
                    INVALID_MFA_PENDING_CREDENTIAL: "invalid-multi-factor-session",
                    MFA_ENROLLMENT_NOT_FOUND: "multi-factor-info-not-found",
                    MISSING_MFA_ENROLLMENT_ID: "missing-multi-factor-info",
                    MISSING_MFA_PENDING_CREDENTIAL: "missing-multi-factor-session",
                    SECOND_FACTOR_EXISTS: "second-factor-already-in-use",
                    SECOND_FACTOR_LIMIT_EXCEEDED: "maximum-second-factor-count-exceeded",
                    BLOCKING_FUNCTION_ERROR_RESPONSE: "internal-error"
                },
                N = new R(3e4, 6e4);
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function A(e, t) {
                return e.tenantId && !t.tenantId ? Object.assign(Object.assign({}, t), {
                    tenantId: e.tenantId
                }) : t
            }
            async function C(e, t, n, r) {
                let s = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : {};
                return L(e, s, async () => {
                    let s = {},
                        o = {};
                    r && ("GET" === t ? o = r : s = {
                        body: JSON.stringify(r)
                    });
                    const a = Object(i.G)(Object.assign({
                            key: e.config.apiKey
                        }, o)).slice(1),
                        c = await e._getAdditionalHeaders();
                    return c["Content-Type"] = "application/json", e.languageCode && (c["X-Firebase-Locale"] = e.languageCode), S.fetch()(D(e, e.config.apiHost, n, a), Object.assign({
                        method: t,
                        headers: c,
                        referrerPolicy: "no-referrer"
                    }, s))
                })
            }
            async function L(e, t, n) {
                e._canInitEmulator = !1;
                const r = Object.assign(Object.assign({}, P), t);
                try {
                    const t = new U(e),
                        i = await Promise.race([n(), t.promise]);
                    t.clearNetworkTimeout();
                    const s = await i.json();
                    if ("needConfirmation" in s) throw j(e, "account-exists-with-different-credential", s);
                    if (i.ok && !("errorMessage" in s)) return s; {
                        const t = i.ok ? s.errorMessage : s.error.message,
                            [n, o] = t.split(" : ");
                        if ("FEDERATED_USER_ID_ALREADY_LINKED" === n) throw j(e, "credential-already-in-use", s);
                        if ("EMAIL_EXISTS" === n) throw j(e, "email-already-in-use", s);
                        if ("USER_DISABLED" === n) throw j(e, "user-disabled", s);
                        const a = r[n] || n.toLowerCase().replace(/[_\s]+/g, "-");
                        if (o) throw m(e, a, o);
                        p(e, a)
                    }
                } catch (t) {
                    if (t instanceof i.c) throw t;
                    p(e, "network-request-failed")
                }
            }
            async function M(e, t, n, i) {
                let r = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : {};
                const s = await C(e, t, n, i, r);
                return "mfaPendingCredential" in s && p(e, "multi-factor-auth-required", {
                    _serverResponse: s
                }), s
            }

            function D(e, t, n, i) {
                const r = "".concat(t).concat(n, "?").concat(i);
                return e.config.emulator ? E(e.config, r) : "".concat(e.config.apiScheme, "://").concat(r)
            }
            class U {
                constructor(e) {
                    this.auth = e, this.timer = null, this.promise = new Promise((e, t) => {
                        this.timer = setTimeout(() => t(f(this.auth, "network-request-failed")), N.get())
                    })
                }
                clearNetworkTimeout() {
                    clearTimeout(this.timer)
                }
            }

            function j(e, t, n) {
                const i = {
                    appName: e.name
                };
                n.email && (i.email = n.email), n.phoneNumber && (i.phoneNumber = n.phoneNumber);
                const r = f(e, t, i);
                return r.customData._tokenResponse = n, r
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function F(e) {
                if (e) try {
                    const t = new Date(Number(e));
                    if (!isNaN(t.getTime())) return t.toUTCString()
                } catch (e) {}
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function x(e) {
                return 1e3 * Number(e)
            }

            function V(e) {
                var t;
                const [n, r, s] = e.split(".");
                if (void 0 === n || void 0 === r || void 0 === s) return d("JWT malformed, contained fewer than 3 sections"), null;
                try {
                    const e = Object(i.i)(r);
                    return e ? JSON.parse(e) : (d("Failed to decode base64 JWT payload"), null)
                } catch (e) {
                    return d("Caught error parsing JWT payload as JSON", null === (t = e) || void 0 === t ? void 0 : t.toString()), null
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function H(e, t) {
                let n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
                if (n) return t;
                try {
                    return await t
                } catch (t) {
                    throw t instanceof i.c && W(t) && e.auth.currentUser === e && await e.auth.signOut(), t
                }
            }

            function W(e) {
                let {
                    code: t
                } = e;
                return t === "auth/".concat("user-disabled") || t === "auth/".concat("user-token-expired")
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class z {
                constructor(e) {
                    this.user = e, this.isRunning = !1, this.timerId = null, this.errorBackoff = 3e4
                }
                _start() {
                    this.isRunning || (this.isRunning = !0, this.schedule())
                }
                _stop() {
                    this.isRunning && (this.isRunning = !1, null !== this.timerId && clearTimeout(this.timerId))
                }
                getInterval(e) {
                    var t;
                    if (e) {
                        const e = this.errorBackoff;
                        return this.errorBackoff = Math.min(2 * this.errorBackoff, 96e4), e
                    } {
                        this.errorBackoff = 3e4;
                        const e = (null !== (t = this.user.stsTokenManager.expirationTime) && void 0 !== t ? t : 0) - Date.now() - 3e5;
                        return Math.max(0, e)
                    }
                }
                schedule() {
                    let e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
                    if (!this.isRunning) return;
                    const t = this.getInterval(e);
                    this.timerId = setTimeout(async () => {
                        await this.iteration()
                    }, t)
                }
                async iteration() {
                    var e;
                    try {
                        await this.user.getIdToken(!0)
                    } catch (t) {
                        return void((null === (e = t) || void 0 === e ? void 0 : e.code) === "auth/".concat("network-request-failed") && this.schedule(!0))
                    }
                    this.schedule()
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class q {
                constructor(e, t) {
                    this.createdAt = e, this.lastLoginAt = t, this._initializeTime()
                }
                _initializeTime() {
                    this.lastSignInTime = F(this.lastLoginAt), this.creationTime = F(this.createdAt)
                }
                _copy(e) {
                    this.createdAt = e.createdAt, this.lastLoginAt = e.lastLoginAt, this._initializeTime()
                }
                toJSON() {
                    return {
                        createdAt: this.createdAt,
                        lastLoginAt: this.lastLoginAt
                    }
                }
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function K(e) {
                var t;
                const n = e.auth,
                    i = await e.getIdToken(),
                    r = await H(e, async function(e, t) {
                        return C(e, "POST", "/v1/accounts:lookup", t)
                    }(n, {
                        idToken: i
                    }));
                v(null === r || void 0 === r ? void 0 : r.users.length, n, "internal-error");
                const o = r.users[0];
                e._notifyReloadListener(o);
                const a = (null === (t = o.providerUserInfo) || void 0 === t ? void 0 : t.length) ? o.providerUserInfo.map(e => {
                    var {
                        providerId: t
                    } = e, n = s(e, ["providerId"]);
                    return {
                        providerId: t,
                        uid: n.rawId || "",
                        displayName: n.displayName || null,
                        email: n.email || null,
                        phoneNumber: n.phoneNumber || null,
                        photoURL: n.photoUrl || null
                    }
                }) : [];
                const c = (u = e.providerData, l = a, [...u.filter(e => !l.some(t => t.providerId === e.providerId)), ...l]);
                var u, l;
                const h = e.isAnonymous,
                    d = !(e.email && o.passwordHash) && !(null === c || void 0 === c ? void 0 : c.length),
                    p = !!h && d,
                    f = {
                        uid: o.localId,
                        displayName: o.displayName || null,
                        photoURL: o.photoUrl || null,
                        email: o.email || null,
                        emailVerified: o.emailVerified || !1,
                        phoneNumber: o.phoneNumber || null,
                        tenantId: o.tenantId || null,
                        providerData: c,
                        metadata: new q(o.createdAt, o.lastLoginAt),
                        isAnonymous: p
                    };
                Object.assign(e, f)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class G {
                constructor() {
                    this.refreshToken = null, this.accessToken = null, this.expirationTime = null
                }
                get isExpired() {
                    return !this.expirationTime || Date.now() > this.expirationTime - 3e4
                }
                updateFromServerResponse(e) {
                    v(e.idToken, "internal-error"), v("undefined" !== typeof e.idToken, "internal-error"), v("undefined" !== typeof e.refreshToken, "internal-error");
                    const t = "expiresIn" in e && "undefined" !== typeof e.expiresIn ? Number(e.expiresIn) : function(e) {
                        const t = V(e);
                        return v(t, "internal-error"), v("undefined" !== typeof t.exp, "internal-error"), v("undefined" !== typeof t.iat, "internal-error"), Number(t.exp) - Number(t.iat)
                    }(e.idToken);
                    this.updateTokensAndExpiration(e.idToken, e.refreshToken, t)
                }
                async getToken(e) {
                    let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                    return v(!this.accessToken || this.refreshToken, e, "user-token-expired"), t || !this.accessToken || this.isExpired ? this.refreshToken ? (await this.refresh(e, this.refreshToken), this.accessToken) : null : this.accessToken
                }
                clearRefreshToken() {
                    this.refreshToken = null
                }
                async refresh(e, t) {
                    const {
                        accessToken: n,
                        refreshToken: r,
                        expiresIn: s
                    } = await
                    /**
                     * @license
                     * Copyright 2020 Google LLC
                     *
                     * Licensed under the Apache License, Version 2.0 (the "License");
                     * you may not use this file except in compliance with the License.
                     * You may obtain a copy of the License at
                     *
                     *   http://www.apache.org/licenses/LICENSE-2.0
                     *
                     * Unless required by applicable law or agreed to in writing, software
                     * distributed under the License is distributed on an "AS IS" BASIS,
                     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                     * See the License for the specific language governing permissions and
                     * limitations under the License.
                     */
                    async function(e, t) {
                        const n = await L(e, {}, async () => {
                            const n = Object(i.G)({
                                    grant_type: "refresh_token",
                                    refresh_token: t
                                }).slice(1),
                                {
                                    tokenApiHost: r,
                                    apiKey: s
                                } = e.config,
                                o = D(e, r, "/v1/token", "key=".concat(s)),
                                a = await e._getAdditionalHeaders();
                            return a["Content-Type"] = "application/x-www-form-urlencoded", S.fetch()(o, {
                                method: "POST",
                                headers: a,
                                body: n
                            })
                        });
                        return {
                            accessToken: n.access_token,
                            expiresIn: n.expires_in,
                            refreshToken: n.refresh_token
                        }
                    }(e, t);
                    this.updateTokensAndExpiration(n, r, Number(s))
                }
                updateTokensAndExpiration(e, t, n) {
                    this.refreshToken = t || null, this.accessToken = e || null, this.expirationTime = Date.now() + 1e3 * n
                }
                static fromJSON(e, t) {
                    const {
                        refreshToken: n,
                        accessToken: i,
                        expirationTime: r
                    } = t, s = new G;
                    return n && (v("string" === typeof n, "internal-error", {
                        appName: e
                    }), s.refreshToken = n), i && (v("string" === typeof i, "internal-error", {
                        appName: e
                    }), s.accessToken = i), r && (v("number" === typeof r, "internal-error", {
                        appName: e
                    }), s.expirationTime = r), s
                }
                toJSON() {
                    return {
                        refreshToken: this.refreshToken,
                        accessToken: this.accessToken,
                        expirationTime: this.expirationTime
                    }
                }
                _assign(e) {
                    this.accessToken = e.accessToken, this.refreshToken = e.refreshToken, this.expirationTime = e.expirationTime
                }
                _clone() {
                    return Object.assign(new G, this.toJSON())
                }
                _performRefresh() {
                    return _("not implemented")
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function J(e, t) {
                v("string" === typeof e || "undefined" === typeof e, "internal-error", {
                    appName: t
                })
            }
            class B {
                constructor(e) {
                    var {
                        uid: t,
                        auth: n,
                        stsTokenManager: i
                    } = e, r = s(e, ["uid", "auth", "stsTokenManager"]);
                    this.providerId = "firebase", this.proactiveRefresh = new z(this), this.reloadUserInfo = null, this.reloadListener = null, this.uid = t, this.auth = n, this.stsTokenManager = i, this.accessToken = i.accessToken, this.displayName = r.displayName || null, this.email = r.email || null, this.emailVerified = r.emailVerified || !1, this.phoneNumber = r.phoneNumber || null, this.photoURL = r.photoURL || null, this.isAnonymous = r.isAnonymous || !1, this.tenantId = r.tenantId || null, this.providerData = r.providerData ? [...r.providerData] : [], this.metadata = new q(r.createdAt || void 0, r.lastLoginAt || void 0)
                }
                async getIdToken(e) {
                    const t = await H(this, this.stsTokenManager.getToken(this.auth, e));
                    return v(t, this.auth, "internal-error"), this.accessToken !== t && (this.accessToken = t, await this.auth._persistUserIfCurrent(this), this.auth._notifyListenersIfCurrent(this)), t
                }
                getIdTokenResult(e) {
                    return async function(e) {
                        let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                        const n = Object(i.t)(e),
                            r = await n.getIdToken(t),
                            s = V(r);
                        v(s && s.exp && s.auth_time && s.iat, n.auth, "internal-error");
                        const o = "object" === typeof s.firebase ? s.firebase : void 0,
                            a = null === o || void 0 === o ? void 0 : o.sign_in_provider;
                        return {
                            claims: s,
                            token: r,
                            authTime: F(x(s.auth_time)),
                            issuedAtTime: F(x(s.iat)),
                            expirationTime: F(x(s.exp)),
                            signInProvider: a || null,
                            signInSecondFactor: (null === o || void 0 === o ? void 0 : o.sign_in_second_factor) || null
                        }
                    }(this, e)
                }
                reload() {
                    return async function(e) {
                        const t = Object(i.t)(e);
                        await K(t), await t.auth._persistUserIfCurrent(t), t.auth._notifyListenersIfCurrent(t)
                    }(this)
                }
                _assign(e) {
                    this !== e && (v(this.uid === e.uid, this.auth, "internal-error"), this.displayName = e.displayName, this.photoURL = e.photoURL, this.email = e.email, this.emailVerified = e.emailVerified, this.phoneNumber = e.phoneNumber, this.isAnonymous = e.isAnonymous, this.tenantId = e.tenantId, this.providerData = e.providerData.map(e => Object.assign({}, e)), this.metadata._copy(e.metadata), this.stsTokenManager._assign(e.stsTokenManager))
                }
                _clone(e) {
                    return new B(Object.assign(Object.assign({}, this), {
                        auth: e,
                        stsTokenManager: this.stsTokenManager._clone()
                    }))
                }
                _onReload(e) {
                    v(!this.reloadListener, this.auth, "internal-error"), this.reloadListener = e, this.reloadUserInfo && (this._notifyReloadListener(this.reloadUserInfo), this.reloadUserInfo = null)
                }
                _notifyReloadListener(e) {
                    this.reloadListener ? this.reloadListener(e) : this.reloadUserInfo = e
                }
                _startProactiveRefresh() {
                    this.proactiveRefresh._start()
                }
                _stopProactiveRefresh() {
                    this.proactiveRefresh._stop()
                }
                async _updateTokensIfNecessary(e) {
                    let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
                        n = !1;
                    e.idToken && e.idToken !== this.stsTokenManager.accessToken && (this.stsTokenManager.updateFromServerResponse(e), n = !0), t && await K(this), await this.auth._persistUserIfCurrent(this), n && this.auth._notifyListenersIfCurrent(this)
                }
                async delete() {
                    const e = await this.getIdToken();
                    return await H(this, async function(e, t) {
                        return C(e, "POST", "/v1/accounts:delete", t)
                    }(this.auth, {
                        idToken: e
                    })), this.stsTokenManager.clearRefreshToken(), this.auth.signOut()
                }
                toJSON() {
                    return Object.assign(Object.assign({
                        uid: this.uid,
                        email: this.email || void 0,
                        emailVerified: this.emailVerified,
                        displayName: this.displayName || void 0,
                        isAnonymous: this.isAnonymous,
                        photoURL: this.photoURL || void 0,
                        phoneNumber: this.phoneNumber || void 0,
                        tenantId: this.tenantId || void 0,
                        providerData: this.providerData.map(e => Object.assign({}, e)),
                        stsTokenManager: this.stsTokenManager.toJSON(),
                        _redirectEventId: this._redirectEventId
                    }, this.metadata.toJSON()), {
                        apiKey: this.auth.config.apiKey,
                        appName: this.auth.name
                    })
                }
                get refreshToken() {
                    return this.stsTokenManager.refreshToken || ""
                }
                static _fromJSON(e, t) {
                    var n, i, r, s, o, a, c, u;
                    const l = null !== (n = t.displayName) && void 0 !== n ? n : void 0,
                        h = null !== (i = t.email) && void 0 !== i ? i : void 0,
                        d = null !== (r = t.phoneNumber) && void 0 !== r ? r : void 0,
                        p = null !== (s = t.photoURL) && void 0 !== s ? s : void 0,
                        f = null !== (o = t.tenantId) && void 0 !== o ? o : void 0,
                        m = null !== (a = t._redirectEventId) && void 0 !== a ? a : void 0,
                        g = null !== (c = t.createdAt) && void 0 !== c ? c : void 0,
                        _ = null !== (u = t.lastLoginAt) && void 0 !== u ? u : void 0,
                        {
                            uid: I,
                            emailVerified: y,
                            isAnonymous: w,
                            providerData: T,
                            stsTokenManager: k
                        } = t;
                    v(I && k, e, "internal-error");
                    const b = G.fromJSON(this.name, k);
                    v("string" === typeof I, e, "internal-error"), J(l, e.name), J(h, e.name), v("boolean" === typeof y, e, "internal-error"), v("boolean" === typeof w, e, "internal-error"), J(d, e.name), J(p, e.name), J(f, e.name), J(m, e.name), J(g, e.name), J(_, e.name);
                    const O = new B({
                        uid: I,
                        auth: e,
                        email: h,
                        emailVerified: y,
                        displayName: l,
                        isAnonymous: w,
                        photoURL: p,
                        phoneNumber: d,
                        tenantId: f,
                        stsTokenManager: b,
                        createdAt: g,
                        lastLoginAt: _
                    });
                    return T && Array.isArray(T) && (O.providerData = T.map(e => Object.assign({}, e))), m && (O._redirectEventId = m), O
                }
                static async _fromIdTokenResponse(e, t) {
                    let n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
                    const i = new G;
                    i.updateFromServerResponse(t);
                    const r = new B({
                        uid: t.localId,
                        auth: e,
                        stsTokenManager: i,
                        isAnonymous: n
                    });
                    return await K(r), r
                }
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class X {
                constructor() {
                    this.type = "NONE", this.storage = {}
                }
                async _isAvailable() {
                    return !0
                }
                async _set(e, t) {
                    this.storage[e] = t
                }
                async _get(e) {
                    const t = this.storage[e];
                    return void 0 === t ? null : t
                }
                async _remove(e) {
                    delete this.storage[e]
                }
                _addListener(e, t) {}
                _removeListener(e, t) {}
            }
            X.type = "NONE";
            const Y = X;
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function Q(e, t, n) {
                return "firebase".concat(":", e, ":").concat(t, ":").concat(n)
            }
            class Z {
                constructor(e, t, n) {
                    this.persistence = e, this.auth = t, this.userKey = n;
                    const {
                        config: i,
                        name: r
                    } = this.auth;
                    this.fullUserKey = Q(this.userKey, i.apiKey, r), this.fullPersistenceKey = Q("persistence", i.apiKey, r), this.boundEventHandler = t._onStorageEvent.bind(t), this.persistence._addListener(this.fullUserKey, this.boundEventHandler)
                }
                setCurrentUser(e) {
                    return this.persistence._set(this.fullUserKey, e.toJSON())
                }
                async getCurrentUser() {
                    const e = await this.persistence._get(this.fullUserKey);
                    return e ? B._fromJSON(this.auth, e) : null
                }
                removeCurrentUser() {
                    return this.persistence._remove(this.fullUserKey)
                }
                savePersistenceForRedirect() {
                    return this.persistence._set(this.fullPersistenceKey, this.persistence.type)
                }
                async setPersistence(e) {
                    if (this.persistence === e) return;
                    const t = await this.getCurrentUser();
                    return await this.removeCurrentUser(), this.persistence = e, t ? this.setCurrentUser(t) : void 0
                }
                delete() {
                    this.persistence._removeListener(this.fullUserKey, this.boundEventHandler)
                }
                static async create(e, t) {
                    let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "authUser";
                    if (!t.length) return new Z(w(Y), e, n);
                    const i = (await Promise.all(t.map(async e => {
                        if (await e._isAvailable()) return e
                    }))).filter(e => e);
                    let r = i[0] || w(Y);
                    const s = Q(n, e.config.apiKey, e.name);
                    let o = null;
                    for (const n of t) try {
                        const t = await n._get(s);
                        if (t) {
                            const i = B._fromJSON(e, t);
                            n !== r && (o = i), r = n;
                            break
                        }
                    } catch (e) {}
                    const a = i.filter(e => e._shouldAllowMigration);
                    return r._shouldAllowMigration && a.length ? (r = a[0], o && await r._set(s, o.toJSON()), await Promise.all(t.map(async e => {
                        if (e !== r) try {
                            await e._remove(s)
                        } catch (e) {}
                    })), new Z(r, e, n)) : new Z(r, e, n)
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function $(e) {
                const t = e.toLowerCase();
                if (t.includes("opera/") || t.includes("opr/") || t.includes("opios/")) return "Opera";
                if (ie(t)) return "IEMobile";
                if (t.includes("msie") || t.includes("trident/")) return "IE";
                if (t.includes("edge/")) return "Edge";
                if (ee(t)) return "Firefox";
                if (t.includes("silk/")) return "Silk";
                if (se(t)) return "Blackberry";
                if (oe(t)) return "Webos";
                if (te(t)) return "Safari";
                if ((t.includes("chrome/") || ne(t)) && !t.includes("edge/")) return "Chrome";
                if (re(t)) return "Android"; {
                    const t = /([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/,
                        n = e.match(t);
                    if (2 === (null === n || void 0 === n ? void 0 : n.length)) return n[1]
                }
                return "Other"
            }

            function ee() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /firefox\//i.test(e)
            }

            function te() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                const t = e.toLowerCase();
                return t.includes("safari/") && !t.includes("chrome/") && !t.includes("crios/") && !t.includes("android")
            }

            function ne() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /crios\//i.test(e)
            }

            function ie() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /iemobile/i.test(e)
            }

            function re() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /android/i.test(e)
            }

            function se() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /blackberry/i.test(e)
            }

            function oe() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /webos/i.test(e)
            }

            function ae() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return /iphone|ipad|ipod/i.test(e) || /macintosh/i.test(e) && /mobile/i.test(e)
            }

            function ce() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                var t;
                return ae(e) && !!(null === (t = window.navigator) || void 0 === t ? void 0 : t.standalone)
            }

            function ue() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(i.u)();
                return ae(e) || re(e) || oe(e) || se(e) || /windows phone/i.test(e) || ie(e)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function le(e) {
                let t, n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
                switch (e) {
                    case "Browser":
                        t = $(Object(i.u)());
                        break;
                    case "Worker":
                        t = "".concat($(Object(i.u)()), "-").concat(e);
                        break;
                    default:
                        t = e
                }
                const s = n.length ? n.join(",") : "FirebaseCore-web";
                return "".concat(t, "/", "JsCore", "/").concat(r.a, "/").concat(s)
            }
            /**
             * @license
             * Copyright 2022 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class he {
                constructor(e) {
                    this.auth = e, this.queue = []
                }
                pushCallback(e, t) {
                    const n = t => new Promise((n, i) => {
                        try {
                            n(e(t))
                        } catch (e) {
                            i(e)
                        }
                    });
                    n.onAbort = t, this.queue.push(n);
                    const i = this.queue.length - 1;
                    return () => {
                        this.queue[i] = () => Promise.resolve()
                    }
                }
                async runMiddleware(e) {
                    var t;
                    if (this.auth.currentUser === e) return;
                    const n = [];
                    try {
                        for (const t of this.queue) await t(e), t.onAbort && n.push(t.onAbort)
                    } catch (e) {
                        n.reverse();
                        for (const e of n) try {
                            e()
                        } catch (e) {}
                        throw this.auth._errorFactory.create("login-blocked", {
                            originalMessage: null === (t = e) || void 0 === t ? void 0 : t.message
                        })
                    }
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class de {
                constructor(e, t, n) {
                    this.app = e, this.heartbeatServiceProvider = t, this.config = n, this.currentUser = null, this.emulatorConfig = null, this.operations = Promise.resolve(), this.authStateSubscription = new fe(this), this.idTokenSubscription = new fe(this), this.beforeStateQueue = new he(this), this.redirectUser = null, this.isProactiveRefreshEnabled = !1, this._canInitEmulator = !0, this._isInitialized = !1, this._deleted = !1, this._initializationPromise = null, this._popupRedirectResolver = null, this._errorFactory = l, this.lastNotifiedUid = void 0, this.languageCode = null, this.tenantId = null, this.settings = {
                        appVerificationDisabledForTesting: !1
                    }, this.frameworks = [], this.name = e.name, this.clientVersion = n.sdkClientVersion
                }
                _initializeWithPersistence(e, t) {
                    return t && (this._popupRedirectResolver = w(t)), this._initializationPromise = this.queue(async () => {
                        var n, i;
                        if (!this._deleted && (this.persistenceManager = await Z.create(this, e), !this._deleted)) {
                            if (null === (n = this._popupRedirectResolver) || void 0 === n ? void 0 : n._shouldInitProactively) try {
                                await this._popupRedirectResolver._initialize(this)
                            } catch (e) {}
                            await this.initializeCurrentUser(t), this.lastNotifiedUid = (null === (i = this.currentUser) || void 0 === i ? void 0 : i.uid) || null, this._deleted || (this._isInitialized = !0)
                        }
                    }), this._initializationPromise
                }
                async _onStorageEvent() {
                    if (this._deleted) return;
                    const e = await this.assertedPersistence.getCurrentUser();
                    return this.currentUser || e ? this.currentUser && e && this.currentUser.uid === e.uid ? (this._currentUser._assign(e), void await this.currentUser.getIdToken()) : void await this._updateCurrentUser(e, !0) : void 0
                }
                async initializeCurrentUser(e) {
                    var t;
                    const n = await this.assertedPersistence.getCurrentUser();
                    let i = n,
                        r = !1;
                    if (e && this.config.authDomain) {
                        await this.getOrInitRedirectPersistenceManager();
                        const n = null === (t = this.redirectUser) || void 0 === t ? void 0 : t._redirectEventId,
                            s = null === i || void 0 === i ? void 0 : i._redirectEventId,
                            o = await this.tryRedirectSignIn(e);
                        n && n !== s || !(null === o || void 0 === o ? void 0 : o.user) || (i = o.user, r = !0)
                    }
                    if (!i) return this.directlySetCurrentUser(null);
                    if (!i._redirectEventId) {
                        if (r) try {
                            await this.beforeStateQueue.runMiddleware(i)
                        } catch (e) {
                            i = n, this._popupRedirectResolver._overrideRedirectResult(this, () => Promise.reject(e))
                        }
                        return i ? this.reloadAndSetCurrentUserOrClear(i) : this.directlySetCurrentUser(null)
                    }
                    return v(this._popupRedirectResolver, this, "argument-error"), await this.getOrInitRedirectPersistenceManager(), this.redirectUser && this.redirectUser._redirectEventId === i._redirectEventId ? this.directlySetCurrentUser(i) : this.reloadAndSetCurrentUserOrClear(i)
                }
                async tryRedirectSignIn(e) {
                    let t = null;
                    try {
                        t = await this._popupRedirectResolver._completeRedirectFn(this, e, !0)
                    } catch (e) {
                        await this._setRedirectUser(null)
                    }
                    return t
                }
                async reloadAndSetCurrentUserOrClear(e) {
                    var t;
                    try {
                        await K(e)
                    } catch (e) {
                        if ((null === (t = e) || void 0 === t ? void 0 : t.code) !== "auth/".concat("network-request-failed")) return this.directlySetCurrentUser(null)
                    }
                    return this.directlySetCurrentUser(e)
                }
                useDeviceLanguage() {
                    this.languageCode = function() {
                        if ("undefined" === typeof navigator) return null;
                        const e = navigator;
                        return e.languages && e.languages[0] || e.language || null
                    }()
                }
                async _delete() {
                    this._deleted = !0
                }
                async updateCurrentUser(e) {
                    const t = e ? Object(i.t)(e) : null;
                    return t && v(t.auth.config.apiKey === this.config.apiKey, this, "invalid-user-token"), this._updateCurrentUser(t && t._clone(this))
                }
                async _updateCurrentUser(e) {
                    let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                    if (!this._deleted) return e && v(this.tenantId === e.tenantId, this, "tenant-id-mismatch"), t || await this.beforeStateQueue.runMiddleware(e), this.queue(async () => {
                        await this.directlySetCurrentUser(e), this.notifyAuthListeners()
                    })
                }
                async signOut() {
                    return await this.beforeStateQueue.runMiddleware(null), (this.redirectPersistenceManager || this._popupRedirectResolver) && await this._setRedirectUser(null), this._updateCurrentUser(null, !0)
                }
                setPersistence(e) {
                    return this.queue(async () => {
                        await this.assertedPersistence.setPersistence(w(e))
                    })
                }
                _getPersistence() {
                    return this.assertedPersistence.persistence.type
                }
                _updateErrorMap(e) {
                    this._errorFactory = new i.b("auth", "Firebase", e())
                }
                onAuthStateChanged(e, t, n) {
                    return this.registerStateListener(this.authStateSubscription, e, t, n)
                }
                beforeAuthStateChanged(e, t) {
                    return this.beforeStateQueue.pushCallback(e, t)
                }
                onIdTokenChanged(e, t, n) {
                    return this.registerStateListener(this.idTokenSubscription, e, t, n)
                }
                toJSON() {
                    var e;
                    return {
                        apiKey: this.config.apiKey,
                        authDomain: this.config.authDomain,
                        appName: this.name,
                        currentUser: null === (e = this._currentUser) || void 0 === e ? void 0 : e.toJSON()
                    }
                }
                async _setRedirectUser(e, t) {
                    const n = await this.getOrInitRedirectPersistenceManager(t);
                    return null === e ? n.removeCurrentUser() : n.setCurrentUser(e)
                }
                async getOrInitRedirectPersistenceManager(e) {
                    if (!this.redirectPersistenceManager) {
                        const t = e && w(e) || this._popupRedirectResolver;
                        v(t, this, "argument-error"), this.redirectPersistenceManager = await Z.create(this, [w(t._redirectPersistence)], "redirectUser"), this.redirectUser = await this.redirectPersistenceManager.getCurrentUser()
                    }
                    return this.redirectPersistenceManager
                }
                async _redirectUserForId(e) {
                    var t, n;
                    return this._isInitialized && await this.queue(async () => {}), (null === (t = this._currentUser) || void 0 === t ? void 0 : t._redirectEventId) === e ? this._currentUser : (null === (n = this.redirectUser) || void 0 === n ? void 0 : n._redirectEventId) === e ? this.redirectUser : null
                }
                async _persistUserIfCurrent(e) {
                    if (e === this.currentUser) return this.queue(async () => this.directlySetCurrentUser(e))
                }
                _notifyListenersIfCurrent(e) {
                    e === this.currentUser && this.notifyAuthListeners()
                }
                _key() {
                    return "".concat(this.config.authDomain, ":").concat(this.config.apiKey, ":").concat(this.name)
                }
                _startProactiveRefresh() {
                    this.isProactiveRefreshEnabled = !0, this.currentUser && this._currentUser._startProactiveRefresh()
                }
                _stopProactiveRefresh() {
                    this.isProactiveRefreshEnabled = !1, this.currentUser && this._currentUser._stopProactiveRefresh()
                }
                get _currentUser() {
                    return this.currentUser
                }
                notifyAuthListeners() {
                    var e, t;
                    if (!this._isInitialized) return;
                    this.idTokenSubscription.next(this.currentUser);
                    const n = null !== (t = null === (e = this.currentUser) || void 0 === e ? void 0 : e.uid) && void 0 !== t ? t : null;
                    this.lastNotifiedUid !== n && (this.lastNotifiedUid = n, this.authStateSubscription.next(this.currentUser))
                }
                registerStateListener(e, t, n, i) {
                    if (this._deleted) return () => {};
                    const r = "function" === typeof t ? t : t.next.bind(t),
                        s = this._isInitialized ? Promise.resolve() : this._initializationPromise;
                    return v(s, this, "internal-error"), s.then(() => r(this.currentUser)), "function" === typeof t ? e.addObserver(t, n, i) : e.addObserver(t)
                }
                async directlySetCurrentUser(e) {
                    this.currentUser && this.currentUser !== e && (this._currentUser._stopProactiveRefresh(), e && this.isProactiveRefreshEnabled && e._startProactiveRefresh()), this.currentUser = e, e ? await this.assertedPersistence.setCurrentUser(e) : await this.assertedPersistence.removeCurrentUser()
                }
                queue(e) {
                    return this.operations = this.operations.then(e, e), this.operations
                }
                get assertedPersistence() {
                    return v(this.persistenceManager, this, "internal-error"), this.persistenceManager
                }
                _logFramework(e) {
                    e && !this.frameworks.includes(e) && (this.frameworks.push(e), this.frameworks.sort(), this.clientVersion = le(this.config.clientPlatform, this._getFrameworks()))
                }
                _getFrameworks() {
                    return this.frameworks
                }
                async _getAdditionalHeaders() {
                    var e;
                    const t = {
                        "X-Client-Version": this.clientVersion
                    };
                    this.app.options.appId && (t["X-Firebase-gmpid"] = this.app.options.appId);
                    const n = await (null === (e = this.heartbeatServiceProvider.getImmediate({
                        optional: !0
                    })) || void 0 === e ? void 0 : e.getHeartbeatsHeader());
                    return n && (t["X-Firebase-Client"] = n), t
                }
            }

            function pe(e) {
                return Object(i.t)(e)
            }
            class fe {
                constructor(e) {
                    this.auth = e, this.observer = null, this.addObserver = Object(i.o)(e => this.observer = e)
                }
                get next() {
                    return v(this.observer, this.auth, "internal-error"), this.observer.next.bind(this.observer)
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class me {
                constructor(e, t) {
                    this.providerId = e, this.signInMethod = t
                }
                toJSON() {
                    return _("not implemented")
                }
                _getIdTokenResponse(e) {
                    return _("not implemented")
                }
                _linkToIdToken(e, t) {
                    return _("not implemented")
                }
                _getReauthenticationResolver(e) {
                    return _("not implemented")
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function ge(e, t) {
                return C(e, "POST", "/v1/accounts:update", t)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class ve extends me {
                constructor(e, t, n) {
                    let i = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : null;
                    super("password", n), this._email = e, this._password = t, this._tenantId = i
                }
                static _fromEmailAndPassword(e, t) {
                    return new ve(e, t, "password")
                }
                static _fromEmailAndCode(e, t) {
                    return new ve(e, t, "emailLink", arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null)
                }
                toJSON() {
                    return {
                        email: this._email,
                        password: this._password,
                        signInMethod: this.signInMethod,
                        tenantId: this._tenantId
                    }
                }
                static fromJSON(e) {
                    const t = "string" === typeof e ? JSON.parse(e) : e;
                    if ((null === t || void 0 === t ? void 0 : t.email) && (null === t || void 0 === t ? void 0 : t.password)) {
                        if ("password" === t.signInMethod) return this._fromEmailAndPassword(t.email, t.password);
                        if ("emailLink" === t.signInMethod) return this._fromEmailAndCode(t.email, t.password, t.tenantId)
                    }
                    return null
                }
                async _getIdTokenResponse(e) {
                    switch (this.signInMethod) {
                        case "password":
                            /**
                             * @license
                             * Copyright 2020 Google LLC
                             *
                             * Licensed under the Apache License, Version 2.0 (the "License");
                             * you may not use this file except in compliance with the License.
                             * You may obtain a copy of the License at
                             *
                             *   http://www.apache.org/licenses/LICENSE-2.0
                             *
                             * Unless required by applicable law or agreed to in writing, software
                             * distributed under the License is distributed on an "AS IS" BASIS,
                             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                             * See the License for the specific language governing permissions and
                             * limitations under the License.
                             */
                            return async function(e, t) {
                                return M(e, "POST", "/v1/accounts:signInWithPassword", A(e, t))
                            }(e, {
                                returnSecureToken: !0,
                                email: this._email,
                                password: this._password
                            });
                        case "emailLink":
                            /**
                             * @license
                             * Copyright 2020 Google LLC
                             *
                             * Licensed under the Apache License, Version 2.0 (the "License");
                             * you may not use this file except in compliance with the License.
                             * You may obtain a copy of the License at
                             *
                             *   http://www.apache.org/licenses/LICENSE-2.0
                             *
                             * Unless required by applicable law or agreed to in writing, software
                             * distributed under the License is distributed on an "AS IS" BASIS,
                             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                             * See the License for the specific language governing permissions and
                             * limitations under the License.
                             */
                            return async function(e, t) {
                                return M(e, "POST", "/v1/accounts:signInWithEmailLink", A(e, t))
                            }(e, {
                                email: this._email,
                                oobCode: this._password
                            });
                        default:
                            p(e, "internal-error")
                    }
                }
                async _linkToIdToken(e, t) {
                    switch (this.signInMethod) {
                        case "password":
                            return ge(e, {
                                idToken: t,
                                returnSecureToken: !0,
                                email: this._email,
                                password: this._password
                            });
                        case "emailLink":
                            return async function(e, t) {
                                return M(e, "POST", "/v1/accounts:signInWithEmailLink", A(e, t))
                            }(e, {
                                idToken: t,
                                email: this._email,
                                oobCode: this._password
                            });
                        default:
                            p(e, "internal-error")
                    }
                }
                _getReauthenticationResolver(e) {
                    return this._getIdTokenResponse(e)
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function _e(e, t) {
                return M(e, "POST", "/v1/accounts:signInWithIdp", A(e, t))
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Ie extends me {
                constructor() {
                    super(...arguments), this.pendingToken = null
                }
                static _fromParams(e) {
                    const t = new Ie(e.providerId, e.signInMethod);
                    return e.idToken || e.accessToken ? (e.idToken && (t.idToken = e.idToken), e.accessToken && (t.accessToken = e.accessToken), e.nonce && !e.pendingToken && (t.nonce = e.nonce), e.pendingToken && (t.pendingToken = e.pendingToken)) : e.oauthToken && e.oauthTokenSecret ? (t.accessToken = e.oauthToken, t.secret = e.oauthTokenSecret) : p("argument-error"), t
                }
                toJSON() {
                    return {
                        idToken: this.idToken,
                        accessToken: this.accessToken,
                        secret: this.secret,
                        nonce: this.nonce,
                        pendingToken: this.pendingToken,
                        providerId: this.providerId,
                        signInMethod: this.signInMethod
                    }
                }
                static fromJSON(e) {
                    const t = "string" === typeof e ? JSON.parse(e) : e,
                        {
                            providerId: n,
                            signInMethod: i
                        } = t,
                        r = s(t, ["providerId", "signInMethod"]);
                    if (!n || !i) return null;
                    const o = new Ie(n, i);
                    return o.idToken = r.idToken || void 0, o.accessToken = r.accessToken || void 0, o.secret = r.secret, o.nonce = r.nonce, o.pendingToken = r.pendingToken || null, o
                }
                _getIdTokenResponse(e) {
                    return _e(e, this.buildRequest())
                }
                _linkToIdToken(e, t) {
                    const n = this.buildRequest();
                    return n.idToken = t, _e(e, n)
                }
                _getReauthenticationResolver(e) {
                    const t = this.buildRequest();
                    return t.autoCreate = !1, _e(e, t)
                }
                buildRequest() {
                    const e = {
                        requestUri: "http://localhost",
                        returnSecureToken: !0
                    };
                    if (this.pendingToken) e.pendingToken = this.pendingToken;
                    else {
                        const t = {};
                        this.idToken && (t.id_token = this.idToken), this.accessToken && (t.access_token = this.accessToken), this.secret && (t.oauth_token_secret = this.secret), t.providerId = this.providerId, this.nonce && !this.pendingToken && (t.nonce = this.nonce), e.postBody = Object(i.G)(t)
                    }
                    return e
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const ye = {
                USER_NOT_FOUND: "user-not-found"
            };
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class we extends me {
                constructor(e) {
                    super("phone", "phone"), this.params = e
                }
                static _fromVerification(e, t) {
                    return new we({
                        verificationId: e,
                        verificationCode: t
                    })
                }
                static _fromTokenResponse(e, t) {
                    return new we({
                        phoneNumber: e,
                        temporaryProof: t
                    })
                }
                _getIdTokenResponse(e) {
                    return async function(e, t) {
                        return M(e, "POST", "/v1/accounts:signInWithPhoneNumber", A(e, t))
                    }(e, this._makeVerificationRequest())
                }
                _linkToIdToken(e, t) {
                    return async function(e, t) {
                        const n = await M(e, "POST", "/v1/accounts:signInWithPhoneNumber", A(e, t));
                        if (n.temporaryProof) throw j(e, "account-exists-with-different-credential", n);
                        return n
                    }(e, Object.assign({
                        idToken: t
                    }, this._makeVerificationRequest()))
                }
                _getReauthenticationResolver(e) {
                    return async function(e, t) {
                        return M(e, "POST", "/v1/accounts:signInWithPhoneNumber", A(e, Object.assign(Object.assign({}, t), {
                            operation: "REAUTH"
                        })), ye)
                    }(e, this._makeVerificationRequest())
                }
                _makeVerificationRequest() {
                    const {
                        temporaryProof: e,
                        phoneNumber: t,
                        verificationId: n,
                        verificationCode: i
                    } = this.params;
                    return e && t ? {
                        temporaryProof: e,
                        phoneNumber: t
                    } : {
                        sessionInfo: n,
                        code: i
                    }
                }
                toJSON() {
                    const e = {
                        providerId: this.providerId
                    };
                    return this.params.phoneNumber && (e.phoneNumber = this.params.phoneNumber), this.params.temporaryProof && (e.temporaryProof = this.params.temporaryProof), this.params.verificationCode && (e.verificationCode = this.params.verificationCode), this.params.verificationId && (e.verificationId = this.params.verificationId), e
                }
                static fromJSON(e) {
                    "string" === typeof e && (e = JSON.parse(e));
                    const {
                        verificationId: t,
                        verificationCode: n,
                        phoneNumber: i,
                        temporaryProof: r
                    } = e;
                    return n || t || i || r ? new we({
                        verificationId: t,
                        verificationCode: n,
                        phoneNumber: i,
                        temporaryProof: r
                    }) : null
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Te {
                constructor(e) {
                    var t, n, r, s, o, a;
                    const c = Object(i.H)(Object(i.s)(e)),
                        u = null !== (t = c.apiKey) && void 0 !== t ? t : null,
                        l = null !== (n = c.oobCode) && void 0 !== n ? n : null,
                        h = function(e) {
                            switch (e) {
                                case "recoverEmail":
                                    return "RECOVER_EMAIL";
                                case "resetPassword":
                                    return "PASSWORD_RESET";
                                case "signIn":
                                    return "EMAIL_SIGNIN";
                                case "verifyEmail":
                                    return "VERIFY_EMAIL";
                                case "verifyAndChangeEmail":
                                    return "VERIFY_AND_CHANGE_EMAIL";
                                case "revertSecondFactorAddition":
                                    return "REVERT_SECOND_FACTOR_ADDITION";
                                default:
                                    return null
                            }
                        }(null !== (r = c.mode) && void 0 !== r ? r : null);
                    v(u && l && h, "argument-error"), this.apiKey = u, this.operation = h, this.code = l, this.continueUrl = null !== (s = c.continueUrl) && void 0 !== s ? s : null, this.languageCode = null !== (o = c.languageCode) && void 0 !== o ? o : null, this.tenantId = null !== (a = c.tenantId) && void 0 !== a ? a : null
                }
                static parseLink(e) {
                    const t = function(e) {
                        const t = Object(i.H)(Object(i.s)(e)).link,
                            n = t ? Object(i.H)(Object(i.s)(t)).deep_link_id : null,
                            r = Object(i.H)(Object(i.s)(e)).deep_link_id;
                        return (r ? Object(i.H)(Object(i.s)(r)).link : null) || r || n || t || e
                    }(e);
                    try {
                        return new Te(t)
                    } catch (e) {
                        return null
                    }
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class ke {
                constructor() {
                    this.providerId = ke.PROVIDER_ID
                }
                static credential(e, t) {
                    return ve._fromEmailAndPassword(e, t)
                }
                static credentialWithLink(e, t) {
                    const n = Te.parseLink(t);
                    return v(n, "argument-error"), ve._fromEmailAndCode(e, n.code, n.tenantId)
                }
            }
            ke.PROVIDER_ID = "password", ke.EMAIL_PASSWORD_SIGN_IN_METHOD = "password", ke.EMAIL_LINK_SIGN_IN_METHOD = "emailLink";
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class be {
                constructor(e) {
                    this.providerId = e, this.defaultLanguageCode = null, this.customParameters = {}
                }
                setDefaultLanguage(e) {
                    this.defaultLanguageCode = e
                }
                setCustomParameters(e) {
                    return this.customParameters = e, this
                }
                getCustomParameters() {
                    return this.customParameters
                }
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Oe extends be {
                constructor() {
                    super(...arguments), this.scopes = []
                }
                addScope(e) {
                    return this.scopes.includes(e) || this.scopes.push(e), this
                }
                getScopes() {
                    return [...this.scopes]
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Re extends Oe {
                constructor() {
                    super("facebook.com")
                }
                static credential(e) {
                    return Ie._fromParams({
                        providerId: Re.PROVIDER_ID,
                        signInMethod: Re.FACEBOOK_SIGN_IN_METHOD,
                        accessToken: e
                    })
                }
                static credentialFromResult(e) {
                    return Re.credentialFromTaggedObject(e)
                }
                static credentialFromError(e) {
                    return Re.credentialFromTaggedObject(e.customData || {})
                }
                static credentialFromTaggedObject(e) {
                    let {
                        _tokenResponse: t
                    } = e;
                    if (!t || !("oauthAccessToken" in t)) return null;
                    if (!t.oauthAccessToken) return null;
                    try {
                        return Re.credential(t.oauthAccessToken)
                    } catch (e) {
                        return null
                    }
                }
            }
            Re.FACEBOOK_SIGN_IN_METHOD = "facebook.com", Re.PROVIDER_ID = "facebook.com";
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Ee extends Oe {
                constructor() {
                    super("google.com"), this.addScope("profile")
                }
                static credential(e, t) {
                    return Ie._fromParams({
                        providerId: Ee.PROVIDER_ID,
                        signInMethod: Ee.GOOGLE_SIGN_IN_METHOD,
                        idToken: e,
                        accessToken: t
                    })
                }
                static credentialFromResult(e) {
                    return Ee.credentialFromTaggedObject(e)
                }
                static credentialFromError(e) {
                    return Ee.credentialFromTaggedObject(e.customData || {})
                }
                static credentialFromTaggedObject(e) {
                    let {
                        _tokenResponse: t
                    } = e;
                    if (!t) return null;
                    const {
                        oauthIdToken: n,
                        oauthAccessToken: i
                    } = t;
                    if (!n && !i) return null;
                    try {
                        return Ee.credential(n, i)
                    } catch (e) {
                        return null
                    }
                }
            }
            Ee.GOOGLE_SIGN_IN_METHOD = "google.com", Ee.PROVIDER_ID = "google.com";
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Se extends Oe {
                constructor() {
                    super("github.com")
                }
                static credential(e) {
                    return Ie._fromParams({
                        providerId: Se.PROVIDER_ID,
                        signInMethod: Se.GITHUB_SIGN_IN_METHOD,
                        accessToken: e
                    })
                }
                static credentialFromResult(e) {
                    return Se.credentialFromTaggedObject(e)
                }
                static credentialFromError(e) {
                    return Se.credentialFromTaggedObject(e.customData || {})
                }
                static credentialFromTaggedObject(e) {
                    let {
                        _tokenResponse: t
                    } = e;
                    if (!t || !("oauthAccessToken" in t)) return null;
                    if (!t.oauthAccessToken) return null;
                    try {
                        return Se.credential(t.oauthAccessToken)
                    } catch (e) {
                        return null
                    }
                }
            }
            Se.GITHUB_SIGN_IN_METHOD = "github.com", Se.PROVIDER_ID = "github.com";
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Pe extends Oe {
                constructor() {
                    super("twitter.com")
                }
                static credential(e, t) {
                    return Ie._fromParams({
                        providerId: Pe.PROVIDER_ID,
                        signInMethod: Pe.TWITTER_SIGN_IN_METHOD,
                        oauthToken: e,
                        oauthTokenSecret: t
                    })
                }
                static credentialFromResult(e) {
                    return Pe.credentialFromTaggedObject(e)
                }
                static credentialFromError(e) {
                    return Pe.credentialFromTaggedObject(e.customData || {})
                }
                static credentialFromTaggedObject(e) {
                    let {
                        _tokenResponse: t
                    } = e;
                    if (!t) return null;
                    const {
                        oauthAccessToken: n,
                        oauthTokenSecret: i
                    } = t;
                    if (!n || !i) return null;
                    try {
                        return Pe.credential(n, i)
                    } catch (e) {
                        return null
                    }
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function Ne(e, t) {
                return M(e, "POST", "/v1/accounts:signUp", A(e, t))
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            Pe.TWITTER_SIGN_IN_METHOD = "twitter.com", Pe.PROVIDER_ID = "twitter.com";
            class Ae {
                constructor(e) {
                    this.user = e.user, this.providerId = e.providerId, this._tokenResponse = e._tokenResponse, this.operationType = e.operationType
                }
                static async _fromIdTokenResponse(e, t, n) {
                    let i = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
                    const r = await B._fromIdTokenResponse(e, n, i),
                        s = Ce(n);
                    return new Ae({
                        user: r,
                        providerId: s,
                        _tokenResponse: n,
                        operationType: t
                    })
                }
                static async _forOperation(e, t, n) {
                    await e._updateTokensIfNecessary(n, !0);
                    const i = Ce(n);
                    return new Ae({
                        user: e,
                        providerId: i,
                        _tokenResponse: n,
                        operationType: t
                    })
                }
            }

            function Ce(e) {
                return e.providerId ? e.providerId : "phoneNumber" in e ? "phone" : null
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function Le(e) {
                var t;
                const n = pe(e);
                if (await n._initializationPromise, null === (t = n.currentUser) || void 0 === t ? void 0 : t.isAnonymous) return new Ae({
                    user: n.currentUser,
                    providerId: null,
                    operationType: "signIn"
                });
                const i = await Ne(n, {
                        returnSecureToken: !0
                    }),
                    r = await Ae._fromIdTokenResponse(n, "signIn", i, !0);
                return await n._updateCurrentUser(r.user), r
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Me extends i.c {
                constructor(e, t, n, i) {
                    var r;
                    super(t.code, t.message), this.operationType = n, this.user = i, Object.setPrototypeOf(this, Me.prototype), this.customData = {
                        appName: e.name,
                        tenantId: null !== (r = e.tenantId) && void 0 !== r ? r : void 0,
                        _serverResponse: t.customData._serverResponse,
                        operationType: n
                    }
                }
                static _fromErrorAndOperation(e, t, n, i) {
                    return new Me(e, t, n, i)
                }
            }

            function De(e, t, n, i) {
                return ("reauthenticate" === t ? n._getReauthenticationResolver(e) : n._getIdTokenResponse(e)).catch(n => {
                    if (n.code === "auth/".concat("multi-factor-auth-required")) throw Me._fromErrorAndOperation(e, n, t, i);
                    throw n
                })
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function Ue(e, t) {
                let n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
                const i = await H(e, t._linkToIdToken(e.auth, await e.getIdToken()), n);
                return Ae._forOperation(e, "link", i)
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function je(e, t) {
                let n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
                var i;
                const {
                    auth: r
                } = e, s = "reauthenticate";
                try {
                    const i = await H(e, De(r, s, t, e), n);
                    v(i.idToken, r, "internal-error");
                    const o = V(i.idToken);
                    v(o, r, "internal-error");
                    const {
                        sub: a
                    } = o;
                    return v(e.uid === a, r, "user-mismatch"), Ae._forOperation(e, s, i)
                } catch (e) {
                    throw (null === (i = e) || void 0 === i ? void 0 : i.code) === "auth/".concat("user-not-found") && p(r, "user-mismatch"), e
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function Fe(e, t) {
                let n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
                const i = "signIn",
                    r = await De(e, i, t),
                    s = await Ae._fromIdTokenResponse(e, i, r);
                return n || await e._updateCurrentUser(s.user), s
            }
            new WeakMap;
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class xe {
                constructor(e, t) {
                    this.storageRetriever = e, this.type = t
                }
                _isAvailable() {
                    try {
                        return this.storage ? (this.storage.setItem("__sak", "1"), this.storage.removeItem("__sak"), Promise.resolve(!0)) : Promise.resolve(!1)
                    } catch (e) {
                        return Promise.resolve(!1)
                    }
                }
                _set(e, t) {
                    return this.storage.setItem(e, JSON.stringify(t)), Promise.resolve()
                }
                _get(e) {
                    const t = this.storage.getItem(e);
                    return Promise.resolve(t ? JSON.parse(t) : null)
                }
                _remove(e) {
                    return this.storage.removeItem(e), Promise.resolve()
                }
                get storage() {
                    return this.storageRetriever()
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Ve extends xe {
                constructor() {
                    super(() => window.localStorage, "LOCAL"), this.boundEventHandler = (e, t) => this.onStorageEvent(e, t), this.listeners = {}, this.localCache = {}, this.pollTimer = null, this.safariLocalStorageNotSynced = function() {
                        const e = Object(i.u)();
                        return te(e) || ae(e)
                    }() && function() {
                        try {
                            return !(!window || window === window.top)
                        } catch (e) {
                            return !1
                        }
                    }(), this.fallbackToPolling = ue(), this._shouldAllowMigration = !0
                }
                forAllChangedKeys(e) {
                    for (const t of Object.keys(this.listeners)) {
                        const n = this.storage.getItem(t),
                            i = this.localCache[t];
                        n !== i && e(t, i, n)
                    }
                }
                onStorageEvent(e) {
                    let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
                    if (!e.key) return void this.forAllChangedKeys((e, t, n) => {
                        this.notifyListeners(e, n)
                    });
                    const n = e.key;
                    if (t ? this.detachListener() : this.stopPolling(), this.safariLocalStorageNotSynced) {
                        const i = this.storage.getItem(n);
                        if (e.newValue !== i) null !== e.newValue ? this.storage.setItem(n, e.newValue) : this.storage.removeItem(n);
                        else if (this.localCache[n] === e.newValue && !t) return
                    }
                    const r = () => {
                            const e = this.storage.getItem(n);
                            (t || this.localCache[n] !== e) && this.notifyListeners(n, e)
                        },
                        s = this.storage.getItem(n);
                    Object(i.y)() && 10 === document.documentMode && s !== e.newValue && e.newValue !== e.oldValue ? setTimeout(r, 10) : r()
                }
                notifyListeners(e, t) {
                    this.localCache[e] = t;
                    const n = this.listeners[e];
                    if (n)
                        for (const e of Array.from(n)) e(t ? JSON.parse(t) : t)
                }
                startPolling() {
                    this.stopPolling(), this.pollTimer = setInterval(() => {
                        this.forAllChangedKeys((e, t, n) => {
                            this.onStorageEvent(new StorageEvent("storage", {
                                key: e,
                                oldValue: t,
                                newValue: n
                            }), !0)
                        })
                    }, 1e3)
                }
                stopPolling() {
                    this.pollTimer && (clearInterval(this.pollTimer), this.pollTimer = null)
                }
                attachListener() {
                    window.addEventListener("storage", this.boundEventHandler)
                }
                detachListener() {
                    window.removeEventListener("storage", this.boundEventHandler)
                }
                _addListener(e, t) {
                    0 === Object.keys(this.listeners).length && (this.fallbackToPolling ? this.startPolling() : this.attachListener()), this.listeners[e] || (this.listeners[e] = new Set, this.localCache[e] = this.storage.getItem(e)), this.listeners[e].add(t)
                }
                _removeListener(e, t) {
                    this.listeners[e] && (this.listeners[e].delete(t), 0 === this.listeners[e].size && delete this.listeners[e]), 0 === Object.keys(this.listeners).length && (this.detachListener(), this.stopPolling())
                }
                async _set(e, t) {
                    await super._set(e, t), this.localCache[e] = JSON.stringify(t)
                }
                async _get(e) {
                    const t = await super._get(e);
                    return this.localCache[e] = JSON.stringify(t), t
                }
                async _remove(e) {
                    await super._remove(e), delete this.localCache[e]
                }
            }
            Ve.type = "LOCAL";
            const He = Ve;
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class We extends xe {
                constructor() {
                    super(() => window.sessionStorage, "SESSION")
                }
                _addListener(e, t) {}
                _removeListener(e, t) {}
            }
            We.type = "SESSION";
            const ze = We;
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class qe {
                constructor(e) {
                    this.eventTarget = e, this.handlersMap = {}, this.boundEventHandler = this.handleEvent.bind(this)
                }
                static _getInstance(e) {
                    const t = this.receivers.find(t => t.isListeningto(e));
                    if (t) return t;
                    const n = new qe(e);
                    return this.receivers.push(n), n
                }
                isListeningto(e) {
                    return this.eventTarget === e
                }
                async handleEvent(e) {
                    const t = e,
                        {
                            eventId: n,
                            eventType: i,
                            data: r
                        } = t.data,
                        s = this.handlersMap[i];
                    if (!(null === s || void 0 === s ? void 0 : s.size)) return;
                    t.ports[0].postMessage({
                        status: "ack",
                        eventId: n,
                        eventType: i
                    });
                    const o = Array.from(s).map(async e => e(t.origin, r)),
                        a = await
                    function(e) {
                        return Promise.all(e.map(async e => {
                            try {
                                return {
                                    fulfilled: !0,
                                    value: await e
                                }
                            } catch (e) {
                                return {
                                    fulfilled: !1,
                                    reason: e
                                }
                            }
                        }))
                    }(o);
                    t.ports[0].postMessage({
                        status: "done",
                        eventId: n,
                        eventType: i,
                        response: a
                    })
                }
                _subscribe(e, t) {
                    0 === Object.keys(this.handlersMap).length && this.eventTarget.addEventListener("message", this.boundEventHandler), this.handlersMap[e] || (this.handlersMap[e] = new Set), this.handlersMap[e].add(t)
                }
                _unsubscribe(e, t) {
                    this.handlersMap[e] && t && this.handlersMap[e].delete(t), t && 0 !== this.handlersMap[e].size || delete this.handlersMap[e], 0 === Object.keys(this.handlersMap).length && this.eventTarget.removeEventListener("message", this.boundEventHandler)
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function Ke() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "",
                    t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10,
                    n = "";
                for (let e = 0; e < t; e++) n += Math.floor(10 * Math.random());
                return e + n
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            qe.receivers = [];
            class Ge {
                constructor(e) {
                    this.target = e, this.handlers = new Set
                }
                removeMessageHandler(e) {
                    e.messageChannel && (e.messageChannel.port1.removeEventListener("message", e.onMessage), e.messageChannel.port1.close()), this.handlers.delete(e)
                }
                async _send(e, t) {
                    let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 50;
                    const i = "undefined" !== typeof MessageChannel ? new MessageChannel : null;
                    if (!i) throw new Error("connection_unavailable");
                    let r, s;
                    return new Promise((o, a) => {
                        const c = Ke("", 20);
                        i.port1.start();
                        const u = setTimeout(() => {
                            a(new Error("unsupported_event"))
                        }, n);
                        s = {
                            messageChannel: i,
                            onMessage(e) {
                                const t = e;
                                if (t.data.eventId === c) switch (t.data.status) {
                                    case "ack":
                                        clearTimeout(u), r = setTimeout(() => {
                                            a(new Error("timeout"))
                                        }, 3e3);
                                        break;
                                    case "done":
                                        clearTimeout(r), o(t.data.response);
                                        break;
                                    default:
                                        clearTimeout(u), clearTimeout(r), a(new Error("invalid_response"))
                                }
                            }
                        }, this.handlers.add(s), i.port1.addEventListener("message", s.onMessage), this.target.postMessage({
                            eventType: e,
                            eventId: c,
                            data: t
                        }, [i.port2])
                    }).finally(() => {
                        s && this.removeMessageHandler(s)
                    })
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function Je() {
                return window
            }
            /**
             * @license
             * Copyright 2020 Google LLC.
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function Be() {
                return "undefined" !== typeof Je().WorkerGlobalScope && "function" === typeof Je().importScripts
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const Xe = "firebaseLocalStorageDb";
            class Ye {
                constructor(e) {
                    this.request = e
                }
                toPromise() {
                    return new Promise((e, t) => {
                        this.request.addEventListener("success", () => {
                            e(this.request.result)
                        }), this.request.addEventListener("error", () => {
                            t(this.request.error)
                        })
                    })
                }
            }

            function Qe(e, t) {
                return e.transaction(["firebaseLocalStorage"], t ? "readwrite" : "readonly").objectStore("firebaseLocalStorage")
            }

            function Ze() {
                const e = indexedDB.open(Xe, 1);
                return new Promise((t, n) => {
                    e.addEventListener("error", () => {
                        n(e.error)
                    }), e.addEventListener("upgradeneeded", () => {
                        const t = e.result;
                        try {
                            t.createObjectStore("firebaseLocalStorage", {
                                keyPath: "fbase_key"
                            })
                        } catch (e) {
                            n(e)
                        }
                    }), e.addEventListener("success", async () => {
                        const n = e.result;
                        n.objectStoreNames.contains("firebaseLocalStorage") ? t(n) : (n.close(), await
                            function() {
                                const e = indexedDB.deleteDatabase(Xe);
                                return new Ye(e).toPromise()
                            }(), t(await Ze()))
                    })
                })
            }
            async function $e(e, t, n) {
                const i = Qe(e, !0).put({
                    fbase_key: t,
                    value: n
                });
                return new Ye(i).toPromise()
            }

            function et(e, t) {
                const n = Qe(e, !0).delete(t);
                return new Ye(n).toPromise()
            }
            class tt {
                constructor() {
                    this.type = "LOCAL", this._shouldAllowMigration = !0, this.listeners = {}, this.localCache = {}, this.pollTimer = null, this.pendingWrites = 0, this.receiver = null, this.sender = null, this.serviceWorkerReceiverAvailable = !1, this.activeServiceWorker = null, this._workerInitializationPromise = this.initializeServiceWorkerMessaging().then(() => {}, () => {})
                }
                async _openDb() {
                    return this.db || (this.db = await Ze()), this.db
                }
                async _withRetries(e) {
                    let t = 0;
                    for (;;) try {
                        const t = await this._openDb();
                        return await e(t)
                    } catch (e) {
                        if (t++ > 3) throw e;
                        this.db && (this.db.close(), this.db = void 0)
                    }
                }
                async initializeServiceWorkerMessaging() {
                    return Be() ? this.initializeReceiver() : this.initializeSender()
                }
                async initializeReceiver() {
                    this.receiver = qe._getInstance(Be() ? self : null), this.receiver._subscribe("keyChanged", async (e, t) => ({
                        keyProcessed: (await this._poll()).includes(t.key)
                    })), this.receiver._subscribe("ping", async (e, t) => ["keyChanged"])
                }
                async initializeSender() {
                    var e, t;
                    if (this.activeServiceWorker = await async function() {
                            if (!(null === navigator || void 0 === navigator ? void 0 : navigator.serviceWorker)) return null;
                            try {
                                return (await navigator.serviceWorker.ready).active
                            } catch (e) {
                                return null
                            }
                        }(), !this.activeServiceWorker) return;
                    this.sender = new Ge(this.activeServiceWorker);
                    const n = await this.sender._send("ping", {}, 800);
                    n && (null === (e = n[0]) || void 0 === e ? void 0 : e.fulfilled) && (null === (t = n[0]) || void 0 === t ? void 0 : t.value.includes("keyChanged")) && (this.serviceWorkerReceiverAvailable = !0)
                }
                async notifyServiceWorker(e) {
                    if (this.sender && this.activeServiceWorker && function() {
                            var e;
                            return (null === (e = null === navigator || void 0 === navigator ? void 0 : navigator.serviceWorker) || void 0 === e ? void 0 : e.controller) || null
                        }() === this.activeServiceWorker) try {
                        await this.sender._send("keyChanged", {
                            key: e
                        }, this.serviceWorkerReceiverAvailable ? 800 : 50)
                    } catch (e) {}
                }
                async _isAvailable() {
                    try {
                        if (!indexedDB) return !1;
                        const e = await Ze();
                        return await $e(e, "__sak", "1"), await et(e, "__sak"), !0
                    } catch (e) {}
                    return !1
                }
                async _withPendingWrite(e) {
                    this.pendingWrites++;
                    try {
                        await e()
                    } finally {
                        this.pendingWrites--
                    }
                }
                async _set(e, t) {
                    return this._withPendingWrite(async () => (await this._withRetries(n => $e(n, e, t)), this.localCache[e] = t, this.notifyServiceWorker(e)))
                }
                async _get(e) {
                    const t = await this._withRetries(t => async function(e, t) {
                        const n = Qe(e, !1).get(t),
                            i = await new Ye(n).toPromise();
                        return void 0 === i ? null : i.value
                    }(t, e));
                    return this.localCache[e] = t, t
                }
                async _remove(e) {
                    return this._withPendingWrite(async () => (await this._withRetries(t => et(t, e)), delete this.localCache[e], this.notifyServiceWorker(e)))
                }
                async _poll() {
                    const e = await this._withRetries(e => {
                        const t = Qe(e, !1).getAll();
                        return new Ye(t).toPromise()
                    });
                    if (!e) return [];
                    if (0 !== this.pendingWrites) return [];
                    const t = [],
                        n = new Set;
                    for (const {
                            fbase_key: i,
                            value: r
                        } of e) n.add(i), JSON.stringify(this.localCache[i]) !== JSON.stringify(r) && (this.notifyListeners(i, r), t.push(i));
                    for (const e of Object.keys(this.localCache)) this.localCache[e] && !n.has(e) && (this.notifyListeners(e, null), t.push(e));
                    return t
                }
                notifyListeners(e, t) {
                    this.localCache[e] = t;
                    const n = this.listeners[e];
                    if (n)
                        for (const e of Array.from(n)) e(t)
                }
                startPolling() {
                    this.stopPolling(), this.pollTimer = setInterval(async () => this._poll(), 800)
                }
                stopPolling() {
                    this.pollTimer && (clearInterval(this.pollTimer), this.pollTimer = null)
                }
                _addListener(e, t) {
                    0 === Object.keys(this.listeners).length && this.startPolling(), this.listeners[e] || (this.listeners[e] = new Set, this._get(e)), this.listeners[e].add(t)
                }
                _removeListener(e, t) {
                    this.listeners[e] && (this.listeners[e].delete(t), 0 === this.listeners[e].size && delete this.listeners[e]), 0 === Object.keys(this.listeners).length && this.stopPolling()
                }
            }
            tt.type = "LOCAL";
            const nt = tt;
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function it(e) {
                return new Promise((t, n) => {
                    const i = document.createElement("script");
                    i.setAttribute("src", e), i.onload = t, i.onerror = e => {
                            const t = f("internal-error");
                            t.customData = e, n(t)
                        }, i.type = "text/javascript", i.charset = "UTF-8",
                        /**
                         * @license
                         * Copyright 2020 Google LLC
                         *
                         * Licensed under the Apache License, Version 2.0 (the "License");
                         * you may not use this file except in compliance with the License.
                         * You may obtain a copy of the License at
                         *
                         *   http://www.apache.org/licenses/LICENSE-2.0
                         *
                         * Unless required by applicable law or agreed to in writing, software
                         * distributed under the License is distributed on an "AS IS" BASIS,
                         * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                         * See the License for the specific language governing permissions and
                         * limitations under the License.
                         */
                        function() {
                            var e, t;
                            return null !== (t = null === (e = document.getElementsByTagName("head")) || void 0 === e ? void 0 : e[0]) && void 0 !== t ? t : document
                        }().appendChild(i)
                })
            }

            function rt(e) {
                return "__".concat(e).concat(Math.floor(1e6 * Math.random()))
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            rt("rcb"), new R(3e4, 6e4);
            async function st(e, t, n) {
                var i;
                const r = await n.verify();
                try {
                    let s;
                    if (v("string" === typeof r, e, "argument-error"), v("recaptcha" === n.type, e, "argument-error"), s = "string" === typeof t ? {
                            phoneNumber: t
                        } : t, "session" in s) {
                        const t = s.session;
                        if ("phoneNumber" in s) {
                            v("enroll" === t.type, e, "internal-error");
                            return (await
                                /**
                                 * @license
                                 * Copyright 2020 Google LLC
                                 *
                                 * Licensed under the Apache License, Version 2.0 (the "License");
                                 * you may not use this file except in compliance with the License.
                                 * You may obtain a copy of the License at
                                 *
                                 *   http://www.apache.org/licenses/LICENSE-2.0
                                 *
                                 * Unless required by applicable law or agreed to in writing, software
                                 * distributed under the License is distributed on an "AS IS" BASIS,
                                 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                                 * See the License for the specific language governing permissions and
                                 * limitations under the License.
                                 */
                                function(e, t) {
                                    return C(e, "POST", "/v2/accounts/mfaEnrollment:start", A(e, t))
                                }(e, {
                                    idToken: t.credential,
                                    phoneEnrollmentInfo: {
                                        phoneNumber: s.phoneNumber,
                                        recaptchaToken: r
                                    }
                                })).phoneSessionInfo.sessionInfo
                        } {
                            v("signin" === t.type, e, "internal-error");
                            const n = (null === (i = s.multiFactorHint) || void 0 === i ? void 0 : i.uid) || s.multiFactorUid;
                            v(n, e, "missing-multi-factor-info");
                            return (await
                                function(e, t) {
                                    return C(e, "POST", "/v2/accounts/mfaSignIn:start", A(e, t))
                                }(e, {
                                    mfaPendingCredential: t.credential,
                                    mfaEnrollmentId: n,
                                    phoneSignInInfo: {
                                        recaptchaToken: r
                                    }
                                })).phoneResponseInfo.sessionInfo
                        }
                    } {
                        const {
                            sessionInfo: t
                        } = await async function(e, t) {
                            return C(e, "POST", "/v1/accounts:sendVerificationCode", A(e, t))
                        }(e, {
                            phoneNumber: s.phoneNumber,
                            recaptchaToken: r
                        });
                        return t
                    }
                } finally {
                    n._reset()
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class ot {
                constructor(e) {
                    this.providerId = ot.PROVIDER_ID, this.auth = pe(e)
                }
                verifyPhoneNumber(e, t) {
                    return st(this.auth, e, Object(i.t)(t))
                }
                static credential(e, t) {
                    return we._fromVerification(e, t)
                }
                static credentialFromResult(e) {
                    const t = e;
                    return ot.credentialFromTaggedObject(t)
                }
                static credentialFromError(e) {
                    return ot.credentialFromTaggedObject(e.customData || {})
                }
                static credentialFromTaggedObject(e) {
                    let {
                        _tokenResponse: t
                    } = e;
                    if (!t) return null;
                    const {
                        phoneNumber: n,
                        temporaryProof: i
                    } = t;
                    return n && i ? we._fromTokenResponse(n, i) : null
                }
            }
            /**
             * @license
             * Copyright 2021 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function at(e, t) {
                return t ? w(t) : (v(e._popupRedirectResolver, e, "argument-error"), e._popupRedirectResolver)
            }
            /**
             * @license
             * Copyright 2019 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            ot.PROVIDER_ID = "phone", ot.PHONE_SIGN_IN_METHOD = "phone";
            class ct extends me {
                constructor(e) {
                    super("custom", "custom"), this.params = e
                }
                _getIdTokenResponse(e) {
                    return _e(e, this._buildIdpRequest())
                }
                _linkToIdToken(e, t) {
                    return _e(e, this._buildIdpRequest(t))
                }
                _getReauthenticationResolver(e) {
                    return _e(e, this._buildIdpRequest())
                }
                _buildIdpRequest(e) {
                    const t = {
                        requestUri: this.params.requestUri,
                        sessionId: this.params.sessionId,
                        postBody: this.params.postBody,
                        tenantId: this.params.tenantId,
                        pendingToken: this.params.pendingToken,
                        returnSecureToken: !0,
                        returnIdpCredential: !0
                    };
                    return e && (t.idToken = e), t
                }
            }

            function ut(e) {
                return Fe(e.auth, new ct(e), e.bypassAuthState)
            }

            function lt(e) {
                const {
                    auth: t,
                    user: n
                } = e;
                return v(n, t, "internal-error"), je(n, new ct(e), e.bypassAuthState)
            }
            async function ht(e) {
                const {
                    auth: t,
                    user: n
                } = e;
                return v(n, t, "internal-error"), Ue(n, new ct(e), e.bypassAuthState)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class dt {
                constructor(e, t, n, i) {
                    let r = arguments.length > 4 && void 0 !== arguments[4] && arguments[4];
                    this.auth = e, this.resolver = n, this.user = i, this.bypassAuthState = r, this.pendingPromise = null, this.eventManager = null, this.filter = Array.isArray(t) ? t : [t]
                }
                execute() {
                    return new Promise(async (e, t) => {
                        this.pendingPromise = {
                            resolve: e,
                            reject: t
                        };
                        try {
                            this.eventManager = await this.resolver._initialize(this.auth), await this.onExecution(), this.eventManager.registerConsumer(this)
                        } catch (e) {
                            this.reject(e)
                        }
                    })
                }
                async onAuthEvent(e) {
                    const {
                        urlResponse: t,
                        sessionId: n,
                        postBody: i,
                        tenantId: r,
                        error: s,
                        type: o
                    } = e;
                    if (s) return void this.reject(s);
                    const a = {
                        auth: this.auth,
                        requestUri: t,
                        sessionId: n,
                        tenantId: r || void 0,
                        postBody: i || void 0,
                        user: this.user,
                        bypassAuthState: this.bypassAuthState
                    };
                    try {
                        this.resolve(await this.getIdpTask(o)(a))
                    } catch (e) {
                        this.reject(e)
                    }
                }
                onError(e) {
                    this.reject(e)
                }
                getIdpTask(e) {
                    switch (e) {
                        case "signInViaPopup":
                        case "signInViaRedirect":
                            return ut;
                        case "linkViaPopup":
                        case "linkViaRedirect":
                            return ht;
                        case "reauthViaPopup":
                        case "reauthViaRedirect":
                            return lt;
                        default:
                            p(this.auth, "internal-error")
                    }
                }
                resolve(e) {
                    I(this.pendingPromise, "Pending promise was never set"), this.pendingPromise.resolve(e), this.unregisterAndCleanUp()
                }
                reject(e) {
                    I(this.pendingPromise, "Pending promise was never set"), this.pendingPromise.reject(e), this.unregisterAndCleanUp()
                }
                unregisterAndCleanUp() {
                    this.eventManager && this.eventManager.unregisterConsumer(this), this.pendingPromise = null, this.cleanUp()
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const pt = new R(2e3, 1e4);
            class ft extends dt {
                constructor(e, t, n, i, r) {
                    super(e, t, i, r), this.provider = n, this.authWindow = null, this.pollId = null, ft.currentPopupAction && ft.currentPopupAction.cancel(), ft.currentPopupAction = this
                }
                async executeNotNull() {
                    const e = await this.execute();
                    return v(e, this.auth, "internal-error"), e
                }
                async onExecution() {
                    I(1 === this.filter.length, "Popup operations only handle one event");
                    const e = Ke();
                    this.authWindow = await this.resolver._openPopup(this.auth, this.provider, this.filter[0], e), this.authWindow.associatedEvent = e, this.resolver._originValidation(this.auth).catch(e => {
                        this.reject(e)
                    }), this.resolver._isIframeWebStorageSupported(this.auth, e => {
                        e || this.reject(f(this.auth, "web-storage-unsupported"))
                    }), this.pollUserCancellation()
                }
                get eventId() {
                    var e;
                    return (null === (e = this.authWindow) || void 0 === e ? void 0 : e.associatedEvent) || null
                }
                cancel() {
                    this.reject(f(this.auth, "cancelled-popup-request"))
                }
                cleanUp() {
                    this.authWindow && this.authWindow.close(), this.pollId && window.clearTimeout(this.pollId), this.authWindow = null, this.pollId = null, ft.currentPopupAction = null
                }
                pollUserCancellation() {
                    const e = () => {
                        var t, n;
                        (null === (n = null === (t = this.authWindow) || void 0 === t ? void 0 : t.window) || void 0 === n ? void 0 : n.closed) ? this.pollId = window.setTimeout(() => {
                            this.pollId = null, this.reject(f(this.auth, "popup-closed-by-user"))
                        }, 2e3): this.pollId = window.setTimeout(e, pt.get())
                    };
                    e()
                }
            }
            ft.currentPopupAction = null;
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const mt = new Map;
            class gt extends dt {
                constructor(e, t) {
                    super(e, ["signInViaRedirect", "linkViaRedirect", "reauthViaRedirect", "unknown"], t, void 0, arguments.length > 2 && void 0 !== arguments[2] && arguments[2]), this.eventId = null
                }
                async execute() {
                    let e = mt.get(this.auth._key());
                    if (!e) {
                        try {
                            const t = await async function(e, t) {
                                const n = It(t),
                                    i = _t(e);
                                if (!await i._isAvailable()) return !1;
                                const r = "true" === await i._get(n);
                                return await i._remove(n), r
                            }(this.resolver, this.auth) ? await super.execute() : null;
                            e = () => Promise.resolve(t)
                        } catch (t) {
                            e = () => Promise.reject(t)
                        }
                        mt.set(this.auth._key(), e)
                    }
                    return this.bypassAuthState || mt.set(this.auth._key(), () => Promise.resolve(null)), e()
                }
                async onAuthEvent(e) {
                    if ("signInViaRedirect" === e.type) return super.onAuthEvent(e);
                    if ("unknown" !== e.type) {
                        if (e.eventId) {
                            const t = await this.auth._redirectUserForId(e.eventId);
                            if (t) return this.user = t, super.onAuthEvent(e);
                            this.resolve(null)
                        }
                    } else this.resolve(null)
                }
                async onExecution() {}
                cleanUp() {}
            }

            function vt(e, t) {
                mt.set(e._key(), t)
            }

            function _t(e) {
                return w(e._redirectPersistence)
            }

            function It(e) {
                return Q("pendingRedirect", e.config.apiKey, e.name)
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            async function yt(e, t) {
                let n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
                const i = pe(e),
                    r = at(i, t),
                    s = new gt(i, r, n),
                    o = await s.execute();
                return o && !n && (delete o.user._redirectEventId, await i._persistUserIfCurrent(o.user), await i._setRedirectUser(null, t)), o
            }
            class wt {
                constructor(e) {
                    this.auth = e, this.cachedEventUids = new Set, this.consumers = new Set, this.queuedRedirectEvent = null, this.hasHandledPotentialRedirect = !1, this.lastProcessedEventTime = Date.now()
                }
                registerConsumer(e) {
                    this.consumers.add(e), this.queuedRedirectEvent && this.isEventForConsumer(this.queuedRedirectEvent, e) && (this.sendToConsumer(this.queuedRedirectEvent, e), this.saveEventToCache(this.queuedRedirectEvent), this.queuedRedirectEvent = null)
                }
                unregisterConsumer(e) {
                    this.consumers.delete(e)
                }
                onEvent(e) {
                    if (this.hasEventBeenHandled(e)) return !1;
                    let t = !1;
                    return this.consumers.forEach(n => {
                        this.isEventForConsumer(e, n) && (t = !0, this.sendToConsumer(e, n), this.saveEventToCache(e))
                    }), this.hasHandledPotentialRedirect || ! function(e) {
                        switch (e.type) {
                            case "signInViaRedirect":
                            case "linkViaRedirect":
                            case "reauthViaRedirect":
                                return !0;
                            case "unknown":
                                return kt(e);
                            default:
                                return !1
                        }
                    }
                    /**
                     * @license
                     * Copyright 2020 Google LLC
                     *
                     * Licensed under the Apache License, Version 2.0 (the "License");
                     * you may not use this file except in compliance with the License.
                     * You may obtain a copy of the License at
                     *
                     *   http://www.apache.org/licenses/LICENSE-2.0
                     *
                     * Unless required by applicable law or agreed to in writing, software
                     * distributed under the License is distributed on an "AS IS" BASIS,
                     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                     * See the License for the specific language governing permissions and
                     * limitations under the License.
                     */
                    (e) || (this.hasHandledPotentialRedirect = !0, t || (this.queuedRedirectEvent = e, t = !0)), t
                }
                sendToConsumer(e, t) {
                    var n;
                    if (e.error && !kt(e)) {
                        const i = (null === (n = e.error.code) || void 0 === n ? void 0 : n.split("auth/")[1]) || "internal-error";
                        t.onError(f(this.auth, i))
                    } else t.onAuthEvent(e)
                }
                isEventForConsumer(e, t) {
                    const n = null === t.eventId || !!e.eventId && e.eventId === t.eventId;
                    return t.filter.includes(e.type) && n
                }
                hasEventBeenHandled(e) {
                    return Date.now() - this.lastProcessedEventTime >= 6e5 && this.cachedEventUids.clear(), this.cachedEventUids.has(Tt(e))
                }
                saveEventToCache(e) {
                    this.cachedEventUids.add(Tt(e)), this.lastProcessedEventTime = Date.now()
                }
            }

            function Tt(e) {
                return [e.type, e.eventId, e.sessionId, e.tenantId].filter(e => e).join("-")
            }

            function kt(e) {
                let {
                    type: t,
                    error: n
                } = e;
                return "unknown" === t && (null === n || void 0 === n ? void 0 : n.code) === "auth/".concat("no-auth-event")
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const bt = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
                Ot = /^https?/;
            async function Rt(e) {
                if (e.config.emulator) return;
                const {
                    authorizedDomains: t
                } = await async function(e) {
                    let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                    return C(e, "GET", "/v1/projects", t)
                }(e);
                for (const e of t) try {
                    if (Et(e)) return
                } catch (e) {}
                p(e, "unauthorized-domain")
            }

            function Et(e) {
                const t = k(),
                    {
                        protocol: n,
                        hostname: i
                    } = new URL(t);
                if (e.startsWith("chrome-extension://")) {
                    const r = new URL(e);
                    return "" === r.hostname && "" === i ? "chrome-extension:" === n && e.replace("chrome-extension://", "") === t.replace("chrome-extension://", "") : "chrome-extension:" === n && r.hostname === i
                }
                if (!Ot.test(n)) return !1;
                if (bt.test(e)) return i === e;
                const r = e.replace(/\./g, "\\.");
                return new RegExp("^(.+\\." + r + "|" + r + ")$", "i").test(i)
            }
            /**
             * @license
             * Copyright 2020 Google LLC.
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const St = new R(3e4, 6e4);

            function Pt() {
                const e = Je().___jsl;
                if (null === e || void 0 === e ? void 0 : e.H)
                    for (const t of Object.keys(e.H))
                        if (e.H[t].r = e.H[t].r || [], e.H[t].L = e.H[t].L || [], e.H[t].r = [...e.H[t].L], e.CP)
                            for (let t = 0; t < e.CP.length; t++) e.CP[t] = null
            }
            let Nt = null;

            function At(e) {
                return Nt = Nt || function(e) {
                    return new Promise((t, n) => {
                        var i, r, s;

                        function o() {
                            Pt(), gapi.load("gapi.iframes", {
                                callback: () => {
                                    t(gapi.iframes.getContext())
                                },
                                ontimeout: () => {
                                    Pt(), n(f(e, "network-request-failed"))
                                },
                                timeout: St.get()
                            })
                        }
                        if (null === (r = null === (i = Je().gapi) || void 0 === i ? void 0 : i.iframes) || void 0 === r ? void 0 : r.Iframe) t(gapi.iframes.getContext());
                        else {
                            if (!(null === (s = Je().gapi) || void 0 === s ? void 0 : s.load)) {
                                const t = rt("iframefcb");
                                return Je()[t] = () => {
                                    gapi.load ? o() : n(f(e, "network-request-failed"))
                                }, it("https://apis.google.com/js/api.js?onload=".concat(t)).catch(e => n(e))
                            }
                            o()
                        }
                    }).catch(e => {
                        throw Nt = null, e
                    })
                }(e), Nt
            }
            /**
             * @license
             * Copyright 2020 Google LLC.
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const Ct = new R(5e3, 15e3),
                Lt = {
                    style: {
                        position: "absolute",
                        top: "-100px",
                        width: "1px",
                        height: "1px"
                    },
                    "aria-hidden": "true",
                    tabindex: "-1"
                },
                Mt = new Map([
                    ["identitytoolkit.googleapis.com", "p"],
                    ["staging-identitytoolkit.sandbox.googleapis.com", "s"],
                    ["test-identitytoolkit.sandbox.googleapis.com", "t"]
                ]);

            function Dt(e) {
                const t = e.config;
                v(t.authDomain, e, "auth-domain-config-required");
                const n = t.emulator ? E(t, "emulator/auth/iframe") : "https://".concat(e.config.authDomain, "/").concat("__/auth/iframe"),
                    s = {
                        apiKey: t.apiKey,
                        appName: e.name,
                        v: r.a
                    },
                    o = Mt.get(e.config.apiHost);
                o && (s.eid = o);
                const a = e._getFrameworks();
                return a.length && (s.fw = a.join(",")), "".concat(n, "?").concat(Object(i.G)(s).slice(1))
            }
            /**
             * @license
             * Copyright 2020 Google LLC.
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            const Ut = {
                location: "yes",
                resizable: "yes",
                statusbar: "yes",
                toolbar: "no"
            };
            class jt {
                constructor(e) {
                    this.window = e, this.associatedEvent = null
                }
                close() {
                    if (this.window) try {
                        this.window.close()
                    } catch (e) {}
                }
            }

            function Ft(e, t) {
                const n = document.createElement("a");
                n.href = e, n.target = t;
                const i = document.createEvent("MouseEvent");
                i.initMouseEvent("click", !0, !0, window, 1, 0, 0, 0, 0, !1, !1, !1, !1, 1, null), n.dispatchEvent(i)
            }
            /**
             * @license
             * Copyright 2021 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function xt(e, t, n, s, o, a) {
                v(e.config.authDomain, e, "auth-domain-config-required"), v(e.config.apiKey, e, "invalid-api-key");
                const c = {
                    apiKey: e.config.apiKey,
                    appName: e.name,
                    authType: n,
                    redirectUrl: s,
                    v: r.a,
                    eventId: o
                };
                if (t instanceof be) {
                    t.setDefaultLanguage(e.languageCode), c.providerId = t.providerId || "", Object(i.x)(t.getCustomParameters()) || (c.customParameters = JSON.stringify(t.getCustomParameters()));
                    for (const [e, t] of Object.entries(a || {})) c[e] = t
                }
                if (t instanceof Oe) {
                    const e = t.getScopes().filter(e => "" !== e);
                    e.length > 0 && (c.scopes = e.join(","))
                }
                e.tenantId && (c.tid = e.tenantId);
                const u = c;
                for (const e of Object.keys(u)) void 0 === u[e] && delete u[e];
                return "".concat(function(e) {
                        let {
                            config: t
                        } = e;
                        if (!t.emulator) return "https://".concat(t.authDomain, "/").concat("__/auth/handler");
                        return E(t, "emulator/auth/handler")
                    }
                    /**
                     * @license
                     * Copyright 2020 Google LLC
                     *
                     * Licensed under the Apache License, Version 2.0 (the "License");
                     * you may not use this file except in compliance with the License.
                     * You may obtain a copy of the License at
                     *
                     *   http://www.apache.org/licenses/LICENSE-2.0
                     *
                     * Unless required by applicable law or agreed to in writing, software
                     * distributed under the License is distributed on an "AS IS" BASIS,
                     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
                     * See the License for the specific language governing permissions and
                     * limitations under the License.
                     */
                    (e), "?").concat(Object(i.G)(u).slice(1))
            }
            const Vt = class {
                constructor() {
                    this.eventManagers = {}, this.iframes = {}, this.originValidationPromises = {}, this._redirectPersistence = ze, this._completeRedirectFn = yt, this._overrideRedirectResult = vt
                }
                async _openPopup(e, t, n, r) {
                    var s;
                    I(null === (s = this.eventManagers[e._key()]) || void 0 === s ? void 0 : s.manager, "_initialize() not called before _openPopup()");
                    return function(e, t, n) {
                        let r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 500,
                            s = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 600;
                        const o = Math.max((window.screen.availHeight - s) / 2, 0).toString(),
                            a = Math.max((window.screen.availWidth - r) / 2, 0).toString();
                        let c = "";
                        const u = Object.assign(Object.assign({}, Ut), {
                                width: r.toString(),
                                height: s.toString(),
                                top: o,
                                left: a
                            }),
                            l = Object(i.u)().toLowerCase();
                        n && (c = ne(l) ? "_blank" : n), ee(l) && (t = t || "http://localhost", u.scrollbars = "yes");
                        const h = Object.entries(u).reduce((e, t) => {
                            let [n, i] = t;
                            return "".concat(e).concat(n, "=").concat(i, ",")
                        }, "");
                        if (ce(l) && "_self" !== c) return Ft(t || "", c), new jt(null);
                        const d = window.open(t || "", c, h);
                        v(d, e, "popup-blocked");
                        try {
                            d.focus()
                        } catch (e) {}
                        return new jt(d)
                    }(e, xt(e, t, n, k(), r), Ke())
                }
                async _openRedirect(e, t, n, i) {
                    var r;
                    return await this._originValidation(e), r = xt(e, t, n, k(), i), Je().location.href = r, new Promise(() => {})
                }
                _initialize(e) {
                    const t = e._key();
                    if (this.eventManagers[t]) {
                        const {
                            manager: e,
                            promise: n
                        } = this.eventManagers[t];
                        return e ? Promise.resolve(e) : (I(n, "If manager is not set, promise should be"), n)
                    }
                    const n = this.initAndGetManager(e);
                    return this.eventManagers[t] = {
                        promise: n
                    }, n.catch(() => {
                        delete this.eventManagers[t]
                    }), n
                }
                async initAndGetManager(e) {
                    const t = await async function(e) {
                            const t = await At(e),
                                n = Je().gapi;
                            return v(n, e, "internal-error"), t.open({
                                where: document.body,
                                url: Dt(e),
                                messageHandlersFilter: n.iframes.CROSS_ORIGIN_IFRAMES_FILTER,
                                attributes: Lt,
                                dontclear: !0
                            }, t => new Promise(async (n, i) => {
                                await t.restyle({
                                    setHideOnLeave: !1
                                });
                                const r = f(e, "network-request-failed"),
                                    s = Je().setTimeout(() => {
                                        i(r)
                                    }, Ct.get());

                                function o() {
                                    Je().clearTimeout(s), n(t)
                                }
                                t.ping(o).then(o, () => {
                                    i(r)
                                })
                            }))
                        }(e),
                        n = new wt(e);
                    return t.register("authEvent", t => {
                        v(null === t || void 0 === t ? void 0 : t.authEvent, e, "invalid-auth-event");
                        return {
                            status: n.onEvent(t.authEvent) ? "ACK" : "ERROR"
                        }
                    }, gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER), this.eventManagers[e._key()] = {
                        manager: n
                    }, this.iframes[e._key()] = t, n
                }
                _isIframeWebStorageSupported(e, t) {
                    this.iframes[e._key()].send("webStorageSupport", {
                        type: "webStorageSupport"
                    }, n => {
                        var i;
                        const r = null === (i = null === n || void 0 === n ? void 0 : n[0]) || void 0 === i ? void 0 : i.webStorageSupport;
                        void 0 !== r && t(!!r), p(e, "internal-error")
                    }, gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)
                }
                _originValidation(e) {
                    const t = e._key();
                    return this.originValidationPromises[t] || (this.originValidationPromises[t] = Rt(e)), this.originValidationPromises[t]
                }
                get _shouldInitProactively() {
                    return ue() || te() || ae()
                }
            };
            var Ht;
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            class Wt {
                constructor(e) {
                    this.auth = e, this.internalListeners = new Map
                }
                getUid() {
                    var e;
                    return this.assertAuthConfigured(), (null === (e = this.auth.currentUser) || void 0 === e ? void 0 : e.uid) || null
                }
                async getToken(e) {
                    if (this.assertAuthConfigured(), await this.auth._initializationPromise, !this.auth.currentUser) return null;
                    return {
                        accessToken: await this.auth.currentUser.getIdToken(e)
                    }
                }
                addAuthTokenListener(e) {
                    if (this.assertAuthConfigured(), this.internalListeners.has(e)) return;
                    const t = this.auth.onIdTokenChanged(t => {
                        var n;
                        e((null === (n = t) || void 0 === n ? void 0 : n.stsTokenManager.accessToken) || null)
                    });
                    this.internalListeners.set(e, t), this.updateProactiveRefresh()
                }
                removeAuthTokenListener(e) {
                    this.assertAuthConfigured();
                    const t = this.internalListeners.get(e);
                    t && (this.internalListeners.delete(e), t(), this.updateProactiveRefresh())
                }
                assertAuthConfigured() {
                    v(this.auth._initializationPromise, "dependent-sdk-initialized-before-auth")
                }
                updateProactiveRefresh() {
                    this.internalListeners.size > 0 ? this.auth._startProactiveRefresh() : this.auth._stopProactiveRefresh()
                }
            }
            /**
             * @license
             * Copyright 2020 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            /**
             * @license
             * Copyright 2021 Google LLC
             *
             * Licensed under the Apache License, Version 2.0 (the "License");
             * you may not use this file except in compliance with the License.
             * You may obtain a copy of the License at
             *
             *   http://www.apache.org/licenses/LICENSE-2.0
             *
             * Unless required by applicable law or agreed to in writing, software
             * distributed under the License is distributed on an "AS IS" BASIS,
             * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
             * See the License for the specific language governing permissions and
             * limitations under the License.
             */
            function zt() {
                let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Object(r.d)();
                const t = Object(r.b)(e, "auth");
                return t.isInitialized() ? t.getImmediate() : T(e, {
                    popupRedirectResolver: Vt,
                    persistence: [nt, He, ze]
                })
            }
            Ht = "Browser", Object(r.c)(new a.a("auth", (e, t) => {
                let {
                    options: n
                } = t;
                const i = e.getProvider("app").getImmediate(),
                    r = e.getProvider("heartbeat"),
                    {
                        apiKey: s,
                        authDomain: o
                    } = i.options;
                return ((e, t) => {
                    v(s && !s.includes(":"), "invalid-api-key", {
                        appName: e.name
                    }), v(!(null === o || void 0 === o ? void 0 : o.includes(":")), "argument-error", {
                        appName: e.name
                    });
                    const i = {
                            apiKey: s,
                            authDomain: o,
                            clientPlatform: Ht,
                            apiHost: "identitytoolkit.googleapis.com",
                            tokenApiHost: "securetoken.googleapis.com",
                            apiScheme: "https",
                            sdkClientVersion: le(Ht)
                        },
                        r = new de(e, t, i);
                    return function(e, t) {
                        const n = (null === t || void 0 === t ? void 0 : t.persistence) || [],
                            i = (Array.isArray(n) ? n : [n]).map(w);
                        (null === t || void 0 === t ? void 0 : t.errorMap) && e._updateErrorMap(t.errorMap), e._initializeWithPersistence(i, null === t || void 0 === t ? void 0 : t.popupRedirectResolver)
                    }(r, n), r
                })(i, r)
            }, "PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e, t, n) => {
                e.getProvider("auth-internal").initialize()
            })), Object(r.c)(new a.a("auth-internal", e => (e => new Wt(e))(pe(e.getProvider("auth").getImmediate())), "PRIVATE").setInstantiationMode("EXPLICIT")), Object(r.f)("@firebase/auth", "0.20.5", function(e) {
                switch (e) {
                    case "Node":
                        return "node";
                    case "ReactNative":
                        return "rn";
                    case "Worker":
                        return "webworker";
                    case "Cordova":
                        return "cordova";
                    default:
                        return
                }
            }(Ht)), Object(r.f)("@firebase/auth", "0.20.5", "esm2017"), n.d(t, "a", (function() {
                return zt
            })), n.d(t, "b", (function() {
                return Le
            }))
        }
    }
]);
//# sourceMappingURL=279.82ead115.chunk.js.map