<!--
 * Cast/Index.vue
 * Created on 2025
 *
 * Copyright (c) Tree Some. Licensed under the MIT License.
-->
<template>
  <v-main class="cast-page">
    <vue-scroll class="cast-scroll-container">
      <v-container class="main-container">
        <!-- 캐스트 플레이어 컴포넌트 -->
        <div class="player-wrapper">
          <div class="player-square">
            <div class="cast-image" 
              :style="{ backgroundImage: `url(${cast ? cast.img_url : ''})` }"
              :class="{'cast-image-hover': true}">
              <div class="overlay"></div>
            </div>
            
            <!-- 오디오 요소 -->
            <audio ref="audioPlayer" @timeupdate="onTimeUpdate" @loadedmetadata="onLoadedMetadata" @ended="onEnded" muted></audio>
            
            <!-- 플레이어 컨트롤 - 호버 시 표시됨 -->
            <div class="player-controls-container">
              <!-- 재생 버튼 (중앙) - 항상 표시 -->
              <div class="center-play-button">
                <v-btn icon x-large class="play-button-large white--text" @click="togglePlay" v-show="!isPlaying">
                  <v-icon size="64">mdi-play</v-icon>
                </v-btn>
              </div>
              
              <!-- 하단 컨트롤 영역 - 호버 시 표시 -->
              <div class="bottom-controls player-controls-hover">
                <!-- 진행바 -->
                <div class="progress-container">
                  <div class="time-info">{{ formatTime(currentTime) }}</div>
                  <div class="progress-wrapper" @click="seek">
                    <v-progress-linear
                      v-model="progress"
                      height="4"
                      rounded
                      color="white"
                      background-color="rgba(255, 255, 255, 0.3)"
                      class="progress-bar"
                    ></v-progress-linear>
                  </div>
                  <div class="time-info">{{ formatTime(duration) }}</div>
                </div>
                
                <!-- 재생 컨트롤 레이아웃 변경 -->
                <div class="control-buttons-container">
                  <!-- 음량 버튼과 슬라이더 (좌측) -->
                  <div class="volume-controls">
                    <div class="volume-button-wrapper">
                      <v-btn icon @click="toggleMute" color="white">
                        <v-icon>{{ isMuted ? 'mdi-volume-off' : 'mdi-volume-high' }}</v-icon>
                      </v-btn>
                      <!-- 음량 조절 슬라이더 (호버 시 표시) -->
                      <div class="volume-slider-wrapper">
                        <div class="volume-slider-container">
                          <v-slider
                            v-model="volume"
                            hide-details
                            class="volume-slider"
                            max="100"
                            min="0"
                            @change="adjustVolume"
                            color="white"
                            track-color="rgba(255, 255, 255, 0.3)"
                            dense
                          ></v-slider>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 재생 컨트롤 버튼 (중앙) -->
                  <div class="playback-controls">
                    <v-btn icon @click="playPreviousTrack" color="white">
                      <v-icon>mdi-rewind</v-icon>
                    </v-btn>
                    <v-btn icon @click="togglePlay" color="white" class="mx-2">
                      <v-icon size="36">{{ isPlaying ? 'mdi-pause' : 'mdi-play' }}</v-icon>
                    </v-btn>
                    <v-btn icon @click="playNextTrack" color="white">
                      <v-icon>mdi-fast-forward</v-icon>
                    </v-btn>
                  </div>
                  
                  <!-- 빈 공간 (우측) -->
                  <div class="empty-space"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 재생목록 확장 패널 -->
        <v-expansion-panels class="playlist-panel my-3" v-model="expandedPlaylist" multiple>
          <v-expansion-panel>
            <v-expansion-panel-header>
              <div class="d-flex align-center">
                <v-icon left>mdi-playlist-music</v-icon>
                <span>재생목록</span>
                <v-chip class="ml-2" x-small>
                  {{ playlistItems.length }}{{ hasMorePlaylistItems ? '+' : '' }}
                </v-chip>
              </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <vue-scroll class="playlist-scroll-container" ref="playlistScroll" @handle-scroll="onPlaylistScrollEvent">
                <div class="playlist-items">
                  <v-list dense>
                    <v-list-item
                      v-for="(item, index) in playlistItems"
                      :key="item.id"
                      :class="{'active-item': currentPlayingId === item.id}"
                      @click="playItem(item.id)"
                    >
                      <v-list-item-avatar size="40" class="mr-3">
                        <v-img :src="item.img_url"></v-img>
                        <div class="play-overlay d-flex align-center justify-center">
                          <v-icon v-if="currentPlayingId === item.id && isPlaying" size="18">mdi-pause</v-icon>
                          <v-icon v-else size="18">mdi-play</v-icon>
                        </div>
                      </v-list-item-avatar>
                      
                      <v-list-item-content>
                        <v-list-item-title class="subtitle-2 text-truncate">{{ item.title }}</v-list-item-title>
                        <v-list-item-subtitle class="caption text-truncate d-flex align-center">
                          <span>{{ formatTime(item.duration) }}</span>
                          <v-icon v-if="item.isLiked" size="14" color="red" class="ml-1">mdi-heart</v-icon>
                        </v-list-item-subtitle>
                      </v-list-item-content>
                      
                      <v-list-item-action>
                        <v-chip x-small>
                          <v-icon left size="12">mdi-comment-outline</v-icon>
                          {{ item.commentCount }}
                        </v-chip>
                      </v-list-item-action>
                    </v-list-item>
                    
                    <!-- 추가 로딩 표시기 -->
                    <div v-if="isLoadingMorePlaylist" class="text-center py-2 loading-more">
                      <v-progress-circular indeterminate color="purple" size="20"></v-progress-circular>
                      <div class="mt-1 grey--text text-caption">더 많은 캐스트 로딩 중...</div>
                    </div>
                    
                    <!-- 모든 캐스트를 로드한 경우 표시 -->
                    <div v-if="playlistItems.length > 0 && !hasMorePlaylistItems && !isLoadingMorePlaylist" class="text-center py-2 grey--text text-caption">
                      모든 캐스트를 로드했습니다
                    </div>
                  </v-list>
                </div>
              </vue-scroll>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
        
        <!-- 캐스트 정보 영역 -->
        <div class="cast-info-section">
          <div class="cast-title-container">
            <h1 class="cast-title">{{ cast ? cast.title : '로딩 중...' }}</h1>
            <div class="cast-actions">
              <v-btn icon @click="toggleLike">
                <v-icon :color="cast && cast.is_like ? 'red' : ''">mdi-heart</v-icon>
              </v-btn>
              <v-btn icon @click="shareUrl">
                <v-icon>mdi-share-variant</v-icon>
              </v-btn>
            </div>
          </div>
          
          <div class="cast-stats">
            <div class="stat-item">
              <v-icon size="16" color="grey darken-1" class="mr-1">mdi-heart</v-icon>
              <span>{{ cast ? cast.like_count : 0 }}</span>
            </div>
            <div class="stat-item">
              <v-icon size="16" color="grey darken-1" class="mr-1">mdi-comment-outline</v-icon>
              <span>{{ cast ? cast.text_comment_count : 0 }}</span>
            </div>
            <div class="stat-item">
              <v-icon size="16" color="grey darken-1" class="mr-1">mdi-play</v-icon>
              <span>{{ cast ? cast.play_count : 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="date">{{ cast ? formatCastDate(cast.created) : '' }}</span>
            </div>
          </div>
          
          <div v-if="cast && cast.author" class="cast-author">
            <v-avatar size="48" class="mr-3" @click="goToUserPage(cast.author.id)">
              <v-img :src="cast.author.profile_url"></v-img>
            </v-avatar>
            <div class="author-info">
              <div class="author-name" @click="goToUserPage(cast.author.id)">{{ cast.author.nickname }}</div>
              <div class="author-tag">@{{ cast.author.tag }}</div>
            </div>
            <v-btn
              rounded
              small
              :color="isFollowingAuthor ? 'purple' : 'purple darken-1'"
              :outlined="isFollowingAuthor"
              :dark="!isFollowingAuthor"
              @click="toggleFollowAuthor"
              class="ml-auto follow-button"
            >
              <v-icon left size="16">{{ isFollowingAuthor ? 'mdi-account-check' : 'mdi-account-plus' }}</v-icon>
              <span>팬</span>
            </v-btn>
          </div>
        </div>
        
        <!-- 댓글 섹션 -->
        <div class="comments-section">
          <h2 class="comments-title">댓글 {{ comments.length }}개</h2>
          
          <!-- 댓글 작성 -->
          <div class="comment-input-container">
            <v-avatar size="36" class="mr-3">
              <v-img :src="userProfileUrl"></v-img>
            </v-avatar>
            <v-text-field
              v-model="newComment"
              placeholder="댓글 추가..."
              outlined
              dense
              hide-details
              class="comment-input"
              @keyup.enter="addComment"
            ></v-text-field>
            <v-btn text color="primary" @click="addComment" :disabled="!newComment.trim()">
              게시
            </v-btn>
          </div>
          
          <!-- 댓글 목록 -->
          <vue-scroll class="comments-scroll-container">
            <div class="comments-list">
              <div v-if="comments.length === 0 && !commentsLoading" class="no-comments">
                아직 댓글이 없습니다. 첫 번째 댓글을 작성해보세요!
              </div>
              <v-progress-circular v-if="commentsLoading" indeterminate color="primary"></v-progress-circular>
              
              <div v-for="comment in comments" :key="comment.id" class="comment-item">
                <div class="avatar-fixed-container">
                  <div class="live-profile-wrapper" @click="goToUserPage(comment.author.id)">
                    <div class="live-profile-image" 
                      :class="{'live-border': comment.author.current_live !== null}"
                      :style="{backgroundImage: `url(${comment.author.profile_url})`}">
                    </div>
                    <div v-if="comment.author.current_live !== null" class="live-indicator">LIVE</div>
                  </div>
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <div class="d-flex align-center">
                      <div class="comment-author" @click="goToUserPage(comment.author.id)">{{ comment.author.nickname }}</div>
                      <div class="comment-date ml-2">{{ formatCommentDate(comment.created) }}</div>
                    </div>
                    <!-- 본인 댓글일 경우 메뉴 표시 -->
                    <v-menu v-if="isMyComment(comment)" offset-y left>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn icon x-small v-bind="attrs" v-on="on">
                          <v-icon size="16" color="grey darken-1">mdi-dots-horizontal</v-icon>
                        </v-btn>
                      </template>
                      <v-list dense>
                        <v-list-item @click="deleteComment(comment.id)">
                          <v-list-item-title>삭제</v-list-item-title>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </div>
                  <div class="comment-text">
                    <template v-if="comment.to_user">
                      <span class="mention-user" @click="goToUserPage(comment.to_user.id)">@{{ comment.to_user.nickname }}</span>
                      {{ comment.contents }}
                    </template>
                    <template v-else>
                      {{ comment.contents }}
                    </template>
                  </div>
                  <div class="comment-actions">
                    <!-- 본인 댓글이 아닌 경우에만 답글 버튼 표시 -->
                    <v-btn 
                      v-if="!isMyComment(comment)" 
                      text 
                      x-small 
                      color="grey darken-1" 
                      @click="replyToComment(comment)"
                    >
                      답글
                    </v-btn>
                  </div>
                  
                  <!-- 답글 입력창 -->
                  <div v-if="replyTo === comment.id" class="reply-input-container mt-2">
                    <v-text-field
                      v-model="replyContent"
                      placeholder="답글 추가..."
                      outlined
                      dense
                      hide-details
                      class="reply-input"
                      @keyup.enter="submitReply"
                    ></v-text-field>
                    <div class="reply-actions">
                      <v-btn text small color="grey" @click="cancelReply">취소</v-btn>
                      <v-btn text small color="primary" @click="submitReply" :disabled="!replyContent.trim()">
                        답글
                      </v-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </vue-scroll>
        </div>
      </v-container>
    </vue-scroll>
    
    <!-- 공유 스낵바 -->
    <v-snackbar
      v-model="showShareSnackbar"
      :timeout="2000"
      color="success"
      bottom
    >
      URL이 클립보드에 복사되었습니다
      <template v-slot:action="{ attrs }">
        <v-btn
          text
          v-bind="attrs"
          @click="showShareSnackbar = false"
        >
          닫기
        </v-btn>
      </template>
    </v-snackbar>
  </v-main>
</template>

<script lang="ts">
import { Component, Mixins, Vue, Watch } from 'vue-property-decorator';
import { User } from '@sopia-bot/core';
import GlobalMixins from '@/plugins/mixins';

@Component
export default class CastPage extends Mixins(GlobalMixins) {
  public cast: any = null;
  public comments: any[] = [];
  public isPlaying: boolean = false;
  public currentTime: number = 0;
  public duration: number = 0;
  public progress: number = 0;
  public commentsLoading: boolean = false;
  public newComment: string = '';
  public userProfileUrl: string = 'https://via.placeholder.com/36'; // 기본 프로필 이미지
  public showShareSnackbar: boolean = false;
  public playlistItems: any[] = [];
  public currentPlayingId: number = 0;
  public currentPlaylistIndex: number = -1; // 현재 재생 중인 트랙의 인덱스
  public expandedPlaylist: number[] = [0]; // 재생목록 패널 초기 상태 (열림)
  public _scrollTimeout: any = null; // 스크롤 타이머 저장용 변수
  
  // 무한 스크롤을 위한 추가 속성
  public playlistOffset: string = ''; // 다음 페이지 로드를 위한 offset 값
  public hasMorePlaylistItems: boolean = true; // 더 로드할 캐스트가 있는지 여부
  public isLoadingMorePlaylist: boolean = false; // 추가 로드 중인지 표시
  public playlistScrollContainer: any = null; // 스크롤 컨테이너 참조
  public boundHandleScroll: any = null; // 스크롤 이벤트 핸들러 참조
  public _lastLoadTime: number = 0; // 마지막 로드 시간 (디바운싱)
  public _lastScrollTime: number = 0; // 마지막 스크롤 시간 (스로틀링)
  
  // 음량 관련 상태 추가
  public volume: number = 0; // 기본 음량 0%
  public isMuted: boolean = true; // 기본 뮤트 상태
  
  public replyTo: number | null = null; // 답글 대상 댓글 ID
  public replyingToUser: any = null; // 답글 대상 사용자
  public replyContent: string = ''; // 답글 내용
  
  get isFollowingAuthor(): boolean {
    if (!this.cast || !this.cast.author) return false;
    return this.cast.author.follow_status === 1 || this.cast.author.follow_status === 3;
  }
  
  get audioPlayer(): HTMLAudioElement {
    return this.$refs.audioPlayer as HTMLAudioElement;
  }
  
  @Watch('$route')
  onRouteChange() {
    this.loadCastData();
  }
  
  created() {
    // 타이틀바의 검색창과 프로필 이미지 숨기기
    this.$evt.$emit('hide-titlebar-elements', true);
    
    // 현재 사용자 프로필 불러오기
    if (window.$sopia && window.$sopia.logonUser) {
      this.userProfileUrl = window.$sopia.logonUser.profile_url;
    }
    
    // localStorage에서 음량 설정 불러오기
    this.loadVolumeSettings();
    
    this.loadCastData();
  }
  
  mounted() {
    // 타이틀바 요소 숨김 상태 설정 (검색창과 프로필 이미지 숨김)
    this.$evt.$emit('hide-titlebar-elements', true);
    
    // 패널이 확실하게 열리도록 약간의 지연 후 상태 다시 설정
    setTimeout(() => {
      this.expandedPlaylist = [0];
      
      // 스크롤 이벤트 리스너 설정 (패널이 열린 후)
      setTimeout(() => {
        this.setupScrollListeners();
      }, 300);
    }, 100);
    
    // 오디오 초기화 - 명시적으로 음소거 설정
    this.$nextTick(() => {
      if (this.audioPlayer) {
        this.audioPlayer.volume = 0;
        this.audioPlayer.muted = true;
        console.log('오디오 초기화: 음소거 설정 완료');
      }
    });
  }
  
  beforeDestroy() {
    // 타이틀바의 검색창과 프로필 이미지 복원
    this.$evt.$emit('hide-titlebar-elements', false);
    
    // 컴포넌트 제거 시 이벤트 해제
    this.removeScrollListeners();
    
    // 페이지 벗어날 때 오디오 정지
    if (this.audioPlayer) {
      this.audioPlayer.pause();
      this.audioPlayer.currentTime = 0;
    }
  }
  
  async loadCastData() {
    const castId = this.$route.params.id;
    if (!castId) return;
    
    try {
      // 캐스트 정보 불러오기
      const response = await fetch(`https://kr-api.spooncast.net/casts/${castId}/`);
      const data = await response.json();
      
      if (data && data.results && data.results.length > 0) {
        this.cast = data.results[0];
        this.currentPlayingId = this.cast.id;
        console.log('캐스트 정보:', this.cast);
        
        // 오디오 소스 설정
        if (this.cast.voice_url) {
          this.audioPlayer.src = this.cast.voice_url;
          this.audioPlayer.load();
          
          // 오디오 로드 후 저장된 음량 설정 적용
          this.audioPlayer.volume = this.volume / 100;
          this.audioPlayer.muted = this.isMuted;
          
          console.log(`오디오 로드: 음소거=${this.isMuted}, 볼륨=${this.volume}%`);
        }
        
        // 자동 재생
        this.play();
        
        // 재생목록 불러오기
        this.loadPlaylist(this.cast.author.id);
      }
      
      // 댓글 불러오기
      this.loadComments(castId);
      
    } catch (error) {
      console.error('캐스트 데이터 로드 중 오류 발생:', error);
    }
  }
  
  async loadComments(castId: string) {
    this.commentsLoading = true;
    
    try {
      const response = await fetch(`https://kr-api.spooncast.net/casts/${castId}/tcomments/`);
      const data = await response.json();
      
      if (data && data.results) {
        this.comments = data.results;
        console.log('댓글 정보:', this.comments);
      }
    } catch (error) {
      console.error('댓글 데이터 로드 중 오류 발생:', error);
    } finally {
      this.commentsLoading = false;
    }
  }
  
  async loadPlaylist(authorId: number, isLoadingMore: boolean = false) {
    if (!authorId) return;
    
    if (isLoadingMore) {
      this.isLoadingMorePlaylist = true;
      console.log('추가 재생목록 로드 중...');
    } else {
      // 처음 로드할 때는 기존 데이터 초기화
      console.log('재생목록 초기 로드 중...');
      this.playlistItems = [];
      this.playlistOffset = '';
      this.hasMorePlaylistItems = true;
    }
    
    try {
      // offset 값이 있으면 다음 페이지 로드를 위해 isNext=true, offset 파라미터 추가
      let apiUrl = `https://kr-gw.spooncast.net/feed/${authorId}/DJ?contentType=CAST&contentSource=GENERAL&excludeContentType=TALK`;
      
      if (isLoadingMore && this.playlistOffset) {
        apiUrl += `&isNext=true&offset=${this.playlistOffset}`;
        console.log(`다음 페이지 재생목록 API 호출: ${apiUrl}`);
      } else {
        apiUrl += '&isNext=false';
        console.log(`첫 페이지 재생목록 API 호출: ${apiUrl}`);
      }
      
      const response = await fetch(apiUrl);
      const data = await response.json();
      
      if (data && data.results) {
        // offset 값 저장
        this.playlistOffset = data.offset || '';
        console.log(`새로운 재생목록 offset 값 저장: ${this.playlistOffset}`);
        
        // 결과가 없거나 빈 배열이면 더 이상 로드할 데이터가 없음
        if (data.results.length === 0) {
          this.hasMorePlaylistItems = false;
          console.log('더 이상 로드할 재생목록 항목이 없습니다.');
        } else {
          // 캐스트 목록 정제
          const newItems = data.results
            .filter(item => item.contentData && item.contentData.id)
            .map(item => ({
              id: item.contentData.id,
              title: item.contentData.title,
              img_url: item.contentData.media[0],
              duration: item.contentData.duration,
              likeCount: item.contentData.likeCount,
              commentCount: item.contentData.commentCount,
              authorId: item.contentData.authorId,
              created: item.created,
              isLiked: item.likeStatus
            }));
          
          // 기존 목록에 추가
          if (isLoadingMore) {
            this.playlistItems = [...this.playlistItems, ...newItems];
            console.log(`${newItems.length}개의 재생목록 항목이 추가되었습니다. 총 ${this.playlistItems.length}개`);
          } else {
            this.playlistItems = newItems;
            console.log(`${newItems.length}개의 재생목록 항목이 로드되었습니다.`);
          }
          
          // 현재 재생 중인 트랙의 인덱스 찾기
          this.currentPlaylistIndex = this.playlistItems.findIndex(item => item.id === this.currentPlayingId);
          console.log(`현재 재생 중인 항목 인덱스: ${this.currentPlaylistIndex}`);
          
          // 모든 DOM 렌더링이 완료된 후 스크롤 조정 (첫 로드 시에만)
          if (!isLoadingMore) {
            this.$nextTick(() => {
              setTimeout(() => {
                this.scrollToCurrentTrack();
              }, 500);
            });
          }
        }
      } else {
        this.hasMorePlaylistItems = false;
        console.warn('재생목록 데이터가 없거나 예상 형식이 아닙니다.');
      }
    } catch (error) {
      console.error('재생목록 로드 중 오류 발생:', error);
      this.hasMorePlaylistItems = false;
    } finally {
      this.isLoadingMorePlaylist = false;
    }
  }
  
  scrollToCurrentTrack() {
    // 재생목록에 현재 곡이 없거나 패널이 닫혀있으면 무시
    if (this.currentPlaylistIndex === -1 || !this.expandedPlaylist.includes(0)) return;
    
    // 현재 곡 인덱스가 매우 작으면 (0, 1) 스크롤을 맨 위로 설정
    if (this.currentPlaylistIndex < 2) {
      // 목록 맨 위를 보여줌 (0번 인덱스부터)
      this.$nextTick(() => {
        const playlistScroll = this.$refs.playlistScroll as any;
        if (playlistScroll) {
          playlistScroll.scrollTo({
            x: 0,
            y: 0
          });
        }
      });
      return;
    }
    
    // 현재 곡 이전 곡부터 목록이 보이도록 설정 (예: 7번째 곡이면 6번째부터 보임)
    const scrollToIndex = this.currentPlaylistIndex - 1;
    const itemHeight = 56; // 재생목록 아이템 1개 높이
    const targetScrollPosition = scrollToIndex * itemHeight;
    
    // 스크롤 위치 설정
    this.$nextTick(() => {
      const playlistScroll = this.$refs.playlistScroll as any;
      if (playlistScroll) {
        // 직접 스크롤 위치 설정 (애니메이션 없이)
        playlistScroll.scrollTo({
          x: 0,
          y: targetScrollPosition
        });
        
        console.log(`재생목록 스크롤: ${scrollToIndex}번 곡부터 표시 (스크롤 위치: ${targetScrollPosition}px)`);
      }
    });
  }
  
  // 이전 트랙 재생
  playPreviousTrack() {
    if (this.playlistItems.length === 0 || this.currentPlaylistIndex === -1) return;
    
    // 현재 트랙이 첫 번째 트랙이면 마지막 트랙으로 이동
    const newIndex = this.currentPlaylistIndex === 0 
                   ? this.playlistItems.length - 1 
                   : this.currentPlaylistIndex - 1;
    
    const prevTrack = this.playlistItems[newIndex];
    if (prevTrack && prevTrack.id) {
      this.$router.push(`/cast/${prevTrack.id}`);
    }
  }
  
  // 다음 트랙 재생
  playNextTrack() {
    if (this.playlistItems.length === 0 || this.currentPlaylistIndex === -1) return;
    
    // 현재 트랙이 마지막 트랙이면 첫 번째 트랙으로 이동
    const newIndex = this.currentPlaylistIndex === this.playlistItems.length - 1 
                   ? 0 
                   : this.currentPlaylistIndex + 1;
    
    const nextTrack = this.playlistItems[newIndex];
    if (nextTrack && nextTrack.id) {
      this.$router.push(`/cast/${nextTrack.id}`);
    }
  }
  
  play() {
    // 재생 전에 현재 음소거 상태 확인
    const currentMuted = this.isMuted;
    
    // 오디오 요소에 명시적으로 음소거 상태 설정
    this.audioPlayer.muted = currentMuted;
    this.audioPlayer.volume = this.volume / 100;
    
    console.log(`재생 시작: 음소거=${currentMuted}, 볼륨=${this.volume}%`);
    
    this.audioPlayer.play().then(() => {
      this.isPlaying = true;
      
      // 재생 후에도 음소거 상태 재확인
      if (this.audioPlayer.muted !== currentMuted) {
        console.log('재생 후 음소거 상태 불일치 수정');
        this.audioPlayer.muted = currentMuted;
      }
    }).catch(error => {
      console.error('오디오 재생 중 오류 발생:', error);
    });
  }
  
  pause() {
    this.audioPlayer.pause();
    this.isPlaying = false;
  }
  
  togglePlay() {
    if (this.isPlaying) {
      this.pause();
    } else {
      this.play();
    }
  }
  
  seek(e: MouseEvent) {
    const audioPlayer = this.$refs.audioPlayer as HTMLAudioElement;
    if (!audioPlayer) return;

    const progressWrapper = e.currentTarget as HTMLElement;
    const rect = progressWrapper.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / progressWrapper.offsetWidth;
    
    // 새로운 시간 계산
    const newTime = pos * audioPlayer.duration;
    
    // 상태 업데이트
    this.currentTime = newTime;
    this.progress = (newTime / audioPlayer.duration) * 100;
    
    // 오디오 플레이어 시간 설정
    audioPlayer.currentTime = newTime;
    
    console.log(`클릭 위치: ${pos}, 새 시간: ${newTime}, 현재 오디오 시간: ${audioPlayer.currentTime}`);
  }
  
  onTimeUpdate() {
    this.currentTime = this.audioPlayer.currentTime;
    this.progress = (this.currentTime / this.duration) * 100;
  }
  
  onLoadedMetadata() {
    this.duration = this.audioPlayer.duration;
  }
  
  onEnded() {
    // 노래가 끝나면 다음 트랙으로 자동 재생
    this.isPlaying = false;
    this.currentTime = 0;
    this.progress = 0;
    
    // 다음 트랙 재생
    this.playNextTrack();
  }
  
  formatTime(seconds: number): string {
    if (isNaN(seconds) || !isFinite(seconds)) return '00:00';
    
    const min = Math.floor(seconds / 60);
    const sec = Math.floor(seconds % 60);
    
    return `${String(min).padStart(2, '0')}:${String(sec).padStart(2, '0')}`;
  }
  
  formatCastDate(dateStr: string): string {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  }
  
  formatCommentDate(dateStr: string): string {
    if (!dateStr) return '';
    
    const now = new Date();
    const commentDate = new Date(dateStr);
    const diffMinutes = Math.floor((now.getTime() - commentDate.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return '방금 전';
    if (diffMinutes < 60) return `${diffMinutes}분 전`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}시간 전`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 30) return `${diffDays}일 전`;
    
    const diffWeeks = Math.floor(diffDays / 7);
    if (diffWeeks < 100) return `${diffWeeks}주 전`;
    
    const diffYears = Math.floor(diffDays / 365);
    return `${diffYears}년 전`;
  }
  
  formatPlaylistDate(dateStr: string): string {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    return `${String(month).padStart(2, '0')}.${String(day).padStart(2, '0')}`;
  }
  
  async toggleLike() {
    if (!this.cast) return;
    
    try {
      const action = this.cast.is_like ? 'unlike' : 'like';
      const response = await this.$sopia.api.request({
        url: `/casts/${this.cast.id}/${action}/`,
        method: 'POST',
      });
      
      if (response && response.res && response.res.status_code === 200) {
        this.cast.is_like = !this.cast.is_like;
        if (this.cast.is_like) {
          this.cast.like_count += 1;
        } else {
          if (this.cast.like_count > 0) {
            this.cast.like_count -= 1;
          }
        }
      }
    } catch (error) {
      console.error('좋아요 토글 중 오류 발생:', error);
    }
  }
  
  async toggleFollowAuthor() {
    if (!this.cast || !this.cast.author) return;
    
    try {
      const isCurrentlyFollowing = this.isFollowingAuthor;
      const action = isCurrentlyFollowing ? 'unfollow' : 'follow';
      const response = await this.$sopia.api.request({
        url: `/users/${this.cast.author.id}/${action}/`,
        method: 'POST',
      });
      
      if (response && response.res && response.res.status_code === 200) {
        if (isCurrentlyFollowing) {
          this.cast.author.follow_status = 0;
          if (this.cast.author.follower_count > 0) {
            this.cast.author.follower_count -= 1;
          }
        } else {
          this.cast.author.follow_status = 1;
          this.cast.author.follower_count += 1;
        }
      }
    } catch (error) {
      console.error('작성자 팔로우 토글 중 오류 발생:', error);
    }
  }
  
  async addComment() {
    if (!this.newComment.trim()) return;
    
    try {
      // 기존 $sopia.api.request를 사용하는 대신 fetch API 직접 사용
      const token = window.$sopia?.token;
      if (!token) {
        throw new Error('인증 토큰이 없습니다. 로그인이 필요합니다.');
      }
      
      const response = await fetch(`https://kr-api.spooncast.net/casts/${this.cast.id}/tcomments/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          contents: this.newComment
        })
      });
      
      const responseData = await response.json();
      console.log('댓글 추가 응답:', responseData);
      
      if (response.ok) {
        // 댓글 목록 새로고침
        this.loadComments(this.cast.id);
        
        // 입력 필드 초기화
        this.newComment = '';
      } else {
        throw new Error('댓글 추가 실패: ' + JSON.stringify(responseData));
      }
    } catch (error) {
      console.error('댓글 추가 중 오류:', error);
    }
  }
  
  goToUserPage(userId: number) {
    this.$router.push(`/user/${userId}`);
  }
  
  async shareUrl() {
    if (!this.cast) return;
    
    const castId = this.cast.id;
    const authorId = this.cast.author ? this.cast.author.id : '';
    const shareUrl = `https://spooncast.net/kr/cast/${castId}?utm_source=spoon_share&utm_medium=referral&utm_campaign=cast_share&utm_content=${castId}&utm_term=${authorId}`;
    
    try {
      await navigator.clipboard.writeText(shareUrl);
      this.showShareSnackbar = true;
      console.log('URL 복사됨:', shareUrl);
    } catch (error) {
      console.error('URL 복사 중 오류 발생:', error);
      // 대체 복사 방법
      this.fallbackCopyTextToClipboard(shareUrl);
    }
  }
  
  fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 화면 밖으로 요소를 위치시킴
    textArea.style.position = 'fixed';
    textArea.style.left = '0';
    textArea.style.top = '0';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        this.showShareSnackbar = true;
      } else {
        console.error('복사 실패');
      }
    } catch (err) {
      console.error('복사 중 오류 발생:', err);
    }
    
    document.body.removeChild(textArea);
  }
  
  playItem(castId: number) {
    if (castId === this.currentPlayingId) {
      // 현재 재생 중인 아이템은 토글
      this.togglePlay();
    } else {
      // 새로운 아이템 재생
      this.$router.push(`/cast/${castId}`);
    }
  }
  
  // 스크롤 이벤트 리스너 설정
  setupScrollListeners() {
    // Vue-Scroll 컴포넌트는 @handle-scroll 이벤트를 사용하므로
    // 추가적인 DOM 이벤트 리스너가 필요하지 않음
    // 이 메서드는 이전 버전과의 호환성을 위해 유지
  }
  
  // 스크롤 이벤트 리스너 제거
  removeScrollListeners() {
    // Vue-Scroll은 컴포넌트가 제거될 때 자동으로 이벤트가 정리되므로
    // 추가 작업이 필요하지 않음
  }
  
  // 스로틀링된 스크롤 핸들러
  throttledHandleScroll(event: Event) {
    // 이미 로딩 중이거나 더 이상 로드할 데이터가 없으면 무시
    if (this.isLoadingMorePlaylist || !this.hasMorePlaylistItems) {
      return;
    }
    
    // 현재 시간 확인
    const now = Date.now();
    // 마지막 실행 시간과 비교하여 100ms 이상 지났는지 확인 (스로틀링)
    if (!this._lastScrollTime || now - this._lastScrollTime >= 100) {
      this._lastScrollTime = now;
      this.handlePlaylistScroll(event);
    }
  }
  
  // 스크롤 핸들러
  handlePlaylistScroll(event: Event) {
    const target = event.target as HTMLElement;
    const scrollHeight = target.scrollHeight;
    const scrollTop = target.scrollTop;
    const clientHeight = target.clientHeight;
    
    // 스크롤이 하단에서 50px 이내일 때 다음 페이지 로드
    if (scrollHeight - (scrollTop + clientHeight) < 50) {
      this.loadMorePlaylistItems();
    }
  }
  
  // 다음 페이지 로드
  loadMorePlaylistItems() {
    // 디바운싱 - 이미 로딩 중이거나 데이터가 없으면 무시
    if (this.isLoadingMorePlaylist || !this.hasMorePlaylistItems || !this.cast?.author?.id) {
      return;
    }
    
    // 디바운싱 - 최근 호출 시간 확인 (600ms 이내 중복 호출 방지)
    const now = Date.now();
    if (this._lastLoadTime && now - this._lastLoadTime < 600) {
      console.log('재생목록 로드 디바운싱: 최근에 이미 호출됨');
      return;
    }
    
    this._lastLoadTime = now;
    console.log('다음 페이지 재생목록 로드 시작 (작성자 ID: ' + this.cast.author.id + ', Offset: ' + this.playlistOffset + ')');
    this.loadPlaylist(this.cast.author.id, true);
  }
  
  @Watch('expandedPlaylist')
  onPlaylistExpanded(newVal: number[]) {
    // 패널이 열렸을 때 스크롤 조정 및 리스너 설정
    if (newVal && newVal.includes(0)) {
      // 패널 확장 애니메이션 완료 후 스크롤 및 리스너 설정
      setTimeout(() => {
        if (this.currentPlaylistIndex > -1) {
          this.scrollToCurrentTrack();
        }
        
        // 스크롤 리스너가 없으면 설정
        if (!this.boundHandleScroll) {
          this.setupScrollListeners();
        }
      }, 300);
    } else {
      // 패널이 닫히면 리스너 제거
      this.removeScrollListeners();
    }
  }
  
  // 새로운 스크롤 이벤트 핸들러 메서드 추가
  public onPlaylistScrollEvent(vertical: any, horizontal: any) {
    // 이미 로딩 중이거나 더 이상 로드할 데이터가 없으면 무시
    if (this.isLoadingMorePlaylist || !this.hasMorePlaylistItems) {
      return;
    }
    
    // 디바운싱 - 최근 호출 시간 확인 (300ms 이내 중복 호출 방지)
    const now = Date.now();
    if (this._lastScrollTime && now - this._lastScrollTime < 300) {
      return;
    }
    
    this._lastScrollTime = now;
    
    // 스크롤 위치가 하단에 가까워졌는지 확인 (85% 이상 스크롤 된 경우)
    if (vertical.process >= 0.85) {
      console.log('재생목록 스크롤 하단 도달: ' + vertical.process.toFixed(2));
      this.loadMorePlaylistItems();
    }
  }
  
  // 음소거 토글 메서드
  toggleMute() {
    this.isMuted = !this.isMuted;
    this.audioPlayer.muted = this.isMuted;
    
    // 음소거 해제 시 이전 볼륨으로 복원 (0이면 50%로 설정)
    if (!this.isMuted && this.volume === 0) {
      this.volume = 50;
      this.audioPlayer.volume = this.volume / 100;
    }
    
    // 설정 저장
    this.saveVolumeSettings();
    
    console.log(`음소거 ${this.isMuted ? '활성화' : '비활성화'}, 현재 볼륨: ${this.volume}%`);
  }
  
  // 음량 조절 메서드
  adjustVolume() {
    this.audioPlayer.volume = this.volume / 100;
    
    // 볼륨이 0이면 음소거 상태로, 그렇지 않으면 음소거 해제
    if (this.volume === 0) {
      this.isMuted = true;
      this.audioPlayer.muted = true;
    } else if (this.isMuted) {
      this.isMuted = false;
      this.audioPlayer.muted = false;
    }
    
    // 설정 저장
    this.saveVolumeSettings();
    
    console.log(`음량 조절: ${this.volume}%, 음소거: ${this.isMuted}`);
  }
  
  // 음량 설정 저장 메서드 추가
  saveVolumeSettings() {
    try {
      localStorage.setItem('castPlayerMuted', this.isMuted.toString());
      localStorage.setItem('castPlayerVolume', this.volume.toString());
    } catch (error) {
      console.error('음량 설정 저장 오류:', error);
    }
  }
  
  // 음량 설정 불러오기 메서드 추가
  loadVolumeSettings() {
    try {
      const savedMuted = localStorage.getItem('castPlayerMuted');
      const savedVolume = localStorage.getItem('castPlayerVolume');
      
      // 저장된 값이 있을 경우 사용
      if (savedMuted !== null) {
        this.isMuted = savedMuted === 'true';
      }
      
      if (savedVolume !== null) {
        this.volume = parseInt(savedVolume, 10);
      }
      
      console.log(`음량 설정 불러옴: 음소거=${this.isMuted}, 볼륨=${this.volume}%`);
    } catch (error) {
      console.error('음량 설정 불러오기 오류:', error);
    }
  }
  
  // 본인 댓글인지 확인하는 메서드 추가
  isMyComment(comment: any): boolean {
    // 현재 로그인한 사용자 ID와 댓글 작성자 ID 비교
    return window.$sopia && window.$sopia.logonUser && 
      window.$sopia.logonUser.id === comment.author.id;
  }
  
  // 댓글 삭제 메서드 수정 - 올바른 API 엔드포인트 사용
  async deleteComment(commentId: number) {
    if (!commentId) return;
    
    try {
      const token = window.$sopia?.token;
      if (!token) {
        throw new Error('인증 토큰이 없습니다. 로그인이 필요합니다.');
      }
      
      // 올바른 API 엔드포인트로 수정
      const response = await fetch(`https://kr-api.spooncast.net/casts/comments/${commentId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        console.log('댓글 삭제 성공');
        // 댓글 목록 새로고침
        this.loadComments(this.cast.id);
      } else {
        const responseData = await response.json();
        throw new Error('댓글 삭제 실패: ' + JSON.stringify(responseData));
      }
    } catch (error) {
      console.error('댓글 삭제 중 오류:', error);
    }
  }
  
  // 답글 작성 모드 전환
  replyToComment(comment: any) {
    this.replyTo = comment.id;
    this.replyingToUser = comment.author;
    this.replyContent = '';
  }
  
  // 답글 작성 취소
  cancelReply() {
    this.replyTo = null;
    this.replyingToUser = null;
    this.replyContent = '';
  }
  
  // 답글 제출
  async submitReply() {
    if (!this.replyContent.trim() || !this.replyingToUser) return;
    
    try {
      const token = window.$sopia?.token;
      if (!token) {
        throw new Error('인증 토큰이 없습니다. 로그인이 필요합니다.');
      }
      
      const response = await fetch(`https://kr-api.spooncast.net/casts/${this.cast.id}/tcomments/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          contents: this.replyContent,
          to_user_id: this.replyingToUser.id
        })
      });
      
      const responseData = await response.json();
      console.log('답글 추가 응답:', responseData);
      
      if (response.ok) {
        // 댓글 목록 새로고침
        this.loadComments(this.cast.id);
        
        // 입력 필드 초기화 및 답글 모드 종료
        this.cancelReply();
      } else {
        throw new Error('답글 추가 실패: ' + JSON.stringify(responseData));
      }
    } catch (error) {
      console.error('답글 추가 중 오류:', error);
    }
  }
}
</script>

<style scoped>
.cast-page {
  background-color: #f8f8f8;
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}

.cast-scroll-container {
  height: 100vh;
  width: 100%;
}

.main-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 16px;
}

/* 플레이어 정사각형 스타일 */
.player-wrapper {
  margin-bottom: 20px;
}

.player-square {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* 1:1 비율 (정사각형) */
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cast-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: transform 0.3s ease, filter 0.3s ease;
}

.cast-image-hover:hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.6));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.player-square:hover .overlay {
  opacity: 1;
}

/* 플레이어 컨트롤 스타일 */
.player-controls-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.center-play-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button-large {
  transform: scale(1.5);
  background-color: rgba(0, 0, 0, 0.3);
  transition: background-color 0.2s;
}

.play-button-large:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.bottom-controls {
  padding: 20px;
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
}

.player-controls-hover {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.player-square:hover .player-controls-hover {
  opacity: 1;
}

.progress-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
}

.time-info {
  color: white;
  font-size: 12px;
  width: 40px;
}

.progress-wrapper {
  position: relative;
  flex: 1;
  margin: 0 8px;
  cursor: pointer;
  z-index: 2;
  height: 20px;
  display: flex;
  align-items: center;
}

.progress-bar {
  width: 100%;
}

.control-buttons-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 캐스트 정보 섹션 */
.cast-info-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.cast-title-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.cast-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.cast-actions {
  display: flex;
}

.cast-stats {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 14px;
  color: #555;
}

.date {
  color: #777;
}

.cast-author {
  display: flex;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  cursor: pointer;
}

.author-tag {
  font-size: 12px;
  color: #777;
}

.follow-button {
  margin-left: auto;
}

/* 댓글 섹션 */
.comments-section {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 40px;
  position: relative;
}

.comments-scroll-container {
  max-height: none;
  overflow: visible;
}

.comments-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
}

.comment-input-container {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.comment-input {
  flex: 1;
  margin-right: 8px;
}

.comments-list {
  padding-top: 16px;
  padding-right: 8px; /* 스크롤바 공간 확보 */
}

.no-comments {
  text-align: center;
  color: #777;
  padding: 30px 0;
}

.comment-item {
  display: flex;
  margin-bottom: 24px;
}

.avatar-fixed-container {
  width: 54px;
  margin-right: 12px;
  flex-shrink: 0;
}

.live-profile-wrapper {
  position: relative;
  width: 54px;
  height: 54px;
  margin-bottom: 15px;
  cursor: pointer;
}

.live-profile-image {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
}

.live-border {
  border: 2px solid #FF3D00;
  box-sizing: border-box;
}

.live-indicator {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #FF3D00;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  width: 36px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.comment-author {
  font-weight: 600;
  cursor: pointer;
}

.comment-date {
  font-size: 12px;
  color: #777;
}

.comment-text {
  font-size: 14px;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.comment-actions {
  display: flex;
  align-items: center;
}

/* 반응형 스타일 */
@media (max-width: 600px) {
  .main-container {
    padding: 12px;
  }
  
  .cast-title {
    font-size: 18px;
  }
  
  .cast-info-section,
  .comments-section {
    padding: 16px;
  }
  
  .time-info {
    width: 35px;
  }
}

/* 커스텀 스크롤바 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(144, 85, 253, 0.5);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 85, 253, 0.8);
}

/* Firefox 스크롤바 대응 */
html {
  scrollbar-color: rgba(144, 85, 253, 0.5) rgba(0, 0, 0, 0.05);
  scrollbar-width: thin;
}

/* 재생목록 패널 */
.playlist-panel {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.playlist-panel >>> .v-expansion-panel-header {
  padding: 12px 16px;
  min-height: auto;
}

.playlist-panel >>> .v-expansion-panel::before {
  box-shadow: none;
}

.playlist-panel >>> .v-expansion-panel {
  background-color: white;
}

.playlist-scroll-container {
  max-height: 350px;
}

.playlist-items .v-list-item {
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.playlist-items .v-list-item:hover {
  background-color: rgba(0, 0, 0, 0.03) !important;
}

.playlist-items .active-item {
  background-color: rgba(103, 58, 183, 0.1) !important;
}

.v-list-item-avatar {
  position: relative;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  color: white;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.v-list-item-avatar:hover .play-overlay,
.active-item .play-overlay {
  opacity: 1;
}

.loading-more {
  margin: 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
}

/* 볼륨 슬라이더 관련 스타일 수정 */
.volume-button-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.volume-slider-wrapper {
  position: absolute;
  left: 40px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
  z-index: 10;
}

.volume-button-wrapper:hover .volume-slider-wrapper {
  width: 80px;
  opacity: 1;
}

.volume-slider-container {
  width: 80px;
  padding-left: 8px;
  background-color: transparent;
  height: 36px;
  display: flex;
  align-items: center;
}

.volume-slider {
  margin: 0;
  height: 24px !important;
}

.volume-slider >>> .v-slider__thumb {
  width: 12px;
  height: 12px;
  cursor: pointer;
}

.volume-slider >>> .v-slider__track-container {
  height: 4px;
}

/* 모바일 대응 유지 */
@media (max-width: 600px) {
  .volume-controls {
    min-width: 90px;
  }
  
  .empty-space {
    min-width: 90px;
  }
  
  .volume-button-wrapper:hover .volume-slider-wrapper {
    width: 60px;
  }
  
  .volume-slider-container {
    width: 60px;
  }
}

/* 컨트롤 버튼 컨테이너 수정 */
.control-buttons-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 음량 컨트롤 영역 */
.volume-controls {
  display: flex;
  align-items: center;
  min-width: 120px;
}

/* 재생 컨트롤 버튼 (중앙 정렬) */
.playback-controls {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 빈 공간 (우측 균형을 위한 공간) */
.empty-space {
  min-width: 120px;
}

.mention-user {
  color: #FF7900; /* 주황색 */
  font-weight: 800;
  margin-right: 4px;
  cursor: pointer;
}

.mention-user:hover {
  text-decoration: underline;
}

/* 답글 입력창 스타일 */
.reply-input-container {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  border-left: 2px solid #e0e0e0;
  padding-left: 12px;
}

.reply-input {
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}
</style> 