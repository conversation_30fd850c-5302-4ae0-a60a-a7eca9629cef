chrome.runtime.onMessage.addListener(
    function(req, sender, res) {
        if ( sender.id !== chrome.runtime.id ) {
            return;
        }

        const crCode = sessionStorage.SPOONCAST_countryCode.toUpperCase();
        const key = `SPOONCAST_${crCode}_userInfo`;
        const refTokenKey = `SPOONCAST_${crCode}_refreshToken`;
        if ( req.method === 'getUserInfo' ) {
            try {
                const item = localStorage.getItem(key) || '';
                if ( item.trim() === '' ) {
                    throw new Error('Can not find ' + key);
                }

                const refKey = localStorage.getItem(refTokenKey) || '';
                const data = JSON.parse(item);
                data.refresh_token = refKey;
                res({
                    success: true,
                    data,
                });
            } catch {
                res({
                    success: false,
                });
            }
        }
    }
);