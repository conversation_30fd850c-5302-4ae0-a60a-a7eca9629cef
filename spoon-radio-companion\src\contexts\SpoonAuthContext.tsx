import React, { createContext, useContext, useState, useEffect, ReactNode, useRef, useCallback } from 'react';
import { db } from '../firebaseConfig';
import { doc, getDoc, setDoc, deleteDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { useAuthContext } from './AuthContext';

interface SpoonUser {
  id: number;
  nickname: string;
  profile_url: string;
  tag: string;
}

interface SpoonAuthContextType {
  spoonUser: SpoonUser | null;
  spoonToken: string | null;
  isSpoonAuthenticated: boolean;
  authenticateWithSpoon: () => void;
  logout: () => void;
  loading: boolean;
  onNavigateToGuide?: () => void;
}

const SpoonAuthContext = createContext<SpoonAuthContextType | undefined>(undefined);

interface SpoonAuthProviderProps {
  children: ReactNode;
  onNavigateToGuide?: () => void;
}

// 사용자 IP 가져오기
const getUserIP = async (): Promise<string> => {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.error('IP 조회 실패:', error);
    return 'unknown';
  }
};

// 사용자 계정별 토큰 조회 (다중 IP 지원)
const getTokenByUserId = async (userId: string): Promise<{ token: string; user: SpoonUser; ip: string; } | null> => {
  try {
    const tokensRef = collection(db, 'spoon_tokens');
    const q = query(tokensRef, where('linkedUserId', '==', userId));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      // 가장 최근 토큰 선택
      let latestDoc: any = null;
      let latestTimestamp = 0;
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.timestamp > latestTimestamp) {
          latestTimestamp = data.timestamp;
          latestDoc = { id: doc.id, ...data };
        }
      });
      
      if (latestDoc) {
        const user: SpoonUser = {
          id: typeof latestDoc.user?.id === 'string' ? parseInt(latestDoc.user.id) : latestDoc.user?.id || parseInt(latestDoc.userId || '0'),
          nickname: latestDoc.user?.nickname || latestDoc.username || '',
          profile_url: latestDoc.user?.profile_url || '',
          tag: latestDoc.user?.tag || ''
        };
        
        return {
          token: latestDoc.token,
          user: user,
          ip: latestDoc.id // 문서 ID가 IP
        };
      }
    }
    return null;
  } catch (error) {
    console.error('사용자별 토큰 조회 실패:', error);
    return null;
  }
};

// Firestore에서 IP별 토큰 조회
const getTokenFromFirestore = async (ip: string): Promise<{ token: string; user: SpoonUser; } | null> => {
  try {
    const tokenDocRef = doc(db, 'spoon_tokens', ip);
    const tokenDocSnap = await getDoc(tokenDocRef);
    
    if (tokenDocSnap.exists()) {
      const data = tokenDocSnap.data();
      
      // tamm-v1 형식과 spoon-radio-companion 형식 모두 지원
      const user: SpoonUser = {
        id: typeof data.user?.id === 'string' ? parseInt(data.user.id) : data.user?.id || parseInt(data.userId || '0'),
        nickname: data.user?.nickname || data.username || '',
        profile_url: data.user?.profile_url || '',
        tag: data.user?.tag || ''
      };
      
      return {
        token: data.token,
        user: user
      };
    }
    return null;
  } catch (error) {
    console.error('Firestore 토큰 조회 실패:', error);
    return null;
  }
};

// Firestore에서 IP별 토큰 삭제
const deleteTokenFromFirestore = async (ip: string): Promise<boolean> => {
  try {
    const tokenDocRef = doc(db, 'spoon_tokens', ip);
    await deleteDoc(tokenDocRef);
    console.log('✅ IP별 토큰 Firestore 삭제 성공:', ip);
    return true;
  } catch (error) {
    console.error('IP별 토큰 Firestore 삭제 실패:', error);
    return false;
  }
};

// Firestore에 IP별 토큰 저장 (사용자 계정 연결 포함)
const saveTokenToFirestore = async (ip: string, token: string, user: SpoonUser, linkedUserId?: string): Promise<boolean> => {
  try {
    const tokenDocRef = doc(db, 'spoon_tokens', ip);
    const tokenData = {
      // tamm-v1 호환 형식으로 저장
      token,
      refreshToken: '', // 리프레시 토큰이 있다면 추가
      username: user.nickname,
      userId: user.id.toString(),
      timestamp: Date.now(),
      userIP: ip,  // IP 추가
      user: {
        id: user.id.toString(),  // 문자열로 저장
        nickname: user.nickname,
        tag: user.tag,
        profile_url: user.profile_url
      },
      // 사용자 계정 연결 정보 추가
      linkedUserId: linkedUserId || null,
      linkedAt: linkedUserId ? new Date() : null,
      // spoon-radio-companion 호환성을 위한 추가 필드
      createdAt: new Date(),
      lastUpdated: new Date()
    };
    
    await setDoc(tokenDocRef, tokenData);
    console.log('✅ IP별 토큰 Firestore 저장 성공:', ip, linkedUserId ? `(사용자 연결: ${linkedUserId})` : '');
    return true;
  } catch (error) {
    console.error('IP별 토큰 Firestore 저장 실패:', error);
    return false;
  }
};

// 기존 토큰을 사용자 계정과 연결
const linkTokenToUser = async (ip: string, userId: string): Promise<boolean> => {
  try {
    const tokenDocRef = doc(db, 'spoon_tokens', ip);
    const tokenDocSnap = await getDoc(tokenDocRef);
    
    if (tokenDocSnap.exists()) {
      const existingData = tokenDocSnap.data();
      await setDoc(tokenDocRef, {
        ...existingData,
        linkedUserId: userId,
        linkedAt: new Date(),
        lastUpdated: new Date()
      });
      console.log('✅ 토큰을 사용자 계정과 연결 성공:', ip, '→', userId);
      return true;
    }
    return false;
  } catch (error) {
    console.error('토큰 사용자 연결 실패:', error);
    return false;
  }
};

// 스푼 API 토큰 유효성 검사
const validateSpoonToken = async (token: string): Promise<boolean> => {
  try {
    // 스푼캐스트 API로 토큰 유효성 확인
    const response = await fetch('https://kr-api.spooncast.net/users/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.status === 403 || response.status === 401) {
      console.log('🔒 스푼 토큰 만료됨 (403/401)');
      return false;
    }
    
    return response.ok;
  } catch (error) {
    console.error('토큰 유효성 검사 실패:', error);
    return false;
  }
};

// 가이드 페이지 버전 관리
const GUIDE_VERSION = '2.6'; // 버전 업데이트

// 캐시 무효화를 위한 강력한 URL 생성
const createGuideUrl = (): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `/spoon-guide?v=${GUIDE_VERSION}&t=${timestamp}&r=${random}&cache=false`;
};

// 가이드 페이지 버전 체크 및 업데이트
const checkAndUpdateGuideVersion = () => {
  const lastVersion = localStorage.getItem('spoon_guide_version');
  if (lastVersion !== GUIDE_VERSION) {
    localStorage.setItem('spoon_guide_version', GUIDE_VERSION);
    // 버전이 변경되었을 때 추가 작업 수행 가능
    console.log(`🔄 스푼 가이드 버전 업데이트: ${lastVersion} → ${GUIDE_VERSION}`);
  }
};

export const SpoonAuthProvider: React.FC<SpoonAuthProviderProps> = ({ children, onNavigateToGuide }) => {
  const [spoonUser, setSpoonUser] = useState<SpoonUser | null>(null);
  const [spoonToken, setSpoonToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const messageHandlerRef = useRef<((event: MessageEvent) => void) | null>(null);
  const { user: authUser } = useAuthContext(); // 로그인된 사용자 정보

  useEffect(() => {
    const checkTokenOnLoad = async () => {
      try {
        setLoading(true);
        
        // 로그인된 사용자가 있으면 사용자별 토큰 먼저 확인 (다중 IP 지원)
        if (authUser?.uid) {
          console.log('🔍 로그인된 사용자 토큰 조회 시도:', authUser.uid);
          const userTokenData = await getTokenByUserId(authUser.uid);
          
          if (userTokenData) {
            // 토큰 유효성 검사
            const isValid = await validateSpoonToken(userTokenData.token);
            if (isValid) {
              console.log('✅ 사용자별 토큰 로드 성공 (IP:', userTokenData.ip + ')');
              setSpoonUser(userTokenData.user);
              setSpoonToken(userTokenData.token);
              localStorage.setItem('spoon_token', userTokenData.token);
              localStorage.setItem('spoon_user', JSON.stringify(userTokenData.user));
              setLoading(false);
              return; // 사용자 토큰이 유효하면 IP별 토큰 조회 건너뛰기
            } else {
              console.log('🔒 사용자별 토큰 만료됨 (보존함)');
              // 만료된 토큰은 삭제하지 않고 보존
            }
          }
        }
        
        // 사용자별 토큰이 없거나 만료된 경우에만 IP별 토큰 확인
        const ip = await getUserIP();
        console.log('🔍 현재 IP별 토큰 조회:', ip);
        
        const tokenData = await getTokenFromFirestore(ip);
        if (tokenData) {
          // 토큰 유효성 검사
          const isValid = await validateSpoonToken(tokenData.token);
          if (isValid) {
            console.log('✅ IP별 토큰 로드 성공');
            setSpoonUser(tokenData.user);
            setSpoonToken(tokenData.token);
            localStorage.setItem('spoon_token', tokenData.token);
            localStorage.setItem('spoon_user', JSON.stringify(tokenData.user));
            
            // 로그인된 사용자가 있고 토큰이 아직 연결되지 않은 경우 자동 연결
            if (authUser?.uid) {
              await linkTokenToUser(ip, authUser.uid);
            }
          } else {
            console.log('🔒 IP별 토큰 만료됨 (보존함), 로컬 저장소만 정리');
            localStorage.removeItem('spoon_token');
            localStorage.removeItem('spoon_user');
            // Firestore의 만료된 토큰은 삭제하지 않고 보존
          }
        }
      } catch (error) {
        console.error('토큰 로드 실패:', error);
      } finally {
        setLoading(false);
      }
    };

    // 초기 로드 시에만 실행 (authUser 변경 시 토큰 재조회 방지)
    if (authUser !== undefined) {
      checkTokenOnLoad();
    }
    
    checkAndUpdateGuideVersion();
  }, [authUser?.uid]); // authUser 전체가 아닌 uid만 의존성으로 설정

  // handleExtensionToken을 useCallback으로 메모이제이션
  const handleExtensionToken = useCallback(async (tokenData: any) => {
    try {
      console.log('🔄 확장프로그램 토큰 처리 시작:', tokenData);
      
      if (tokenData.token && tokenData.user) {
        const user: SpoonUser = {
          id: typeof tokenData.user.id === 'string' ? parseInt(tokenData.user.id) : tokenData.user.id,
          nickname: tokenData.user.nickname || tokenData.username || '',
          profile_url: tokenData.user.profile_url || '',
          tag: tokenData.user.tag || ''
        };

        // 토큰 유효성 검사
        const isValid = await validateSpoonToken(tokenData.token);
        if (!isValid) {
          console.error('❌ 받은 토큰이 유효하지 않음');
          return;
        }

        setSpoonUser(user);
        setSpoonToken(tokenData.token);
        localStorage.setItem('spoon_token', tokenData.token);
        localStorage.setItem('spoon_user', JSON.stringify(user));

        // IP별 Firestore 저장 (사용자 계정 연결 포함)
        const ip = await getUserIP();
        const linkedUserId = authUser?.uid || undefined; // 로그인된 사용자가 있으면 자동 연결
        await saveTokenToFirestore(ip, tokenData.token, user, linkedUserId);

        console.log('✅ 스푼 토큰 처리 완료');
        
        if (linkedUserId) {
          console.log('🔗 사용자 계정과 자동 연결됨:', linkedUserId);
        }
      }
    } catch (error) {
      console.error('확장프로그램 토큰 처리 실패:', error);
    }
  }, [authUser]);

  useEffect(() => {
    const handleMessage = async (event: MessageEvent) => {
      if (event.data.type === 'SPOON_TOKEN_RECEIVED') {
        console.log('🎯 확장프로그램에서 토큰 수신:', event.data);
        await handleExtensionToken(event.data.tokenData);
      }
    };

    messageHandlerRef.current = handleMessage;
    window.addEventListener('message', handleMessage);

    return () => {
      if (messageHandlerRef.current) {
        window.removeEventListener('message', messageHandlerRef.current);
      }
    };
  }, [handleExtensionToken]); // handleExtensionToken 의존성 추가

  const authenticateWithSpoon = async () => {
    try {
      console.log('🚀 스푼 인증 시작...');
      
      if (onNavigateToGuide) {
        onNavigateToGuide();
      } else {
        // 기본 동작: 새 창에서 가이드 페이지 열기
        const guideUrl = createGuideUrl();
        const fullUrl = `${window.location.origin}${guideUrl}`;
        
        console.log('🔗 가이드 페이지 URL:', fullUrl);
        
        window.open(fullUrl, '_blank', 'width=800,height=900,scrollbars=yes,resizable=yes');
      }
    } catch (error) {
      console.error('스푼 인증 실패:', error);
    }
  };

  const logout = async () => {
    setSpoonUser(null);
    setSpoonToken(null);
    localStorage.removeItem('spoon_token');
    localStorage.removeItem('spoon_user');
    
    // IP별 토큰은 삭제하지 않고 보존 (관리자가 수동으로 관리)
    console.log('🗑️ 로컬 저장소만 정리, Firestore 토큰은 보존');
  };

  const value: SpoonAuthContextType = {
    spoonUser,
    spoonToken,
    isSpoonAuthenticated: !!spoonUser && !!spoonToken,
    authenticateWithSpoon,
    logout,
    loading,
    onNavigateToGuide
  };

  return (
    <SpoonAuthContext.Provider value={value}>
      {children}
    </SpoonAuthContext.Provider>
  );
};

export const useSpoonAuth = (): SpoonAuthContextType => {
  const context = useContext(SpoonAuthContext);
  if (context === undefined) {
    throw new Error('useSpoonAuth must be used within a SpoonAuthProvider');
  }
  return context;
}; 