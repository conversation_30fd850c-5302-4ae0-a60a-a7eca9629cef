<template>
	<div :style="{ flexBasis: value ? '358px' : '138px', }">
		<!-- S:SendChat -->
		<v-row class="ma-0" align="center">
			<v-col cols="1" class="pl-2">
				<!-- 좋아요 버튼과 프로그레스 표시기 -->
				<div class="like-button-container">
					<v-progress-circular
						v-if="$store.state.liked"
						:value="likeProgressValue"
						color="red lighten-3"
						size="42"
						width="2"
						class="like-progress"
						reverse
					></v-progress-circular>
					<v-btn
						icon
						medium
						dark
						color="red lighten-3"
						@click="clickLike"
						:disabled="$store.state.liked"
						class="like-button"
					>
						<v-icon>mdi-heart</v-icon>
					</v-btn>
				</div>
			</v-col>
			<v-col cols="8" class="pr-0">
				<v-textarea
					:label="$t('lives.input-chat')"
					class="ml-2 chat-input"
					style="font-family: JoyPixels, GangwonEdu_OTFBoldA"
					@keydown="keyEvent"
					color="indigo lighten-4"
					no-resize dark hide-details solo dense
					rows="1"
					v-model="chat"></v-textarea>
			</v-col>
			<v-col cols="3" align="right" class="pl-0">
				<v-btn
					dark 
					depressed
					rounded
					class="mr-2 send-button"
					@click="sendMessage"
					color="#6200ea">
					{{ $t('send') }}
				</v-btn>
			</v-col>
		</v-row>
		<!-- E:SendChat -->
		<div>
			<v-row
				v-if="value"
				class="ma-0"
				style="position: relative; overflow-y: auto;"
				:style="{ height: menuHeight, maxHeight: menuHeight }">
				<v-col cols="3" v-for="(menu, idx) of menuList" :key="'menu'+idx" align="center">
					<component :is="menu" :live="live" :player="player"></component>
				</v-col> 
			</v-row>
		</div>
		
		<!-- 하단 알림 메시지 -->
		<v-snackbar
			v-model="showSnackbar"
			:timeout="1000"
			color="error"
			bottom
			class="like-error-snackbar"
		>
			{{ snackbarMessage }}
		</v-snackbar>
	</div>
</template>
<script lang="ts">
import { Component, Prop, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { Live } from '@sopia-bot/core';
import { Player } from './player';

@Component
export default class LivePlayerFooter extends Mixins(GlobalMixins) {
	@Prop(Object) public live!: Live;
	@Prop(Boolean) public value!: boolean;
	@Prop(String) public menuHeight!: string;
	@Prop(Object) public player!: Player;
	
	public chat: string = '';
	public likeProgressValue: number = 0; // 초기값 0%로 설정 (차오르는 느낌을 위해)
	public likeProgressInterval: number | null = null;
	public showSnackbar: boolean = false;
	public snackbarMessage: string = '';
	
	// 좋아요 쿨다운 상수
	private readonly LIKE_COOLDOWN_MS = 600000; // 10분 (600,000ms)
	private readonly LIKE_ERROR_COOLDOWN_MS = 60000; // 1분 (60,000ms)
	private readonly PROGRESS_UPDATE_INTERVAL_MS = 1000; // 1초마다 업데이트

	public menuList: any[] = [
		//() => import('./Menus/Volume.vue'), // 볼륨 컨트롤 제거
		() => import('./Menus/Like.vue'),
	];

	public keyEvent(evt: KeyboardEvent) {
		if ( !evt.shiftKey && evt.keyCode === 13 ) {
			// enter
			this.sendMessage();
			evt.preventDefault();
		}
	}

	public sendMessage() {
		if ( this.chat.trim() ) {
			const chat = this.chat
					.replace(/\\/g, '\\\\')
					.replace(/\n/g, '\\n');
			this.$logger.debug('live', `send message [${chat}]`);
			this.live.socket.message(chat);
			this.$nextTick(() => {
				this.chat = '';
				const scrollParent = this.$parent?.$parent?.$parent;
				if ( scrollParent ) {
					const scroll: any = scrollParent.$refs['scroll'];
					scroll.scrollBy({ dy: '100%' }, 100, 'easeInQuad');
				}
			});
		}
	}
	
	public async clickLike() {
		// 프로그레스 초기값을 0%로 설정
		this.likeProgressValue = 0;
		
		const req = await window.$sopia.api.lives.setLike(this.live);
		if (req.res.status_code !== 200) {
			// 스낵바로 에러 메시지 표시
			this.snackbarMessage = this.$t('lives.errors.fail-like').toString();
			this.showSnackbar = true;
			
			// 에러 발생 시 1분 동안 쿨다운 적용
			this.$store.state.liked = true;
			this.startLikeProgressAnimation(this.LIKE_ERROR_COOLDOWN_MS);
			
			// 1분 후 좋아요 상태 초기화
			setTimeout(() => {
				this.$store.state.liked = false;
				if (this.likeProgressInterval) {
					clearInterval(this.likeProgressInterval);
					this.likeProgressInterval = null;
				}
				this.likeProgressValue = 0;
			}, this.LIKE_ERROR_COOLDOWN_MS);
			
			return;
		}
		
		// 좋아요 상태 설정 및 프로그레스 시작
		this.$store.state.liked = true;
		this.startLikeProgressAnimation(this.LIKE_COOLDOWN_MS);
		
		// 10분 후 좋아요 상태 초기화 (프로그레스 완료 후)
		setTimeout(() => {
			this.$store.state.liked = false;
			if (this.likeProgressInterval) {
				clearInterval(this.likeProgressInterval);
				this.likeProgressInterval = null;
			}
			// 쿨다운 완료 후 프로그레스 값 다시 0%로 설정
			this.likeProgressValue = 0;
		}, this.LIKE_COOLDOWN_MS);
	}
	
	private startLikeProgressAnimation(duration: number = this.LIKE_COOLDOWN_MS) {
		// 기존 인터벌 클리어
		if (this.likeProgressInterval) {
			clearInterval(this.likeProgressInterval);
		}
		
		// 프로그레스 바 업데이트를 위한 변수
		const progressSteps = duration / this.PROGRESS_UPDATE_INTERVAL_MS;
		let currentStep = 0;
		
		// 1초마다 프로그레스 값 업데이트 (증가하는 방향으로)
		this.likeProgressInterval = window.setInterval(() => {
			currentStep++;
			// 0%에서 100%로 증가하도록 변경
			this.likeProgressValue = (currentStep / progressSteps) * 100;
			
			// 프로그레스가 완료되면 인터벌 종료
			if (this.likeProgressValue >= 100) {
				if (this.likeProgressInterval) {
					clearInterval(this.likeProgressInterval);
					this.likeProgressInterval = null;
				}
			}
		}, this.PROGRESS_UPDATE_INTERVAL_MS);
	}
	
	beforeDestroy() {
		// 컴포넌트 소멸 시 인터벌 정리
		if (this.likeProgressInterval) {
			clearInterval(this.likeProgressInterval);
		}
	}
}
</script>
<style scoped>
/* 좋아요 버튼 스타일 */
.like-button-container {
    position: relative;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.like-progress {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.like-button {
    position: absolute;
    z-index: 2;
}

.v-btn--icon.v-size--medium {
    height: 36px;
    width: 36px;
}

.v-btn--icon.v-size--medium .v-icon {
    height: 26px;
    font-size: 26px;
    width: 26px;
}

/* 채팅 입력창 스타일 */
.chat-input >>> .v-input__control .v-input__slot {
    border-radius: 24px !important;
    background-color: rgba(30, 30, 30, 0.7) !important;
    backdrop-filter: blur(2px);
}

/* 전송 버튼 스타일 */
.send-button {
    border-radius: 20px !important;
    text-transform: none;
    letter-spacing: 0.5px;
    font-weight: 500;
    font-size: 14px;
    height: 36px !important;
    min-width: 70px !important;
    padding: 0 16px !important;
}

/* 좋아요 에러 스낵바 스타일 */
.like-error-snackbar {
    margin-bottom: 15px;
    opacity: 0.9 !important;
}
</style>