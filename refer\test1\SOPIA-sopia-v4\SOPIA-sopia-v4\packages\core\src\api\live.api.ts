import { ContentInfo } from '../struct/info.struct'
import { Live } from '../struct/live.struct'
import { ApiHttpClient } from '../client/http.client'
import { Category } from '../const/category.const'

interface PageRequestOptions {
  page_size?: number
  page?: number
}

interface PopularRequestOptions extends PageRequestOptions {
  category?: Category
}

interface CreateLiveOptions {
  allow_donations: 'LUCKYBOX' | 'QUIZ'
  is_access_ghost_user: boolean
  is_adult: boolean
  is_save: boolean
  donation: number
  title: string
  type: number
  welcome_message: string
  invite_member_ids: number[]
  tags: string[]
  categories: Category[]
  engine: {
    name: 'sing' | 'sori'
    host: string
  }
  is_live_call: boolean
  device_unique_id: string
  spoon_aim: {
    title: string
    count: number
  }
  img_key?: string
}

export class LiveApi {
  constructor(private http: ApiHttpClient) {}

  getBanner() {
    return this.http.request(
      `/lives/banner`,
      {
        method: 'GET'
      },
      ContentInfo
    )
  }

  getPopular(options: PopularRequestOptions) {
    return this.http.request(
      `/lives/popular`,
      {
        method: 'GET',
        params: options
      },
      Live
    )
  }

  getSubscribed(options: PageRequestOptions) {
    return this.http.request(
      `/lives/subscribed`,
      {
        method: 'GET',
        params: options
      },
      Live
    )
  }

  getInfo(id: number) {
    return this.http.request(
      `/lives/${id}`,
      {
        method: 'GET'
      },
      Live
    )
  }

  createLive(options: CreateLiveOptions) {
    return this.http.request(
      `/lives`,
      {
        method: 'POST',
        body: options
      },
      Live
    )
  }
}
