/**
 * 배포용 환경변수 설정 관리
 */
export class EnvironmentConfig {
  
  /**
   * GitHub 토큰 가져오기 (배포용)
   */
  public static getGitHubToken(): string {
    // 1. 환경변수에서 우선 가져오기
    const envToken = process.env.GITHUB_TOKEN;
    if (envToken) {
      console.log('✅ 환경변수에서 GitHub 토큰을 찾았습니다.');
      return envToken;
    }

    // 2. Node.js 환경변수 재시도
    try {
      const nodeEnv = window.require('process').env.GITHUB_TOKEN;
      if (nodeEnv) {
        console.log('✅ Node.js 환경변수에서 GitHub 토큰을 찾았습니다.');
        return nodeEnv;
      }
    } catch (error) {
      console.warn('Node.js 환경변수 접근 실패:', error);
    }

    // 3. 설정 파일에서 가져오기 (개발용)
    try {
      const CfgLite = require('./cfg-lite-ipc').default;
      const path = window.require('path');
      const os = window.require('os');
      const cfgPath = path.join(os.homedir(), '.tamm-github.cfg');
      const cfg = new CfgLite(cfgPath);
      const configToken = cfg.get('github_token');
      
      if (configToken) {
        console.log('✅ 설정 파일에서 GitHub 토큰을 찾았습니다.');
        return configToken;
      }
    } catch (error) {
      console.warn('설정 파일에서 토큰 읽기 실패:', error);
    }

    console.error('⚠️ GitHub 토큰을 찾을 수 없습니다!');
    console.error('배포 환경에서는 GITHUB_TOKEN 환경변수가 필수입니다.');
    return '';
  }

  /**
   * GitHub 토큰 저장하기 (개발용)
   */
  public static saveGitHubToken(token: string): boolean {
    try {
      const CfgLite = require('./cfg-lite-ipc').default;
      const path = window.require('path');
      const os = window.require('os');
      const cfgPath = path.join(os.homedir(), '.tamm-github.cfg');
      const cfg = new CfgLite(cfgPath);
      
      cfg.set('github_token', token);
      cfg.save();
      
      console.log('💾 GitHub 토큰이 설정 파일에 저장되었습니다.');
      return true;
    } catch (error) {
      console.error('GitHub 토큰 저장 실패:', error);
      return false;
    }
  }

  /**
   * 배포 환경 확인
   */
  public static isProduction(): boolean {
    return process.env.NODE_ENV === 'production' || 
           process.env.ELECTRON_IS_DEV === 'false' ||
           !window.require('electron').remote;
  }

  /**
   * 환경 정보 출력
   */
  public static printEnvironmentInfo(): void {
    console.log('🔧 TAMM 환경 정보:');
    console.log(`📦 환경: ${this.isProduction() ? '프로덕션' : '개발'}`);
    console.log(`🔑 GitHub 토큰: ${this.getGitHubToken() ? '설정됨' : '미설정'}`);
    console.log(`🌍 Node 환경: ${process.env.NODE_ENV || '미정의'}`);
    
    if (!this.getGitHubToken()) {
      console.log('\n🚨 GitHub 토큰이 설정되지 않았습니다!');
      console.log('배포 환경에서는 다음 중 하나를 설정해주세요:');
      console.log('1. 환경변수: GITHUB_TOKEN=your_token');
      console.log('2. 개발 환경: EnvironmentConfig.saveGitHubToken("your_token")');
    }
  }

  /**
   * GitHub 설정 가이드 표시
   */
  public static showGitHubSetupGuide(): void {
    if (this.getGitHubToken()) {
      console.log('✅ GitHub 설정이 완료되었습니다!');
      return;
    }

    console.log('\n📋 GitHub 설정 가이드:');
    console.log('');
    console.log('1️⃣ GitHub에서 Personal Access Token 생성');
    console.log('   - GitHub → Settings → Developer settings → Personal access tokens');
    console.log('   - 권한: repo, contents');
    console.log('');
    console.log('2️⃣ 토큰 설정 (둘 중 하나 선택)');
    console.log('   개발 환경: EnvironmentConfig.saveGitHubToken("your_token")');
    console.log('   배포 환경: GITHUB_TOKEN 환경변수 설정');
    console.log('');
    console.log('3️⃣ 저장소 확인');
    console.log('   - 저장소: leekyuk/tamm-users');
    console.log('   - 설정: Private 권장');
    console.log('');
  }
}

// 앱 시작 시 환경 정보 출력
EnvironmentConfig.printEnvironmentInfo(); 