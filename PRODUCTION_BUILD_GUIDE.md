# 프로덕션 빌드 가이드

## 최적화된 빌드 명령어

### Windows 환경
```bash
# 프로덕션 빌드 (일반)
npm run window:build:prod

# 프로덕션 빌드 (Electron)
npm run electron:window:build:prod
```

### macOS/Linux 환경
```bash
# 프로덕션 빌드 (일반)
npm run build:prod

# 프로덕션 빌드 (Electron)
npm run electron:build:prod

# macOS 빌드
npm run electron:mac:build:prod
```

## 적용된 최적화 사항

### 1. 번들 크기 최적화
- 불필요한 파일 제외 (소스맵, 테스트 파일, 문서 등)
- 트리 쉐이킹을 통한 데드 코드 제거
- 벤더 청크 분리로 캐싱 최적화

### 2. 개발용 코드 제거
- 프로덕션에서 console.log 자동 제거
- 개발자 도구 비활성화
- 런타임 컴파일러 비활성화

### 3. 보안 강화
- webSecurity 활성화 (프로덕션)
- contextIsolation 활성화 (프로덕션)
- nodeIntegration 제한 (프로덕션)

### 4. 성능 최적화
- 코드 압축 및 최적화
- 소스맵 비활성화
- 백그라운드 스로틀링 비활성화

## 빌드 전 체크리스트

1. **환경 변수 확인**
   - NODE_ENV=production 설정 확인

2. **의존성 정리**
   ```bash
   npm audit
   npm run lint
   ```

3. **테스트 실행**
   ```bash
   npm test
   ```

4. **빌드 분석 (옵션)**
   ```bash
   npm run build:prod -- --report
   ```

## 빌드 후 검증

1. **번들 크기 확인**
   - dist 폴더 크기 확인
   - 불필요한 파일 포함 여부 확인

2. **기능 테스트**
   - 주요 기능 정상 동작 확인
   - 성능 이슈 확인

3. **보안 확인**
   - 개발자 도구 접근 불가 확인
   - 디버깅 정보 노출 여부 확인

## 빌드 성능 향상 팁

1. **의존성 관리**
   - 불필요한 패키지 제거
   - 가벼운 대안 패키지 사용

2. **코드 최적화**
   - 동적 import 활용
   - 지연 로딩 구현

3. **에셋 최적화**
   - 이미지 압축
   - 폰트 최적화 