import path from 'path';
import fs from 'fs';
import { app } from 'electron';


/* INIT DIR */
const bundleDir = path.join(app.getPath('userData'), 'bundles');
if ( !fs.existsSync(bundleDir) ) {
	fs.mkdirSync(bundleDir);
}

const tammDir = path.join(app.getPath('userData'), 'tamm');
if ( !fs.existsSync(tammDir) ) {
	fs.mkdirSync(tammDir);
}

app.commandLine.appendSwitch('disable-features', 'OutOfBlinkCors');
