/*
 * main.ts
 * Created on Sat Jul 18 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
 */
// native modules
import path from 'path';
import pkg from '../package.json';
const fs = window.require('fs');

console.log('[Main] App starting...');

// pkacage modules
import Vue from 'vue';
import * as spoon from '@sopia-bot/core';
import { v4 as uuidv4 } from 'uuid';
import LiquorTree from 'liquor-tree';
import VueScroll from 'vuescroll';
import Carousel3d from 'vue-carousel-3d';
import VueSweetalert2 from 'vue-sweetalert2';
const electron = window.require('electron');

import 'sweetalert2/dist/sweetalert2.min.css';

// custom modules
import router from '@/router';
import store from '@/store';
import vuetify from '@/plugins/vuetify';
import Logger from '@/plugins/logger';
import CfgLite from '@/plugins/cfg-lite-ipc';
import { SopiaAPI } from '@/plugins/sopia-api';
import axios, { AxiosInstance } from 'axios';

import App from '@/App.vue';
import { getAppPath, getStartTime } from './plugins/ipc-renderer';
const { ipcRenderer } = electron;

// 스크립트 리로딩 횟수 제한을 위한 카운터
let reloadScriptCounter = 0;
const MAX_RELOAD_COUNT = 5;

// 원래 reloadScript 함수 저장
const originalReloadScript = window.reloadScript;

// reloadScript 함수 감시 및 중복 호출 제한
window.reloadScript = function() {
	reloadScriptCounter++;
	console.log(`[DEBUG] reloadScript called (${reloadScriptCounter}/${MAX_RELOAD_COUNT})`);
	
	if (reloadScriptCounter > MAX_RELOAD_COUNT) {
		console.warn(`[DEBUG] Too many reloadScript calls (${reloadScriptCounter}). Ignoring.`);
		return;
	}
	
	// 직접 원래 함수 호출하기 (인자 없음)
	if (typeof originalReloadScript === 'function') {
		originalReloadScript();
	}
};

// Import custom toast styles
import './styles/custom-toast.css';

/* tslint:disable-next-line */
(document.querySelector('title') as any).innerText = `TAMM - ${pkg.version}`;

// Vue Use
Vue.use(VueSweetalert2);
Vue.use(Carousel3d);
Vue.use(Logger);
Vue.use(LiquorTree);
Vue.use(VueScroll, {
	ops: {
		rail: {
			background: 'transparent',
			opacity: 0,
			size: '0px',
			gutterOfEnds: null,
			gutterOfSide: null,
			keepShow: false
		},
		bar: {
			showDelay: 0,
			onlyShowBarOnScroll: false,
			keepShow: false,
			background: 'transparent',
			opacity: 0,
			hoverStyle: false,
			specifyBorderRadius: false,
			minSize: 0,
			size: '0px',
			disable: true
		},
		scrollPanel: {
			scrollingX: true,
			scrollingY: true
		}
	},
});

window.isElectron = true;
window.isDevelopment = ipcRenderer.sendSync('isdev');
const appCfgPath = path.join(getAppPath('userData'), 'app.cfg');
Vue.prototype.$cfg = window.appCfg = new CfgLite(appCfgPath);
Vue.prototype.$api = new SopiaAPI();
window.axios = axios;

// Axios 응답 인터셉터 설정 - 401 오류 시 자동 로그아웃
axios.interceptors.response.use(
	response => response,
	error => {
		// 401 Unauthorized 오류 처리
		if (error.response?.status === 401) {
			const requestUrl = error.config?.url || '';
			
			// 스푼캐스트 토큰 관련 API에서 401 오류 발생 시 자동 로그아웃
			if (requestUrl.includes('spooncast.net') && 
				(requestUrl.includes('/tokens/') || requestUrl.includes('/auth/'))) {
				
				console.log('토큰 인증 실패 - 자동 로그아웃 진행');
				
				// 로그아웃 처리
				if (window.logout) {
					window.logout();
				} else {
					// fallback 로그아웃 처리
					window.appCfg.delete('auth');
					window.appCfg.save();
					setTimeout(() => {
						window.location.reload();
					}, 100);
				}
			}
		}
		
		return Promise.reject(error);
	}
);

// Fetch API 인터셉터 설정 - 401 오류 시 자동 로그아웃
const originalFetch = window.fetch;
window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
	try {
		const response = await originalFetch(input, init);
		
		// 401 Unauthorized 오류 처리
		if (response.status === 401) {
			const requestUrl = input?.toString() || '';
			
			// 스푼캐스트 토큰 관련 API에서 401 오류 발생 시 자동 로그아웃
			if (requestUrl.includes('spooncast.net') && 
				(requestUrl.includes('/tokens/') || requestUrl.includes('/auth/'))) {
				
				console.log('토큰 인증 실패 (fetch) - 자동 로그아웃 진행');
				
				// 로그아웃 처리
				if (window.logout) {
					window.logout();
				} else {
					// fallback 로그아웃 처리
					window.appCfg.delete('auth');
					window.appCfg.save();
					setTimeout(() => {
						window.location.reload();
					}, 100);
				}
			}
		}
		
		return response;
	} catch (error) {
		return Promise.reject(error);
	}
};

const api = window.appCfg.get('api');
if ( api ) {
	if ( api.host ) {
		Vue.prototype.$api.host = api.host;
	}
	if ( api.protocol ) {
		Vue.prototype.$api.protocol = api.protocol;
	}
}

// Vue 프로덕션 설정
Vue.config.productionTip = false;
Vue.config.performance = process.env.NODE_ENV === 'development';

// 개발 환경에서만 Vue 개발자 도구 활성화
if (process.env.NODE_ENV === 'development') {
	Vue.config.devtools = true;
} else {
	Vue.config.devtools = false;
	Vue.config.silent = true;
}

window.$spoon = spoon;
Vue.prototype.$sopia = window.$sopia = new spoon.SpoonClient(uuidv4()); // TODO: set country

// Event Bus
Vue.prototype.$evt = new Vue();

declare global {
	interface Window {
		logger: any;
		appCfg: CfgLite;
		isElectron: boolean;
		isDevelopment: boolean;
		axios: AxiosInstance;
		logout: () => void;
		reloadScript: () => void;
	}
}
window.logger = Logger;

// config
Vue.config.errorHandler = function(err: any, vm: any, info) {
	Logger.critical('error', err);

	let str = '';
	str += `${new Date().toLocaleString()}\n`;
	str += `ReferenceError: ${err.message}\n`;
	if ( err.stack ) {
		str += `${err.stack.split('\n')[1]}\n`;
	}
	str += `    - ${vm.$options._componentTag}::${info}\n\n`;

	const logDir = path.join(getAppPath('userData'), 'logs');
	if ( !fs.existsSync(logDir) ) {
		fs.mkdirSync(logDir);
	}
	const sTime = getStartTime();
	const logFile = path.join(logDir, `${sTime}-error.log`);

	fs.appendFileSync(logFile, str, 'utf8');
};

// Firebase SDK에서 필요한 함수들을 임포트합니다.
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics"; // Google Analytics용 (선택 사항)
import { getFirestore } from 'firebase/firestore'; // Firebase Firestore 데이터베이스용
import { getAuth } from 'firebase/auth';         // Firebase Authentication 인증용

// 웹 앱의 Firebase 구성 (사용자님께서 제공해주신 값입니다.)
// Firebase JS SDK v7.20.0 이상에서는 measurementId가 선택 사항입니다.
const firebaseConfig = {
	apiKey: "AIzaSyDt1d7J8QS-mrRmVcNZ21AEd8f03ePL3Xs",
	authDomain: "tammapp-4cbb3.firebaseapp.com",
	projectId: "tammapp-4cbb3",
	storageBucket: "tammapp-4cbb3.firebasestorage.app",
	messagingSenderId: "839696613595",
	appId: "1:839696613595:web:cb4545c20a1558fdfeccfe",
	measurementId: "G-2M8HBKPLV4"
};

// Firebase 앱을 초기화합니다.
const app = initializeApp(firebaseConfig);

// Google Analytics를 초기화합니다. (필요 없으면 이 줄과 위의 import를 제거할 수 있습니다.)
const analytics = getAnalytics(app);

// 사용할 Firebase 서비스들(Firestore, Authentication)을 초기화하고 다른 모듈에서 임포트할 수 있도록 내보냅니다.
export const db = getFirestore(app);
export const auth = getAuth(app);

window.$sopia.init()
	.then(() => {
		console.log('[Main] SpoonClient initialized, creating Vue app');
		
		// 번들 로드 중 발생하는 설정 파일 접근을 추적하기 위한 코드
		if (window.isDevelopment) {
			// 원래 config get 함수 백업
			const originalConfigGet = window.appCfg.get;
			
			// 설정 파일 접근 모니터링
			window.appCfg.get = function(key) {
				const result = originalConfigGet.apply(this, [key]);
				console.log(`[Config Access] key=${key}, caller=${new Error().stack?.split('\n')[2]?.trim() || 'unknown'}`);
				return result;
			};
		}
		
		new Vue({
			router,
			store,
			vuetify,
			render: (h) => h(App),
		}).$mount('#app');
	});
