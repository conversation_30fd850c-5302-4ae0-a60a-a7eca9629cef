{"compilerOptions": {"target": "es2017", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "noImplicitAny": false, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env", "vuetify", "vue-sweetalert2"], "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "paths": {"assets/*": ["src/assets/*"], "@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules"]}