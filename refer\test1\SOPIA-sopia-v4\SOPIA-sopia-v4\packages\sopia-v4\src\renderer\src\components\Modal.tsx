import {
  Modal as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from '@heroui/react'
import { useTranslation } from 'react-i18next'
import React from 'react'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}

export const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  const { t } = useTranslation()

  return (
    <HeroModal isOpen={isOpen} scrollBehavior="inside" size="5xl" onClose={onClose}>
      <ModalContent>
        <ModalHeader>
          <h2 className="text-xl font-semibold">{title}</h2>
        </ModalHeader>
        <ModalBody>
          <div className="prose dark:prose-invert max-w-none">{children}</div>
        </ModalBody>
        <ModalFooter>
          <Button onPress={onClose}>{t('common.close')}</Button>
        </ModalFooter>
      </ModalContent>
    </HeroModal>
  )
}
