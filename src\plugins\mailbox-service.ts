import { authService } from './auth-service';

export interface MailboxMessage {
    id: string;
    sender_id: string;
    sender_name: string;
    sender_email: string;
    subject: string;
    content: string;
    created_at: string;
    read: boolean;
}

export class MailboxService {
    private messages: MailboxMessage[] = [];

    // 편지 목록 로드
    public async loadMessages(): Promise<MailboxMessage[]> {
        try {
            this.messages = await authService.getMessages();
            return this.messages;
        } catch (error) {
            console.error('편지 로드 오류:', error);
            return [];
        }
    }

    // 편지 읽음 처리
    public async markAsRead(messageId: string): Promise<boolean> {
        try {
            await authService.markMessageAsRead(messageId);
            const message = this.messages.find(m => m.id === messageId);
            if (message) {
                message.read = true;
            }
            return true;
        } catch (error) {
            console.error('편지 읽음 처리 오류:', error);
            return false;
        }
    }

    // 읽지 않은 편지 개수
    public getUnreadCount(): number {
        return this.messages.filter(msg => !msg.read).length;
    }

    // 편지 통계
    public getStats() {
        const total = this.messages.length;
        const unread = this.getUnreadCount();
        const read = total - unread;
        
        return { total, unread, read };
    }

    // 날짜 포맷팅
    public formatDate(dateString: string): string {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diff = now.getTime() - date.getTime();
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (days === 0) {
                return date.toLocaleTimeString('ko-KR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } else if (days === 1) {
                return '어제 ' + date.toLocaleTimeString('ko-KR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } else if (days < 7) {
                return `${days}일 전`;
            } else {
                return date.toLocaleDateString('ko-KR', {
                    month: '2-digit',
                    day: '2-digit'
                });
            }
        } catch {
            return '날짜 오류';
        }
    }

    // 편지 검색
    public searchMessages(query: string): MailboxMessage[] {
        if (!query.trim()) return this.messages;
        
        const lowerQuery = query.toLowerCase();
        return this.messages.filter(msg => 
            msg.subject.toLowerCase().includes(lowerQuery) ||
            msg.content.toLowerCase().includes(lowerQuery) ||
            msg.sender_name.toLowerCase().includes(lowerQuery)
        );
    }

    // 편지 정렬
    public sortMessages(messages: MailboxMessage[], sortBy: string, desc: boolean): MailboxMessage[] {
        return [...messages].sort((a, b) => {
            let aVal, bVal;
            
            switch (sortBy) {
                case 'created_at':
                    aVal = new Date(a.created_at).getTime();
                    bVal = new Date(b.created_at).getTime();
                    break;
                case 'subject':
                    aVal = a.subject.toLowerCase();
                    bVal = b.subject.toLowerCase();
                    break;
                case 'sender_name':
                    aVal = a.sender_name.toLowerCase();
                    bVal = b.sender_name.toLowerCase();
                    break;
                case 'read':
                    aVal = a.read ? 1 : 0;
                    bVal = b.read ? 1 : 0;
                    break;
                default:
                    return 0;
            }
            
            if (desc) {
                return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
            } else {
                return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
            }
        });
    }
}

export const mailboxService = new MailboxService(); 