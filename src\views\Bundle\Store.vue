<!--
 * Index.vue
 * Created on Thu Oct 28 2021
 *
 * Copyright (c) Raravel. Licensed under the MIT License.
-->
<template>
	<v-main class="pa-6 grey lighten-5" style="min-height: 100vh;">
		<!-- 헤더 섹션 -->
		<v-card class="mb-6 rounded-lg elevation-1" color="white">
			<v-card-text>
		<v-row class="ma-0" align="center">
					<v-col cols="12" md="6">
						<h1 class="text-h4 font-weight-bold primary--text">{{ $t('bundle.store.title') }}</h1>
						<p class="mt-2 grey--text text--darken-1">{{ $t('bundle.store.description') }}</p>
			</v-col>
					<v-col cols="12" md="6" align="right">
				<v-text-field
					v-model="searchText"
					solo
					single-line
							rounded
							prepend-inner-icon="mdi-magnify"
					:placeholder="$t('bundle.store.search')"
					@keydown="searchKeyDown"
							hide-details
							class="search-field elevation-0"></v-text-field>
				<v-btn
							color="purple"
							dark
							rounded
							class="mt-4"
							@click="openStealDialog">
							<v-icon left>mdi-package-variant-closed</v-icon>
							{{ $t('bundle.store.manager') }}
				</v-btn>
			</v-col>
		</v-row>
			</v-card-text>
		</v-card>

		<!-- 로컬 번들 섹션 -->
		<v-card class="my-6 rounded-lg elevation-1" color="white">
			<v-card-text>
				<v-row class="ma-0" align="center">
			<v-col cols="9">
						<h3 class="text-h5 font-weight-bold">{{ $t('bundle.store.local-bundle') }}</h3>
			</v-col>
					<v-col cols="3" align="right">
						<v-btn color="success" dark rounded @click="addLocalBundle">
							<v-icon left>mdi-plus</v-icon>
					{{ $t('bundle.store.add-local-bundle') }}
				</v-btn>
			</v-col>
		</v-row>
			</v-card-text>
		</v-card>

		<!-- 로컬 번들 목록 -->
		<div class="bundle-grid">
		<bundle-item
			v-for="bundle in localBundleList"
				:key="`local-bundle-${bundle.name}`"
			:pkg="bundle"
			isLocal>
		</bundle-item>
		</div>

		<!-- 소피아 번들 훔쳐오기 다이얼로그 -->
		<v-dialog v-model="stealDialog" max-width="800px">
			<v-card>
				<v-card-title class="headline purple white--text">
					<v-icon left color="white">mdi-package-variant-closed</v-icon>
					{{ $t('bundle.store.manager') }}
				</v-card-title>
				<v-card-text class="pt-4">
					<div v-if="loading" class="d-flex justify-center align-center pa-5">
						<v-progress-circular indeterminate color="purple"></v-progress-circular>
						<span class="ml-3">번들 목록을 불러오는 중...</span>
					</div>
					<div v-else-if="sopiaPackages.length === 0" class="text-center pa-5">
						<v-icon large color="grey lighten-1">mdi-package-variant-closed</v-icon>
						<p class="mt-2 grey--text">훔쳐올 수 있는 번들이 없습니다.</p>
					</div>
					<v-list v-else>
						<v-list-item
							v-for="pkg in sopiaPackages"
							:key="pkg.name"
							@click="confirmStealBundle(pkg)"
							class="mb-2 rounded-lg"
							hover
						>
							<v-list-item-avatar>
								<v-icon color="purple">mdi-package-variant-closed</v-icon>
							</v-list-item-avatar>
							<v-list-item-content>
								<v-list-item-title class="font-weight-medium">
									{{ pkg.name }} 
									<span class="text-caption grey--text ml-2">v{{ pkg.version }}</span>
								</v-list-item-title>
								<v-list-item-subtitle>{{ pkg.description || '설명 없음' }}</v-list-item-subtitle>
							</v-list-item-content>
							<v-list-item-action>
								<v-btn icon>
									<v-icon color="grey lighten-1">mdi-chevron-right</v-icon>
								</v-btn>
							</v-list-item-action>
						</v-list-item>
					</v-list>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn color="grey" text @click="stealDialog = false">닫기</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</v-main>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import BundleMixins from './bundle-mixin';
import BundleItem from './BundleItem/Index.vue';
import { BundlePackage } from '@/interface/bundle';
import path from 'path';
import { getAppPath, npmInstall } from '@/plugins/ipc-renderer';
const fs = window.require('fs');
const { ipcRenderer } = window.require('electron');

@Component({
	components: {
		BundleItem,
	},
})
export default class BundleStore extends Mixins(BundleMixins) {

	public bundleList: BundlePackage[] = [];
	public localBundleList: BundlePackage[] = [];
	public originalBundleList: BundlePackage[] = [];
	public originalLocalBundleList: BundlePackage[] = [];
	public searchText = '';
	public increment = 0;
	public localBundles: string[] = [];
	
	// 추가된 속성
	public stealDialog = false;
	public loading = false;
	public sopiaPackages: BundlePackage[] = [];
	public sopiaBundlePath = '';

	public async created() {
		console.log('[Store] Component created');
		
		// 초기 로드 시 한 번만 번들 목록 가져오기
		await this.refreshLocalBundleList();
		
		// store:reload 이벤트 핸들러 제거 - 자동 갱신 방지
		this.$evt.$off('store:reload');
		
		// 소피아 번들 경로 설정
		const appDataPath = getAppPath('appData');
		this.sopiaBundlePath = path.join(appDataPath, 'sopia-v3', 'bundles');
	}

	public async addLocalBundle() {
		const res = await ipcRenderer.invoke('open-dialog', {
			title: '로컬 번들 경로',
			defaultPath: getAppPath('documents'),
			properties: [
				'openDirectory',
			],
		});

		if ( res.canceled ) {
			return;
		}

		const [ folder ] = res.filePaths;
		const packageSrc = path.join(folder, 'package.json');
		if ( fs.existsSync(packageSrc) ) {
			const pkg = JSON.parse(fs.readFileSync(packageSrc, 'utf-8')) as BundlePackage;
			this.localBundleList.push(pkg);
			fs.symlinkSync(folder,this.getBundlePath(pkg), 'junction');

			if ( pkg.dependencies ) {
				await ipcRenderer.invoke('bun:install', folder);
				window.reloadScript();
				console.log('reloadScript');
				// await npmInstall(Object.entries(pkg.dependencies).map(([name, version]) => ({
				// 	name,
				// 	version,
				// })), {
				// 	rootDir: folder,
				// });
			}
		} else {

		}
	}

	public async refreshBundleList() {
		console.log('[Store] Refreshing bundle list from API');
		const res = await this.$api.req('GET', '/bundle/');
		this.originalBundleList = res.data.sort((a: BundlePackage, b: BundlePackage) => {
			const $T = fs.existsSync(this.getBundlePath(a));
			const _T = fs.existsSync(this.getBundlePath(b));
			if ( $T === _T ) {
				if ( $T && _T ) {
					return (!!b.page && !a.page) ? 1 : -1;
				}
				return 0;
			}
			return $T > _T ? -1 : 1;
		});
		this.search();
	}

	public async refreshLocalBundleList() {
		console.log('[Store] Refreshing local bundle list');
		if (!fs.existsSync(this.bundleRootPath)) {
			console.log('[Store] Bundle root path does not exist:', this.bundleRootPath);
			this.originalLocalBundleList = this.localBundleList = [];
			return;
		}
		
		try {
			// 모든 로컬 번들을 읽어옴 (필터링 로직 제거)
		const bundleList = fs.readdirSync(this.bundleRootPath)
								.map((name: string) => {
									try {
										const packagePath = path.join(this.bundleRootPath, name, 'package.json');
										if (fs.existsSync(packagePath) && 
											fs.lstatSync(path.join(this.bundleRootPath, name)).isDirectory()) {
											return JSON.parse(fs.readFileSync(packagePath, 'utf8'));
										}
										return null;
									} catch (e) {
										console.error('Failed to read package.json for local bundle:', name, e);
										return null;
									}
								})
								.filter((pkg: any) => pkg !== null);
								
		this.originalLocalBundleList = this.localBundleList = bundleList || [];
			console.log('[Store] Local bundle list updated, count:', this.localBundleList.length);
		} catch (e) {
			console.error('[Store] Error refreshing local bundle list:', e);
			this.originalLocalBundleList = this.localBundleList = [];
		}
	}

	public searchCondition(bundle: BundlePackage) {
		return bundle['name']?.includes(this.searchText) ||
			bundle['name:ko']?.includes(this.searchText) ||
			bundle['description']?.includes(this.searchText) ||
			bundle['description:ko']?.includes(this.searchText);
	}

	public search() {
		this.increment -= 1;
		if ( this.increment <= 0 ) {
			// 전체 번들 리스트가 삭제되었으므로 이 부분 주석 처리
			// this.bundleList = this.originalBundleList
			// 	.filter(this.searchCondition.bind(this)) || [];
			
			// 로컬 번들만 필터링
			this.localBundleList = this.originalLocalBundleList
				.filter(this.searchCondition.bind(this)) || [];
				
			this.increment = 0;
		}
	}

	public searchKeyDown() {
		if ( this.searchText.trim().length > 0 ) {
			this.increment += 1;
			setTimeout(() => this.search(), 500);
		}
	}

	public async openStealDialog() {
		this.stealDialog = true;
		this.loading = true;
		
		try {
			// sopia-v3 번들 폴더에서 번들 목록 가져오기
			if (fs.existsSync(this.sopiaBundlePath)) {
				// 번들 폴더 직접 가져오기
				const bundleNames = fs.readdirSync(this.sopiaBundlePath);
				
				// 각 번들 정보 추출
				const bundles = bundleNames
					.map(name => {
						try {
							const packagePath = path.join(this.sopiaBundlePath, name, 'package.json');
							// 폴더이고 package.json이 있는지 확인
							if (fs.existsSync(packagePath) && 
								fs.lstatSync(path.join(this.sopiaBundlePath, name)).isDirectory()) {
								const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8')) as any;
								pkg.folderName = name; // 실제 폴더 이름 저장 (패키지 이름과 다를 수 있음)
								return pkg;
							}
							return null;
						} catch (e) {
							console.error('Failed to read package.json for bundle:', name, e);
							return null;
						}
					})
					.filter(pkg => pkg !== null);
				
				this.sopiaPackages = bundles;
				console.log('Found bundles:', this.sopiaPackages.length, this.sopiaPackages);
			} else {
				console.error('Sopia bundle path does not exist:', this.sopiaBundlePath);
				this.sopiaPackages = [];
			}
		} catch (e) {
			console.error('Failed to read sopia bundles:', e);
			this.sopiaPackages = [];
		} finally {
			this.loading = false;
		}
	}
	
	public async confirmStealBundle(pkg: BundlePackage & { folderName?: string }) {
		// 확인 창 표시
		const result = await this.$swal({
			title: pkg.name,
			html: this.$t('bundle.store.steal-confirm').toString(),
			icon: 'question',
			showCancelButton: true,
			confirmButtonColor: '#673ab7',
			cancelButtonColor: '#d33',
			confirmButtonText: '확인',
			cancelButtonText: '취소'
		});
		
		if (result.isConfirmed) {
			try {
				// 소스 및 대상 경로 설정 - 실제 폴더 이름 사용
				const folderName = pkg.folderName || pkg.name;
				const sourcePath = path.join(this.sopiaBundlePath, folderName);
				const destPath = path.join(this.bundleRootPath, folderName);
				
				console.log('Stealing bundle from:', sourcePath);
				console.log('To:', destPath);
				
				// 소스 폴더 존재 확인
				if (!fs.existsSync(sourcePath)) {
					throw new Error(`Source folder doesn't exist: ${sourcePath}`);
				}
				
				// 이미 존재하는 경우 삭제
				if (fs.existsSync(destPath)) {
					console.log('Destination folder already exists, removing:', destPath);
					this.deleteFolderRecursive(destPath);
				}
				
				// 폴더 복사
				this.copyFolderRecursive(sourcePath, destPath);
				console.log('Bundle successfully copied!');
				
				// 성공 메시지
				this.$swal({
					title: '성공!',
					text: this.$t('bundle.store.steal-success').toString(),
					icon: 'success'
				});
				
				// 로컬 번들 목록 새로고침
				await this.refreshLocalBundleList();
				
				// 다이얼로그 닫기
				this.stealDialog = false;
			} catch (error) {
				const e = error as Error;
				console.error('Failed to steal bundle:', e);
				this.$swal({
					title: '오류',
					text: `${this.$t('bundle.store.steal-error').toString()}: ${e.message}`,
					icon: 'error'
				});
			}
		}
	}
	
	// 폴더 재귀적으로 삭제하는 유틸리티 함수
	private deleteFolderRecursive(folderPath: string) {
		if (fs.existsSync(folderPath)) {
			fs.readdirSync(folderPath).forEach((file) => {
				const curPath = path.join(folderPath, file);
				if (fs.lstatSync(curPath).isDirectory()) {
					this.deleteFolderRecursive(curPath);
				} else {
					fs.unlinkSync(curPath);
				}
			});
			fs.rmdirSync(folderPath);
		}
	}
	
	// 폴더 재귀적으로 복사하는 유틸리티 함수
	private copyFolderRecursive(source: string, target: string) {
		// 대상 디렉토리 없으면 생성
		if (!fs.existsSync(target)) {
			fs.mkdirSync(target, { recursive: true });
		}
		
		// 소스 폴더의 모든 파일 복사
		if (fs.existsSync(source)) {
			fs.readdirSync(source).forEach((file) => {
				const srcFile = path.join(source, file);
				const tgtFile = path.join(target, file);
				
				if (fs.lstatSync(srcFile).isDirectory()) {
					this.copyFolderRecursive(srcFile, tgtFile);
				} else {
					fs.copyFileSync(srcFile, tgtFile);
				}
			});
		}
	}
}
</script>

<style scoped>
.search-field >>> .v-input__slot {
  background-color: #f5f5f5 !important;
  border: 1px solid #eeeeee;
}

.bundle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.v-btn {
  letter-spacing: 0;
}
</style>
