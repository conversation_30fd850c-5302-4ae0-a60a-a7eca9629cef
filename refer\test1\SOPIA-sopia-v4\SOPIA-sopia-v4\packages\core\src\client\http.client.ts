import { plainToClassFromExist } from 'class-transformer'
import { ApiResponse, HttpResponse } from './response'
import { merge } from 'lodash'

export type ClassType<T> = new (...args: any[]) => T

export interface HttpRequest extends Omit<RequestInit, 'body'> {
  body?: Record<string, any> | BodyInit
  params?: Record<string, any>
}

export class HttpClient {
  public userAgent =
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
  public referer = 'https://www.spooncast.net'

  private baseConfig: HttpRequest = {
    headers: {
      'User-Agent': this.userAgent,
      Referer: this.referer + '/',
      Origin: this.referer
    }
  }
  constructor(
    private baseUrl: string,
    baseConfig: HttpRequest = {}
  ) {
    this.appendBaseConfig(baseConfig)
  }

  appendBaseConfig(config: HttpRequest) {
    this.baseConfig = merge(this.baseConfig, config)
  }

  async request<T>(path: string, options: HttpRequest, responseType?: ClassType<T>): Promise<T> {
    const url = new URL(path, this.baseUrl)
    if (typeof options.body === 'object' && options.constructor.name === 'Object') {
      options.body = JSON.stringify(options.body)
      options.headers = {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }
    if (typeof options.params === 'object') {
      const params = url.searchParams
      Object.entries(options.params).forEach(([key, value]) => {
        params.set(key, value)
      })
      delete options.params
    }
    const requestOptions = merge(this.baseConfig, options) as RequestInit
    const response = (await fetch(url.toString(), requestOptions)
      .then((res) => {
        console.log('res', res)
        return res.json()
      })
      .catch((error) => {
        return {
          status: 500,
          message: 'Internal Server Error',
          error
        }
      })) as HttpResponse

    if (responseType) {
      return plainToClassFromExist(new responseType(), response) as unknown as T
    }
    return response as T
  }
}

export class ApiHttpClient {
  constructor(public httpClient: HttpClient) {}

  async request<T>(
    path: string,
    options: HttpRequest,
    responseType: ClassType<T>
  ): Promise<ApiResponse<T>> {
    const res = await this.httpClient.request<ApiResponse<T>>(path, options, ApiResponse)
    res.results = plainToClassFromExist(new responseType(), res.results) as T[]
    return res
  }
}
