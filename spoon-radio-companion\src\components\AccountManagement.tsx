import React, { useState, useEffect } from 'react';
import { UnifiedData } from './AccountManagement/types';
import { useAccountData } from './AccountManagement/hooks/useAccountData';
import { getFilterOptions, getSearchPlaceholder } from './AccountManagement/utils';
import { UserCard } from './AccountManagement/components/UserCard';
import { DetailModal } from './AccountManagement/components/DetailModal';
import { TokenSelectModal } from './AccountManagement/components/TokenSelectModal';
import {
  Container,
  Header,
  HeaderLeft,
  Title,
  Subtitle,
  SearchContainer,
  SearchInput,
  FilterSelect,
  TableContainer,
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  EmptyRow,
  LoadingContainer
} from './AccountManagement/styles';

const AccountManagement: React.FC = () => {
  const {
    users,
    spoonTokens,
    loading,
    tokensLoading,
    handleDeleteUser,
    handleDeleteToken,
    handleUnlinkToken,
    createUnifiedData,
    getLinkedUserInfo
  } = useAccountData();

  const [filteredData, setFilteredData] = useState<UnifiedData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [selectedToken, setSelectedToken] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTokenSelectModalOpen, setIsTokenSelectModalOpen] = useState(false);
  const [selectedAdminTokens, setSelectedAdminTokens] = useState<{[key: string]: any}>({});

  // 데이터 필터링
  useEffect(() => {
    const unifiedData = createUnifiedData();
    // 토큰 타입 항목 제거 - 계정 카드만 표시
    let filtered = unifiedData.filter(item => item.type === 'user');

    if (searchTerm) {
      filtered = filtered.filter(item => 
        item.searchableText.includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      if (statusFilter === 'linked') {
        filtered = filtered.filter(item => item.connectionStatus === 'linked');
      } else if (statusFilter === 'unlinked') {
        filtered = filtered.filter(item => item.connectionStatus === 'unlinked');
      } else if (statusFilter === 'admin') {
        filtered = filtered.filter(item => item.userAccount?.role === 'admin');
      } else if (statusFilter === 'user') {
        filtered = filtered.filter(item => item.userAccount?.role === 'user');
      }
    }

    setFilteredData(filtered);
  }, [users, spoonTokens, searchTerm, statusFilter, createUnifiedData]);

  // 모달 핸들러
  const handleViewUser = (user: any) => {
    setSelectedUser(user);
    setSelectedToken(null);
    setIsModalOpen(true);
  };

  const handleViewToken = (token: any) => {
    setSelectedToken(token);
    setSelectedUser(null);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    setSelectedToken(null);
  };

  // 관리자 토큰 선택 모달 핸들러
  const [currentAdminUserId, setCurrentAdminUserId] = useState<string>('');
  
  const handleAdminTokenSelect = (userId: string) => {
    setCurrentAdminUserId(userId);
    setIsTokenSelectModalOpen(true);
  };

  const handleTokenSelectionClose = () => {
    setIsTokenSelectModalOpen(false);
    setCurrentAdminUserId('');
  };

  const handleTokenSelectionConfirm = (tokenId: string) => {
    // 선택된 토큰 찾기
    const selectedToken = spoonTokens.find(token => token.id === tokenId);
    if (selectedToken && currentAdminUserId) {
      setSelectedAdminTokens(prev => ({
        ...prev,
        [currentAdminUserId]: selectedToken
      }));
    }
    setIsTokenSelectModalOpen(false);
    setCurrentAdminUserId('');
  };

  // 로딩 상태
  if (loading || tokensLoading) {
    return (
      <Container>
        <LoadingContainer>
          <div>데이터를 불러오는 중...</div>
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <Title>계정관리</Title>
          <Subtitle>모든 사용자 계정을 통합 관리할 수 있습니다</Subtitle>
        </HeaderLeft>
        <SearchContainer>
          <SearchInput
            type="text"
            placeholder={getSearchPlaceholder()}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <FilterSelect
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            {getFilterOptions().map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FilterSelect>
        </SearchContainer>
      </Header>

      {/* 테이블 레이아웃 */}
      <TableContainer>
        <Table>
          <TableHeader>
            <TableHeaderRow>
              <TableHeaderCell>프로필/이름</TableHeaderCell>
              <TableHeaderCell>연결 상태</TableHeaderCell>
              <TableHeaderCell>역할/권한</TableHeaderCell>
              <TableHeaderCell>연결된 토큰</TableHeaderCell>
              <TableHeaderCell>마지막 활동</TableHeaderCell>
              <TableHeaderCell>액션</TableHeaderCell>
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <EmptyRow>
                <td colSpan={7}>
                  🔍 검색 결과가 없습니다
                </td>
              </EmptyRow>
            ) : (
              filteredData.map((item) => (
                <UserCard
                  key={item.id}
                  item={item}
                  onViewUser={handleViewUser}
                  onViewToken={handleViewToken}
                  onDeleteUser={handleDeleteUser}
                  onDeleteToken={handleDeleteToken}
                  onUnlinkToken={handleUnlinkToken}
                  onAdminTokenSelect={handleAdminTokenSelect}
                  selectedAdminToken={item.userAccount?.id ? selectedAdminTokens[item.userAccount.id] : undefined}
                />
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 상세 정보 모달 */}
      <DetailModal
        isOpen={isModalOpen}
        selectedUser={selectedUser}
        selectedToken={selectedToken}
        spoonTokens={spoonTokens}
        getLinkedUserInfo={getLinkedUserInfo}
        onClose={handleCloseModal}
        onDeleteUser={handleDeleteUser}
        onDeleteToken={handleDeleteToken}
        onUnlinkToken={handleUnlinkToken}
      />

      {/* 관리자 토큰 선택 모달 */}
      <TokenSelectModal
        isOpen={isTokenSelectModalOpen}
        spoonTokens={spoonTokens}
        onClose={handleTokenSelectionClose}
        onConfirm={handleTokenSelectionConfirm}
      />
    </Container>
  );
};

export default AccountManagement; 