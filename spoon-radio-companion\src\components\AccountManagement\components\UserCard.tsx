import React from 'react';
import { UnifiedData } from '../types';
import { TableRow, TableCell, ActionButton } from '../styles';

interface UserCardProps {
  item: UnifiedData;
  onViewUser: (user: any) => void;
  onViewToken: (token: any) => void;
  onDeleteUser: (userId: string) => void;
  onDeleteToken: (tokenId: string) => void;
  onUnlinkToken: (tokenId: string) => void;
  onAdminTokenSelect: (userId: string) => void;
  selectedAdminToken?: any;
}

export const UserCard: React.FC<UserCardProps> = ({
  item,
  onViewUser,
  onViewToken,
  onDeleteUser,
  onDeleteToken,
  onUnlinkToken,
  onAdminTokenSelect,
  selectedAdminToken
}) => {
  const isAdmin = item.userAccount?.role === 'admin';

  // 프로필 이미지 에러 핸들러
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = '/icon128.png';
  };

  const renderConnectedTokens = () => {
    if (isAdmin) {
      // 관리자 계정의 경우 토큰 선택 UI 표시
      if (selectedAdminToken) {
        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ position: 'relative' }}>
                <img
                  src={selectedAdminToken.user?.profile_url || '/icon128.png'}
                  alt="프로필"
                  style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    objectFit: 'cover'
                  }}
                  onError={handleImageError}
                />
              </div>
              <div>
                <div style={{ fontWeight: '500', fontSize: '14px' }}>
                  {selectedAdminToken.user?.nickname || '닉네임 없음'}
                </div>
                <div style={{ color: '#666', fontSize: '12px' }}>
                  #{selectedAdminToken.user?.tag}
                </div>
              </div>
            </div>
            <ActionButton
              variant="token"
              onClick={() => onAdminTokenSelect(item.userAccount?.id || '')}
              style={{ 
                padding: '8px 16px',
                fontSize: '12px',
                backgroundColor: '#ffffff',
                color: '#7c3aed',
                border: '1px solid #7c3aed',
                borderRadius: '8px',
                fontWeight: '500',
                transition: 'all 0.2s ease',
                cursor: 'pointer'
              }}
            >
              변경
            </ActionButton>
          </div>
        );
      } else {
        return (
          <ActionButton
            variant="token"
            onClick={() => onAdminTokenSelect(item.userAccount?.id || '')}
          >
            토큰 선택
          </ActionButton>
        );
      }
    } else {
      // 일반 사용자의 기존 로직
      if (item.spoonTokens && item.spoonTokens.length > 0) {
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {item.spoonTokens.map((token: any) => (
              <div key={token.id} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{ position: 'relative' }}>
                  <img
                    src={token.user?.profile_url || '/icon128.png'}
                    alt="프로필"
                    style={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      objectFit: 'cover'
                    }}
                    onError={handleImageError}
                  />
                </div>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <span style={{ fontSize: '13px', fontWeight: '500' }}>
                    {token.user?.nickname || '닉네임 없음'}
                  </span>
                  <span style={{ fontSize: '11px', color: '#666' }}>
                    #{token.user?.tag}
                  </span>
                </div>
              </div>
            ))}
          </div>
        );
      } else {
        return <span style={{ color: '#9ca3af' }}>연결된 토큰 없음</span>;
      }
    }
  };

  return (
    <TableRow>
      <TableCell>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <img
            src={item.connectionStatus === 'linked' ? item.profileUrl : '/icon128.png'}
            alt="프로필"
            style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              objectFit: 'cover',
              backgroundColor: item.connectionStatus === 'linked' ? 'transparent' : '#ffffff',
              border: item.connectionStatus === 'linked' ? 'none' : '1px solid #e5e7eb'
            }}
            onError={handleImageError}
          />
          <div>
            <div style={{ fontWeight: '600', marginBottom: '2px' }}>
              {item.displayName}
            </div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              {item.subtitle}
            </div>
          </div>
        </div>
      </TableCell>
      
      <TableCell>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px',
          padding: '4px 8px',
          backgroundColor: item.connectionStatus === 'linked' ? '#dcfce7' : '#fef3c7',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: '500',
          color: item.connectionStatus === 'linked' ? '#166534' : '#92400e',
          width: 'fit-content'
        }}>
          {item.connectionStatus === 'linked' ? '🔗' : '🔓'}
          {item.connectionStatus === 'linked' ? '연결됨' : '연결 안됨'}
        </div>
      </TableCell>
      
      <TableCell>
        {item.userAccount?.role && (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            padding: '4px 8px',
            backgroundColor: item.userAccount.role === 'admin' ? '#fee2e2' : '#e0f2fe',
            borderRadius: '12px',
            fontSize: '12px',
            fontWeight: '500',
            color: item.userAccount.role === 'admin' ? '#dc2626' : '#0369a1',
            width: 'fit-content'
          }}>
            {item.userAccount.role === 'admin' ? '👑' : '👤'}
            {item.userAccount.role === 'admin' ? '관리자' : '사용자'}
          </div>
        )}
      </TableCell>
      
      <TableCell>
        {renderConnectedTokens()}
      </TableCell>
      
      <TableCell>
        <div style={{ fontSize: '13px', color: '#6b7280' }}>
          {item.lastActivity}
        </div>
      </TableCell>
      
      <TableCell>
        <div style={{ display: 'flex', gap: '8px' }}>
          <ActionButton
            variant="view"
            onClick={() => item.type === 'user' ? onViewUser(item.userAccount) : onViewToken(item.spoonToken)}
          >
            상세
          </ActionButton>
          
          {item.type === 'user' && (
            <ActionButton
              variant="delete"
              onClick={() => onDeleteUser(item.userAccount!.id)}
            >
              삭제
            </ActionButton>
          )}
          
          {item.type === 'token' && (
            <>
              <ActionButton
                variant="delete"
                onClick={() => onDeleteToken(item.spoonToken!.id)}
              >
                삭제
              </ActionButton>
              {item.spoonToken?.userId && (
                <ActionButton
                  variant="unlink"
                  onClick={() => onUnlinkToken(item.spoonToken!.id)}
                >
                  연결해제
                </ActionButton>
              )}
            </>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}; 