{"name": "@sopia-bundle-react-ts-template/source", "version": "0.0.0", "license": "MIT", "scripts": {"serve": "nx serve views", "build": "nx run-many -t build", "postbuild": "node tools/postbuild.mjs", "preinstall": "npx only-allow pnpm"}, "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.1.6", "axios": "^1.6.0", "express": "^4.21.1", "react": "18.3.1", "react-dom": "18.3.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nx/eslint": "20.0.7", "@nx/eslint-plugin": "20.0.7", "@nx/jest": "20.0.7", "@nx/js": "20.0.7", "@nx/nest": "^20.0.7", "@nx/node": "20.0.7", "@nx/react": "20.0.7", "@nx/vite": "20.0.7", "@nx/web": "20.0.7", "@nx/webpack": "20.0.7", "@nx/workspace": "20.0.7", "@sopia-bot/core": "^2.4.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/electron": "^1.4.38", "@types/express": "^5.0.0", "@types/jest": "^29.5.12", "@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^1.3.1", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jsdom": "~22.1.0", "nx": "20.0.7", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.5.2", "typescript-eslint": "^8.0.0", "vite": "^5.0.0", "vitest": "^1.3.1", "webpack-cli": "^5.1.4"}}