# GitHub 연동 설정 가이드 (선택사항)

## ⚡ 빠른 답변

**GitHub 토큰 설정 없이도 모든 기능이 정상 작동합니다!**
로컬 파일 기반으로 완벽하게 작동하므로 GitHub 설정은 선택사항입니다.

## 🤷‍♂️ GitHub가 필요한 경우

- 여러 컴퓨터에서 사용자 데이터 공유
- 데이터 백업 및 동기화
- 팀에서 공동 사용자 관리

## 🛠️ GitHub 설정 방법 (원할 경우에만)

### 1단계: GitHub 저장소 생성

```bash
# 1. GitHub에서 새 저장소 생성
https://github.com/leekyuk/tamm-users

# 2. 저장소에 approved-users.json 파일 업로드
{
  "approved_users": [
    {
      "id": "admin",
      "email": "<EMAIL>",
      "name": "관리자",
      "password": "hashed_password",
      "role": "admin",
      "approved_at": "2024-01-01T00:00:00.000Z",
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pending_users": []
}
```

### 2단계: Personal Access Token 생성

1. **GitHub 설정** → **Developer settings** → **Personal access tokens**
2. **Generate new token (classic)** 클릭
3. **권한 설정**:
   - `repo` (전체 저장소 접근)
   - `contents:write` (파일 읽기/쓰기)

### 3단계: 환경변수 설정

**Windows:**
```cmd
# 시스템 환경변수에 추가
set GITHUB_TOKEN=your_github_token_here
```

**또는 코드에서 직접 설정:**
```typescript
// src/plugins/auth-service.ts 수정
private readonly GITHUB_TOKEN = 'your_actual_token_here';
```

### 4단계: 확인

```typescript
// 콘솔에서 GitHub 동기화 확인
// "GitHub 동기화 실패" 메시지가 없으면 정상 작동
```

## ⚠️ 주의사항

1. **토큰 보안**: GitHub 토큰을 코드에 직접 넣지 말고 환경변수 사용
2. **공개 저장소**: 사용자 데이터가 포함되므로 private 저장소 권장
3. **권한 관리**: 토큰에 최소 필요 권한만 부여

## 🚀 결론

**GitHub 설정 없이도 완벽하게 사용 가능합니다!**
필요하다고 느낄 때 나중에 설정해도 됩니다.

---

**현재 상태**: 로컬 파일 기반으로 모든 기능 정상 작동 ✅ 