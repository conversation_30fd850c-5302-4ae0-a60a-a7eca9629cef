{"name": "sopia-bundle-starter", "name:ko": "소피아 번들 시작팩", "description": "this is descript", "description:ko": "이건 디스크립션입니다.", "version": "0.0.1", "main": "./apps/worker/src/index.js", "page": "http://localhost:4200", "pageType": "http", "pageRoot": "apps/views", "page-version": 2, "debug": true, "stp": {"domain": "starter.sopia.dev", "file": "./apps/background/src/index.js"}, "release-note": {}, "sopia": {"ignore:upload": ["node_modules"]}, "dependencies": {"@nestjs/common": "^10.4.6", "@nestjs/core": "^10.4.6", "@nestjs/mapped-types": "^2.0.5", "@nestjs/platform-express": "^10.4.6"}}