﻿<!--
 * App.vue
 * Created on Sat Jul 18 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
-->
<template>
	<v-app style="">
		<title-bar :isLogin="isLogin" />
		<update-dialog v-if="isLogin"/>
		<bundle-update-dialog v-if="isLogin" v-model="bundleUpdateDialogShow" :items="bundleUpdateList" />
		<side-menu v-if="isLogin" />
		<div class="ma-0 d-flex">
			<v-sheet id="router-view" tile :key="$route.fullPath" color="white" style="max-width: 100vw; flex-basis: 80%; flex-grow: 1; flex-shrink: 1;">
				<transition name="scroll-y-reverse-transition">
					<router-view></router-view>
				</transition>
			</v-sheet>
			<live-player v-if="isLogin && currentLive.id" :live="currentLive" />
		</div>
		<notification></notification>
		<following-dialog></following-dialog>
		<!--<tutorials/>-->
		<button @click="handleLogout">로그아웃</button>
	</v-app>
</template>
<style>
@import './assets/suit/SUIT-Variable.css';
.h-100v {
	height: 100vh;
}
html, body {
	overflow: hidden;
}
* {
	font-family: 'SUIT Variable';
}

/* 전역 스크롤바 숨김 스타일 */
::-webkit-scrollbar {
	width: 0 !important;
	height: 0 !important;
	display: none !important;
	background: transparent !important;
}

* {
	scrollbar-width: none !important;
	-ms-overflow-style: none !important;
}

/* vuescroll 스타일 재정의 */
.vuescroll-bar {
	opacity: 0 !important;
	width: 0 !important;
	height: 0 !important;
	visibility: hidden !important;
	display: none !important;
}

.vuescroll-rail {
	opacity: 0 !important;
	width: 0 !important;
	height: 0 !important;
	visibility: hidden !important;
	display: none !important;
}

.vuescroll-panel::-webkit-scrollbar,
.vuescroll::-webkit-scrollbar {
	width: 0 !important;
	height: 0 !important;
	display: none !important;
	background: transparent !important;
}

.vuescroll-panel,
.vuescroll {
	scrollbar-width: none !important;
	-ms-overflow-style: none !important;
}
</style>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { User, Live, SpoonClient, ApiLivesInfo } from '@sopia-bot/core';
import CfgLite from '@/plugins/cfg-lite-ipc';
import { authService } from '@/plugins/auth-service';
import path from 'path';
import { BundlePackage } from '@/interface';

import LivePlayer from '@/views/Live/Player.vue';
import LoginDialog from '@/views/Login/Index.vue';
import BundleUpdateDialog from '@/views/Bundle/UpdateDialog.vue';
import UpdateDialog from '@/views/Components/UpdateDialog.vue';
import TitleBar from '@/views/Components/TitleBar.vue';
import SideMenu from '@/views/Components/SideMenu.vue';
import Tutorials from '@/views/Tutorials/Index.vue';
import Notification from '@/views/Components/Notification.vue';
import FollowingDialog from '@/views/Components/FollowingDialog.vue';

const fs = window.require('fs');

declare global {
	interface Window {
		user: User;
		$spoon: any;
		$sopia: SpoonClient;
		reloadCfg: () => void;
		appCfg: CfgLite;
		logout: () => void;
	}
}

@Component({
	components: {
		SideMenu,
		LivePlayer,
		LoginDialog,
		BundleUpdateDialog,
		UpdateDialog,
		TitleBar,
		Tutorials,
		Notification,
		FollowingDialog,
	},
})
export default class App extends Mixins(GlobalMixins) {
	public currentLive: Live = {} as Live;
	public bundleUpdateDialogShow: boolean = false;
	public bundleUpdateList: BundlePackage[] = [];
	public isLogin: boolean = false;
	public agreeLiveInfoDialogOpen: boolean = false;

	public enter(...args: any[]) {
		console.log('enter transition', args);
	}

	public async created() {
		const req = await this.$sopia.api.users.followings(4324890);
		this.$store.commit('partners', req.res.results);
	}

	public async mounted() {
		// TAMM 인증 확인
		const tammUser = authService.getCurrentUser();
		const auth = this.$cfg.get('auth');

		window.logout = () => {
			// TAMM 로그아웃과 기존 로그아웃 통합
			authService.logout();
			this.$cfg.delete('auth');
			this.$cfg.save();
			setTimeout(() => {
				window.location.reload();
			}, 100);
		};

		// TAMM 인증이 없으면 인증 페이지로 이동
		if (!tammUser) {
			console.log('TAMM 인증이 필요합니다. 인증 페이지로 이동합니다.');
			this.isLogin = false;
			setTimeout(() => {
				if (this.$route.path !== '/auth') {
					this.$router.push('/auth');
				}
			}, 100);
			return;
		}

		console.log('TAMM 인증 확인됨:', tammUser.name, tammUser.email);

		// TAMM 인증이 있지만 스푼 인증이 없는 경우
		if (!auth || !auth.spoon) {
			// 소피아 로그인은 더미로 처리
			const dummySopia = {
				id: 'auto',
				user_id: Date.now(),
				name: tammUser.name, // TAMM 사용자 이름 사용
				gender: 'M',
				spoon_id: '0'
			};
			
			// 소피아 더미 데이터만 설정
			if (!auth) {
				this.$cfg.set('auth', { sopia: dummySopia });
				this.$cfg.save();
			} else if (!auth.spoon) {
				this.$cfg.set('auth.sopia', dummySopia);
				this.$cfg.save();
			}
			
			// 스푼 로그인이 없으면 로그인 페이지로 이동 (실제 스푼 로그인 유도)
			this.isLogin = false;
			setTimeout(() => {
				if (this.$route.path !== '/login') {
					this.$router.push('/login');
				}
			}, 100);
			
			return; // 여기서 함수 종료
		}

		// 스푼 로그인 정보가 있는 경우 정상 로그인 진행
		if (auth && auth.spoon) {
			try {
				const user = await this.$sopia.loginToken(auth.spoon.id, auth.spoon.token, auth.spoon.refresh_token);
				const token = await this.$sopia.refreshToken(user.id, auth.spoon.token, auth.spoon.refresh_token);
				
				if ( token ) {
					// 소피아 계정이 없으면 더미 데이터 생성 (TAMM 사용자 정보 활용)
					if (!auth.sopia) {
						auth.sopia = {
							id: 'auto',
							user_id: Date.now(),
							name: tammUser.name, // TAMM 사용자 이름 사용
							gender: user.gender,
							spoon_id: user.id.toString()
						} as unknown as any;
						this.$cfg.set('auth.sopia', auth.sopia);
					}
					
					auth.spoon.token = token;
					this.$store.commit('user', user);
					this.$evt.$emit('user', user);
					this.$cfg.set('auth.spoon.token', token);
					this.$cfg.set('auth.spoon.refresh_token', auth.spoon.refresh_token);
					this.$cfg.save();

					this.isLogin = true;
					
					if (this.$route.path === '/login' || this.$route.path === '/auth') {
						console.log('로그인 완료 - 홈으로 이동');
						this.$router.push('/');
					}

					await this.$api.activityLog('logon');
					console.log('TAMM + Spoon 인증 완료:', tammUser.name, user.tag);
				} else {
					throw Error('Invalid token');
				}
			} catch (err) {
				console.error('스푼 로그인 실패:', err);
				// 로그인 실패 시 auth.spoon만 삭제하고 소피아 계정은 유지
				this.$cfg.delete('auth.spoon');
				this.$cfg.save();
				this.isLogin = false;
				setTimeout(() => {
					this.$router.push('/login');
				}, 100);
			}
		}

		this.$evt.$off('live-join');
		this.$evt.$on('live-join', async (live: number, isMembership: boolean) => {
			let config!: ApiLivesInfo.Request;
			if ( isMembership ) {
				const req = await this.$sopia.api.lives.token(live, {
					'data': {
						'device_unique_id': this.$sopia.deviceUUID,
					},
				});
				if ( req.res.status_code !== 200 ) {
					throw req;
				}
				config = {
					headers: {
						'x-live-authorization': 'Bearer ' + req.res.results[0]?.jwt,
					}
				};
			}
			const req = await this.$sopia.api.lives.info(live, config);
			this.$nextTick(async () => {
				this.currentLive = req.res.results[0];
				await this.$api.activityLog('live-join', req.res.results[0].id.toString());
			});
		});

		this.$evt.$off('live-leave');
		this.$evt.$on('live-leave', () => {
			this.currentLive = {} as Live;
		});

		this.$evt.$off('user');
		this.$evt.$on('user', (user: User) => {
			this.isLogin = true;
		});

		if ( !this.$store.state.loginDialog ) {
			this.checkBundleUpldate();
		}

		// 페이지 경로에 따라 타이틀바 요소 숨김 처리
		this.$router.afterEach((to) => {
			const hideTitlebarElements = to.path.includes('/cast/');
			this.$evt.$emit('hide-titlebar-elements', hideTitlebarElements);
		});
	}

	public async checkBundleUpldate() {
		// 번들 업데이트 체크 기능 비활성화
		return;
		
		/* 원본 코드 주석 처리
		const bundleDirectory = this.$path('userData', 'bundles');
		const updateRequest = fs.readdirSync(bundleDirectory)
			.filter((item: string) => fs.lstatSync(path.join(bundleDirectory, item)).isDirectory())
			.map(async (item: string) => {
				try {
					return await this.$api.req('GET', `/bundle/${item}`)
				} catch {
					return;
				}
			});

		const bundleInfoList = (await Promise.all(updateRequest) as any[])
			.filter((res) => res && !res.error)
			.map((res) => res.data[0])
			.filter((bundle) => {
				const pkgPath = path.join(bundleDirectory, bundle.name, 'package.json');
				const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));
				console.log(pkg.name, pkg.version, bundle.version);
				return pkg.version !== bundle.version;
			});

		if ( bundleInfoList.length > 0 ) {
			this.bundleUpdateList = bundleInfoList;
			this.bundleUpdateDialogShow = true;
		}
		*/
	}

	public async showAgreeLiveInfoDialog() {
		this.agreeLiveInfoDialogOpen = false;
		return;
	}

	public async handleLogout() {
		try {
			await authService.logout();
			// 로그아웃 성공 후 로그인 페이지로 리디렉션
			this.$router.push('/login'); // 적절한 로그인 페이지 경로로 변경
		} catch (error) {
			console.error('로그아웃 실패:', error);
			alert('로그아웃 중 오류가 발생했습니다.');
		}
	}

}
</script>
<style lang="scss">
@font-face {
	font-family: JoyPixels;
	src: url('./assets/JoyPixels-SBIX.woff2') format('woff2');
	/* using the unicode-range attribute to limit the reach of the JoyPixels web font */
    unicode-range: U+00A9, U+00AE, U+203C, U+2049, U+20E3, U+2122, U+2139, U+2194-2199, U+21A9-21AA, U+231A, U+231B, U+2328, U+23CF, U+23E9-23F3, U+23F8-23FA, U+24C2, U+25AA, U+25AB, U+25B6, U+25C0, U+25FB-25FE, U+2600-27EF, U+2934, U+2935, U+2B00-2BFF, U+3030, U+303D, U+3297, U+3299, U+1F000-1F02F, U+1F0A0-1F0FF, U+1F100-1F64F, U+1F680-1F6FF, U+1F910-1F96B, U+1F980-1F9E0;
}
@font-face {
    font-family: 'GangwonEdu_OTFBoldA';
    src: url('https://cdn.jsdelivr.net/gh/projectnoonnu/noonfonts_2201-2@1.0/GangwonEdu_OTFBoldA.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}
.v-application {
   font-family: JoyPixels, sans-serif !important;
}
</style>