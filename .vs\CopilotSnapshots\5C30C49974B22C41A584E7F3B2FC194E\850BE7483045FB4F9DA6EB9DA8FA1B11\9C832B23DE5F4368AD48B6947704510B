﻿import { auth, db } from '../main'; // src/main.ts에서 초기화된 auth와 db 임포트
import store from '@/store';

// Firebase Authentication에서 필요한 함수들을 임포트합니다.
import {
  createUserWithEmailAndPassword, // 사용자 생성 (회원가입)
  signInWithEmailAndPassword,     // 사용자 로그인
  signOut,                        // 사용자 로그아웃
  User,                           // Firebase User 객체 타입
  sendPasswordResetEmail,         // 비밀번호 재설정 이메일 전송
  updateProfile,                  // 프로필 업데이트
} from 'firebase/auth';

// Firestore에서 필요한 함수들을 임포트합니다.
import {
  doc,        // 문서 참조를 생성
  setDoc,     // 문서 생성 또는 업데이트
  getDoc,     // 문서 가져오기
  collection, // 컬렉션 참조를 생성
  query,      // 쿼리 생성
  where,      // 쿼리 조건 (필요한 경우)
  getDocs,    // 쿼리 결과 문서들 가져오기
  updateDoc,  // 문서 일부 업데이트
  deleteDoc,  // 문서 삭제
} from 'firebase/firestore';

// 사용자 데이터 인터페이스 (Firestore 문서 스키마에 맞게 정의)
export interface UserData {
  id?: string;       // Firestore 문서 ID (사용자 UID)
  email: string;
  name: string;
  role: 'user' | 'admin'; // 사용자 역할
  isApproved: boolean;    // 관리자 승인 여부
  createdAt: Date;        // 가입 시간
  // 여기에 필요한 다른 사용자 정보 필드를 추가할 수 있습니다.
}

// 대기 중인 사용자 인터페이스
export interface PendingUser extends UserData {
  id: string; // Firestore 문서 ID (사용자 UID)
}

// 로그인 결과 인터페이스
interface LoginResult {
  success: boolean;
  message: string;
  user?: UserData;
}

// 승인 결과 인터페이스
interface ApprovalResult {
  success: boolean;
  message: string;
}

// 사용자 자격 증명 인터페이스
interface UserCredentials {
  email: string;
  password: string;
}

export const authService = {
  /**
   * 사용자 회원가입 처리 (Firebase Authentication 및 Firestore 사용)
   * 사용자는 Firebase Auth에 생성되고, Firestore 'users' 컬렉션에 초기 데이터가 저장됩니다.
   * @param email 사용자 이메일
   * @param password 사용자 비밀번호
   * @param name 사용자 이름
   * @returns Firebase User 객체 또는 null
   */
  async register(email: string, password: string, name: string): Promise<User | null> {
    try {
      // 1. Firebase Authentication에 사용자 생성
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (user) {
        // 사용자 표시 이름 설정 (선택사항)
        await updateProfile(user, { displayName: name });
        
        // 2. Firestore 'users' 컬렉션에 사용자 정보 저장
        //    사용자 UID를 문서 ID로 사용하여 특정 사용자 문서에 접근합니다.
        await setDoc(doc(db, 'users', user.uid), {
          email: email,
          name: name,
          role: 'user',       // 기본 역할은 'user'
          isApproved: false,  // 초기에는 관리자 승인 대기 상태
          createdAt: new Date(),
        } as UserData);

        console.log('회원가입 성공, 관리자 승인 대기 중:', user.email);
        return user;
      }
      return null;
    } catch (error: any) {
      console.error('회원가입 오류:', error.message);
      // Firebase Auth 오류 코드에 따라 더 상세한 메시지 처리 가능합니다. (예: 'auth/email-already-in-use')
      throw error;
    }
  },

  /**
   * 사용자 로그인 처리 (Firebase Authentication 및 Firestore 사용)
   * 로그인 후 Firestore에서 사용자의 승인 상태를 확인합니다.
   * @param email 사용자 이메일
   * @param password 사용자 비밀번호
   * @returns 로그인 결과 객체
   */
  async login(email: string, password: string): Promise<LoginResult> {
    try {
      // 1. Firebase Authentication으로 로그인 시도
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (user) {
        // 2. Firestore에서 사용자 승인 상태 확인
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnap = await getDoc(userDocRef);

        // 문서가 존재하고 isApproved 필드가 true인지 확인
        if (userDocSnap.exists()) {
          const userData = userDocSnap.data() as UserData;
          
          if (userData.isApproved) {
            console.log('로그인 성공:', user.email);
            
            // Vuex 스토어에 사용자 데이터 저장
            store.commit('setTammUser', {
              uid: user.uid,
              ...userData
            });
            
            return {
              success: true,
              message: '로그인 성공',
              user: { id: user.uid, ...userData }
            };
          } else {
            // 승인되지 않은 사용자
            await signOut(auth); // 로그인된 세션 강제 해제
            return {
              success: false,
              message: '계정이 승인되지 않았습니다. 관리자 승인을 기다려주세요.'
            };
          }
        } else {
          // Firestore에 사용자 데이터가 없는 경우
          await signOut(auth);
          return {
            success: false,
            message: '사용자 데이터를 찾을 수 없습니다.'
          };
        }
      }
      
      return {
        success: false,
        message: '로그인에 실패했습니다.'
      };
    } catch (error: any) {
      console.error('로그인 오류:', error.message);
      let errorMessage = '로그인 중 오류가 발생했습니다.';
      
      // Firebase 오류 코드에 따른 사용자 친화적인 메시지
      switch (error.code) {
        case 'auth/invalid-credential':
        case 'auth/user-not-found':
        case 'auth/wrong-password':
          errorMessage = '이메일 또는 비밀번호가 올바르지 않습니다.';
          break;
        case 'auth/user-disabled':
          errorMessage = '계정이 비활성화되었습니다. 관리자에게 문의하세요.';
          break;
        case 'auth/too-many-requests':
          errorMessage = '로그인 시도가 너무 많습니다. 잠시 후 다시 시도하세요.';
          break;
      }
      
      return {
        success: false,
        message: errorMessage
      };
    }
  },

  /**
   * 현재 로그인된 사용자 객체를 가져옵니다.
   * @returns Firebase User 객체 또는 null
   */
  getCurrentUser(): User | null {
    return auth.currentUser;
  },

  /**
   * 현재 로그인된 사용자의 Firestore 데이터를 가져옵니다.
   * @returns 사용자 데이터 객체 또는 null
   */
  async getCurrentUserData(): Promise<UserData | null> {
    const user = auth.currentUser;
    if (!user) return null;

    try {
      const docRef = doc(db, 'users', user.uid);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: user.uid, ...docSnap.data() } as UserData;
      }
      return null;
    } catch (error) {
      console.error('사용자 데이터 조회 오류:', error);
      return null;
    }
  },

  /**
   * 현재 사용자를 로그아웃 처리합니다.
   */
  async logout(): Promise<void> {
    try {
      await signOut(auth);
      // Vuex 스토어에서 사용자 정보 제거
      store.commit('clearTammUser');
      console.log('로그아웃 성공');
    } catch (error: any) {
      console.error('로그아웃 오류:', error.message);
      throw error;
    }
  },

  /**
   * 관리자가 사용자 승인을 처리합니다. (Firestore 문서 업데이트)
   * 특정 사용자의 'isApproved' 필드를 true로 변경합니다.
   * @param userId 승인할 사용자의 UID (Firebase Authentication에서 얻은 ID)
   * @returns 승인 결과 객체
   */
  async approveUser(userId: string): Promise<ApprovalResult> {
    try {
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);
      
      if (!userSnap.exists()) {
        return { success: false, message: '사용자를 찾을 수 없습니다.' };
      }
      
      // isApproved 필드만 true로 업데이트하고 다른 필드는 그대로 둡니다.
      await setDoc(userRef, { isApproved: true }, { merge: true });
      console.log(`사용자 ${userId} 승인 완료.`);
      
      return { success: true, message: '사용자 승인이 완료되었습니다.' };
    } catch (error: any) {
      console.error('사용자 승인 오류:', error.message);
      return { success: false, message: `승인 처리 중 오류가 발생했습니다: ${error.message}` };
    }
  },

  /**
   * 승인 대기 중인 사용자 목록을 가져옵니다. (관리자 패널용)
   * 'users' 컬렉션에서 'isApproved' 필드가 false인 문서들을 쿼리합니다.
   * @returns 승인 대기 중인 사용자 데이터 배열
   */
  async getPendingUsers(): Promise<PendingUser[]> {
    try {
      const q = query(collection(db, 'users'), where('isApproved', '==', false));
      const querySnapshot = await getDocs(q);

      const pendingUsers: PendingUser[] = [];
      querySnapshot.forEach((docSnap) => {
        // 문서 ID (UID)를 포함하여 반환합니다.
        pendingUsers.push({ 
          id: docSnap.id, 
          ...docSnap.data() 
        } as PendingUser);
      });
      return pendingUsers;
    } catch (error: any) {
      console.error('승인 대기 사용자 가져오기 오류:', error.message);
      throw error;
    }
  },

  /**
   * 모든 사용자 목록을 가져옵니다. (예시, 관리자용)
   * @returns 모든 사용자 데이터 배열
   */
  async getAllUsers(): Promise<UserData[]> {
    try {
      const querySnapshot = await getDocs(collection(db, 'users'));
      const allUsers: UserData[] = [];
      querySnapshot.forEach((docSnap) => {
        // Firestore 문서의 ID(UID)를 포함하여 반환합니다.
        allUsers.push({ 
          id: docSnap.id, 
          ...docSnap.data() 
        } as UserData);
      });
      return allUsers;
    } catch (error: any) {
      console.error('모든 사용자 가져오기 오류:', error.message);
      throw error;
    }
  },

  /**
   * 비밀번호 재설정 이메일을 전송합니다.
   * @param email 비밀번호를 재설정할 이메일 주소
   * @returns 전송 결과 객체
   */
  async sendPasswordResetEmail(email: string): Promise<{ success: boolean; message: string }> {
    try {
      await sendPasswordResetEmail(auth, email);
      return { success: true, message: '비밀번호 재설정 이메일이 전송되었습니다.' };
    } catch (error: any) {
      console.error('비밀번호 재설정 이메일 전송 오류:', error);
      let errorMessage = '이메일 전송 중 오류가 발생했습니다.';
      
      // Firebase 오류 코드에 따른 사용자 친화적인 메시지
      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = '해당 이메일로 등록된 계정이 없습니다.';
          break;
        case 'auth/invalid-email':
          errorMessage = '유효하지 않은 이메일 주소입니다.';
          break;
      }
      
      return { success: false, message: errorMessage };
    }
  },

  /**
   * 로그인 자격 증명 저장 (로컬 스토리지)
   * @param email 사용자 이메일
   * @param password 사용자 비밀번호
   */
  saveCredentials(email: string, password: string): void {
    try {
      // 보안을 위해 간단한 인코딩만 적용 (실제 암호화는 아님)
      const credentials = btoa(JSON.stringify({ email, password }));
      localStorage.setItem('tamm_credentials', credentials);
    } catch (error) {
      console.error('자격 증명 저장 오류:', error);
    }
  },

  /**
   * 저장된 로그인 자격 증명 가져오기
   * @returns 저장된 자격 증명 객체 또는 null
   */
  getSavedCredentials(): UserCredentials | null {
    try {
      const saved = localStorage.getItem('tamm_credentials');
      if (!saved) return null;
      
      // 디코딩하여 자격 증명 반환
      return JSON.parse(atob(saved));
    } catch (error) {
      console.error('자격 증명 로드 오류:', error);
      return null;
    }
  },

  /**
   * 저장된 로그인 자격 증명 삭제
   */
  clearSavedCredentials(): void {
    localStorage.removeItem('tamm_credentials');
  },

  /**
   * 사용자가 관리자인지 확인합니다.
   * @returns 관리자 여부
   */
  async isAdmin(): Promise<boolean> {
    const userData = await this.getCurrentUserData();
    return userData?.role === 'admin';
  }
};