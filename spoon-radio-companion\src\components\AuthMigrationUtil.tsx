import React, { useState } from 'react';
import { auth, db } from '../firebaseConfig';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import styled from 'styled-components';

const Container = styled.div`
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
`;

const Button = styled.button`
  background-color: #8b5cf6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  margin: 10px;
  
  &:hover {
    background-color: #7c3aed;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const LogArea = styled.div`
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  max-height: 400px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 14px;
  white-space: pre-wrap;
`;

const AuthMigrationUtil: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const migrateAuthUsersToFirestore = async () => {
    setLoading(true);
    setLogs([]);
    addLog('🔄 Firebase Authentication 사용자 마이그레이션 시작...');

    try {
      // Firebase Admin SDK를 사용하지 않고 클라이언트에서 직접 처리
      // 현재 로그인된 사용자만 처리할 수 있음
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        addLog('❌ 현재 로그인된 사용자가 없습니다. 관리자로 로그인해주세요.');
        return;
      }

      addLog(`📋 현재 사용자: ${currentUser.email}`);

      // 현재 사용자의 Firestore 문서 확인
      const userDocRef = doc(db, 'users', currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        addLog('🔧 현재 사용자를 Firestore에 추가합니다...');
        
        await setDoc(userDocRef, {
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName || '사용자',
          isApproved: currentUser.email === '<EMAIL>', // 관리자는 자동 승인
          createdAt: new Date().toISOString(),
          status: currentUser.email === '<EMAIL>' ? 'active' : 'pending',
          role: currentUser.email === '<EMAIL>' ? 'admin' : 'user'
        });
        
        addLog(`✅ 사용자 ${currentUser.email} Firestore 추가 완료`);
      } else {
        addLog(`ℹ️ 사용자 ${currentUser.email}는 이미 Firestore에 존재합니다.`);
      }

      addLog('✅ 마이그레이션 완료');
      
    } catch (error: any) {
      addLog(`❌ 마이그레이션 오류: ${error.message}`);
      console.error('마이그레이션 오류:', error);
    } finally {
      setLoading(false);
    }
  };

  const createMissingUsersManually = async () => {
    setLoading(true);
    setLogs([]);
    addLog('🔄 누락된 사용자 수동 생성 시작...');

    try {
      // 임시로 누락된 사용자들을 수동으로 생성
      const missingUsers = [
        {
          uid: 'temp-user-1',
          email: '<EMAIL>',
          displayName: '사용자1',
          isApproved: false,
          status: 'pending',
          role: 'user'
        },
        {
          uid: 'temp-user-2', 
          email: '<EMAIL>',
          displayName: '사용자2',
          isApproved: false,
          status: 'pending',
          role: 'user'
        }
      ];

      for (const user of missingUsers) {
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnap = await getDoc(userDocRef);

        if (!userDocSnap.exists()) {
          await setDoc(userDocRef, {
            ...user,
            createdAt: new Date().toISOString()
          });
          addLog(`✅ 임시 사용자 ${user.email} 생성 완료`);
        } else {
          addLog(`ℹ️ 사용자 ${user.email}는 이미 존재합니다.`);
        }
      }

      addLog('✅ 수동 사용자 생성 완료');
      
    } catch (error: any) {
      addLog(`❌ 수동 생성 오류: ${error.message}`);
      console.error('수동 생성 오류:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <Container>
      <h2>Firebase 사용자 마이그레이션 유틸리티</h2>
      <p>Firebase Authentication에 있는 사용자들을 Firestore로 마이그레이션합니다.</p>
      
      <div>
        <Button 
          onClick={migrateAuthUsersToFirestore}
          disabled={loading}
        >
          {loading ? '처리 중...' : '현재 사용자 마이그레이션'}
        </Button>
        
        <Button 
          onClick={createMissingUsersManually}
          disabled={loading}
        >
          {loading ? '처리 중...' : '테스트 사용자 생성'}
        </Button>
        
        <Button onClick={clearLogs}>로그 지우기</Button>
      </div>

      <LogArea>
        {logs.length === 0 ? '로그가 없습니다.' : logs.join('\n')}
      </LogArea>
    </Container>
  );
};

export default AuthMigrationUtil; 