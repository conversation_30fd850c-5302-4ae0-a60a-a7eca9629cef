const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 750,
    height: 1050,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    icon: process.platform === 'win32' 
      ? path.resolve(process.cwd(), 'public/app.ico')
      : path.resolve(process.cwd(), 'public/app.png')
  });

  mainWindow.loadFile(path.join(__dirname, 'dist_electron/index.html'));
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
}); 