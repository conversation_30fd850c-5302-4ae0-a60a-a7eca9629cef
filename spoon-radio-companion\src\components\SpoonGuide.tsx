import React from 'react';
import styled from 'styled-components';

const GuideContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const GuideCard = styled.div`
  background: white;
  border-radius: 24px;
  padding: 3rem;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #718096;
  line-height: 1.6;
`;

const StepsContainer = styled.div`
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
`;

const Step = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: #f7fafc;
  border-radius: 16px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  }
`;

const StepNumber = styled.div`
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  flex-shrink: 0;
`;

const StepContent = styled.div`
  flex: 1;
`;

const StepTitle = styled.h3`
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const StepDescription = styled.p`
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const DownloadButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }
`;

const ImportantNote = styled.div`
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  padding: 1.5rem;
  border-radius: 16px;
  margin: 2rem 0;
  border-left: 4px solid #e17055;
`;

const NoteTitle = styled.h4`
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const NoteText = styled.p`
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
`;

const BackButton = styled.button`
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    background: #cbd5e0;
    transform: translateY(-1px);
  }
`;

const SpoonLink = styled.a`
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  
  &:hover {
    text-decoration: underline;
    color: #764ba2;
  }
`;

const SpoonGuide: React.FC = () => {
  const handleBack = () => {
    window.history.back();
  };

  return (
    <GuideContainer>
      <GuideCard>
        <Header>
          <Title>스푼캐스트 연동 가이드</Title>
          <Subtitle>
            스푼캐스트와 연동하여 더 많은 기능을 이용해보세요!<br/>
            간단한 3단계로 연동을 완료할 수 있습니다.
          </Subtitle>
        </Header>

        <StepsContainer>
          <Step>
            <StepNumber>1</StepNumber>
            <StepContent>
              <StepTitle>확장프로그램 다운로드</StepTitle>
              <StepDescription>
                아래 버튼을 클릭하여 최신 확장프로그램을 다운로드하세요.
              </StepDescription>
              <DownloadButton href="/spoon-extension.zip" download>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M5 20h14v-2H5v2zM19 9h-4V3H9v6H5l7 7 7-7z"/>
                </svg>
                확장프로그램 다운로드
              </DownloadButton>
            </StepContent>
          </Step>

          <Step>
            <StepNumber>2</StepNumber>
            <StepContent>
              <StepTitle>Chrome에 설치하기</StepTitle>
              <StepDescription>
                1. Chrome 브라우저에서 <strong>chrome://extensions/</strong> 주소로 이동<br/>
                2. 우측 상단의 <strong>"개발자 모드"</strong> 활성화<br/>
                3. <strong>"압축해제된 확장프로그램을 로드합니다"</strong> 클릭<br/>
                4. 다운로드한 파일의 압축을 해제한 폴더 선택
              </StepDescription>
            </StepContent>
          </Step>

          <Step>
            <StepNumber>3</StepNumber>
            <StepContent>
              <StepTitle>스푼캐스트에서 로그인</StepTitle>
              <StepDescription>
                1. <SpoonLink href="https://www.spooncast.net/kr" target="_blank" rel="noopener noreferrer">spooncast.net</SpoonLink>에서 로그인<br/>
                2. 확장프로그램 아이콘 클릭<br/>
                3. <strong>"토큰 추출"</strong> 버튼 클릭<br/>
                4. Tamm에서 <strong>"스푼연동"</strong> 버튼 클릭하여 연동 완료!
              </StepDescription>
            </StepContent>
          </Step>
        </StepsContainer>

        <ImportantNote>
          <NoteTitle>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
            </svg>
            중요 안내
          </NoteTitle>
          <NoteText>
            • 확장프로그램은 Chrome 브라우저에서만 작동합니다<br/>
            • 스푼캐스트 로그인 상태를 유지해주세요<br/>
            • 연동 후에는 자동으로 토큰이 갱신됩니다
          </NoteText>
        </ImportantNote>

        <div style={{ textAlign: 'center' }}>
          <BackButton onClick={handleBack}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
            </svg>
            돌아가기
          </BackButton>
        </div>
      </GuideCard>
    </GuideContainer>
  );
};

export default SpoonGuide; 