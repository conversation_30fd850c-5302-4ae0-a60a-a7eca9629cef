import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { fetchAllBroadcasts, SpoonBroadcast } from '../services/spoonApi';
import BroadcastCard from './BroadcastCard';

// 스타일 컴포넌트들
const MainContainer = styled.div`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
`;

const ContentHeader = styled.div`
  margin-bottom: 24px;
  
  h2 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
`;

const BroadcastGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  p {
    margin-top: 16px;
    font-size: 16px;
    color: #666;
  }
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  p {
    font-size: 16px;
    color: #ff6b6b;
    margin-bottom: 16px;
    text-align: center;
  }
`;

const RetryButton = styled.button`
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: #ff5252;
  }
`;

// 인터페이스 정의
interface MainContentProps {
  onBroadcastSelect: (broadcast: SpoonBroadcast) => void;
  searchQuery: string;
}

const MainContent: React.FC<MainContentProps> = ({ onBroadcastSelect, searchQuery }) => {
  const [broadcasts, setBroadcasts] = useState<SpoonBroadcast[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [lastInteractionTime, setLastInteractionTime] = useState(Date.now());
  const [page, setPage] = useState(1); // 페이지 번호
  
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingElementRef = useRef<HTMLDivElement | null>(null);

  // 사용자 상호작용 감지 (성능 최적화)
  const updateInteractionTime = useCallback(() => {
    setLastInteractionTime(Date.now());
  }, []);

  // 스크롤, 마우스 이벤트 리스너 (성능 최적화)
  useEffect(() => {
    const events = ['scroll', 'mousedown', 'mousemove', 'keydown', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, updateInteractionTime, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateInteractionTime);
      });
    };
  }, [updateInteractionTime]);

  // 방송 데이터 로드 함수 (성능 최적화)
  const loadBroadcasts = async (cursor?: string, append: boolean = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setPage(1); // 첫 페이지로 리셋
      }
      setError(null);
      
      console.log('⚡ 빠른 로딩 시작...');
      const result = await fetchAllBroadcasts(30, cursor); // 페이지 크기 증가로 성능 향상
      
      if (append) {
        setBroadcasts(prev => [...prev, ...result.broadcasts]);
        setPage(prev => prev + 1); // 페이지 증가
      } else {
        setBroadcasts(result.broadcasts);
      }
      
      setNextCursor(result.nextCursor);
      console.log('⚡ 빠른 로딩 완료!');
    } catch (error) {
      console.error('방송 데이터 로드 실패:', error);
      setError('방송 데이터를 불러올 수 없습니다. 잠시 후 다시 시도해주세요.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 무한스크롤 콜백 (성능 최적화)
  const loadMoreBroadcasts = useCallback(() => {
    if (!loadingMore && nextCursor) {
      console.log('⚡ 무한스크롤 트리거');
      loadBroadcasts(nextCursor, true);
    }
  }, [loadingMore, nextCursor]);

  // Intersection Observer 설정 (성능 최적화)
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMoreBroadcasts();
        }
      },
      { 
        threshold: 0.5, // 더 빠른 트리거를 위해 50%로 증가
        rootMargin: '100px' // 미리 로드를 위한 마진 추가
      }
    );

    if (loadingElementRef.current) {
      observerRef.current.observe(loadingElementRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadMoreBroadcasts]);

  // 초기 로드 및 스마트 새로고침 (성능 최적화)
  useEffect(() => {
    loadBroadcasts();
  }, []); // 초기 로드는 한 번만

  // 스마트 새로고침을 별도 useEffect로 분리
  useEffect(() => {
    // 스마트 새로고침 (2분 간격, 사용자 활동 확인)
    const smartRefresh = () => {
      const now = Date.now();
      const timeSinceLastInteraction = now - lastInteractionTime;
      
      // 사용자가 2분 이상 비활성 상태이고 첫 페이지에 있을 때만 새로고침
      if (timeSinceLastInteraction > 120000 && page === 1) {
        console.log('⚡ 스마트 새로고침 실행');
        loadBroadcasts();
      }
    };

    const refreshInterval = setInterval(smartRefresh, 120000); // 2분 간격

    return () => {
      clearInterval(refreshInterval);
    };
  }, [lastInteractionTime, page]);

  // 검색 필터링 (성능 최적화)
  const filteredBroadcasts = React.useMemo(() => {
    if (!searchQuery) return broadcasts;
    
    return broadcasts.filter(broadcast => 
      broadcast.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      broadcast.author.nickname.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [broadcasts, searchQuery]);

  // 재시도 핸들러
  const handleRetry = () => {
    loadBroadcasts();
  };

  // 로딩 상태 렌더링
  if (loading && broadcasts.length === 0) {
    return (
      <MainContainer>
        <LoadingContainer>
          <LoadingSpinner />
          <p>⚡ 빠른 로딩 중...</p>
        </LoadingContainer>
      </MainContainer>
    );
  }

  // 에러 상태 렌더링
  if (error && broadcasts.length === 0) {
    return (
      <MainContainer>
        <ErrorContainer>
          <p>{error}</p>
          <RetryButton onClick={handleRetry}>다시 시도</RetryButton>
        </ErrorContainer>
      </MainContainer>
    );
  }

  return (
    <MainContainer>
      <ContentHeader>
        <h2>🎙️ 실시간 방송</h2>
      </ContentHeader>
      
      <BroadcastGrid>
        {filteredBroadcasts.map((broadcast) => (
          <BroadcastCard
            key={broadcast.id}
            broadcast={broadcast}
            onSelect={onBroadcastSelect}
          />
        ))}
      </BroadcastGrid>
      
      {/* 무한스크롤 트리거 */}
      {nextCursor && (
        <div ref={loadingElementRef} style={{ height: '20px', margin: '20px 0' }}>
          {loadingMore && (
            <LoadingContainer>
              <LoadingSpinner />
              <p>⚡ 더 많은 방송 로딩 중...</p>
            </LoadingContainer>
          )}
        </div>
      )}
      
      {/* 더 이상 로드할 데이터가 없을 때 */}
      {!nextCursor && broadcasts.length > 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#666' }}>
          <p>🎉 모든 방송을 확인했습니다!</p>
        </div>
      )}
    </MainContainer>
  );
};

export default MainContent; 