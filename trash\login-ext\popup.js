function isSpoonPage(url) {
    if ( /https?:\/\/(?:www\.)?spooncast.net/.test(url) ) {
        return true;
    }
    return false;
}

let currentUserInfo = null;
window.addEventListener('DOMContentLoaded', async () => {
    console.log('Window onload', new Date().toLocaleString());
    const [ tab ] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
    
    if ( !isSpoonPage(tab.url) ) {
        document.querySelector('#wrong-page').classList.remove('hidden');
        return;
    }

    chrome.tabs.sendMessage(tab.id, { method: 'getUserInfo' }, function(res) {
        if ( res.success ) {
            const userInfo = res.data;
            currentUserInfo = userInfo;
            document.querySelector('#login-page').classList.remove('hidden');
        } else {
            document.querySelector('#not-login').classList.remove('hidden');
        }
    });
});

document.querySelector('#login-btn').addEventListener('click', async () => {
    await fetch('http://localhost:19595/spoon-login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(currentUserInfo),
    });
    const [ tab ] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
    console.log('close tab', tab);
    chrome.tabs.remove(tab.id, function() { });
});