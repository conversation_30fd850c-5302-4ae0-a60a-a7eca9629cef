<!--
 * Index.vue
 * Created on Fri Nov 27 2020
 *
 * Copyright (c) Raravel. Licensed under the MIT License.
-->
<template>
	<v-main class="bot-container">
		<v-container class="pa-4">
			<v-card class="mx-auto bot-card" flat>
				<!-- 헤더 영역 -->
				<v-card-title class="py-3 bot-header">
					<v-row align="center" class="ma-0">
						<v-col cols="8" align="left" class="pa-0">
							<div class="d-flex align-center">
								<v-icon class="mr-2">mdi-robot</v-icon>
								<span class="text-subtitle-1 font-weight-medium">{{ $t('cmd.title') }}</span>
							</div>
						</v-col>
						<v-col cols="4" align="end" class="pa-0">
							<v-switch
								v-model="use"
								color="primary"
								class="mt-0"
								hide-details>
							</v-switch>
						</v-col>
					</v-row>
				</v-card-title>

				<v-card-text class="pt-4 px-0">
					<!-- 설명 영역 -->
					<p class="text-body-2 mb-4 description-text" v-html="$t('cmd.'+setType+'-desc')"></p>
					
					<!-- 탭 버튼 영역 -->
					<div class="command-tabs mb-4">
						<v-btn
							v-for="type of typeList"
							:key="type.href"
							:color="type.isActive(type.href) ? 'primary' : ''"
							:class="[
								'mr-2 mb-2 tab-button',
								type.isActive(type.href) ? 'active-tab' : 'inactive-tab'
							]"
							text
							small
							@click="$assign(type.href)"
						>
							<v-icon left x-small>{{ getIconForType(type.href) }}</v-icon>
							<span>{{ type.text }}</span>
						</v-btn>
					</div>

					<v-divider class="mb-4"></v-divider>

					<!-- 예제 및 저장 버튼 영역 -->
					<div class="command-description mb-4">
						<v-row align="center" class="ma-0">
							<v-col cols="12" sm="8" align="left" class="px-0">
								<div class="example-block">
									<span class="text-caption" v-html="$t('cmd.'+setType+'-ex')"></span>
								</div>
							</v-col>
							<v-col cols="12" sm="4" align="right" class="px-0 mt-3 mt-sm-0">
								<v-btn
									depressed
									small
									color="primary"
									:loading="loading"
									:disabled="loading"
									class="save-button"
									@click="save"
								>
									<v-icon left x-small>mdi-content-save</v-icon>
									{{ $t('apply') }}
								</v-btn>
							</v-col>
						</v-row>
					</div>

					<!-- 내용 영역 -->
					<div class="command-content">
						<transition name="fade-transition" mode="out-in">
							<router-view></router-view>
						</transition>
					</div>
				</v-card-text>
			</v-card>
		</v-container>
	</v-main>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import CfgLite from '@/plugins/cfg-lite-ipc';
import { LiveEvent } from '@sopia-bot/core';


@Component
export default class Cmd extends Mixins(GlobalMixins) {
	public setType: string = 'live_join';
	public use: boolean = this.$cfg.get(`cmd.${this.setType}.use`) ?? false;
	public cfgPath: string = this.$path('userData', 'cmd.cfg');
	public cfg: CfgLite = new CfgLite(this.cfgPath);
	public loading: boolean = false;

	public typeList: any[] = [
		{
			href: '/cmd/live_join/',
			text: this.$t('page.Join'),
			isActive: this.isActive.bind(this),
		},
		{
			href: '/cmd/live_like/',
			text: this.$t('page.Like'),
			isActive: this.isActive.bind(this),
		},
		{
			href: '/cmd/live_present/',
			text: this.$t('page.Present'),
			isActive: this.isActive.bind(this),
		},
		{
			href: '/cmd/live_message/',
			text: this.$t('page.Message'),
			isActive: this.isActive.bind(this),
		},
	];

	public liveLike: string = '';

	public render = {
		present: true,
	};

	public isActive(href: string) {
		return this.$route.path === href;
	}

	public getIconForType(href: string) {
		switch(href) {
			case '/cmd/live_join/': return 'mdi-login';
			case '/cmd/live_like/': return 'mdi-heart';
			case '/cmd/live_present/': return 'mdi-gift';
			case '/cmd/live_message/': return 'mdi-message-text';
			default: return 'mdi-check';
		}
	}

	public async mounted() {
		const m = this.$route.path.match(/\/cmd\/(.*)?\//);
		if ( m ) {
			this.setType = m[1] as string;
			this.use = this.$cfg.get(`cmd.${this.setType}.use`) ?? false;
		}
	}

	public save() {
		this.loading = true;
		this.$evt.$emit('cmd:save');
		this.$evt.$emit('cmd:reload');
		this.$swal({
			icon: 'success',
			toast: true,
			position: 'top-end',
			html: this.$t('save-success'),
			showCloseButton: false,
			showConfirmButton: false,
			timer: 5000,
		});

		this.$cfg.set(`cmd.${this.setType}.use`, this.use);
		if ( this.setType === LiveEvent.LIVE_PRESENT ) {
			this.$cfg.set(`cmd.${LiveEvent.LIVE_PRESENT_LIKE}.use`, this.use);
		}
		this.$cfg.save();
		this.$logger.success('cmd', `Save success config file. [${this.cfgPath}]`, this.cfg.get());
		this.loading = false;
		window.reloadCmdCfg();
	}
}
</script>
<style>
.bot-container {
  background-color: #ffffff;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

.bot-card {
  max-width: 800px;
  border-radius: 0;
  box-shadow: none !important;
}

.bot-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.description-text {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
  line-height: 1.4;
}

.example-block {
  background-color: #fafafa;
  padding: 12px;
  border-left: 2px solid rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.6);
}

.tab-button {
  font-size: 0.8rem !important;
  font-weight: 400;
  text-transform: none;
  letter-spacing: 0;
  min-width: auto;
  height: 30px !important;
  border-radius: 4px;
}

.active-tab {
  background-color: rgba(0, 0, 0, 0.04);
}

.inactive-tab {
  color: rgba(0, 0, 0, 0.6);
}

.save-button {
  font-weight: 400;
  text-transform: none;
  letter-spacing: 0;
  font-size: 0.8rem !important;
  height: 30px !important;
}

.command-tabs {
  display: flex;
  flex-wrap: wrap;
}

.fade-transition-enter-active, .fade-transition-leave-active {
  transition: opacity 0.2s;
}
.fade-transition-enter, .fade-transition-leave-to {
  opacity: 0;
}
</style>
