<!--
 * Leave.vue
 * Created on Fri Nov 27 2020
 *
 * Copyright (c) Raravel. Licensed under the MIT License.
-->
<template>
	<v-card flat class="mt-4">
		<v-card-text>
			<v-textarea
				v-model="liveLeave"
				outlined
				hide-details
				auto-grow
				rows="4"
				counter="500"
				:label="$t('cmd.message')"
				:placeholder="$t('cmd.message-input')"
				class="rounded-lg"
				background-color="grey lighten-5">
			</v-textarea>
			
			<v-alert
				v-if="liveLeave.length > 0"
				class="mt-4 rounded-lg"
				color="purple lighten-5"
				border="left"
				elevation="1"
				icon="mdi-information-outline">
				{{ $t('cmd.preview') || '미리보기' }}: <strong>{{ liveLeave }}</strong>
			</v-alert>
		</v-card-text>
	</v-card>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';

@Component
export default class Cmd extends Mixins(GlobalMixins) {
	public liveLeave: string = '';

	public created() {
		this.liveLeave = this.$cfg.get('cmd.live_leave.message') || '';
		this.$evt.$on('cmd:save', this.save);
	}

	public save() {
		this.$cfg.set('cmd.live_leave.message', this.liveLeave);
		this.$cfg.save();
	}

	public destroyed() {
		this.$evt.$off('cmd:save', this.save);
	}
}
</script>

<style scoped>
.v-textarea >>> .v-input__slot {
  min-height: 120px;
}
</style> 