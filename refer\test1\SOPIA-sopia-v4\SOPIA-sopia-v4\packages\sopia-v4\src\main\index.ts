import { NestFactory } from '@nestjs/core'
import type { MicroserviceOptions } from '@nestjs/microservices'
import { app } from 'electron'
import { ElectronIpcTransport } from '@doubleshot/nest-electron'
import { AppModule } from './app.module'
import { electronApp } from '@electron-toolkit/utils'
import { optimizer } from '@electron-toolkit/utils'

async function bootstrap() {
  try {
    await app.whenReady()
    electronApp.setAppUserModelId('com.sopia')

    // Default open or close DevTools by F12 in development
    // and ignore CommandOrControl + R in production.
    // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
    app.on('browser-window-created', (_, window) => {
      optimizer.watchWindowShortcuts(window)
    })

    // Quit when all windows are closed, except on macOS. There, it's common
    // for applications and their menu bar to stay active until the user quits
    // explicitly with Cmd + Q.
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })

    const nestApp = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
      strategy: new ElectronIpcTransport()
    })

    await nestApp.listen()
  } catch {
    app.quit()
  }
}

bootstrap()

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
