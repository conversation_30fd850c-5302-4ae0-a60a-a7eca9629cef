<template>
	<v-dialog v-model="dialog" max-width="1100" persistent>
		<v-card class="mailbox-card" elevation="24">
			<!-- 헤더 섹션 -->
			<div class="mailbox-header">
				<div class="header-content">
					<div class="header-icon">
						<v-icon size="32" color="white">mdi-email-multiple</v-icon>
					</div>
					<div class="header-text">
						<h2 class="header-title">관리자 편지함</h2>
						<p class="header-subtitle">사용자들의 메시지 관리</p>
					</div>
				</div>
				<v-btn 
					icon 
					large 
					color="white" 
					@click="close" 
					class="close-button"
					elevation="2"
				>
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</div>

			<v-divider class="divider-gradient"></v-divider>

			<!-- 메인 콘텐츠 -->
			<v-card-text class="main-content">
				<!-- 로딩 상태 -->
				<div v-if="isLoading" class="loading-section">
					<div class="loading-content">
						<v-progress-circular 
							indeterminate 
							color="primary" 
							size="64"
							width="4"
						></v-progress-circular>
						<h3 class="loading-title">편지함 로드 중</h3>
						<p class="loading-subtitle">메시지를 불러오고 있습니다...</p>
					</div>
				</div>

				<!-- 메인 콘텐츠 -->
				<div v-else class="content-section">
					<!-- 통계 카드 -->
					<div class="stats-row">
						<v-card class="stats-card total-card" elevation="8">
							<div class="stats-icon">
								<v-icon size="40" color="blue">mdi-email-multiple-outline</v-icon>
							</div>
							<div class="stats-content">
								<h3 class="stats-number">{{ totalMessages }}</h3>
								<p class="stats-label">전체 메시지</p>
							</div>
						</v-card>

						<v-card class="stats-card unread-card" elevation="8">
							<div class="stats-icon">
								<v-icon size="40" color="orange">mdi-email-outline</v-icon>
							</div>
							<div class="stats-content">
								<h3 class="stats-number">{{ unreadMessages }}</h3>
								<p class="stats-label">읽지 않음</p>
							</div>
						</v-card>

						<v-card class="stats-card read-card" elevation="8">
							<div class="stats-icon">
								<v-icon size="40" color="green">mdi-email-open-outline</v-icon>
							</div>
							<div class="stats-content">
								<h3 class="stats-number">{{ readMessages }}</h3>
								<p class="stats-label">읽음</p>
							</div>
						</v-card>
					</div>

					<!-- 메시지 목록 섹션 -->
					<v-card class="messages-section" elevation="12">
						<div class="section-header">
							<div class="section-title">
								<v-icon color="primary" class="mr-3">mdi-message-text-outline</v-icon>
								<h3>받은 메시지</h3>
							</div>
							<v-btn 
								color="primary" 
								@click="refreshMessages"
								:loading="isLoading"
								class="refresh-btn"
								elevation="2"
							>
								<v-icon left>mdi-refresh</v-icon>
								새로고침
							</v-btn>
						</div>

						<!-- 빈 상태 -->
						<div v-if="messages.length === 0" class="empty-state">
							<div class="empty-icon">
								<v-icon size="120" color="grey lighten-2">mdi-email-remove-outline</v-icon>
							</div>
							<h3 class="empty-title">받은 메시지가 없습니다</h3>
							<p class="empty-subtitle">아직 사용자들로부터 메시지를 받지 못했습니다.</p>
						</div>

						<!-- 메시지 카드 리스트 -->
						<div v-else class="messages-grid">
							<v-card 
								v-for="message in messages" 
								:key="message.id"
								class="message-card"
								elevation="6"
								:class="{ 'unread': !message.read, 'read': message.read }"
								@click="openMessage(message)"
							>
								<div class="message-header">
									<v-avatar size="48" class="message-avatar">
										<v-icon size="24" color="white">mdi-account</v-icon>
									</v-avatar>
									<div class="message-info">
										<h4 class="message-sender">{{ message.sender_name }}</h4>
										<h5 class="message-subject">{{ message.subject }}</h5>
										<div class="message-meta">
											<v-icon small color="grey">mdi-calendar</v-icon>
											<span>{{ formatDate(message.created_at) }}</span>
											<v-chip 
												v-if="!message.read" 
												x-small 
												color="orange" 
												text-color="white" 
												class="ml-2"
											>
												새로운 메시지
											</v-chip>
										</div>
									</div>
									<div class="message-status">
										<v-icon 
											:color="message.read ? 'green' : 'orange'" 
											size="24"
										>
											{{ message.read ? 'mdi-email-open' : 'mdi-email' }}
										</v-icon>
									</div>
								</div>

								<v-divider class="my-3"></v-divider>

								<div class="message-preview">
									<p class="message-content">{{ getPreviewText(message.content) }}</p>
									<div class="message-actions">
										<v-chip small color="primary" outlined>
											<v-icon left small>mdi-eye</v-icon>
											읽기
										</v-chip>
									</div>
								</div>
							</v-card>
						</div>
					</v-card>
				</div>
			</v-card-text>
		</v-card>
	</v-dialog>
</template>

<script lang="ts">
import { Component, Prop, Mixins, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { mailboxService, MailboxMessage } from '@/plugins/mailbox-service';

@Component
export default class AdminMailbox extends Mixins(GlobalMixins) {
	@Prop({ type: Boolean, default: false }) value!: boolean;

	public isLoading: boolean = false;
	public messages: MailboxMessage[] = [];

	public get dialog() {
		return this.value;
	}

	public set dialog(value: boolean) {
		this.$emit('input', value);
	}

	// 통계 데이터
	public get totalMessages(): number {
		return this.messages.length;
	}

	public get unreadMessages(): number {
		return this.messages.filter(msg => !msg.read).length;
	}

	public get readMessages(): number {
		return this.messages.filter(msg => msg.read).length;
	}

	@Watch('value')
	public onDialogOpen(newVal: boolean) {
		if (newVal) {
			this.loadMessages();
		}
	}

	public close() {
		this.dialog = false;
	}

	// 메시지 로드
	public async loadMessages() {
		this.isLoading = true;
		try {
			this.messages = await mailboxService.loadMessages();
		} catch (error) {
			console.error('메시지 로드 실패:', error);
		} finally {
			this.isLoading = false;
		}
	}

	// 메시지 새로고침
	public async refreshMessages() {
		await this.loadMessages();
	}

	// 메시지 열기
	public openMessage(message: MailboxMessage) {
		this.$emit('open-message', message);
	}

	// 메시지 미리보기 텍스트
	public getPreviewText(content: string): string {
		if (!content) return '';
		return content.length > 120 ? content.substring(0, 120) + '...' : content;
	}

	// 날짜 포맷팅
	public formatDate(dateString: string): string {
		try {
			const date = new Date(dateString);
			const now = new Date();
			const diffTime = Math.abs(now.getTime() - date.getTime());
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			
			if (diffDays === 1) {
				return '오늘';
			} else if (diffDays === 2) {
				return '어제';
			} else if (diffDays <= 7) {
				return `${diffDays - 1}일 전`;
			} else {
				return date.toLocaleDateString('ko-KR');
			}
		} catch (error) {
			return dateString;
		}
	}
}
</script>

<style scoped>
.mailbox-card {
	border-radius: 20px !important;
	overflow: hidden;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 헤더 스타일 */
.mailbox-header {
	background: linear-gradient(135deg, #e73c7e 0%, #23a6d5 100%);
	padding: 24px 32px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	overflow: hidden;
}

.mailbox-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="envelope" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M10,15 L25,25 L40,15 M10,15 L10,35 L40,35 L40,15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23envelope)"/></svg>');
	opacity: 0.3;
}

.header-content {
	display: flex;
	align-items: center;
	z-index: 1;
	position: relative;
}

.header-icon {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16px;
	padding: 16px;
	margin-right: 20px;
	backdrop-filter: blur(10px);
}

.header-text {
	color: white;
}

.header-title {
	font-size: 28px;
	font-weight: 700;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
	font-size: 16px;
	margin: 4px 0 0 0;
	opacity: 0.9;
	font-weight: 400;
}

.close-button {
	z-index: 1;
	position: relative;
	background: rgba(255, 255, 255, 0.2) !important;
	backdrop-filter: blur(10px);
}

.divider-gradient {
	height: 4px !important;
	background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
	border: none !important;
}

/* 메인 콘텐츠 */
.main-content {
	padding: 32px !important;
	background: #f8fafc;
	min-height: 500px;
}

/* 로딩 섹션 */
.loading-section {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 400px;
}

.loading-content {
	text-align: center;
}

.loading-title {
	margin-top: 24px;
	font-size: 24px;
	color: #2d3748;
	font-weight: 600;
}

.loading-subtitle {
	margin-top: 8px;
	color: #718096;
	font-size: 16px;
}

/* 통계 카드 */
.stats-row {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 20px;
	margin-bottom: 32px;
}

.stats-card {
	padding: 24px;
	border-radius: 16px !important;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stats-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
	z-index: 0;
}

.stats-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
}

.total-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.unread-card {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	color: white;
}

.read-card {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
	color: white;
}

.stats-icon {
	margin-right: 16px;
	z-index: 1;
	position: relative;
}

.stats-content {
	z-index: 1;
	position: relative;
}

.stats-number {
	font-size: 32px;
	font-weight: 700;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stats-label {
	font-size: 16px;
	margin: 4px 0 0 0;
	opacity: 0.9;
	font-weight: 500;
}

/* 메시지 섹션 */
.messages-section {
	border-radius: 20px !important;
	background: white;
	padding: 32px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24px;
}

.section-title {
	display: flex;
	align-items: center;
}

.section-title h3 {
	font-size: 24px;
	font-weight: 600;
	color: #2d3748;
	margin: 0;
}

.refresh-btn {
	border-radius: 12px !important;
	text-transform: none !important;
	font-weight: 600;
}

/* 빈 상태 */
.empty-state {
	text-align: center;
	padding: 64px 32px;
}

.empty-icon {
	margin-bottom: 24px;
}

.empty-title {
	font-size: 24px;
	color: #2d3748;
	font-weight: 600;
	margin-bottom: 12px;
}

.empty-subtitle {
	font-size: 16px;
	color: #718096;
	margin: 0;
}

/* 메시지 그리드 */
.messages-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
	gap: 20px;
}

.message-card {
	border-radius: 16px !important;
	padding: 24px;
	transition: all 0.3s ease;
	background: white;
	border: 2px solid transparent;
	cursor: pointer;
}

.message-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1) !important;
	border-color: #e2e8f0;
}

.message-card.unread {
	background: linear-gradient(135deg, #fff5f5 0%, #fef5e7 100%);
	border-color: #fed7d7;
}

.message-card.read {
	opacity: 0.8;
}

.message-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16px;
}

.message-avatar {
	background: linear-gradient(135deg, #e73c7e 0%, #23a6d5 100%) !important;
	margin-right: 16px;
	flex-shrink: 0;
}

.message-info {
	flex: 1;
	min-width: 0;
}

.message-sender {
	font-size: 16px;
	font-weight: 600;
	color: #2d3748;
	margin: 0 0 4px 0;
	word-break: break-word;
}

.message-subject {
	font-size: 14px;
	font-weight: 500;
	color: #4a5568;
	margin: 0 0 8px 0;
	word-break: break-word;
}

.message-meta {
	display: flex;
	align-items: center;
	font-size: 12px;
	color: #a0aec0;
}

.message-meta span {
	margin-left: 4px;
}

.message-status {
	flex-shrink: 0;
	margin-left: 12px;
}

.message-preview {
	padding-top: 8px;
}

.message-content {
	font-size: 14px;
	color: #718096;
	line-height: 1.5;
	margin: 0 0 16px 0;
	word-break: break-word;
}

.message-actions {
	display: flex;
	justify-content: flex-end;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
	.mailbox-header {
		padding: 20px;
		flex-direction: column;
		text-align: center;
	}

	.header-content {
		margin-bottom: 16px;
	}

	.main-content {
		padding: 20px !important;
	}

	.stats-row {
		grid-template-columns: 1fr;
		gap: 16px;
	}

	.messages-section {
		padding: 20px;
	}

	.section-header {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.messages-grid {
		grid-template-columns: 1fr;
	}
}

/* Custom 애니메이션 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.message-card {
	animation: fadeInUp 0.4s ease-out;
}

.message-card:nth-child(1) { animation-delay: 0.1s; }
.message-card:nth-child(2) { animation-delay: 0.2s; }
.message-card:nth-child(3) { animation-delay: 0.3s; }
.message-card:nth-child(4) { animation-delay: 0.4s; }
.message-card:nth-child(5) { animation-delay: 0.5s; }
.message-card:nth-child(6) { animation-delay: 0.6s; }
</style> 