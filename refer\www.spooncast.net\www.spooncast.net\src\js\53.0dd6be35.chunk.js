(window["webpackJsonpspooncast-web-next"] = window["webpackJsonpspooncast-web-next"] || []).push([
    [53], {
        1090: function(e, t, a) {
            e.exports = a.p + "src/images/header/header_search.9da375ac.png"
        },
        1265: function(e, t, a) {},
        1546: function(e, t) {
            e.exports = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTExLjc5OTggNkw1Ljk5OTggMTIuMDAwMUwxMS43OTk4IDE4IiBzdHJva2U9IiMzMzMzMzMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02IDEySDIwLjc4ODYiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg=="
        },
        1547: function(e, t) {
            e.exports = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xOC41IDkuNjg0OUMxOC41IDkuOTg4MjQgMTguMzg3OCAxMC4yOTE2IDE4LjE2MzQgMTAuNTIyOUwxMi43MDA4IDE2LjE1MjZDMTIuNDg1MSAxNi4zNzUzIDEyLjE5MjcgMTYuNSAxMS44ODc4IDE2LjVDMTEuNTgyOSAxNi41IDExLjI5MDQgMTYuMzc1MyAxMS4wNzUyIDE2LjE1MjZMNS44MzY2MSAxMC43NTM3QzUuMzg3OCAxMC4yOTExIDUuMzg3OCA5LjU0MDM1IDUuODM2NjEgOS4wNzgyM0M2LjI4NTQzIDguNjE1MTcgNy4wMTMzNyA4LjYxNTE3IDcuNDYyNjUgOS4wNzgyM0wxMS44ODc4IDEzLjYzOTJMMTYuNTM3NCA4Ljg0Njk0QzE2Ljk4NjIgOC4zODQzNSAxNy43MTQ2IDguMzg0MzUgMTguMTYzNCA4Ljg0Njk0QzE4LjM4NzggOS4wNzgyMyAxOC41IDkuMzgxNTcgMTguNSA5LjY4NDlaIiBmaWxsPSIjMzMzMzMzIi8+Cjwvc3ZnPgo="
        },
        1662: function(e, t, a) {},
        1663: function(e, t, a) {},
        1664: function(e, t, a) {},
        1665: function(e, t, a) {},
        1666: function(e, t, a) {
            e.exports = a.p + "src/images/message/img_empty_itembox.c52a80eb.svg"
        },
        1667: function(e, t, a) {},
        1668: function(e, t, a) {},
        1669: function(e, t, a) {
            e.exports = a.p + "src/images/message/img_empty_users.e2ad2681.svg"
        },
        1670: function(e, t, a) {},
        2514: function(e, t, a) {
            "use strict";
            a.r(t);
            var n = a(1),
                s = a(66),
                o = a.n(s),
                l = a(2406),
                i = a(413),
                c = a(96),
                r = a.n(c),
                d = a(0),
                u = a.n(d),
                m = a(63),
                g = a(293),
                v = a(420),
                b = a(6),
                h = a(27),
                f = a(212),
                p = a(70),
                O = a(242),
                j = a(412),
                E = a(8),
                I = a(150),
                M = a(18),
                C = a(164),
                k = a(125),
                y = a(126),
                w = a(201),
                _ = a(31),
                S = a(42),
                x = a(93),
                L = a(959),
                N = a(3),
                T = a(101),
                R = a(848),
                U = a(36),
                A = a(108),
                B = a(16),
                P = a(149),
                D = a.n(P),
                z = a(190),
                W = a(280),
                F = a(1662),
                G = a.n(F);
            const H = Object(M.a)(() => Promise.resolve().then(a.bind(null, 364))),
                Z = o.a.bind(G.a);
            var K = e => {
                    let {
                        message: t,
                        toUser: n,
                        isToUserStaff: s,
                        isPrevMyMessage: o,
                        isNextMyMessage: l
                    } = e;
                    const {
                        indexUrl: i,
                        string: c,
                        isWebview: r,
                        stickerList: m,
                        userInfoId: g,
                        userInfoNickname: h,
                        userInfoProfileUrl: f,
                        unfitWordList: p
                    } = Object(b.b)(e => {
                        var t, a, n, s, o, l, i, c;
                        return {
                            indexUrl: e.data.get("indexUrl"),
                            string: e.data.get("string"),
                            isWebview: e.data.get("isWebview"),
                            stickerList: e.sticker.list,
                            userInfoId: null !== (t = null === (a = e.auth) || void 0 === a ? void 0 : a.getIn(["userInfo", "id"])) && void 0 !== t ? t : -1,
                            userInfoNickname: null !== (n = null === (s = e.auth) || void 0 === s ? void 0 : s.getIn(["userInfo", "nickname"])) && void 0 !== n ? n : "",
                            userInfoTag: null !== (o = null === (l = e.auth) || void 0 === l ? void 0 : l.getIn(["userInfo", "tag"])) && void 0 !== o ? o : "",
                            userInfoProfileUrl: null !== (i = null === (c = e.auth) || void 0 === c ? void 0 : c.getIn(["userInfo", "profile_url"])) && void 0 !== i ? i : "",
                            unfitWordList: e.directMessage.unfitWordList
                        }
                    }), O = Object(b.a)(), j = Object(d.useRef)(null), [I, M] = Object(d.useState)(!1), C = Object(d.useMemo)(() => {
                        var e, a;
                        return null !== (e = null === t || void 0 === t || null === (a = t.author) || void 0 === a ? void 0 : a.id) && void 0 !== e ? e : -1
                    }, [t]), k = Object(d.useMemo)(() => Object(E.w)(t.timestamp), [t]), y = Object(d.useMemo)(() => {
                        var e, t;
                        return k.length > 0 ? k[0] ? null !== (e = null === (t = c.get("".concat(k[0]))) || void 0 === t ? void 0 : t.replace(/OOOO/g, k[1])) && void 0 !== e ? e : "" : k[1] : ""
                    }, [c, k]), w = Object(d.useMemo)(() => g === C, [g, C]), _ = Object(d.useMemo)(() => {
                        var e, a;
                        return w ? null !== (e = null === t || void 0 === t || null === (a = t.author) || void 0 === a ? void 0 : a.is_staff) && void 0 !== e && e : s
                    }, [t, s, w]), x = Object(d.useMemo)(() => _ ? c.get("spoon_manager") : w ? h : null === n || void 0 === n ? void 0 : n.nickname, [n, c, h, w, _]), L = Object(d.useMemo)(() => _ ? "" : w ? f : null === n || void 0 === n ? void 0 : n.profile_url, [n, f, w, _]), N = Object(d.useMemo)(() => {
                        var e, n, s, o;
                        if (null === t || void 0 === t || !t.sticker) return;
                        const l = null === (e = m.filter(e => e.name === t.sticker)) || void 0 === e ? void 0 : e[0];
                        return {
                            name: null !== (n = null === l || void 0 === l ? void 0 : l.name) && void 0 !== n ? n : t.sticker,
                            price: null !== (s = null === l || void 0 === l ? void 0 : l.price) && void 0 !== s ? s : -1,
                            image: null !== (o = null === l || void 0 === l ? void 0 : l.image_thumbnail_web) && void 0 !== o ? o : a(618)
                        }
                    }, [t, m]), T = Object(d.useMemo)(() => Object(z.a)(String(C), "home"), [C]), R = Object(d.useCallback)(() => {
                        console.log("handleClickBtnThumbnail ", L), L && O(Object(S.openModal)({
                            openModalType: "imageViewer",
                            modalParams: {
                                image: Object(E.kb)(L)
                            }
                        }))
                    }, [O, L]), U = e => w || _ ? e : P(e), P = e => {
                        const t = new RegExp(p.join("|"), "gi"),
                            a = e.match(t);
                        if (!a) return e;
                        return a.reduce((e, t) => e.replaceAll(t, "*".repeat(t.length)), e)
                    };
                    return Object(d.useEffect)(() => {
                        var e;
                        (null === (e = j.current) || void 0 === e ? void 0 : e.clientHeight) > 32 && M(!0)
                    }, []), u.a.createElement("div", {
                        className: Z("message-detail-list-item-container", {
                            my: w,
                            present: !!N
                        })
                    }, !w && u.a.createElement("div", {
                        className: Z("thumbnail-wrap")
                    }, !o && u.a.createElement(u.a.Fragment, null, u.a.createElement(W.a, {
                        nickname: x,
                        profileUrl: L,
                        size: "xs",
                        isStaff: _
                    }), !_ && C > -1 && (r ? u.a.createElement("div", {
                        title: x,
                        className: Z("btn-message"),
                        onClick: R
                    }) : u.a.createElement(H, {
                        className: Z("btn-profile"),
                        title: x,
                        to: {
                            pathname: "".concat(i).concat(T)
                        }
                    })))), u.a.createElement("div", {
                        className: Z("contents")
                    }, (null === N || void 0 === N ? void 0 : N.name) && u.a.createElement("div", {
                        className: Z("present-box")
                    }, u.a.createElement("div", {
                        className: Z("sticker-thumbnail")
                    }, u.a.createElement(A.c, {
                        src: N.image,
                        alt: x
                    })), u.a.createElement(A.e, {
                        className: Z("sticker-detail"),
                        dangerouslySetInnerHTML: {
                            __html: N.price > 0 ? "".concat(Object(E.lb)(c.get("gift_sticker_message"), "OOOO", Object(E.u)(N.price))) : D.a.sanitize(t.message)
                        }
                    })), !(null !== N && void 0 !== N && N.name) && u.a.createElement("div", {
                        className: Z("message-detail-wrap", {
                            "multi-line": I
                        })
                    }, u.a.createElement(A.e, {
                        className: Z("message"),
                        ref: j
                    }, U(t.message)), u.a.createElement("button", {
                        className: Z("message-more-btn"),
                        onClick: () => {
                            var e;
                            console.log("handleClickBtnMore"), O(Object(v.d)(!0)), O(Object(v.c)(null !== (e = U(null === t || void 0 === t ? void 0 : t.message)) && void 0 !== e ? e : ""))
                        }
                    }, u.a.createElement(B.e, {
                        icon: "ic_more_xs",
                        fill: B.d.gray[400]
                    }))), !l && u.a.createElement(A.d, {
                        className: Z("created")
                    }, y)))
                },
                Q = a(1545),
                J = a(1209);
            var Y = e => {
                    let {
                        sendPresent: t
                    } = e;
                    const {
                        string: a,
                        stickerCategories: n,
                        stickerList: s,
                        isLogin: o
                    } = Object(b.b)(e => ({
                        string: e.data.get("string"),
                        stickerCategories: e.sticker.categories,
                        stickerList: e.sticker.list,
                        isLogin: e.auth.get("isLogin")
                    })), l = Object(b.a)(), i = Object(d.useCallback)(() => {
                        n.length < 1 && s.length < 1 || l(o ? Object(S.openModal)({
                            openModalType: "directMessageGift",
                            modalParams: {
                                type: "cast"
                            },
                            callback: (e, a, n, s) => {
                                a > 0 && t(e, a, n, s)
                            }
                        }) : Object(S.openModal)({
                            openModalType: "account"
                        }))
                    }, [n.length, s.length, o, l, t]);
                    return u.a.createElement(Q.a, {
                        buttonTitle: a.get("store_gift"),
                        onClickSendGift: i,
                        type: J.STICKER_GIFT_TYPE.MESSAGE
                    })
                },
                V = a(1663),
                X = a.n(V);
            const q = o.a.bind(X.a);
            var $, ee, te, ae = Object(d.memo)((function(e) {
                    let {
                        sendMessage: t,
                        handleFocusMessageInput: a,
                        handleBlurMessageInput: n,
                        setInputMessageText: s,
                        isEnteredRoom: o,
                        inputMessageText: l
                    } = e;
                    const {
                        string: i,
                        isWebview: c,
                        toUserId: r
                    } = Object(k.e)(e => ({
                        string: e.data.get("string"),
                        isWebview: e.data.get("isWebview"),
                        toUserId: e.directMessage.toUserId
                    }), k.c), [m, g] = Object(d.useState)(!1), v = Object(d.useRef)(null), b = Object(d.useCallback)(() => {
                        console.log("onClickSendMessage"), t(), setTimeout(() => {
                            try {
                                console.log("inputMessageRef.current send", v.current), v.current.focus(), v.current.removeAttribute("style"), v.current.style.height = "18px"
                            } catch (e) {
                                console.log("inputMessageRef.current err", e)
                            }
                        })
                    }, [t]), h = Object(d.useCallback)(() => {
                        a(), g(!0)
                    }, [a]), f = Object(d.useCallback)(() => {
                        n(), g(!1)
                    }, [n]), p = Object(d.useCallback)(e => {
                        "Enter" !== e.key && 13 !== e.keyCode || e.shiftKey || 229 === e.keyCode || (b(), e.preventDefault())
                    }, [b]), O = Object(d.useCallback)(e => {
                        let t = e.currentTarget.value;
                        (t.replace(/[\n]/gi, "").length <= 0 || t.replace(/\s/gi, "").length <= 0) && (t = ""), t = Object(E.wb)(t, 400), s(t);
                        try {
                            v.current.removeAttribute("style");
                            const e = v.current.scrollHeight,
                                t = e > 54 ? 54 : e;
                            v.current.style.height = "".concat(t, "px")
                        } catch (e) {}
                    }, [s]);
                    return Object(d.useEffect)(() => {
                        if (!c && r > -1 && o) try {
                            var e;
                            null === v || void 0 === v || null === (e = v.current) || void 0 === e || e.focus()
                        } catch (e) {}
                    }, [c, r, o]), u.a.createElement("div", {
                        className: q("message-wrap", {
                            focus: m
                        })
                    }, u.a.createElement("textarea", {
                        className: q("input-message"),
                        placeholder: i.get("live_donation_message_placeholder"),
                        value: l,
                        onFocus: h,
                        onBlur: f,
                        onKeyDown: p,
                        onChange: O,
                        ref: v
                    }), c ? u.a.createElement("div", {
                        className: q("btn-send", {
                            on: l.length > 0
                        }),
                        title: i.get("common_send"),
                        onTouchStart: b
                    }, i.get("common_send")) : u.a.createElement(A.a, {
                        className: q("btn-send", {
                            on: l.length > 0
                        }),
                        title: i.get("common_send"),
                        onClick: b
                    }, i.get("common_send"), u.a.createElement("div", {
                        className: q("cover")
                    })))
                })),
                ne = a(839),
                se = a(20),
                oe = a(5),
                le = a(4);
            const ie = le.d.div($ || ($ = Object(oe.a)(["\n  position: absolute;\n  ", ";\n  width: 100%;\n  height: 100%;\n  z-index: 2;\n\n  box-sizing: border-box;\n  & * {\n    box-sizing: border-box;\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexColumnSet()
                }),
                ce = le.d.div(ee || (ee = Object(oe.a)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n"]))),
                re = le.d.div(te || (te = Object(oe.a)(["\n  position: relative;\n  z-index: 1;\n  /* width: calc(100% * 2 / 3); */\n  min-width: 246px;\n"]))),
                de = ["children"];
            var ue = e => {
                    let {
                        children: t
                    } = e, a = Object(se.a)(e, de);
                    return u.a.createElement(ie, null, u.a.createElement(ce, {
                        onClick: a.onClick
                    }), u.a.createElement(re, null, t))
                },
                me = a(1664),
                ge = a.n(me);
            const ve = Object(M.a)(() => a.e(115).then(a.bind(null, 1303))),
                be = o.a.bind(ge.a);
            class he extends d.PureComponent {
                constructor(e) {
                    super(e), this.scrollElm = void 0;
                    const {
                        countryCode: t,
                        isProduction: a
                    } = e, n = Object(i.n)(Object(j.a)(), "direct-message/".concat(a ? "prd" : origin.indexOf("dev") > -1 ? "dev" : "stg", "/").concat(t));
                    this.state = {
                        isNewRoom: !1,
                        isCreateRoomLoading: !1,
                        isMessagesLoading: !1,
                        isMessagesLoadEnd: !1,
                        isUserBlockLoading: !1,
                        subscribeInfo: null,
                        isToUserStaff: !1,
                        dbRef: n,
                        toUser: null,
                        lastMsgLeave: "",
                        inputMessageText: "",
                        messageList: [],
                        isEnteredRoom: !1
                    }, this.scrollElm = null, this.createRoom = this.createRoom.bind(this), this.addMessage = this.addMessage.bind(this), this.onValueMessages = this.onValueMessages.bind(this), this.getMessages = this.getMessages.bind(this), this.setMessage = this.setMessage.bind(this), this.sendMessage = this.sendMessage.bind(this), this.leaveRoom = this.leaveRoom.bind(this), this.sendPresent = this.sendPresent.bind(this), this.handleClickBtnLeave = this.handleClickBtnLeave.bind(this), this.handleClickBtnReport = this.handleClickBtnReport.bind(this), this.handleClickBtnBlock = this.handleClickBtnBlock.bind(this), this.handleScrollMessageList = this.handleScrollMessageList.bind(this), this.setInputMessageText = this.setInputMessageText.bind(this), this.onTransitionEnd = this.onTransitionEnd.bind(this), this.hideContextMenu = this.hideContextMenu.bind(this), this.handleClickCopy = this.handleClickCopy.bind(this), this.getUnfitWordList = this.getUnfitWordList.bind(this)
                }
                componentDidUpdate(e) {
                    const {
                        joinedRoomId: t,
                        toUserId: a
                    } = e, {
                        joinedRoomId: n,
                        toUserId: s
                    } = this.props, {
                        dbRef: o
                    } = this.state;
                    t !== n && (n && (t && Object(i.h)(Object(i.a)(o, "messages/".concat(t))), this.enterChatRoom()), n || (Object(i.h)(Object(i.a)(o, "messages/".concat(t))), this.initializeState())), a !== s && this.getStickerData(), a !== s && s > -1 && !n && this.checkRoomId(), this.getUnfitWordList()
                }
                componentWillUnmount() {
                    const {
                        joinedRoomId: e
                    } = this.props, {
                        dbRef: t
                    } = this.state;
                    Object(i.h)(Object(i.a)(t, "messages/".concat(e))), this.initializeState()
                }
                initializeState(e, t, a, s, o) {
                    this.setState(l => Object(n.a)(Object(n.a)({}, l), {}, {
                        isNewRoom: null !== e && void 0 !== e && e,
                        isCreateRoomLoading: !1,
                        isMessagesLoading: !1,
                        isMessagesLoadEnd: !1,
                        isUserBlockLoading: !1,
                        subscribeInfo: null !== o && void 0 !== o ? o : null,
                        isToUserStaff: null !== t && void 0 !== t && t,
                        toUser: null !== a && void 0 !== a ? a : null,
                        lastMsgLeave: null !== s && void 0 !== s ? s : "",
                        inputMessageText: "",
                        messageList: [],
                        isEnteredRoom: !1
                    }))
                }
                async checkRoomId() {
                    const {
                        DirectMessageActions: e,
                        userInfoId: t,
                        myRooms: a,
                        toUserId: n
                    } = this.props, {
                        dbRef: s,
                        toUser: o
                    } = this.state;
                    let l = "";
                    a && Object.values(a).forEach(e => {
                        l || e && null !== e && void 0 !== e && e.users && null !== e && void 0 !== e && e.users[n] && (l = e.id)
                    }), l ? e.enterChatRoom({
                        joinedRoomId: l,
                        toUserId: n
                    }) : (await Object(i.d)(Object(i.a)(s, "users/".concat(n))).then(async e => {
                        var t, a, l, c;
                        e.exists() || (console.log("GET users/".concat(n), n), await Object(i.o)(Object(i.a)(s, "users/".concat(n)), {
                            id: n,
                            nickname: null !== (t = null === o || void 0 === o ? void 0 : o.nickname) && void 0 !== t ? t : "",
                            tag: null !== (a = null === o || void 0 === o ? void 0 : o.tag) && void 0 !== a ? a : "",
                            profile_url: null !== (l = null === o || void 0 === o ? void 0 : o.profile_url) && void 0 !== l ? l : "",
                            tier: null !== (c = null === o || void 0 === o ? void 0 : o.tier) && void 0 !== c ? c : null,
                            rooms: null
                        }))
                    }).catch(e => {
                        console.log("GET users/".concat(n, " Error"), e)
                    }), Object(i.i)(Object(i.m)(Object(i.a)(s, "users/".concat(t, "/rooms")), Object(i.j)("is_leave"), Object(i.p)(!1)), a => {
                        const o = null === a || void 0 === a ? void 0 : a.val();
                        o && Object.values(o).forEach(e => {
                            l || e && null !== e && void 0 !== e && e.users && null !== e && void 0 !== e && e.users[n] && (l = e.id)
                        }), console.log("onValue users/".concat(t, "/rooms"), l), l || (l = Object(i.l)(Object(i.a)(s, "users/".concat(t, "/rooms"))).key), e.enterChatRoom({
                            joinedRoomId: l,
                            toUserId: n
                        })
                    }, {
                        onlyOnce: !0
                    }))
                }
                async enterChatRoom() {
                    const {
                        AlertActions: e,
                        ToastActions: t,
                        joinedRoomId: a,
                        toUserId: n,
                        myRooms: s,
                        handleClickBtnChatClose: o,
                        isWebview: l,
                        userInfoId: c,
                        userInfoNickname: r,
                        userInfoTag: d,
                        userInfoProfileUrl: u,
                        userInfoTier: m,
                        userInfoIsStaff: g,
                        string: v
                    } = this.props, {
                        dbRef: b
                    } = this.state;
                    let h = !s || Boolean(!s[a]),
                        f = null,
                        j = !1,
                        E = null,
                        I = "";
                    if ((!s || s && !s[a]) && await Object(i.d)(Object(i.a)(b, "users/".concat(c, "/rooms/").concat(a))).then(async e => {
                            if (e.exists()) {
                                const t = e.val();
                                null !== t && void 0 !== t && t.is_leave && (h = !1)
                            }
                        }), n) {
                        let s = "",
                            r = -1;
                        if (await Object(i.d)(Object(i.a)(b, "users/".concat(n, "/is_staff"))).then(async e => {
                                console.log("GET users/".concat(n, " Success"), e.exists(), e.val()), e.exists() && (j = Boolean(e.val()))
                            }).catch(e => {
                                console.log("GET users/".concat(c, " Error"), e);
                                const {
                                    toUserId: t
                                } = this.props;
                                t > -1 && this.enterChatRoom()
                            }), !j) try {
                            var M, C, k;
                            const e = await Object(p.c)(n);
                            if (E = null !== (M = null === e || void 0 === e || null === (C = e.data) || void 0 === C ? void 0 : C.results[0]) && void 0 !== M ? M : null, r = null !== (k = null === e || void 0 === e ? void 0 : e.status) && void 0 !== k ? k : -1, console.log("getProfileById Success ", e, r, a), 200 === r) try {
                                var y, w;
                                const e = await Object(O.a)(c, n.toString()),
                                    [t] = null !== (y = null === e || void 0 === e ? void 0 : e.results) && void 0 !== y ? y : null,
                                    a = null !== (w = null === e || void 0 === e ? void 0 : e.statusCode) && void 0 !== w ? w : -1;
                                f = {
                                    colorCode: t.colorCode,
                                    userPlanLevel: t.userPlanLevel,
                                    isSubscriber: Boolean(t.userPlanLevel)
                                }, console.log("getEffectAsync Success ", a, f, t)
                            } catch (e) {
                                console.log("getEffectAsync Error ", e)
                            } else s = "directMessageCanNotTalk"
                        } catch (e) {
                            var _, S;
                            r = null !== (_ = null === e || void 0 === e || null === (S = e.response) || void 0 === S ? void 0 : S.status) && void 0 !== _ ? _ : -1, s = "directMessageCanNotTalk", console.log("getProfileById Error ", r)
                        }
                        if (s) return l ? t.openToast({
                            message: v.get("direct_message_can_not_talk")
                        }) : e.openAlert({
                            openAlertType: s
                        }), this.leaveRoom(403 === r), void o()
                    }
                    await Object(i.d)(Object(i.a)(b, "rooms/".concat(a, "/users/").concat(c, "/last_msg_leave"))).then(e => {
                        var t;
                        e.exists() && (I = null !== (t = null === e || void 0 === e ? void 0 : e.val()) && void 0 !== t ? t : "")
                    });
                    const {
                        toUserId: x
                    } = this.props;
                    n === x && (this.initializeState(h, j, E, I, f), setTimeout(() => {
                        var e;
                        if (this.onValueMessages(), this.getMessages(), h) return;
                        const t = {};
                        t["rooms/".concat(a, "/users/").concat(c, "/id")] = c, t["rooms/".concat(a, "/users/").concat(c, "/nickname")] = r, t["rooms/".concat(a, "/users/").concat(c, "/tag")] = d, t["rooms/".concat(a, "/users/").concat(c, "/profile_url")] = u, t["rooms/".concat(a, "/users/").concat(c, "/tier")] = m && null !== (e = null === m || void 0 === m ? void 0 : m.toJS()) && void 0 !== e ? e : null, t["rooms/".concat(a, "/users/").concat(c, "/is_staff")] = g || null, Object(i.r)(b, t)
                    }))
                }
                async createRoom() {
                    var e, t, a, s, o;
                    const {
                        joinedRoomId: l,
                        userInfoId: c,
                        userInfoNickname: r,
                        userInfoTag: d,
                        userInfoProfileUrl: u,
                        userInfoTier: m,
                        userInfoIsStaff: g,
                        toUserId: v
                    } = this.props, {
                        isCreateRoomLoading: b,
                        isToUserStaff: h,
                        dbRef: f,
                        toUser: p
                    } = this.state;
                    if (v < 0 || !p) return;
                    const O = {
                        id: l,
                        title: "",
                        users: {
                            [v]: Object(n.a)({
                                id: v,
                                nickname: null !== (e = null === p || void 0 === p ? void 0 : p.nickname) && void 0 !== e ? e : "",
                                tag: null !== (t = null === p || void 0 === p ? void 0 : p.tag) && void 0 !== t ? t : "",
                                profile_url: null !== (a = null === p || void 0 === p ? void 0 : p.profile_url) && void 0 !== a ? a : "",
                                tier: null !== (s = null === p || void 0 === p ? void 0 : p.tier) && void 0 !== s ? s : null
                            }, h && {
                                is_staff: h
                            }),
                            [c]: Object(n.a)({
                                id: c,
                                nickname: g ? "" : r,
                                tag: g ? "" : d,
                                profile_url: g ? "" : u,
                                tier: !g && m && null !== (o = null === m || void 0 === m ? void 0 : m.toJS()) && void 0 !== o ? o : null
                            }, g && {
                                is_staff: g
                            })
                        },
                        timestamp: (new Date).toISOString()
                    };
                    b || (this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                        isCreateRoomLoading: !0
                    })), await Object(i.o)(Object(i.a)(f, "rooms/".concat(l)), O).then(e => {
                        console.log("SET rooms/".concat(l, " Success "), e);
                        const t = {},
                            a = {
                                id: l,
                                users: {
                                    [v]: {
                                        id: v
                                    },
                                    [c]: {
                                        id: c
                                    }
                                },
                                timestamp: (new Date).toISOString()
                            };
                        t["users/".concat(v, "/rooms/").concat(l)] = a, t["users/".concat(c, "/rooms/").concat(l)] = a, Object(i.r)(f, t), this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                            isNewRoom: !1
                        }))
                    }).catch(e => {
                        console.log("SET rooms/".concat(l, " Error "), e)
                    }), this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                        isCreateRoomLoading: !1
                    })))
                }
                addMessage(e) {
                    const {
                        joinedRoomId: t
                    } = this.props, {
                        messageList: a
                    } = this.state;
                    var s;
                    (console.log("addMessage ", t, e, a), t && e) && (a.length <= 0 ? this.setState(t => Object(n.a)(Object(n.a)({}, t), {}, {
                        messageList: [e]
                    })) : (null === e || void 0 === e ? void 0 : e.timestamp) > (null === (s = a[a.length - 1]) || void 0 === s ? void 0 : s.timestamp) && (this.setState(t => Object(n.a)(Object(n.a)({}, t), {}, {
                        messageList: a.concat([e])
                    })), setTimeout(() => {
                        this.scrollElm.scrollTop = this.scrollElm.scrollHeight
                    })))
                }
                onValueMessages() {
                    const {
                        joinedRoomId: e,
                        userInfoId: t
                    } = this.props, {
                        dbRef: a
                    } = this.state;
                    e && Object(i.i)(Object(i.m)(Object(i.a)(a, "messages/".concat(e)), Object(i.g)(1)), n => {
                        const {
                            messageList: s,
                            lastMsgLeave: o
                        } = this.state;
                        try {
                            var l, c;
                            const r = null === n || void 0 === n ? void 0 : n.val(),
                                d = null !== (l = r[Object.keys(r)[0]]) && void 0 !== l ? l : null,
                                u = s[s.length - 1];
                            if (console.log("onValue messages/".concat(e), null === d || void 0 === d ? void 0 : d.timestamp, null === u || void 0 === u ? void 0 : u.timestamp), !d || d && u && (null === d || void 0 === d ? void 0 : d.timestamp) < (null === u || void 0 === u ? void 0 : u.timestamp) || d.id === o) return;
                            this.addMessage(d), d.timestamp && (null === d || void 0 === d || null === (c = d.author) || void 0 === c ? void 0 : c.id) !== t && Object(i.r)(a, {
                                ["rooms/".concat(e, "/users/").concat(t, "/last_read")]: d.id
                            })
                        } catch (e) {}
                    })
                }
                async getMessages() {
                    var e;
                    const {
                        joinedRoomId: t,
                        maxListLength: a
                    } = this.props, {
                        isMessagesLoading: s,
                        isMessagesLoadEnd: o,
                        dbRef: l,
                        lastMsgLeave: c,
                        messageList: r
                    } = this.state;
                    if (!t || s || o) return;
                    this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                        isMessagesLoading: !0
                    }));
                    const d = Object(i.a)(l, "messages/".concat(t)),
                        u = r.length > 0 ? Object(i.m)(d, Object(i.k)(), Object(i.g)(30), Object(i.c)(null === (e = r[0]) || void 0 === e ? void 0 : e.id)) : Object(i.m)(d, Object(i.k)(), Object(i.g)(30));
                    await Object(i.d)(u).then(e => {
                        var s, o, l;
                        const i = null !== (s = null === e || void 0 === e ? void 0 : e.val()) && void 0 !== s ? s : null,
                            d = null !== (o = null === this || void 0 === this || null === (l = this.scrollElm) || void 0 === l ? void 0 : l.scrollHeight) && void 0 !== o ? o : 0;
                        if (console.log("GET messages/".concat(t, " Success "), i, e.size, r.length), e.exists() && i) {
                            let t = Object.keys(i),
                                s = [];
                            const o = t.findIndex(e => e === c);
                            t = t.slice(o + 1), t.forEach(e => {
                                s.push(i[e])
                            }), s = s.concat(r);
                            const l = s.length >= a || e.size <= 0 || o > -1;
                            this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                                isMessagesLoadEnd: l,
                                messageList: s
                            })), r.length <= 0 ? this.scrollElm.scrollTop = this.scrollElm.scrollHeight : this.scrollElm.scrollTop = this.scrollElm.scrollHeight - d
                        } else this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                            isMessagesLoadEnd: !0
                        }))
                    }).catch(e => {
                        console.log("GET messages/".concat(t, " Error "), e)
                    }), setTimeout(() => {
                        this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                            isMessagesLoading: !1
                        }))
                    })
                }
                async setMessage(e, t) {
                    const {
                        string: a,
                        joinedRoomId: s,
                        myRooms: o,
                        userInfoId: l,
                        toUserId: c
                    } = this.props, {
                        dbRef: r,
                        inputMessageText: d
                    } = this.state;
                    if (console.log("setMessage ", c), c < 0) return;
                    const u = (new Date).toISOString(),
                        m = Object(i.l)(Object(i.a)(r, "messages/".concat(s))).key,
                        g = e ? Object(E.lb)(a.get("gift_sticker_message"), "OOOO", Object(E.u)(t)) : d,
                        v = e ? a.get("direct_message_send_present") : d;
                    if ((null === g || void 0 === g ? void 0 : g.length) > 400 || !g.length) return;
                    const b = Object(n.a)({
                        id: m,
                        author: {
                            id: l
                        },
                        message: g,
                        timestamp: u
                    }, e && {
                        sticker: e
                    });
                    console.log("newMessageKey ", m), await Object(i.o)(Object(i.a)(r, "messages/".concat(s, "/").concat(m)), b).then(() => {
                        var e, t;
                        console.log("SET messages/".concat(s), g);
                        const a = null !== (e = null === (t = (null !== o && void 0 !== o ? o : {})[s]) || void 0 === t ? void 0 : t.users) && void 0 !== e ? e : {},
                            n = {};
                        n["rooms/".concat(s, "/title")] = v, n["rooms/".concat(s, "/timestamp")] = u, n["rooms/".concat(s, "/users/").concat(l, "/last_read")] = m, n["users/".concat(l, "/rooms/").concat(s, "/is_leave")] = !1, n["users/".concat(c, "/rooms/").concat(s, "/is_leave")] = !1, Object.keys(a).forEach(e => {
                            n["users/".concat(e, "/rooms/").concat(s, "/timestamp")] = u
                        }), Object(i.r)(r, n);
                        try {
                            Object(h.r)({
                                room_id: s,
                                to_user_id: c,
                                msg: v
                            })
                        } catch (e) {}
                    })
                }
                async sendMessage() {
                    const {
                        joinedRoomId: e
                    } = this.props, {
                        isNewRoom: t,
                        isToUserStaff: a,
                        toUser: s,
                        inputMessageText: o
                    } = this.state;
                    console.log("sendMessage ", t, o), !e || !a && !s || o.length <= 0 || (t && await this.createRoom(), this.setMessage(), this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                        inputMessageText: ""
                    })))
                }
                leaveRoom(e) {
                    const {
                        joinedRoomId: t,
                        userInfoId: a
                    } = this.props, {
                        isNewRoom: n,
                        dbRef: s,
                        toUser: o,
                        messageList: l
                    } = this.state, c = l[l.length - 1];
                    if (console.log("leaveRoom ", e, t, a, n, c), !t || a < 0 || n) return;
                    const r = {};
                    r["users/".concat(a, "/rooms/").concat(t, "/is_leave")] = !0, c && (r["rooms/".concat(t, "/users/").concat(a, "/last_msg_leave")] = c.id), e && null !== o && void 0 !== o && o.id && (r["users/".concat(null === o || void 0 === o ? void 0 : o.id, "/rooms/").concat(t, "/is_leave")] = !0, c && (r["rooms/".concat(t, "/users/").concat(null === o || void 0 === o ? void 0 : o.id, "/last_msg_leave")] = c.id)), Object(i.r)(s, r)
                }
                handleClickBtnLeave() {
                    console.log("handleClickBtnLeave");
                    const {
                        AlertActions: e
                    } = this.props;
                    e.openAlert({
                        openAlertType: "directMessageLeaveRoomQ",
                        callback: async e => {
                            e && this.leaveRoom()
                        }
                    })
                }
                handleClickBtnReport() {
                    const {
                        ModalActions: e,
                        AlertActions: t,
                        ToastActions: a,
                        joinedRoomId: n,
                        isWebview: s,
                        userInfoId: o,
                        string: l
                    } = this.props, {
                        dbRef: c,
                        toUser: r
                    } = this.state;
                    console.log("handleClickBtnBlock ", r), r && e.openModal({
                        openModalType: "report",
                        modalParams: {
                            type: "dm",
                            reportId: r.id,
                            nickname: r.nickname
                        },
                        callback: async r => {
                            await Object(i.o)(Object(i.a)(c, "rooms/".concat(n, "/reports/users/").concat(o)), r).then(() => {
                                e.closeModal(), s ? a.openToast({
                                    message: l.get("result_reported")
                                }) : setTimeout(() => {
                                    t.openAlert({
                                        openAlertType: "reportSuccess"
                                    })
                                });
                                const o = {};
                                o["rooms/".concat(n, "/reports/timestamp")] = (new Date).toISOString(), Object(i.r)(c, o)
                            })
                        }
                    })
                }
                handleClickBtnBlock() {
                    const {
                        AlertActions: e,
                        ToastActions: t,
                        isWebview: a,
                        string: s
                    } = this.props, {
                        isUserBlockLoading: o,
                        toUser: l
                    } = this.state;
                    !o && l && e.openAlert({
                        openAlertType: "setBlockUser",
                        alertParams: {
                            nickname: l.nickname
                        },
                        callback: async o => {
                            if (o) {
                                this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                                    isUserBlockLoading: !0
                                }));
                                try {
                                    var i;
                                    const n = await Object(p.x)(l.id);
                                    200 === (null !== (i = null === n || void 0 === n ? void 0 : n.status) && void 0 !== i ? i : -1) && (a ? t.openToast({
                                        message: Object(E.yb)(s.get("result_blocked"), {
                                            OOOO: l.nickname
                                        })
                                    }) : e.openAlert({
                                        openAlertType: "blockedUser",
                                        alertParams: {
                                            nickname: l.nickname
                                        }
                                    }), this.leaveRoom(!0))
                                } catch (e) {
                                    Object(N.p)(e)
                                }
                                this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                                    isUserBlockLoading: !1
                                }))
                            }
                        }
                    })
                }
                handleScrollMessageList(e) {
                    var t, a;
                    const {
                        joinedRoomId: n,
                        maxListLength: s
                    } = this.props, {
                        isMessagesLoading: o,
                        messageList: l
                    } = this.state, i = null !== (t = null === e || void 0 === e || null === (a = e.target) || void 0 === a ? void 0 : a.scrollTop) && void 0 !== t ? t : 0;
                    n && !o && l.length > 0 && l.length < s && i <= 0 && this.getMessages()
                }
                getStickerData() {
                    const {
                        StickerActions: e,
                        stickerCategories: t,
                        stickerList: a
                    } = this.props;
                    try {
                        t.length < 1 && a.length < 1 && e.getStickerDataAsync()
                    } catch (e) {}
                }
                async sendPresent(e, t, a, n) {
                    const {
                        AlertActions: s,
                        DirectMessageActions: o,
                        BalanceActions: l,
                        joinedRoomId: i,
                        userInfoId: c,
                        string: r,
                        userAgent: d,
                        toUserId: u,
                        isWebview: m
                    } = this.props, {
                        isNewRoom: g,
                        isToUserStaff: v,
                        toUser: b,
                        inputMessageText: h
                    } = this.state;
                    if (console.log("sendMessage ", g, h), i && (v || b)) try {
                        if (200 !== (await Object(p.t)(u, {
                                sticker: e
                            })).status) return;
                        const i = e.indexOf("sticker_like") < 0;
                        Object(L.a)({
                            amount: t,
                            itemId: e,
                            itemType: i ? "sticker" : "10like",
                            targetUserId: u,
                            type: "dm"
                        }), Object(T.d)() && window.kakaoPixel(U.w).search({
                            keyword: "dm",
                            tag: "send_spoon"
                        }), g && await this.createRoom(), this.setMessage(e, t), m || s.openAlert({
                            openAlertType: "sendStickerComplete",
                            alertParams: {
                                stickerPrice: t,
                                sendStickerIdx: a,
                                sendStickerCategoryIdx: n
                            }
                        }), o.setSendStickerIdx(a), o.setSendStickerCategoryIdx(n), l.getUserBalanceAsync(c)
                    } catch (e) {
                        var f;
                        if (406 === (null === e || void 0 === e || null === (f = e.response) || void 0 === f ? void 0 : f.status))
                            if (m)
                                if (/android/.test(d)) window.SpoonCommonBridge.onShowMessage(r.get("sticker_vip_guide"));
                                else {
                                    var O;
                                    const e = null === (O = window.webkit) || void 0 === O || null === (O = O.messageHandlers) || void 0 === O ? void 0 : O.commonMessageHandler;
                                    null === e || void 0 === e || e.postMessage({
                                        type: "showToastError",
                                        body: {
                                            message: r.get("sticker_vip_guide")
                                        }
                                    })
                                }
                        else s.openAlert({
                            openAlertType: "sendStickerVipGuide"
                        });
                        console.error("sendPresent Error ", e)
                    }
                }
                setInputMessageText(e) {
                    this.setState(t => Object(n.a)(Object(n.a)({}, t), {}, {
                        inputMessageText: e
                    }))
                }
                onTransitionEnd() {
                    const {
                        toUserId: e
                    } = this.props;
                    console.log("onTransitionEnd ", e), e < 0 || this.setState(e => Object(n.a)(Object(n.a)({}, e), {}, {
                        isEnteredRoom: !0
                    }))
                }
                hideContextMenu() {
                    const {
                        DirectMessageTabActions: e
                    } = this.props;
                    e.setShowContextMenu(!1)
                }
                async handleClickCopy() {
                    const {
                        DirectMessageTabActions: e,
                        copyMessage: t
                    } = this.props;
                    if (t) try {
                        if (!await Object(R.a)(t)) return
                    } catch (e) {
                        console.error(e)
                    } finally {
                        e.setShowContextMenu(!1)
                    }
                }
                async getUnfitWordList() {
                    const {
                        DirectMessageActions: e,
                        unfitWordList: t,
                        serverSettings: a
                    } = this.props;
                    if (!t.length) try {
                        const t = await Object(h.d)();
                        if (200 === t.status) {
                            const {
                                data: n
                            } = t, s = null === a || void 0 === a ? void 0 : a.DM_UNFIT_WORD_LIST;
                            e.setUnfitWordList(Array.from(new Set([...n.results, ...s])))
                        }
                    } catch (e) {
                        console.error(e)
                    }
                }
                render() {
                    var e;
                    const {
                        totalUnReadMsgCount: t,
                        handleClickBtnChatClose: n,
                        handleClickBtnToggleChat: s,
                        handleFocusMessageInput: o,
                        handleBlurMessageInput: l,
                        string: i,
                        isWebview: c,
                        maxListLength: r,
                        isOpenChat: d,
                        toUserId: m,
                        isShowContextMenu: g
                    } = this.props, {
                        subscribeInfo: v,
                        isToUserStaff: b,
                        toUser: h,
                        inputMessageText: f,
                        messageList: p,
                        isEnteredRoom: O
                    } = this.state, j = t;
                    let E = [{
                        label: i.get("common_leave"),
                        onClick: this.handleClickBtnLeave
                    }];
                    return b || (E = E.concat([{
                        label: i.get("common_report"),
                        onClick: this.handleClickBtnReport
                    }, {
                        label: i.get("common_block"),
                        onClick: this.handleClickBtnBlock
                    }])), u.a.createElement("div", {
                        className: be("direct-message-detail-container", {
                            on: m > -1,
                            webview: c
                        }),
                        onTransitionEnd: this.onTransitionEnd
                    }, u.a.createElement("div", {
                        className: be("message-detail-header")
                    }, u.a.createElement("div", {
                        className: be("left")
                    }, c ? u.a.createElement("div", {
                        title: i.get("login_recommend_back"),
                        className: be("btn-back", {
                            hide: !d
                        }),
                        onClick: n
                    }, u.a.createElement(A.c, {
                        src: a(1546),
                        alt: i.get("login_recommend_back")
                    }), "0" !== j && u.a.createElement("span", {
                        className: be("unread-count", {
                            "auto-width": j.length > 1
                        })
                    }, j)) : u.a.createElement(A.a, {
                        title: i.get("login_recommend_back"),
                        className: be("btn-back", {
                            hide: !d
                        }),
                        onClick: n
                    }, u.a.createElement(A.c, {
                        src: a(1546),
                        alt: i.get("login_recommend_back")
                    }), "0" !== j && u.a.createElement("span", {
                        className: be("unread-count", {
                            "auto-width": j.length > 1
                        })
                    }, j))), u.a.createElement("div", {
                        className: be("title", {
                            hide: !d
                        })
                    }, u.a.createElement("p", {
                        className: be("nickname", "text-box", {
                            subscribe: null === v || void 0 === v ? void 0 : v.isSubscriber
                        })
                    }, (null === v || void 0 === v ? void 0 : v.isSubscriber) && u.a.createElement(ne.a, {
                        className: "badge",
                        level: v.userPlanLevel,
                        colorCode: v.colorCode
                    }), b ? i.get("spoon_manager") : null !== (e = null === h || void 0 === h ? void 0 : h.nickname) && void 0 !== e ? e : ""), !b && (null === h || void 0 === h ? void 0 : h.tag) && u.a.createElement("p", {
                        className: be("tag", "text-box", {
                            subscribe: null === v || void 0 === v ? void 0 : v.isSubscriber
                        })
                    }, "@".concat(h.tag))), u.a.createElement("div", {
                        className: be("right")
                    }, d && p.length > 0 && u.a.createElement(ve, {
                        color: 80,
                        list: E
                    }), !d && "0" !== j && u.a.createElement("p", {
                        className: be("unread-count", {
                            "auto-width": j.length > 1
                        })
                    }, j), u.a.createElement("div", {
                        className: be("btn-toggle-dm", {
                            hide: !d
                        })
                    }, !c && u.a.createElement(A.a, {
                        className: be("btn", {
                            on: d
                        }),
                        onClick: s
                    }, u.a.createElement(A.c, {
                        src: a(1547),
                        alt: i.get("common_search")
                    })))), !d && u.a.createElement(A.a, {
                        className: be("btn-open-message"),
                        title: "".concat(h ? "".concat(h.nickname, "(@").concat(h.tag, ")") : ""),
                        onClick: s
                    })), u.a.createElement("div", {
                        className: be("list-wrap-notice"),
                        dangerouslySetInnerHTML: {
                            __html: i.get("direct_message_room_notice")
                        }
                    }), u.a.createElement("div", {
                        className: be("list-wrap"),
                        ref: e => {
                            this.scrollElm || (this.scrollElm = e)
                        },
                        onScroll: this.handleScrollMessageList
                    }, (null === p || void 0 === p ? void 0 : p.length) > 0 && u.a.createElement("ul", {
                        className: be("list")
                    }, p.map((e, t) => {
                        var a, n, s, o, l, i;
                        const c = p.length - r,
                            d = null !== (a = null === e || void 0 === e || null === (n = e.author) || void 0 === n ? void 0 : n.id) && void 0 !== a ? a : -1,
                            m = null !== (s = p[t - 1]) && void 0 !== s ? s : null,
                            g = null !== (o = p[t + 1]) && void 0 !== o ? o : null,
                            v = !!m && (null === m || void 0 === m || null === (l = m.author) || void 0 === l ? void 0 : l.id) === d,
                            f = !!g && (null === g || void 0 === g || null === (i = g.author) || void 0 === i ? void 0 : i.id) === d;
                        return t > c && u.a.createElement("li", {
                            className: be("list-item", {
                                combo: v
                            }),
                            key: t
                        }, u.a.createElement(K, {
                            message: e,
                            toUser: h,
                            isToUserStaff: b,
                            isPrevMyMessage: v,
                            isNextMyMessage: f
                        }))
                    }))), m > -1 && u.a.createElement("div", {
                        className: be("message-detail-bottom")
                    }, u.a.createElement(Y, {
                        sendPresent: this.sendPresent
                    }), u.a.createElement(ae, {
                        sendMessage: this.sendMessage,
                        handleFocusMessageInput: o,
                        handleBlurMessageInput: l,
                        setInputMessageText: this.setInputMessageText,
                        inputMessageText: f,
                        isEnteredRoom: O
                    })), g && u.a.createElement(ue, {
                        onClick: this.hideContextMenu
                    }, u.a.createElement("div", {
                        className: "dm-context-menu"
                    }, u.a.createElement("button", {
                        onClick: this.handleClickCopy
                    }, i.get("common_copy")))))
                }
            }
            var fe = Object(k.b)(e => {
                    var t, a, n, s, o, l, i, c, r, d, u, m;
                    return {
                        origin: e.data.get("origin"),
                        userAgent: e.data.get("userAgent"),
                        countryCode: e.data.get("countryCode"),
                        string: e.data.get("string"),
                        isProduction: e.data.get("isProduction"),
                        isWebview: e.data.get("isWebview"),
                        maxListLength: e.data.get("maxListLength"),
                        stickerCategories: e.sticker.categories,
                        stickerList: e.sticker.list,
                        userInfoId: null !== (t = null === (a = e.auth) || void 0 === a ? void 0 : a.getIn(["userInfo", "id"])) && void 0 !== t ? t : -1,
                        userInfoNickname: null !== (n = null === (s = e.auth) || void 0 === s ? void 0 : s.getIn(["userInfo", "nickname"])) && void 0 !== n ? n : "",
                        userInfoTag: null !== (o = null === (l = e.auth) || void 0 === l ? void 0 : l.getIn(["userInfo", "tag"])) && void 0 !== o ? o : "",
                        userInfoProfileUrl: null !== (i = null === (c = e.auth) || void 0 === c ? void 0 : c.getIn(["userInfo", "profile_url"])) && void 0 !== i ? i : "",
                        userInfoTier: null !== (r = null === (d = e.auth) || void 0 === d ? void 0 : d.getIn(["userInfo", "tier"])) && void 0 !== r ? r : "",
                        userInfoIsStaff: null !== (u = null === (m = e.auth) || void 0 === m ? void 0 : m.getIn(["userInfo", "is_staff"])) && void 0 !== u ? u : "",
                        isOpenChat: e.directMessage.isOpenChat,
                        joinedRoomId: e.directMessage.joinedRoomId,
                        toUserId: e.directMessage.toUserId,
                        isShowContextMenu: e.directMessageTab.isShowContextMenu,
                        copyMessage: e.directMessageTab.copyMessage,
                        unfitWordList: e.directMessage.unfitWordList,
                        serverSettings: e.commonConfig.serverSettings
                    }
                }, e => ({
                    StickerActions: Object(C.b)({
                        getStickerDataAsync: w.e
                    }, e),
                    ModalActions: Object(C.b)({
                        openModal: S.openModal,
                        closeModal: S.closeModal
                    }, e),
                    AlertActions: Object(C.b)({
                        openAlert: _.openAlert,
                        closeAlert: _.closeAlert
                    }, e),
                    ToastActions: Object(C.b)({
                        openToast: x.openToast
                    }, e),
                    DirectMessageActions: Object(C.b)(g, e),
                    DirectMessageTabActions: Object(C.b)({
                        setShowContextMenu: v.d
                    }, e),
                    BalanceActions: Object(C.b)({
                        getUserBalanceAsync: y.b
                    }, e)
                }))(he),
                pe = a(258),
                Oe = a.n(pe),
                je = a(1265),
                Ee = a.n(je);
            const Ie = Object(M.a)(() => Promise.resolve().then(a.bind(null, 364))),
                Me = o.a.bind(Ee.a);
            var Ce = e => {
                    let {
                        data: t,
                        renderTick: a,
                        handleClickBtnRoom: n,
                        handleTotalUnReadMsgCount: s,
                        handleNewMessage: o
                    } = e;
                    const {
                        origin: l,
                        indexUrl: c,
                        countryCode: r,
                        string: m,
                        isProduction: g,
                        isWebview: v,
                        userInfoId: h,
                        joinedRoomId: f,
                        unfitWordList: p
                    } = Object(b.b)(e => {
                        var t, a;
                        return {
                            origin: e.data.get("origin"),
                            indexUrl: e.data.get("indexUrl"),
                            countryCode: e.data.get("countryCode"),
                            string: e.data.get("string"),
                            isProduction: e.data.get("isProduction"),
                            isWebview: e.data.get("isWebview"),
                            userInfoId: null !== (t = null === (a = e.auth) || void 0 === a ? void 0 : a.getIn(["userInfo", "id"])) && void 0 !== t ? t : -1,
                            joinedRoomId: e.directMessage.joinedRoomId,
                            unfitWordList: e.directMessage.unfitWordList
                        }
                    }), [O, I] = Object(d.useState)(null), [M, C] = Object(d.useState)(0), [k, y] = Object(d.useState)(null), [w, _] = Object(d.useState)(null), [S, x] = Object(d.useState)(!1), [L, N] = Object(d.useState)(!0), T = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === t || void 0 === t ? void 0 : t.id) && void 0 !== e ? e : ""
                    }, [t]), R = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === O || void 0 === O ? void 0 : O.title) && void 0 !== e ? e : ""
                    }, [O]), U = Object(d.useMemo)(() => {
                        try {
                            var e, t, a;
                            const n = null !== (e = null === (t = Object.keys(null === O || void 0 === O ? void 0 : O.users)) || void 0 === t ? void 0 : t.find(e => parseInt(e) !== h)) && void 0 !== e ? e : "";
                            return null !== (a = null === O || void 0 === O ? void 0 : O.users[n]) && void 0 !== a ? a : null
                        } catch (e) {
                            return null
                        }
                    }, [O, h]), B = Object(d.useMemo)(() => f === T, [f, T]), P = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === U || void 0 === U ? void 0 : U.is_staff) && void 0 !== e && e
                    }, [U]), D = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === U || void 0 === U ? void 0 : U.id) && void 0 !== e ? e : -1
                    }, [U]), F = Object(d.useMemo)(() => {
                        var e;
                        return P ? m.get("spoon_manager") : null !== (e = null === U || void 0 === U ? void 0 : U.nickname) && void 0 !== e ? e : ""
                    }, [m, U, P]), G = Object(d.useMemo)(() => {
                        var e;
                        return P ? "" : null !== (e = null === U || void 0 === U ? void 0 : U.tag) && void 0 !== e ? e : ""
                    }, [U, P]), H = Object(d.useMemo)(() => {
                        var e;
                        return P ? "" : null !== (e = null === U || void 0 === U ? void 0 : U.profile_url) && void 0 !== e ? e : ""
                    }, [U, P]), Z = Object(d.useMemo)(() => Object(i.n)(Object(j.a)(), "direct-message/".concat(g ? "prd" : l.indexOf("dev") > -1 ? "dev" : "stg", "/").concat(r)), [r, g, l]), K = Object(d.useMemo)(() => String(Object(E.F)(M)), [M]), Q = Object(b.r)(k), J = Object(d.useMemo)(() => R === m.get("direct_message_send_present"), [m, R]), Y = Object(d.useMemo)(() => Object(z.a)(String(D), "home"), [D]), V = Object(d.useCallback)(() => {
                        Object(i.i)(Object(i.a)(Z, "rooms/".concat(T)), async e => {
                            if (e.exists()) {
                                var t, a;
                                try {
                                    N(!0);
                                    const e = Object(i.m)(Object(i.a)(Z, "messages/".concat(T)), Object(i.k)(), Object(i.g)(1));
                                    await Object(i.d)(e).then(e => {
                                        const t = e.val(),
                                            a = t[Object.keys(t)[0]].author.id;
                                        x(a === h)
                                    })
                                } catch (e) {
                                    console.error(e)
                                } finally {
                                    N(!1)
                                }
                                const n = e.val(),
                                    s = null !== (t = null === n || void 0 === n || null === (a = n.users) || void 0 === a || null === (a = a[h]) || void 0 === a ? void 0 : a.last_read) && void 0 !== t ? t : "";
                                I(n);
                                const o = s ? Object(i.m)(Object(i.a)(Z, "messages/".concat(T)), Object(i.k)(), Object(i.p)(s)) : Object(i.m)(Object(i.a)(Z, "messages/".concat(T)), Object(i.k)());
                                await Object(i.d)(o).then(async e => {
                                    var t;
                                    const a = null !== (t = null === e || void 0 === e ? void 0 : e.size) && void 0 !== t ? t : 0;
                                    if (C(B ? 0 : a), a > 0) {
                                        var s;
                                        const t = null !== (s = null === e || void 0 === e ? void 0 : e.val()) && void 0 !== s ? s : null,
                                            a = t[Object.keys(t).sort().pop()];
                                        a.author = n.users[a.author.id], y(a), x(a.author.id === h)
                                    }
                                }), N(!1)
                            }
                        })
                    }, [Z, T, h, B]), X = e => {
                        const t = new RegExp(p.join("|"), "gi"),
                            a = e.match(t);
                        if (!a) return e;
                        return a.reduce((e, t) => e.replaceAll(t, "*".repeat(t.length)), e)
                    };
                    return Object(d.useEffect)(() => (T && V(), () => {
                        console.log("off rooms/".concat(T)), Object(i.h)(Object(i.a)(Z, "rooms/".concat(T)))
                    }), [Z, T]), Object(d.useEffect)(() => {
                        T && s(M, T)
                    }, [s, T, M]), Object(d.useEffect)(() => {
                        k && (null === k || void 0 === k ? void 0 : k.id) !== (null === Q || void 0 === Q ? void 0 : Q.id) && o(k)
                    }, [k, Q, o]), Object(d.useEffect)(() => {
                        var e;
                        const n = a > 0 ? Object(E.w)(null !== (e = null === t || void 0 === t ? void 0 : t.timestamp) && void 0 !== e ? e : "") : "",
                            s = n.length > 0 ? n[0] ? Object(E.lb)(m.get("".concat(n[0])), "OOOO", n[1]) : n[1] : "";
                        _(s)
                    }, [t, a, m]), O ? u.a.createElement("div", {
                        className: Me("message-list-item-container", {
                            webview: v
                        })
                    }, u.a.createElement("div", {
                        className: Me("thumbnail-wrap")
                    }, u.a.createElement(W.a, {
                        nickname: F,
                        profileUrl: H,
                        size: v ? "s" : "xs",
                        isStaff: P
                    }), !v && U && !P && u.a.createElement(Ie, {
                        className: Me("btn-profile"),
                        title: F,
                        to: {
                            pathname: "".concat(c).concat(Y)
                        }
                    })), u.a.createElement("div", {
                        className: Me("contents")
                    }, u.a.createElement("p", {
                        className: Me("nickname")
                    }, u.a.createElement("span", {
                        className: Me("text-box")
                    }, F)), u.a.createElement("pre", {
                        className: Me("text", "text-box", {
                            present: J
                        })
                    }, L ? u.a.createElement("div", null, u.a.createElement(A.c, {
                        src: Oe.a,
                        alt: m.get("common_spoon"),
                        width: 18
                    })) : (q = R, S || P ? q : X(q)))), u.a.createElement("div", {
                        className: Me("created")
                    }, u.a.createElement("p", {
                        className: Me("time")
                    }, "".concat(w)), M > 0 && u.a.createElement("p", {
                        className: Me("count", {
                            "auto-width": K.length > 1
                        })
                    }, K)), v ? u.a.createElement("div", {
                        title: P ? F : "".concat(F, "(@").concat(G, ")"),
                        className: Me("btn-message"),
                        onClick: () => n(t)
                    }) : u.a.createElement("button", {
                        title: P ? F : "".concat(F, "(@").concat(G, ")"),
                        className: Me("btn-message"),
                        onClick: () => n(t)
                    })) : null;
                    var q
                },
                ke = a(1665),
                ye = a.n(ke);
            const we = o.a.bind(ye.a);
            var _e = e => {
                let {
                    viewIdx: t,
                    myRoomList: n,
                    renderTick: s,
                    isMyRoomListLoading: o,
                    handleClickBtnRoom: l,
                    handleTotalUnReadMsgCount: i,
                    handleNewMessage: c
                } = e;
                const {
                    string: r
                } = Object(b.s)();
                return o && 0 === t ? u.a.createElement("div", {
                    className: we("message-room-list-loader")
                }, u.a.createElement(A.c, {
                    src: a(258),
                    alt: r.get("common_spoon")
                })) : u.a.createElement("div", {
                    className: we("message-room-list-container", {
                        on: 0 === t
                    })
                }, u.a.createElement("div", {
                    className: we("list-wrap", {
                        empty: n.length <= 0
                    })
                }, n.length > 0 && u.a.createElement("ul", {
                    className: we("list")
                }, n.map((e, t) => e && u.a.createElement("li", Object.assign({
                    className: we("list-item"),
                    key: t
                }, /^\d+$/.test("".concat(null === e || void 0 === e ? void 0 : e.order)) && {
                    style: {
                        order: e.order
                    }
                }), u.a.createElement(Ce, {
                    data: e,
                    renderTick: s,
                    handleClickBtnRoom: l,
                    handleTotalUnReadMsgCount: i,
                    handleNewMessage: c
                }))))), n.length <= 0 && u.a.createElement("div", {
                    className: we("empty-result")
                }, u.a.createElement(A.c, {
                    src: a(1666),
                    alt: r.get("common_search")
                }), u.a.createElement(A.d, {
                    dangerouslySetInnerHTML: {
                        __html: r.get("direct_message_empty_message")
                    }
                })))
            };
            const Se = o.a.bind(Ee.a);
            var xe = e => {
                    let {
                        data: t,
                        renderTick: a,
                        handleClickBtnNewMessage: n
                    } = e;
                    const {
                        string: s
                    } = Object(b.s)(), [o, l] = Object(d.useState)(!1), [i, c] = Object(d.useState)(""), r = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === t || void 0 === t ? void 0 : t.getIn(["author", "is_staff"])) && void 0 !== e && e
                    }, [t]), m = Object(d.useMemo)(() => {
                        var e;
                        return r ? s.get("spoon_manager") : null !== (e = null === t || void 0 === t ? void 0 : t.getIn(["author", "nickname"])) && void 0 !== e ? e : ""
                    }, [s, t, r]), g = Object(d.useMemo)(() => {
                        var e;
                        return r ? "" : null !== (e = null === t || void 0 === t ? void 0 : t.getIn(["author", "profile_url"])) && void 0 !== e ? e : ""
                    }, [t, r]), v = Object(d.useMemo)(() => null !== t && void 0 !== t && t.get("sticker") ? s.get("direct_message_send_present") : null === t || void 0 === t ? void 0 : t.get("message"), [s, t]);
                    return Object(d.useEffect)(() => {
                        var e;
                        const n = a > 0 ? Object(E.w)(null !== (e = null === t || void 0 === t ? void 0 : t.get("timestamp")) && void 0 !== e ? e : "") : "",
                            o = n.length > 0 ? n[0] ? Object(E.lb)(s.get("".concat(n[0])), "OOOO", n[1]) : n[1] : "";
                        c(o)
                    }, [t, a, s]), Object(d.useEffect)(() => {
                        setTimeout(() => {
                            l(!0)
                        })
                    }, []), t ? u.a.createElement("div", {
                        className: Se("message-list-item-container", "new", {
                            on: o
                        })
                    }, u.a.createElement("div", {
                        className: Se("thumbnail-wrap")
                    }, u.a.createElement(W.a, {
                        nickname: m,
                        profileUrl: g,
                        size: "xs",
                        isStaff: r
                    })), u.a.createElement("div", {
                        className: Se("contents")
                    }, u.a.createElement("p", {
                        className: Se("nickname")
                    }, u.a.createElement("span", {
                        className: Se("text-box")
                    }, m)), u.a.createElement("p", {
                        className: Se("text", "text-box", {
                            present: null === t || void 0 === t ? void 0 : t.get("sticker")
                        })
                    }, v)), u.a.createElement("div", {
                        className: Se("created")
                    }, u.a.createElement("p", {
                        className: Se("time")
                    }, "".concat(i))), u.a.createElement("button", {
                        title: "".concat(m, ": ").concat(t.get("message")),
                        className: Se("btn-message"),
                        onClick: n(t)
                    })) : null
                },
                Le = a(1667),
                Ne = a.n(Le);
            const Te = o.a.bind(Ne.a);
            var Re, Ue, Ae = function(e) {
                let {
                    myRoomList: t,
                    renderTick: a
                } = e;
                const n = Object(b.a)(),
                    {
                        origin: s,
                        countryCode: o,
                        isProduction: l,
                        userInfoId: c,
                        isOpenChat: r,
                        newMessageList: m
                    } = Object(b.b)(e => {
                        var t, a;
                        return {
                            origin: e.data.get("origin"),
                            countryCode: e.data.get("countryCode"),
                            isProduction: e.data.get("isProduction"),
                            userInfoId: null !== (t = null === (a = e.auth) || void 0 === a ? void 0 : a.getIn(["userInfo", "id"])) && void 0 !== t ? t : -1,
                            isOpenChat: e.directMessage.isOpenChat,
                            newMessageList: e.directMessage.newMessageList
                        }
                    }),
                    [v, h] = Object(d.useState)(!1),
                    f = Object(d.useMemo)(() => Object(i.n)(Object(j.a)(), "direct-message/".concat(l ? "prd" : s.indexOf("dev") > -1 ? "dev" : "stg", "/").concat(o)), [o, l, s]),
                    p = Object(b.r)(null !== m && void 0 !== m ? m : null),
                    O = Object(d.useRef)(null),
                    E = Object(d.useCallback)(() => {
                        O.current && (clearTimeout(O.current), O.current = null)
                    }, []),
                    I = Object(d.useCallback)(() => {
                        E(), O.current = setTimeout(() => {
                            h(!0), E(), O.current = setTimeout(() => {
                                h(!1), n(Object(g.setNewMessageList)([])), E()
                            }, 500)
                        }, 8e3)
                    }, [n, E]),
                    M = Object(d.useCallback)(e => () => {
                        var a;
                        console.log("handleClickBtnNewMessage ", null === e || void 0 === e ? void 0 : e.getIn(["author", "id"]));
                        const s = null !== (a = null === e || void 0 === e ? void 0 : e.getIn(["author", "id"])) && void 0 !== a ? a : -1;
                        let o = "";
                        r || n(Object(g.setOpenChat)(!0)), E(), h(!0), O.current = setTimeout(() => {
                            E(), h(!1), n(Object(g.setNewMessageList)([]))
                        }, 500), t.length > 0 && t.forEach(e => {
                            o || null !== e && void 0 !== e && e.users[s] && (o = e.id)
                        }), s < 0 || (o || (o = Object(i.l)(Object(i.a)(f, "users/".concat(c, "/rooms"))).key), n(Object(g.enterChatRoom)({
                            joinedRoomId: o,
                            toUserId: s
                        })))
                    }, [n, c, r, t, f, E]);
                return Object(d.useEffect)(() => {
                    (null === p || void 0 === p ? void 0 : p.length) !== (null === m || void 0 === m ? void 0 : m.length) && (null === m || void 0 === m ? void 0 : m.length) > 0 && I()
                }, [I, p, m]), Object(d.useEffect)(() => () => {
                    E()
                }, [E]), (null === m || void 0 === m ? void 0 : m.length) > 0 && u.a.createElement("div", {
                    className: Te("message-new-list-container", {
                        "fade-out": v
                    })
                }, u.a.createElement("ul", {
                    className: Te("message-new-list")
                }, m.map((e, t) => {
                    const n = m.length - 2,
                        s = m.length - 1,
                        o = s > 0 ? 66 * (s - t) : 0;
                    return t >= n && u.a.createElement("li", Object.assign({
                        className: Te("message-new-list-item")
                    }, s > 0 && {
                        style: {
                            transform: "translateY(".concat(o, "px)")
                        }
                    }, {
                        key: t
                    }), u.a.createElement(xe, {
                        data: e,
                        renderTick: a,
                        handleClickBtnNewMessage: M
                    }))
                })))
            };
            const Be = le.d.div(Re || (Re = Object(oe.a)(["\n  &.message-menu-list-wrap {\n    display: flex;\n    justify-content: center;\n    position: relative;\n    padding: 0 0 20px;\n\n    .gradient {\n      position: absolute;\n      top: -32px;\n      left: 0;\n      width: 100%;\n      height: 32px;\n      background-image: linear-gradient(to top, $white, $white_a0);\n      pointer-events: none;\n    }\n\n    .message-menu-list {\n      display: flex;\n      padding: 2px;\n      border-radius: 18px;\n      background-color: ", ";\n    }\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_5
                }),
                Pe = le.d.button(Ue || (Ue = Object(oe.a)(["\n  min-width: 98px;\n  padding: 7px 0;\n  ", ";\n  font-weight: 600;\n  text-align: center;\n  border-radius: 16px;\n  background-color: ", ";\n  color: ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._textSet("body1")
                }, e => {
                    let {
                        theme: t,
                        active: a
                    } = e;
                    return t._colors.grayscale[a ? "black" : "gray_5"]
                }, e => {
                    let {
                        theme: t,
                        active: a
                    } = e;
                    return t._colors.grayscale[a ? "white" : "gray_70"]
                });
            const De = [{
                order: 0,
                title: "common_message"
            }, {
                order: 1,
                title: "common_friend"
            }];
            var ze = () => {
                const e = Object(b.a)(),
                    {
                        string: t
                    } = Object(b.s)(),
                    {
                        activeTab: a
                    } = Object(b.b)(e => ({
                        activeTab: e.directMessageTab.activeTab,
                        countryCode: e.data.get("countryCode")
                    }));
                return u.a.createElement(Be, {
                    className: "message-menu-list-wrap"
                }, u.a.createElement("div", {
                    className: "gradient"
                }), u.a.createElement("ul", {
                    className: "message-menu-list"
                }, De.map(n => {
                    let {
                        title: s,
                        order: o
                    } = n;
                    return u.a.createElement("li", {
                        key: s,
                        className: "message-menu-list-item"
                    }, u.a.createElement(Pe, {
                        active: a === o,
                        title: t.get(s),
                        onClick: (l = o, () => {
                            e(Object(v.b)(l))
                        })
                    }, t.get(s)));
                    var l
                })))
            };
            const We = Object(M.a)(() => Promise.resolve().then(a.bind(null, 364))),
                Fe = o.a.bind(Ee.a);
            var Ge = e => {
                    let {
                        data: t,
                        handleClickBtnUser: a
                    } = e;
                    const {
                        indexUrl: n,
                        string: s,
                        isWebview: o
                    } = Object(b.b)(e => ({
                        indexUrl: e.data.get("indexUrl"),
                        string: e.data.get("string"),
                        isWebview: e.data.get("isWebview")
                    })), l = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === t || void 0 === t ? void 0 : t.is_staff) && void 0 !== e && e
                    }, [t]), i = Object(d.useMemo)(() => {
                        var e;
                        return null !== (e = null === t || void 0 === t ? void 0 : t.id) && void 0 !== e ? e : -1
                    }, [t]), c = Object(d.useMemo)(() => {
                        var e;
                        return l ? s.get("spoon_manager") : null !== (e = null === t || void 0 === t ? void 0 : t.nickname) && void 0 !== e ? e : ""
                    }, [s, t, l]), r = Object(d.useMemo)(() => {
                        var e;
                        return l ? "" : null !== (e = null === t || void 0 === t ? void 0 : t.tag) && void 0 !== e ? e : ""
                    }, [t, l]), m = Object(d.useMemo)(() => {
                        var e;
                        return l ? "" : null !== (e = null === t || void 0 === t ? void 0 : t.profile_url) && void 0 !== e ? e : ""
                    }, [t, l]), g = Object(d.useMemo)(() => Object(z.a)(String(i), "home"), [i]), {
                        colorCode: v,
                        userPlanLevel: h,
                        isSubscriber: f
                    } = {
                        colorCode: null === t || void 0 === t ? void 0 : t.colorCode,
                        userPlanLevel: null === t || void 0 === t ? void 0 : t.userPlanLevel,
                        isSubscriber: Boolean(null === t || void 0 === t ? void 0 : t.userPlanLevel)
                    };
                    return u.a.createElement("div", {
                        className: Fe("message-list-item-container", {
                            webview: o
                        })
                    }, u.a.createElement("div", {
                        className: Fe("thumbnail-wrap")
                    }, u.a.createElement(W.a, {
                        nickname: c,
                        profileUrl: m,
                        size: o ? "s" : "xs",
                        isStaff: l
                    }), !o && i > -1 && !l && u.a.createElement(We, {
                        className: Fe("btn-profile"),
                        title: c,
                        to: {
                            pathname: "".concat(n).concat(g)
                        }
                    })), u.a.createElement("div", {
                        className: Fe("contents")
                    }, u.a.createElement("p", {
                        className: Fe("nickname", {
                            subscribe: f
                        })
                    }, f && u.a.createElement(ne.a, {
                        level: h,
                        colorCode: v
                    }), u.a.createElement("span", {
                        className: Fe("text-box")
                    }, c)), u.a.createElement("p", {
                        className: Fe("tag", "text-box", {
                            subscribe: f
                        })
                    }, l ? "" : "@".concat(r))), o ? u.a.createElement("div", {
                        title: l ? c : "".concat(c, "(@").concat(r, ")"),
                        className: Fe("btn-message"),
                        onClick: a(t)
                    }) : u.a.createElement("button", {
                        title: l ? c : "".concat(c, "(@").concat(r, ")"),
                        className: Fe("btn-message"),
                        onClick: a(t)
                    }))
                },
                He = a(1668),
                Ze = a.n(He);
            const Ke = o.a.bind(Ze.a);
            var Qe = function(e) {
                    let {
                        viewIdx: t,
                        isSearchFocus: n,
                        inputSearchKeyword: s,
                        requestSearchKeyword: o,
                        userList: l,
                        handleChangeSearchKeyword: i,
                        handleKeyDownSearchKeyword: c,
                        handleFocusSearchInput: r,
                        handleBlurSearchInput: d,
                        handleClickBtnSearchClear: m,
                        handleClickBtnSearch: g,
                        handleClickBtnSearchCancel: v,
                        handleScrollUserList: b,
                        handleClickBtnUser: h
                    } = e;
                    const {
                        string: f,
                        isWebview: p,
                        maxListLength: O
                    } = Object(k.e)(e => ({
                        string: e.data.get("string"),
                        isWebview: e.data.get("isWebview"),
                        maxListLength: e.data.get("maxListLength")
                    }), k.c);
                    return u.a.createElement("div", {
                        className: Ke("message-user-list-container", {
                            on: 1 === t
                        })
                    }, u.a.createElement("div", {
                        className: Ke("search-wrap")
                    }, u.a.createElement("div", {
                        className: Ke("search-field", {
                            on: n
                        })
                    }, u.a.createElement("input", {
                        type: "search",
                        value: s,
                        placeholder: f.get("direct_message_search_placeholder"),
                        onChange: i,
                        onKeyDown: c,
                        onFocus: r,
                        onBlur: d
                    }), s.length > 0 && u.a.createElement("button", {
                        className: Ke("btn-search-clear"),
                        title: f.get("common_delete_short"),
                        onClick: m
                    }, u.a.createElement("img", {
                        src: a(845),
                        alt: f.get("common_delete_short")
                    })), u.a.createElement("button", {
                        className: Ke("btn-search"),
                        title: f.get("common_search"),
                        onClick: g
                    }, s.length > 0 ? u.a.createElement("img", {
                        src: a(960),
                        alt: f.get("common_search")
                    }) : u.a.createElement("img", {
                        src: a(1090),
                        alt: f.get("common_search")
                    }))), o.length > 0 && u.a.createElement("button", {
                        className: Ke("btn-search-cancel"),
                        title: f.get("common_cancel"),
                        onClick: v
                    }, f.get("common_cancel"))), u.a.createElement("div", {
                        className: Ke("list-wrap", {
                            empty: l.length <= 0
                        }),
                        onScroll: b
                    }, u.a.createElement("p", {
                        className: Ke("search-guide"),
                        dangerouslySetInnerHTML: {
                            __html: f.get("direct_message_search_guide_".concat(p ? "app" : "web"))
                        }
                    }), l.length > 0 && u.a.createElement("ul", {
                        className: Ke("list")
                    }, l.map((e, t) => !(null !== e && void 0 !== e && e.is_staff) && t < O && u.a.createElement("li", {
                        className: Ke("list-item"),
                        key: t
                    }, u.a.createElement(Ge, {
                        data: e,
                        handleClickBtnUser: h
                    }))))), l.length <= 0 && u.a.createElement("div", {
                        className: Ke("empty-result")
                    }, u.a.createElement("img", {
                        src: a(1669),
                        alt: f.get("common_search")
                    }), u.a.createElement("p", {
                        dangerouslySetInnerHTML: {
                            __html: f.get(o.length <= 0 ? "direct_message_empty_following" : "result_search_no_result")
                        }
                    })))
                },
                Je = a(1670),
                Ye = a.n(Je);
            const Ve = o.a.bind(Ye.a);
            t.default = Object(d.memo)(() => {
                const {
                    origin: e,
                    countryCode: t,
                    string: s,
                    userAgent: o,
                    isProduction: c,
                    isWebview: M,
                    maxListLength: C,
                    webPushStatus: k,
                    userInfoId: y,
                    userInfoNickname: w,
                    userInfoTag: _,
                    userInfoProfileUrl: S,
                    userInfoTier: x,
                    userInfoIsStaff: L,
                    isOpenChat: N,
                    joinedRoomId: T,
                    toUserId: R,
                    activeTab: U
                } = Object(b.b)(e => {
                    var t, a, n, s, o, l, i, c, r, d, u, m;
                    return {
                        origin: e.data.get("origin"),
                        countryCode: e.data.get("countryCode"),
                        string: e.data.get("string"),
                        userAgent: e.data.get("userAgent"),
                        isProduction: e.data.get("isProduction"),
                        isWebview: e.data.get("isWebview"),
                        maxListLength: e.data.get("maxListLength"),
                        webPushStatus: e.data.get("webPushStatus"),
                        userInfoId: null !== (t = null === (a = e.auth) || void 0 === a ? void 0 : a.getIn(["userInfo", "id"])) && void 0 !== t ? t : -1,
                        userInfoNickname: null !== (n = null === (s = e.auth) || void 0 === s ? void 0 : s.getIn(["userInfo", "nickname"])) && void 0 !== n ? n : "",
                        userInfoTag: null !== (o = null === (l = e.auth) || void 0 === l ? void 0 : l.getIn(["userInfo", "tag"])) && void 0 !== o ? o : "",
                        userInfoProfileUrl: null !== (i = null === (c = e.auth) || void 0 === c ? void 0 : c.getIn(["userInfo", "profile_url"])) && void 0 !== i ? i : "",
                        userInfoTier: null !== (r = null === (d = e.auth) || void 0 === d ? void 0 : d.getIn(["userInfo", "tier"])) && void 0 !== r ? r : "",
                        userInfoIsStaff: null !== (u = null === (m = e.auth) || void 0 === m ? void 0 : m.getIn(["userInfo", "is_staff"])) && void 0 !== u && u,
                        isOpenChat: e.directMessage.isOpenChat,
                        joinedRoomId: e.directMessage.joinedRoomId,
                        toUserId: e.directMessage.toUserId,
                        activeTab: e.directMessageTab.activeTab
                    }
                }), A = Object(b.a)(), B = Object(m.h)(), [P, D] = Object(d.useState)(!1), [z, W] = Object(d.useState)(!1), [F, G] = Object(d.useState)(!1), [H, Z] = Object(d.useState)(!1), [K, Q] = Object(d.useState)(!1), [J, Y] = Object(d.useState)(null), [V, X] = Object(d.useState)([]), [q, $] = Object(d.useState)(!1), [ee, te] = Object(d.useState)(""), [ae, ne] = Object(d.useState)(""), [se, oe] = Object(d.useState)([]), [le, ie] = Object(d.useState)(""), [ce, re] = Object(d.useState)(), [de, ue] = Object(d.useState)(0), [me, ge] = Object(d.useState)(0), [ve, be] = Object(d.useState)(!0), he = Object(d.useMemo)(() => Object(i.n)(Object(j.a)(), "direct-message/".concat(c ? "prd" : e.indexOf("dev") > -1 ? "dev" : "stg", "/").concat(t)), [t, c, e]), pe = Object(d.useMemo)(() => r.a.parse(B.search), [B]), Oe = Object(d.useMemo)(() => {
                    var e;
                    return String(Object(E.F)(ce ? null === (e = Object.values(ce)) || void 0 === e ? void 0 : e.reduce((e, t) => e + t, 0) : 0))
                }, [ce]), je = 0 === U ? Oe : "0", Ee = Object(b.r)(V), Ie = Object(d.useRef)(null), Me = Object(d.useRef)(null), Ce = De.find(e => {
                    let {
                        order: t
                    } = e;
                    return t === U
                }).title, ke = Object(d.useCallback)(() => {
                    Ie.current && (clearInterval(Ie.current), Ie.current = null)
                }, []), ye = Object(d.useCallback)(() => {
                    ke(), ue((new Date).getTime()), Ie.current = setInterval(() => {
                        ue((new Date).getTime())
                    }, 6e4)
                }, [ke]), we = Object(d.useCallback)(() => {
                    Object(i.i)(Object(i.m)(Object(i.a)(he, "users/".concat(y, "/rooms")), Object(i.j)("is_leave"), Object(i.c)(!0)), e => {
                        const t = null === e || void 0 === e ? void 0 : e.val(),
                            a = [];
                        if (t) {
                            Object.values(t).forEach(e => {
                                a.push(e)
                            });
                            const e = a.slice();
                            e.sort((e, t) => new Date(t.timestamp).getTime() - new Date(e.timestamp).getTime()), e.forEach((e, t) => {
                                const n = a.findIndex(t => (null === t || void 0 === t ? void 0 : t.id) === (null === e || void 0 === e ? void 0 : e.id));
                                n > -1 && (a[n].order = t)
                            })
                        }
                        Y(t), X(a), be(!1), D(!0)
                    })
                }, [y, he]), Se = Object(d.useCallback)(async () => {
                    y < 0 || Object(i.d)(Object(i.a)(he, "users/".concat(y))).then(async e => {
                        var t;
                        const a = L ? "" : w,
                            s = L ? "" : _,
                            o = L ? "" : S,
                            l = !L && x && null !== (t = null === x || void 0 === x ? void 0 : x.toJS()) && void 0 !== t ? t : null;
                        if (e.exists()) {
                            const e = {};
                            e["users/".concat(y, "/id")] = y, e["users/".concat(y, "/nickname")] = a, e["users/".concat(y, "/tag")] = s, e["users/".concat(y, "/profile_url")] = o, e["users/".concat(y, "/tier")] = l, e["users/".concat(y, "/is_staff")] = L || null, Object(i.r)(he, e)
                        } else Object(i.o)(Object(i.a)(he, "users/".concat(y)), Object(n.a)({
                            id: y,
                            nickname: a,
                            tag: s,
                            profile_url: o,
                            tier: l
                        }, L && {
                            is_staff: L
                        }))
                    }).catch(e => {
                        console.log("GET users/".concat(y, " Error"), e)
                    })
                }, [y, he, L, w, _, S, x]), xe = Object(d.useCallback)(() => {
                    A(Object(g.setOpenChat)(!N))
                }, [A, N]), Le = Object(d.useCallback)(async e => {
                    if (!K) {
                        await Q(!0);
                        try {
                            var t, a;
                            const s = await Object(E.qb)(Object(n.a)({
                                    userListApi: e ? f.d : p.l,
                                    apiId: e ? "" : y,
                                    userId: y
                                }, e && {
                                    apiParams: {
                                        keyword: e,
                                        page_size: 30
                                    }
                                })),
                                o = null !== (t = null === s || void 0 === s ? void 0 : s.next) && void 0 !== t ? t : "";
                            let l = null !== (a = null === s || void 0 === s ? void 0 : s.userList) && void 0 !== a ? a : [];
                            if (l.length > 0) {
                                const e = l.findIndex(e => e.id === y);
                                e > -1 && l.splice(e, 1)
                            }
                            const i = null === s || void 0 === s ? void 0 : s.userList.map(e => {
                                    let {
                                        id: t
                                    } = e;
                                    return t
                                }).join(","),
                                c = await Object(O.a)(y, i),
                                r = Object(I.d)(["id", "userId"], [l, null === c || void 0 === c ? void 0 : c.results]);
                            ne(null !== e && void 0 !== e ? e : ""), oe(r), ie(o)
                        } catch (e) {
                            console.log("getUserList Error ", e)
                        } finally {
                            Q(!1)
                        }
                    }
                }, [y, K, ne]), Ne = Object(d.useCallback)(async e => {
                    var t, a, n, s, o, l;
                    const i = null !== (t = null === e || void 0 === e || null === (a = e.currentTarget) || void 0 === a ? void 0 : a.scrollTop) && void 0 !== t ? t : 0,
                        c = null !== (n = null === e || void 0 === e || null === (s = e.currentTarget) || void 0 === s ? void 0 : s.clientHeight) && void 0 !== n ? n : 0,
                        r = null !== (o = null === e || void 0 === e || null === (l = e.currentTarget) || void 0 === l ? void 0 : l.scrollHeight) && void 0 !== o ? o : 0;
                    if (!(K || se.length <= 0 || se.length >= C || !le || r - (i + c) > 100)) {
                        Q(!0);
                        try {
                            var d, u;
                            const e = await Object(E.qb)({
                                    userListApi: h.c,
                                    apiId: le,
                                    userId: y
                                }),
                                t = null !== (d = null === e || void 0 === e ? void 0 : e.userList) && void 0 !== d ? d : [],
                                a = null !== (u = null === e || void 0 === e ? void 0 : e.next) && void 0 !== u ? u : "";
                            console.log("getNextUserList Success ", e), oe(se.concat(t)), ie(a)
                        } catch (e) {
                            console.log("getNextUserList Error ", e)
                        }
                        Q(!1)
                    }
                }, [C, y, K, se, le, Q, oe]), Te = Object(d.useCallback)(e => () => {
                    A(Object(v.b)(e))
                }, [A]), Re = Object(d.useCallback)(e => async () => {
                    var t;
                    const a = null !== (t = null === e || void 0 === e ? void 0 : e.id) && void 0 !== t ? t : -1;
                    console.log("handleClickBtnUser ", z, a, e), !z || a < 0 || A(Object(g.enterChatRoom)({
                        joinedRoomId: "",
                        toUserId: a
                    }))
                }, [A, z]), Ue = Object(d.useCallback)(e => {
                    var t, a, n;
                    const s = null !== (t = null === (a = Object.keys(null === e || void 0 === e ? void 0 : e.users)) || void 0 === a ? void 0 : a.find(e => parseInt(e) !== y)) && void 0 !== t ? t : "",
                        o = s ? parseInt(s) : -1,
                        l = null !== (n = null === e || void 0 === e ? void 0 : e.id) && void 0 !== n ? n : "";
                    !z || !e || o < 0 || A(Object(g.enterChatRoom)({
                        joinedRoomId: l,
                        toUserId: o
                    }))
                }, [A, y, z]);
                const Be = Object(d.useCallback)(() => {
                        !ee || ee && ee === ae || Le(ee)
                    }, [ee, ae, Le]),
                    Pe = Object(d.useCallback)(() => {
                        te("")
                    }, []),
                    We = Object(d.useCallback)(() => {
                        te(""), ne(""), Le()
                    }, [Le]),
                    Fe = Object(d.useCallback)((e, t) => {
                        re(a => {
                            const s = Object(n.a)({}, a);
                            return s.hasOwnProperty(t), s[t] = e, s
                        })
                    }, []),
                    Ge = Object(d.useCallback)(e => {
                        var t, a;
                        "allow" === k && e && y !== (null === e || void 0 === e || null === (t = e.author) || void 0 === t ? void 0 : t.id) && R !== (null === e || void 0 === e || null === (a = e.author) || void 0 === a ? void 0 : a.id) && (console.log("handleNewMessage ", e), A(Object(g.setNewMessageList)([e])))
                    }, [A, k, y, R]),
                    He = Object(d.useCallback)(() => {
                        console.log("handleFocusMessageInput ", window.innerHeight), M && /iphone|ipad|ipod/g.test(o) && (Me.current && (clearTimeout(Me.current), Me.current = null), Me.current = setTimeout(() => {
                            ge(window.innerHeight), window.scrollTo(0, 0), window.document.body.scrollTop = 0, Me.current && (clearTimeout(Me.current), Me.current = null)
                        }, 500))
                    }, [o, M]),
                    Ze = Object(d.useCallback)(() => {
                        console.log("handleBlurMessageInput ", document.documentElement.clientHeight), M && /iphone|ipad|ipod/g.test(o) && (Me.current && (clearTimeout(Me.current), Me.current = null), ge(document.documentElement.clientHeight))
                    }, [o, M]),
                    Ke = Object(d.useCallback)(() => {
                        A(Object(g.leaveChatRoom)()), Ze()
                    }, [A, Ze]),
                    Je = Object(d.useCallback)(async () => {
                        if (!z) try {
                            const e = await Object(l.b)(Object(l.a)());
                            e && (console.log("signInAnonymously Success ", e), W(!0), Se(), Le())
                        } catch (e) {
                            console.log("signInAnonymously Error ", e)
                        }
                    }, [Le, z, Se]);
                return Object(d.useEffect)(() => {
                    Je()
                }, [Je]), Object(d.useEffect)(() => {
                    var e;
                    M && null !== pe && void 0 !== pe && pe.to_user_id && z && P && !H && (Z(!0), A(Object(g.enterChatRoom)({
                        joinedRoomId: null !== (e = null === pe || void 0 === pe ? void 0 : pe.room_id) && void 0 !== e ? e : "",
                        toUserId: Number(pe.to_user_id)
                    })))
                }, [A, M, pe, z, P, H]), Object(d.useEffect)(() => (z && we(), () => {
                    console.log("off users/".concat(y, "/rooms")), Object(i.h)(Object(i.a)(he, "users/".concat(y, "/rooms")))
                }), [y, z, he, we]), Object(d.useEffect)(() => {
                    if (T && V.length < Ee.length) {
                        be(!0);
                        const e = V.findIndex(e => e.id === T),
                            t = Ee.findIndex(e => e.id === T);
                        e < 0 && t > -1 && (Ke(), setTimeout(() => {
                            be(!1)
                        }))
                    }
                }, [T, V, Ee, Ke]), Object(d.useEffect)(() => (ye(), () => {
                    ke()
                }), [ye, ke]), Object(d.useEffect)(() => {
                    N && !F && G(!0)
                }, [M, N, F, pe]), Object(d.useEffect)(() => (M && ge(document.documentElement.clientHeight), () => {
                    A(Object(g.initializeDirectMessage)()), A(Te(0)), W(!1), G(!1), Q(!1), oe([]), $(!1), te(""), ne(""), ue(0)
                }), [A, Te, M]), Object(d.useEffect)(() => {
                    if (M) return window.addEventListener("resize", e), () => {
                        M && window.removeEventListener("resize", e)
                    };

                    function e(e) {
                        ge(e.target.innerHeight)
                    }
                }, [A, o, M]), Object(d.useEffect)(() => {
                    null !== pe && void 0 !== pe && pe.type && "0" !== (null === pe || void 0 === pe ? void 0 : pe.type) || Object(v.b)(0)
                }, [pe.type]), u.a.createElement("div", {
                    className: Ve("direct-message-container", {
                        webview: M
                    })
                }, u.a.createElement("div", {
                    className: Ve("messenger-wrap")
                }, u.a.createElement("div", Object.assign({
                    className: Ve("direct-message-wrap", {
                        on: N
                    })
                }, me > 0 && {
                    style: {
                        height: "".concat(me, "px")
                    }
                }), u.a.createElement("div", {
                    className: Ve("message-list-header")
                }, u.a.createElement("div", {
                    className: Ve("left")
                }, u.a.createElement("p", {
                    className: Ve("title")
                }, s.get(Ce)), "0" !== je && u.a.createElement("p", {
                    className: Ve("unread-count", {
                        "auto-width": je.length > 1
                    })
                }, je)), u.a.createElement("div", {
                    className: Ve("right")
                }, !M && u.a.createElement("button", {
                    className: Ve("btn-toggle-dm", {
                        on: N
                    }),
                    title: s.get(N ? "common_close" : "common_message"),
                    onClick: xe
                }, u.a.createElement("img", {
                    src: a(1547),
                    alt: s.get("common_search")
                }))), !M && !N && u.a.createElement("button", {
                    className: Ve("btn-open-message"),
                    title: s.get(N ? "common_close" : "common_message"),
                    onClick: xe
                })), u.a.createElement(u.a.Fragment, null, 0 === U && u.a.createElement(_e, {
                    viewIdx: U,
                    myRoomList: V,
                    isMyRoomListLoading: ve,
                    renderTick: de,
                    handleClickBtnRoom: Ue,
                    handleTotalUnReadMsgCount: Fe,
                    handleNewMessage: Ge
                }), 1 === U && u.a.createElement(Qe, {
                    viewIdx: U,
                    isSearchFocus: q,
                    inputSearchKeyword: ee,
                    requestSearchKeyword: ae,
                    userList: se,
                    handleChangeSearchKeyword: function(e) {
                        let t = e.target.value;
                        (t.replace(/[\n]/gi, "").length <= 0 || t.replace(/\s/gi, "").length <= 0) && (t = ""), t = Object(E.wb)(t), te(t)
                    },
                    handleKeyDownSearchKeyword: function(e) {
                        if (13 === e.keyCode && !e.shiftKey) {
                            if (!ee || ee && ee === ae) return;
                            Le(ee), e.preventDefault()
                        }
                    },
                    handleFocusSearchInput: function() {
                        $(!0)
                    },
                    handleBlurSearchInput: function() {
                        $(!1)
                    },
                    handleClickBtnSearchClear: Pe,
                    handleClickBtnSearch: Be,
                    handleClickBtnSearchCancel: We,
                    handleScrollUserList: Ne,
                    handleClickBtnUser: Re
                })), u.a.createElement(ze, null), u.a.createElement(fe, {
                    myRooms: J,
                    totalUnReadMsgCount: Oe,
                    handleClickBtnToggleChat: xe,
                    handleClickBtnChatClose: Ke,
                    handleFocusMessageInput: He,
                    handleBlurMessageInput: Ze
                }))), z && !M && "allow" === k && u.a.createElement(Ae, {
                    myRoomList: V,
                    renderTick: de
                }))
            })
        },
        839: function(e, t, a) {
            "use strict";
            var n = a(841);
            a.d(t, "a", (function() {
                return n.a
            }))
        },
        841: function(e, t, a) {
            "use strict";
            var n, s, o, l = a(20),
                i = a(0),
                c = a.n(i),
                r = a(6),
                d = a(148),
                u = a(335);
            const m = ["svgRef", "title"];

            function g() {
                return (g = Object.assign ? Object.assign.bind() : function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var a = arguments[t];
                        for (var n in a)({}).hasOwnProperty.call(a, n) && (e[n] = a[n])
                    }
                    return e
                }).apply(null, arguments)
            }
            const v = e => {
                    let {
                        svgRef: t,
                        title: a
                    } = e, l = function(e, t) {
                        if (null == e) return {};
                        var a, n, s = function(e, t) {
                            if (null == e) return {};
                            var a = {};
                            for (var n in e)
                                if ({}.hasOwnProperty.call(e, n)) {
                                    if (-1 !== t.indexOf(n)) continue;
                                    a[n] = e[n]
                                }
                            return a
                        }(e, t);
                        if (Object.getOwnPropertySymbols) {
                            var o = Object.getOwnPropertySymbols(e);
                            for (n = 0; n < o.length; n++) a = o[n], -1 === t.indexOf(a) && {}.propertyIsEnumerable.call(e, a) && (s[a] = e[a])
                        }
                        return s
                    }(e, m);
                    return c.a.createElement("svg", g({
                        width: 24,
                        height: 16,
                        viewBox: "0 0 24 16",
                        fill: "none",
                        ref: t
                    }, l), a ? c.a.createElement("title", null, a) : null, n || (n = c.a.createElement("rect", {
                        width: 24,
                        height: 16,
                        rx: 8,
                        fill: "current"
                    })), s || (s = c.a.createElement("g", {
                        filter: "url(#filter0_d_8193_281270)"
                    }, c.a.createElement("path", {
                        d: "M11.7999 4.54069C11.8687 4.35496 12.1313 4.35496 12.2001 4.54069L13.0463 6.82764C13.0679 6.88603 13.114 6.93207 13.1724 6.95368L15.4593 7.79993C15.645 7.86865 15.645 8.13135 15.4593 8.20008L13.1724 9.04632C13.114 9.06793 13.0679 9.11397 13.0463 9.17236L12.2001 11.4593C12.1313 11.645 11.8687 11.645 11.7999 11.4593L10.9537 9.17236C10.9321 9.11397 10.886 9.06793 10.8276 9.04632L8.54069 8.20007C8.35496 8.13135 8.35496 7.86865 8.54069 7.79993L10.8276 6.95368C10.886 6.93207 10.9321 6.88603 10.9537 6.82764L11.7999 4.54069Z",
                        fill: "white"
                    }))), o || (o = c.a.createElement("defs", null, c.a.createElement("filter", {
                        id: "filter0_d_8193_281270",
                        x: 7.60137,
                        y: 3.60137,
                        width: 8.79727,
                        height: 8.79727,
                        filterUnits: "userSpaceOnUse",
                        colorInterpolationFilters: "sRGB"
                    }, c.a.createElement("feFlood", {
                        floodOpacity: 0,
                        result: "BackgroundImageFix"
                    }), c.a.createElement("feColorMatrix", { in: "SourceAlpha",
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                        result: "hardAlpha"
                    }), c.a.createElement("feOffset", null), c.a.createElement("feGaussianBlur", {
                        stdDeviation: .4
                    }), c.a.createElement("feComposite", {
                        in2: "hardAlpha",
                        operator: "out"
                    }), c.a.createElement("feColorMatrix", {
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in2: "BackgroundImageFix",
                        result: "effect1_dropShadow_8193_281270"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in: "SourceGraphic",
                        in2: "effect1_dropShadow_8193_281270",
                        result: "shape"
                    })))))
                },
                b = c.a.forwardRef((e, t) => c.a.createElement(v, g({
                    svgRef: t
                }, e)));
            var h, f, p;
            a.p;
            const O = ["svgRef", "title"];

            function j() {
                return (j = Object.assign ? Object.assign.bind() : function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var a = arguments[t];
                        for (var n in a)({}).hasOwnProperty.call(a, n) && (e[n] = a[n])
                    }
                    return e
                }).apply(null, arguments)
            }
            const E = e => {
                    let {
                        svgRef: t,
                        title: a
                    } = e, n = function(e, t) {
                        if (null == e) return {};
                        var a, n, s = function(e, t) {
                            if (null == e) return {};
                            var a = {};
                            for (var n in e)
                                if ({}.hasOwnProperty.call(e, n)) {
                                    if (-1 !== t.indexOf(n)) continue;
                                    a[n] = e[n]
                                }
                            return a
                        }(e, t);
                        if (Object.getOwnPropertySymbols) {
                            var o = Object.getOwnPropertySymbols(e);
                            for (n = 0; n < o.length; n++) a = o[n], -1 === t.indexOf(a) && {}.propertyIsEnumerable.call(e, a) && (s[a] = e[a])
                        }
                        return s
                    }(e, O);
                    return c.a.createElement("svg", j({
                        width: 24,
                        height: 16,
                        viewBox: "0 0 24 16",
                        fill: "none",
                        ref: t
                    }, n), a ? c.a.createElement("title", null, a) : null, h || (h = c.a.createElement("rect", {
                        width: 24,
                        height: 16,
                        rx: 8,
                        fill: "current"
                    })), f || (f = c.a.createElement("g", {
                        filter: "url(#filter0_d_8193_281266)"
                    }, c.a.createElement("path", {
                        fillRule: "evenodd",
                        clipRule: "evenodd",
                        d: "M8.75011 3.17412C8.83602 2.94196 9.16439 2.94196 9.2503 3.17412L10.038 5.30289C10.065 5.37589 10.1226 5.43344 10.1956 5.46045L12.3243 6.24816C12.5565 6.33407 12.5565 6.66244 12.3243 6.74835L10.1956 7.53607C10.1226 7.56308 10.065 7.62062 10.038 7.69362L9.2503 9.82239C9.16439 10.0546 8.83602 10.0546 8.75012 9.82239L7.9624 7.69362C7.93539 7.62063 7.87784 7.56308 7.80485 7.53607L5.67608 6.74835C5.44391 6.66244 5.44391 6.33407 5.67608 6.24816L7.80485 5.46045C7.87784 5.43344 7.93539 5.37589 7.9624 5.3029L8.75011 3.17412ZM14.7499 6.17392C14.8358 5.94175 15.1642 5.94175 15.2501 6.17391L16.0378 8.30269C16.0648 8.37568 16.1224 8.43323 16.1954 8.46024L18.3241 9.24795C18.5563 9.33386 18.5563 9.66223 18.3241 9.74814L16.1954 10.5359C16.1224 10.5629 16.0648 10.6204 16.0378 10.6934L15.2501 12.8222C15.1642 13.0543 14.8358 13.0543 14.7499 12.8222L13.9622 10.6934C13.9352 10.6204 13.8776 10.5629 13.8046 10.5359L11.6759 9.74814C11.4437 9.66223 11.4437 9.33386 11.6759 9.24795L13.8046 8.46024C13.8776 8.43323 13.9352 8.37568 13.9622 8.30269L14.7499 6.17392Z",
                        fill: "white"
                    }))), p || (p = c.a.createElement("defs", null, c.a.createElement("filter", {
                        id: "filter0_d_8193_281266",
                        x: 4.50195,
                        y: 2,
                        width: 14.9963,
                        height: 11.9963,
                        filterUnits: "userSpaceOnUse",
                        colorInterpolationFilters: "sRGB"
                    }, c.a.createElement("feFlood", {
                        floodOpacity: 0,
                        result: "BackgroundImageFix"
                    }), c.a.createElement("feColorMatrix", { in: "SourceAlpha",
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                        result: "hardAlpha"
                    }), c.a.createElement("feOffset", null), c.a.createElement("feGaussianBlur", {
                        stdDeviation: .5
                    }), c.a.createElement("feComposite", {
                        in2: "hardAlpha",
                        operator: "out"
                    }), c.a.createElement("feColorMatrix", {
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in2: "BackgroundImageFix",
                        result: "effect1_dropShadow_8193_281266"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in: "SourceGraphic",
                        in2: "effect1_dropShadow_8193_281266",
                        result: "shape"
                    })))))
                },
                I = c.a.forwardRef((e, t) => c.a.createElement(E, j({
                    svgRef: t
                }, e)));
            var M, C, k;
            a.p;
            const y = ["svgRef", "title"];

            function w() {
                return (w = Object.assign ? Object.assign.bind() : function(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var a = arguments[t];
                        for (var n in a)({}).hasOwnProperty.call(a, n) && (e[n] = a[n])
                    }
                    return e
                }).apply(null, arguments)
            }
            const _ = e => {
                    let {
                        svgRef: t,
                        title: a
                    } = e, n = function(e, t) {
                        if (null == e) return {};
                        var a, n, s = function(e, t) {
                            if (null == e) return {};
                            var a = {};
                            for (var n in e)
                                if ({}.hasOwnProperty.call(e, n)) {
                                    if (-1 !== t.indexOf(n)) continue;
                                    a[n] = e[n]
                                }
                            return a
                        }(e, t);
                        if (Object.getOwnPropertySymbols) {
                            var o = Object.getOwnPropertySymbols(e);
                            for (n = 0; n < o.length; n++) a = o[n], -1 === t.indexOf(a) && {}.propertyIsEnumerable.call(e, a) && (s[a] = e[a])
                        }
                        return s
                    }(e, y);
                    return c.a.createElement("svg", w({
                        width: 24,
                        height: 16,
                        viewBox: "0 0 24 16",
                        fill: "none",
                        ref: t
                    }, n), a ? c.a.createElement("title", null, a) : null, M || (M = c.a.createElement("rect", {
                        width: 24,
                        height: 16,
                        rx: 8,
                        fill: "current"
                    })), C || (C = c.a.createElement("g", {
                        clipPath: "url(#clip0_8193_281262)"
                    }, c.a.createElement("g", {
                        filter: "url(#filter0_d_8193_281262)"
                    }, c.a.createElement("path", {
                        d: "M7.18855 2.5069C7.12412 2.33278 6.87784 2.33278 6.81341 2.5069L6.22262 4.10348C6.20236 4.15822 6.1592 4.20139 6.10446 4.22164L4.50788 4.81243C4.33375 4.87686 4.33375 5.12314 4.50788 5.18757L6.10446 5.77836C6.1592 5.79861 6.20236 5.84178 6.22262 5.89652L6.81341 7.4931C6.87784 7.66722 7.12412 7.66722 7.18855 7.4931L7.77933 5.89652C7.79959 5.84178 7.84275 5.79861 7.8975 5.77836L9.49408 5.18757C9.6682 5.12314 9.6682 4.87686 9.49408 4.81243L7.8975 4.22164C7.84275 4.20139 7.79959 4.15822 7.77933 4.10348L7.18855 2.5069Z",
                        fill: "white"
                    })), c.a.createElement("g", {
                        filter: "url(#filter1_d_8193_281262)"
                    }, c.a.createElement("path", {
                        d: "M17.1885 8.50885C17.1241 8.33473 16.8778 8.33473 16.8134 8.50885L16.2226 10.1054C16.2024 10.1602 16.1592 10.2033 16.1045 10.2236L14.5079 10.8144C14.3338 10.8788 14.3338 11.1251 14.5079 11.1895L16.1045 11.7803C16.1592 11.8006 16.2024 11.8437 16.2226 11.8985L16.8134 13.4951C16.8778 13.6692 17.1241 13.6692 17.1885 13.4951L17.7793 11.8985C17.7996 11.8437 17.8428 11.8006 17.8975 11.7803L19.4941 11.1895C19.6682 11.1251 19.6682 10.8788 19.4941 10.8144L17.8975 10.2236C17.8428 10.2033 17.7996 10.1602 17.7793 10.1054L17.1885 8.50885Z",
                        fill: "white"
                    })), c.a.createElement("g", {
                        filter: "url(#filter2_d_8193_281262)"
                    }, c.a.createElement("path", {
                        d: "M11.8222 4.48452C11.8832 4.31943 12.1168 4.31943 12.1778 4.48452L13.0501 6.84177C13.0693 6.89367 13.1102 6.9346 13.1621 6.9538L15.5194 7.82606C15.6845 7.88715 15.6845 8.12066 15.5194 8.18175L13.1621 9.05401C13.1102 9.07322 13.0693 9.11414 13.0501 9.16605L12.1778 11.5233C12.1168 11.6884 11.8832 11.6884 11.8222 11.5233L10.9499 9.16605C10.9307 9.11414 10.8898 9.07322 10.8379 9.05401L8.48062 8.18175C8.31552 8.12066 8.31552 7.88715 8.48062 7.82606L10.8379 6.9538C10.8898 6.9346 10.9307 6.89367 10.9499 6.84177L11.8222 4.48452Z",
                        fill: "white"
                    })))), k || (k = c.a.createElement("defs", null, c.a.createElement("filter", {
                        id: "filter0_d_8193_281262",
                        x: 3.62726,
                        y: 1.62622,
                        width: 6.74741,
                        height: 6.74756,
                        filterUnits: "userSpaceOnUse",
                        colorInterpolationFilters: "sRGB"
                    }, c.a.createElement("feFlood", {
                        floodOpacity: 0,
                        result: "BackgroundImageFix"
                    }), c.a.createElement("feColorMatrix", { in: "SourceAlpha",
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                        result: "hardAlpha"
                    }), c.a.createElement("feOffset", null), c.a.createElement("feGaussianBlur", {
                        stdDeviation: .375
                    }), c.a.createElement("feComposite", {
                        in2: "hardAlpha",
                        operator: "out"
                    }), c.a.createElement("feColorMatrix", {
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in2: "BackgroundImageFix",
                        result: "effect1_dropShadow_8193_281262"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in: "SourceGraphic",
                        in2: "effect1_dropShadow_8193_281262",
                        result: "shape"
                    })), c.a.createElement("filter", {
                        id: "filter1_d_8193_281262",
                        x: 13.6273,
                        y: 7.62817,
                        width: 6.74738,
                        height: 6.74756,
                        filterUnits: "userSpaceOnUse",
                        colorInterpolationFilters: "sRGB"
                    }, c.a.createElement("feFlood", {
                        floodOpacity: 0,
                        result: "BackgroundImageFix"
                    }), c.a.createElement("feColorMatrix", { in: "SourceAlpha",
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                        result: "hardAlpha"
                    }), c.a.createElement("feOffset", null), c.a.createElement("feGaussianBlur", {
                        stdDeviation: .375
                    }), c.a.createElement("feComposite", {
                        in2: "hardAlpha",
                        operator: "out"
                    }), c.a.createElement("feColorMatrix", {
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in2: "BackgroundImageFix",
                        result: "effect1_dropShadow_8193_281262"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in: "SourceGraphic",
                        in2: "effect1_dropShadow_8193_281262",
                        result: "shape"
                    })), c.a.createElement("filter", {
                        id: "filter2_d_8193_281262",
                        x: 7.64567,
                        y: 3.64948,
                        width: 8.70866,
                        height: 8.70884,
                        filterUnits: "userSpaceOnUse",
                        colorInterpolationFilters: "sRGB"
                    }, c.a.createElement("feFlood", {
                        floodOpacity: 0,
                        result: "BackgroundImageFix"
                    }), c.a.createElement("feColorMatrix", { in: "SourceAlpha",
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",
                        result: "hardAlpha"
                    }), c.a.createElement("feOffset", null), c.a.createElement("feGaussianBlur", {
                        stdDeviation: .355556
                    }), c.a.createElement("feComposite", {
                        in2: "hardAlpha",
                        operator: "out"
                    }), c.a.createElement("feColorMatrix", {
                        type: "matrix",
                        values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in2: "BackgroundImageFix",
                        result: "effect1_dropShadow_8193_281262"
                    }), c.a.createElement("feBlend", {
                        mode: "normal",
                        in: "SourceGraphic",
                        in2: "effect1_dropShadow_8193_281262",
                        result: "shape"
                    })), c.a.createElement("clipPath", {
                        id: "clip0_8193_281262"
                    }, c.a.createElement("rect", {
                        width: 16,
                        height: 16,
                        fill: "white",
                        transform: "translate(4)"
                    })))))
                },
                S = c.a.forwardRef((e, t) => c.a.createElement(_, w({
                    svgRef: t
                }, e)));
            a.p;
            const x = ["level", "colorCode"];
            t.a = e => {
                let {
                    level: t = 10,
                    colorCode: a
                } = e, n = Object(l.a)(e, x);
                const {
                    colorCodeList: s
                } = Object(r.b)(e => {
                    var t, a;
                    return {
                        colorCodeList: null !== (t = null === (a = e.commonConfig.serverSettings) || void 0 === a ? void 0 : a.SUBSCRIPTION_COLOR_CODE) && void 0 !== t ? t : u.a
                    }
                });
                return o = t, Object.values(d.i).includes(o) ? c.a.createElement(c.a.Fragment, null, 10 === t && c.a.createElement(b, Object.assign({
                    fill: null === s || void 0 === s ? void 0 : s[null !== a && void 0 !== a ? a : d.f]
                }, n)), 20 === t && c.a.createElement(I, Object.assign({
                    fill: null === s || void 0 === s ? void 0 : s[null !== a && void 0 !== a ? a : d.f]
                }, n)), 30 === t && c.a.createElement(S, Object.assign({
                    fill: null === s || void 0 === s ? void 0 : s[null !== a && void 0 !== a ? a : d.f]
                }, n))) : null;
                var o
            }
        },
        845: function(e, t, a) {
            e.exports = a.p + "src/images/modal/login_input_clear.39c759b7.png"
        },
        848: function(e, t, a) {
            "use strict";
            a.d(t, "b", (function() {
                return n
            })), a.d(t, "c", (function() {
                return o
            })), a.d(t, "a", (function() {
                return l
            }));
            const n = e => null === e || void 0 === e ? void 0 : e.replace(/[\u00A0\u1680\u180E\u2000-\u200D\u202F\u205F\u3000\u00AD]/g, ""),
                s = /^[\s\u1680\u180e\u2000-\u200a\u2028\u2029\u202f\u205f\u3000]+|[\s\u1680\u180e\u2000-\u200a\u2028\u2029\u202f\u205f\u3000]+$/g,
                o = e => e.replace(s, ""),
                l = async e => {
                    if (navigator.clipboard && "function" === typeof navigator.clipboard.writeText) {
                        if (await (async e => {
                                try {
                                    return await navigator.clipboard.writeText(e), !0
                                } catch (e) {
                                    return console.error("Clipboard API error:", e), !1
                                }
                            })(e)) return !0
                    }
                    return (e => {
                        const t = document.createElement("textarea");
                        t.value = e, document.body.appendChild(t), t.select();
                        let a = !1;
                        try {
                            a = document.execCommand("copy")
                        } catch (e) {
                            console.error("Unable to copy", e)
                        }
                        return document.body.removeChild(t), a
                    })(e)
                }
        },
        959: function(e, t, a) {
            "use strict";
            a.d(t, "a", (function() {
                return i
            }));
            var n = a(48),
                s = a(120),
                o = a(101),
                l = a(28);
            const i = e => {
                let {
                    targetUserId: t,
                    type: a,
                    itemId: i,
                    itemType: c,
                    amount: r,
                    authorNickname: d,
                    hashtag: u
                } = e;
                const m = {
                    target_user_id: t,
                    type: a,
                    item_id: i,
                    item_type: c,
                    amount: r,
                    author_nickname: d,
                    hashtag: u
                };
                Object(n.j)(l.r.ETC_USE_SPOON, m), Object(s.a)({
                    eventName: l.r.ETC_USE_SPOON,
                    eventParams: m
                }), Object(o.m)()
            }
        },
        960: function(e, t, a) {
            e.exports = a.p + "src/images/header/header_search_on.3dde824b.png"
        }
    }
]);
//# sourceMappingURL=53.0dd6be35.chunk.js.map