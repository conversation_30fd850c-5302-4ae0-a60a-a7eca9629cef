{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@sopia-bundle-react-ts-template/background": ["apps/background/src/index.ts"], "@sopia-bundle-react-ts-template/worker": ["apps/worker/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}