import { Card, CardBody, Input, Button, Checkbox } from '@heroui/react'
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import Slider from 'react-slick'
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import { Modal } from '../components/Modal'
import { TermsOfService } from '../components/TermsOfService'
import { PrivacyPolicy } from '../components/PrivacyPolicy'

const slides = [
  {
    titleKey: 'app.title',
    descriptionKey: 'app.description',
    icon: '🎥'
  },
  {
    titleKey: 'features.chat',
    descriptionKey: 'features.chat.description',
    icon: '💬'
  },
  {
    titleKey: 'features.customize',
    descriptionKey: 'features.customize.description',
    icon: '⚙️'
  }
]

export default function SignupPage() {
  const [formData, setFormData] = useState({
    id: '',
    password: '',
    passwordConfirm: ''
  })
  const [agreements, setAgreements] = useState({
    terms: false,
    privacy: false
  })
  const [errors, setErrors] = useState({
    id: '',
    password: '',
    passwordConfirm: ''
  })
  const [isSignupEnabled, setIsSignupEnabled] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalContent, setModalContent] = useState<{
    title: string
    content: string | React.ReactNode
  }>({ title: '', content: '' })
  const navigate = useNavigate()
  const { t } = useTranslation()

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    arrows: false
  }

  // 입력값 검증
  const validateForm = () => {
    const newErrors = {
      id: '',
      password: '',
      passwordConfirm: ''
    }

    // ID 검증
    if (formData.id.length < 4) {
      newErrors.id = t('signup.errors.idTooShort')
    }

    // 비밀번호 검증
    if (formData.password.length < 8) {
      newErrors.password = t('signup.errors.passwordTooShort')
    }

    // 비밀번호 확인 검증
    if (formData.password !== formData.passwordConfirm) {
      newErrors.passwordConfirm = t('signup.errors.passwordMismatch')
    }

    setErrors(newErrors)

    // 모든 조건이 충족되었는지 확인
    const isValid =
      Object.values(newErrors).every((error) => !error) && agreements.terms && agreements.privacy

    setIsSignupEnabled(isValid)
  }

  // 입력값이나 약관 동의가 변경될 때마다 검증
  useEffect(() => {
    // 모든 필드가 비어있으면 validation check를 하지 않음
    if (!formData.id && !formData.password && !formData.passwordConfirm) {
      return
    }
    validateForm()
  }, [formData, agreements])

  const handleSignup = async () => {
    try {
      // TODO: 실제 회원가입 API 호출
      const response = await fetch('/api/signup', {
        method: 'POST',
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        navigate('/login')
      }
    } catch (error) {
      console.error('Signup failed:', error)
    }
  }

  const handleShowTerms = () => {
    setModalContent({
      title: t('signup.termsTitle'),
      content: <TermsOfService />
    })
    setIsModalOpen(true)
  }

  const handleShowPrivacy = () => {
    setModalContent({
      title: t('signup.privacyTitle'),
      content: <PrivacyPolicy />
    })
    setIsModalOpen(true)
  }

  return (
    <div className="min-h-screen flex dark:bg-gray-900">
      {/* 왼쪽 슬라이드 섹션 */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary to-primary-dark dark:from-primary-dark dark:to-primary">
        <div className="w-full flex items-center justify-center p-12">
          <div className="w-full max-w-md">
            <Slider {...sliderSettings}>
              {slides.map((slide, index) => (
                <div key={index} className="outline-none">
                  <div className="text-center px-6 py-12">
                    <div className="text-6xl mb-6">{slide.icon}</div>
                    <h3 className="text-3xl font-bold text-white mb-4">{t(slide.titleKey)}</h3>
                    <p className="text-xl text-white">{t(slide.descriptionKey)}</p>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </div>
      </div>

      {/* 오른쪽 회원가입 섹션 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
        <div className="w-full max-w-md space-y-8">
          <div className="flex flex-col items-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('signup.title')}
            </h2>
            <p className="mt-2 text-gray-600 dark:text-gray-400">{t('signup.description')}</p>
          </div>

          <Card className="w-full shadow-lg dark:bg-gray-800">
            <CardBody className="p-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('auth.id')}
                </label>
                <Input
                  type="text"
                  value={formData.id}
                  onChange={(e) => setFormData({ ...formData, id: e.target.value })}
                  className="w-full"
                  placeholder={t('signup.idPlaceholder')}
                  size="lg"
                />
                {errors.id && <p className="mt-1 text-sm text-red-500">{errors.id}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('auth.password')}
                </label>
                <Input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="w-full"
                  placeholder={t('signup.passwordPlaceholder')}
                  size="lg"
                />
                {errors.password && <p className="mt-1 text-sm text-red-500">{errors.password}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('signup.passwordConfirm')}
                </label>
                <Input
                  type="password"
                  value={formData.passwordConfirm}
                  onChange={(e) => setFormData({ ...formData, passwordConfirm: e.target.value })}
                  className="w-full"
                  placeholder={t('signup.passwordConfirmPlaceholder')}
                  size="lg"
                />
                {errors.passwordConfirm && (
                  <p className="mt-1 text-sm text-red-500">{errors.passwordConfirm}</p>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <Checkbox
                    id="terms-checkbox"
                    checked={agreements.terms}
                    onChange={(e) => setAgreements({ ...agreements, terms: e.target.checked })}
                  >
                    <label
                      htmlFor="terms-checkbox"
                      className="ml-2 text-sm text-gray-600 dark:text-gray-400"
                    >
                      <span className="cursor-pointer">{t('signup.agreeToTerms')}</span>{' '}
                    </label>
                  </Checkbox>
                  <span
                    className="text-primary ms-2 text-sm hover:text-primary-light cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleShowTerms()
                    }}
                  >
                    {t('signup.viewTerms')}
                  </span>
                </div>

                <div className="flex items-center">
                  <Checkbox
                    id="privacy-checkbox"
                    checked={agreements.privacy}
                    onChange={(e) => setAgreements({ ...agreements, privacy: e.target.checked })}
                  >
                    <label
                      htmlFor="privacy-checkbox"
                      className="ml-2 text-sm text-gray-600 dark:text-gray-400"
                    >
                      <span className="cursor-pointer">{t('signup.agreeToPrivacy')}</span>{' '}
                    </label>
                  </Checkbox>
                  <span
                    className="text-primary ms-2 text-sm hover:text-primary-light cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleShowPrivacy()
                    }}
                  >
                    {t('signup.viewPrivacy')}
                  </span>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button
                  onPress={() => navigate('/login')}
                  className="w-1/3 bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
                  size="lg"
                >
                  {t('common.back')}
                </Button>
                <Button
                  onPress={handleSignup}
                  className="w-2/3 bg-primary text-white hover:bg-primary-light dark:bg-primary-light dark:hover:bg-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  size="lg"
                  isDisabled={!isSignupEnabled}
                >
                  {t('signup.submit')}
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>

      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} title={modalContent.title}>
        <div>{modalContent.content}</div>
      </Modal>
    </div>
  )
}
