{"name": "@sopia/core", "version": "0.0.1", "description": "SOPIA Core Library", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "swc src -d dist  --strip-leading-paths && tsc --emitDeclarationOnly --outDir dist", "dev": "swc src -d dist --watch --strip-leading-paths", "typecheck": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"@swc-node/register": "^1.10.10", "@swc/cli": "^0.3.10", "@swc/core": "^1.3.101", "@swc/helpers": "^0.5.15", "@types/lodash": "^4.17.16", "@types/node": "^20.11.19", "regenerator-runtime": "^0.14.1", "rimraf": "^5.0.5", "typescript": "^5.3.3"}, "dependencies": {"class-transformer": "^0.5.1", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2"}}