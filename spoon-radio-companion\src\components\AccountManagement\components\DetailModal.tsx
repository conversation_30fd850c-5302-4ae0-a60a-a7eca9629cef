import React, { useState } from 'react';
import { User, SpoonTokenData } from '../types';
import { formatDate, formatTimestamp, getStatusText, getRoleText, decodeJWT } from '../utils';
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalBody,
  DetailRow,
  DetailLabel,
  DetailValue,
  ModalActions,
  ModalButton,
  TokenSection,
  TokenSectionHeader,
  TokenSectionContent,
  JWTField,
  JWTLabel,
  JWTValue,
  ExpiryBadge,
  ErrorMessage,
  StatusBadge,
  RoleBadge
} from '../styles';

interface DetailModalProps {
  isOpen: boolean;
  selectedUser: User | null;
  selectedToken: SpoonTokenData | null;
  spoonTokens: SpoonTokenData[];
  getLinkedUserInfo: (tokenIP: string) => User | null;
  onClose: () => void;
  onDeleteUser: (userId: string) => void;
  onDeleteToken: (ip: string) => void;
  onUnlinkToken: (ip: string) => void;
}

export const DetailModal: React.FC<DetailModalProps> = ({
  isOpen,
  selectedUser,
  selectedToken,
  spoonTokens,
  getLinkedUserInfo,
  onClose,
  onDeleteUser,
  onDeleteToken,
  onUnlinkToken
}) => {
  const [showFullToken, setShowFullToken] = useState(false);

  return (
    <Modal isOpen={isOpen}>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>
            {selectedUser ? '계정 상세 정보' : '토큰 상세 정보'}
          </ModalTitle>
        </ModalHeader>
        
        {selectedUser && (
          <ModalBody>
            <DetailRow>
              <DetailLabel>이름:</DetailLabel>
              <DetailValue>{selectedUser.displayName}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>이메일:</DetailLabel>
              <DetailValue>{selectedUser.email}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>비밀번호:</DetailLabel>
              <DetailValue>{selectedUser.password}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>가입일:</DetailLabel>
              <DetailValue>{selectedUser.joinDate}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>마지막 로그인:</DetailLabel>
              <DetailValue>{selectedUser.lastLogin}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>상태:</DetailLabel>
              <DetailValue>
                <StatusBadge status={selectedUser.status}>
                  {getStatusText(selectedUser.status)}
                </StatusBadge>
              </DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>역할:</DetailLabel>
              <DetailValue>
                <RoleBadge role={selectedUser.role}>
                  {getRoleText(selectedUser.role)}
                </RoleBadge>
              </DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>현재 IP:</DetailLabel>
              <DetailValue>{selectedUser.currentIP || '알 수 없음'}</DetailValue>
            </DetailRow>
            
            {/* 연결된 토큰 정보 */}
            {(() => {
              // linkedUserId로 연결된 토큰 찾기
              const linkedToken = spoonTokens.find(token => token.linkedUserId === selectedUser.id);
              
              return (
                <>
                  <DetailRow style={{ background: '#f0f8ff', padding: '8px', borderRadius: '4px', margin: '8px 0' }}>
                    <DetailLabel style={{ color: '#1976d2', fontWeight: '600' }}>연결된 스푼 토큰</DetailLabel>
                    <DetailValue>
                      <StatusBadge status={linkedToken ? 'linked' : 'unlinked'}>
                        {linkedToken ? '연결됨' : '연결안됨'}
                      </StatusBadge>
                    </DetailValue>
                  </DetailRow>
                  
                  {linkedToken && (
                    <TokenSection>
                      <TokenSectionHeader>스푼 토큰 상세 정보</TokenSectionHeader>
                      <TokenSectionContent>
                        <DetailRow>
                          <DetailLabel>IP 주소:</DetailLabel>
                          <DetailValue>{linkedToken.id}</DetailValue>
                        </DetailRow>
                        <DetailRow>
                          <DetailLabel>스푼 닉네임:</DetailLabel>
                          <DetailValue>{linkedToken.user?.nickname || '없음'}</DetailValue>
                        </DetailRow>
                        <DetailRow>
                          <DetailLabel>스푼 ID:</DetailLabel>
                          <DetailValue>{linkedToken.user?.id || '없음'}</DetailValue>
                        </DetailRow>
                        <DetailRow>
                          <DetailLabel>스푼 태그:</DetailLabel>
                          <DetailValue>{linkedToken.user?.tag || '없음'}</DetailValue>
                        </DetailRow>
                        <DetailRow>
                          <DetailLabel>토큰 생성일:</DetailLabel>
                          <DetailValue>{formatDate(linkedToken.timestamp)}</DetailValue>
                        </DetailRow>
                        
                        {/* JWT 토큰 해석 */}
                        {(() => {
                          const decodedToken = decodeJWT(linkedToken.token);
                          
                          return (
                            <>
                              <DetailRow style={{ background: '#fff8e1', padding: '8px', borderRadius: '4px', margin: '8px 0' }}>
                                <DetailLabel style={{ color: '#f57c00', fontWeight: '600' }}>JWT 토큰 정보</DetailLabel>
                                <DetailValue>
                                  <ExpiryBadge isExpired={decodedToken.isExpired}>
                                    {decodedToken.isExpired ? '만료됨' : '유효함'}
                                  </ExpiryBadge>
                                </DetailValue>
                              </DetailRow>
                              
                              {decodedToken.payload && (
                                <>
                                  {decodedToken.payload.iat && (
                                    <DetailRow>
                                      <DetailLabel>토큰 발급 시간:</DetailLabel>
                                      <DetailValue>{formatTimestamp(decodedToken.payload.iat)}</DetailValue>
                                    </DetailRow>
                                  )}
                                  {decodedToken.payload.exp && (
                                    <DetailRow>
                                      <DetailLabel>토큰 만료 시간:</DetailLabel>
                                      <DetailValue>{formatTimestamp(decodedToken.payload.exp)}</DetailValue>
                                    </DetailRow>
                                  )}
                                  {decodedToken.payload.cntry && (
                                    <DetailRow>
                                      <DetailLabel>국가:</DetailLabel>
                                      <DetailValue>{decodedToken.payload.cntry}</DetailValue>
                                    </DetailRow>
                                  )}
                                </>
                              )}
                              
                              {/* 원본 사용자 데이터 */}
                              {linkedToken.originalData && (
                                <>
                                  <DetailRow style={{ background: '#f3e5f5', padding: '8px', borderRadius: '4px', margin: '8px 0' }}>
                                    <DetailLabel style={{ color: '#7b1fa2', fontWeight: '600' }}>스푼 계정 정보</DetailLabel>
                                    <DetailValue></DetailValue>
                                  </DetailRow>
                                  
                                  <DetailRow>
                                    <DetailLabel>계정 생성일:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.date_joined ? new Date(linkedToken.originalData.date_joined).toLocaleDateString('ko-KR') : '없음'}</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>구독플랜:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.subscription_dj ? '구독중' : '미구독'}</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>생일:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.date_of_birth ? new Date(linkedToken.originalData.date_of_birth).toLocaleDateString('ko-KR') : '없음'}</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>폰번호:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.phone_number || '없음'}</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>이메일:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.email || '없음'}</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>성별:</DetailLabel>
                                    <DetailValue>{
                                      linkedToken.originalData.gender === 1 ? '남성' : 
                                      linkedToken.originalData.gender === 0 ? '여성' : '선택안함'
                                    }</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>팬 수:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.follower_count?.toLocaleString() || 0}명</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>팔로잉 수:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.following_count?.toLocaleString() || 0}명</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>보유 스푼:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.budget?.present_spoon?.toLocaleString() || 0}</DetailValue>
                                  </DetailRow>
                                  <DetailRow>
                                    <DetailLabel>구매 스푼:</DetailLabel>
                                    <DetailValue>{linkedToken.originalData.budget?.purchase_spoon?.toLocaleString() || 0}</DetailValue>
                                  </DetailRow>
                                </>
                              )}
                            </>
                          );
                        })()}
                      </TokenSectionContent>
                    </TokenSection>
                  )}
                  
                  {/* 토큰 전체 정보 (클릭하면 확장) */}
                  {linkedToken && (
                    <TokenSection>
                      <TokenSectionHeader 
                        style={{ cursor: 'pointer', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                        onClick={() => setShowFullToken(!showFullToken)}
                      >
                        <span>토큰 전체 정보</span>
                        <span style={{ fontSize: '12px' }}>
                          {showFullToken ? '▼ 접기' : '▶ 펼치기'}
                        </span>
                      </TokenSectionHeader>
                      {showFullToken && (
                        <TokenSectionContent>
                          {/* 헤더 */}
                          <JWTField>
                            <JWTLabel>JWT 헤더 (Header)</JWTLabel>
                            <JWTValue>
                              {(() => {
                                const decodedToken = decodeJWT(linkedToken.token);
                                return decodedToken.header ? JSON.stringify(decodedToken.header, null, 2) : '헤더 정보 없음';
                              })()}
                            </JWTValue>
                          </JWTField>

                          {/* 페이로드 */}
                          <JWTField>
                            <JWTLabel>JWT 페이로드 (Payload)</JWTLabel>
                            <JWTValue>
                              {(() => {
                                const decodedToken = decodeJWT(linkedToken.token);
                                return decodedToken.payload ? JSON.stringify(decodedToken.payload, null, 2) : '페이로드 정보 없음';
                              })()}
                            </JWTValue>
                          </JWTField>

                          {/* 시그니처 */}
                          <JWTField>
                            <JWTLabel>JWT 시그니처 (Signature)</JWTLabel>
                            <JWTValue style={{ fontSize: '10px' }}>
                              {(() => {
                                const decodedToken = decodeJWT(linkedToken.token);
                                return decodedToken.signature || '시그니처 정보 없음';
                              })()}
                            </JWTValue>
                          </JWTField>

                          {/* 원본 토큰 */}
                          <JWTField>
                            <JWTLabel>원본 JWT 토큰</JWTLabel>
                            <JWTValue style={{ fontSize: '10px', maxHeight: '100px', overflow: 'auto' }}>
                              {linkedToken.token}
                            </JWTValue>
                          </JWTField>

                          {/* 원본 사용자 데이터 */}
                          {linkedToken.originalData && (
                            <JWTField>
                              <JWTLabel>원본 스푼 사용자 데이터</JWTLabel>
                              <JWTValue style={{ fontSize: '10px', maxHeight: '200px', overflow: 'auto' }}>
                                {JSON.stringify(linkedToken.originalData, null, 2)}
                              </JWTValue>
                            </JWTField>
                          )}
                        </TokenSectionContent>
                      )}
                    </TokenSection>
                  )}
                </>
              );
            })()}
          </ModalBody>
        )}

        {selectedToken && (
          <ModalBody>
            {/* 기본 토큰 정보 */}
            <TokenSection>
              <TokenSectionHeader>기본 정보</TokenSectionHeader>
              <TokenSectionContent>
                <DetailRow>
                  <DetailLabel>IP 주소:</DetailLabel>
                  <DetailValue>{selectedToken.id}</DetailValue>
                </DetailRow>
                
                {/* 스푼 정보 */}
                <DetailRow style={{ background: '#f0f8ff', padding: '8px', borderRadius: '4px', margin: '8px 0' }}>
                  <DetailLabel style={{ color: '#1976d2', fontWeight: '600' }}>스푼 정보</DetailLabel>
                  <DetailValue></DetailValue>
                </DetailRow>
                <DetailRow>
                  <DetailLabel>스푼 닉네임:</DetailLabel>
                  <DetailValue>{selectedToken.user.nickname}</DetailValue>
                </DetailRow>
                <DetailRow>
                  <DetailLabel>스푼 ID:</DetailLabel>
                  <DetailValue>{selectedToken.user.id}</DetailValue>
                </DetailRow>
                <DetailRow>
                  <DetailLabel>스푼 태그:</DetailLabel>
                  <DetailValue>{selectedToken.user.tag || '없음'}</DetailValue>
                </DetailRow>
                
                {/* 연결된 웹 서버 사용자 정보 (IP 기반) */}
                {(() => {
                  const linkedUser = getLinkedUserInfo(selectedToken.id);
                  const isLinked = !!linkedUser;
                  
                  return (
                    <>
                      <DetailRow style={{ background: '#fff8e1', padding: '8px', borderRadius: '4px', margin: '8px 0' }}>
                        <DetailLabel style={{ color: '#f57c00', fontWeight: '600' }}>
                          연결된 웹 서버 계정
                        </DetailLabel>
                        <DetailValue>
                          <StatusBadge status={isLinked ? 'linked' : 'unlinked'}>
                            {isLinked ? '연결됨' : '연결안됨'}
                          </StatusBadge>
                        </DetailValue>
                      </DetailRow>
                      
                      {linkedUser ? (
                        <>
                          <DetailRow>
                            <DetailLabel>계정 ID:</DetailLabel>
                            <DetailValue>{linkedUser.id}</DetailValue>
                          </DetailRow>
                          <DetailRow>
                            <DetailLabel>계정 이름:</DetailLabel>
                            <DetailValue>{linkedUser.displayName}</DetailValue>
                          </DetailRow>
                          <DetailRow>
                            <DetailLabel>계정 이메일:</DetailLabel>
                            <DetailValue>{linkedUser.email}</DetailValue>
                          </DetailRow>
                          <DetailRow>
                            <DetailLabel>계정 상태:</DetailLabel>
                            <DetailValue>
                              <StatusBadge status={linkedUser.status}>
                                {getStatusText(linkedUser.status)}
                              </StatusBadge>
                            </DetailValue>
                          </DetailRow>
                          <DetailRow>
                            <DetailLabel>계정 역할:</DetailLabel>
                            <DetailValue>
                              <RoleBadge role={linkedUser.role}>
                                {getRoleText(linkedUser.role)}
                              </RoleBadge>
                            </DetailValue>
                          </DetailRow>
                          <DetailRow>
                            <DetailLabel>가입일:</DetailLabel>
                            <DetailValue>{linkedUser.joinDate}</DetailValue>
                          </DetailRow>
                          <DetailRow>
                            <DetailLabel>마지막 로그인:</DetailLabel>
                            <DetailValue>{linkedUser.lastLogin}</DetailValue>
                          </DetailRow>
                        </>
                      ) : (
                        <DetailRow>
                          <DetailLabel>연결 상태:</DetailLabel>
                          <DetailValue style={{ color: '#999', fontStyle: 'italic' }}>
                            이 IP에 연결된 웹 서버 계정이 없습니다
                          </DetailValue>
                        </DetailRow>
                      )}
                    </>
                  );
                })()}
                
                <DetailRow>
                  <DetailLabel>생성일:</DetailLabel>
                  <DetailValue>{formatDate(selectedToken.timestamp)}</DetailValue>
                </DetailRow>
              </TokenSectionContent>
            </TokenSection>

            {/* JWT 토큰 해석 */}
            {(() => {
              const decodedToken = decodeJWT(selectedToken.token);
              
              return (
                <TokenSection>
                  <TokenSectionHeader>JWT 토큰 해석</TokenSectionHeader>
                  <TokenSectionContent>
                    {decodedToken.error ? (
                      <ErrorMessage>{decodedToken.error}</ErrorMessage>
                    ) : (
                      <>
                        {/* 토큰 상태 */}
                        <JWTField>
                          <JWTLabel>토큰 상태</JWTLabel>
                          <div>
                            <ExpiryBadge isExpired={decodedToken.isExpired}>
                              {decodedToken.isExpired ? '만료됨' : '유효함'}
                            </ExpiryBadge>
                          </div>
                        </JWTField>

                        {/* 헤더 */}
                        <JWTField>
                          <JWTLabel>헤더 (Header)</JWTLabel>
                          <JWTValue>
                            {JSON.stringify(decodedToken.header, null, 2)}
                          </JWTValue>
                        </JWTField>

                        {/* 페이로드 */}
                        <JWTField>
                          <JWTLabel>페이로드 (Payload)</JWTLabel>
                          <JWTValue>
                            {JSON.stringify(decodedToken.payload, null, 2)}
                          </JWTValue>
                        </JWTField>

                        {/* 주요 정보 요약 */}
                        {decodedToken.payload && (
                          <JWTField>
                            <JWTLabel>주요 정보</JWTLabel>
                            <div style={{ background: '#f8f9fa', padding: '12px', borderRadius: '4px', border: '1px solid #e0e0e0' }}>
                              {decodedToken.payload.sub && (
                                <DetailRow style={{ padding: '4px 0', borderBottom: '1px solid #e0e0e0' }}>
                                  <DetailLabel>사용자 ID (sub):</DetailLabel>
                                  <DetailValue>{decodedToken.payload.sub}</DetailValue>
                                </DetailRow>
                              )}
                              {decodedToken.payload.iat && (
                                <DetailRow style={{ padding: '4px 0', borderBottom: '1px solid #e0e0e0' }}>
                                  <DetailLabel>발급 시간 (iat):</DetailLabel>
                                  <DetailValue>{formatTimestamp(decodedToken.payload.iat)}</DetailValue>
                                </DetailRow>
                              )}
                              {decodedToken.payload.exp && (
                                <DetailRow style={{ padding: '4px 0', borderBottom: '1px solid #e0e0e0' }}>
                                  <DetailLabel>만료 시간 (exp):</DetailLabel>
                                  <DetailValue>{formatTimestamp(decodedToken.payload.exp)}</DetailValue>
                                </DetailRow>
                              )}
                              {decodedToken.payload.cntry && (
                                <DetailRow style={{ padding: '4px 0', borderBottom: '1px solid #e0e0e0' }}>
                                  <DetailLabel>국가 (cntry):</DetailLabel>
                                  <DetailValue>{decodedToken.payload.cntry}</DetailValue>
                                </DetailRow>
                              )}
                              {decodedToken.payload.grant && (
                                <DetailRow style={{ padding: '4px 0', borderBottom: 'none' }}>
                                  <DetailLabel>권한 (grant):</DetailLabel>
                                  <DetailValue>{Array.isArray(decodedToken.payload.grant) ? decodedToken.payload.grant.join(', ') : decodedToken.payload.grant}</DetailValue>
                                </DetailRow>
                              )}
                            </div>
                          </JWTField>
                        )}

                        {/* 시그니처 */}
                        <JWTField>
                          <JWTLabel>시그니처 (Signature)</JWTLabel>
                          <JWTValue style={{ fontSize: '10px' }}>
                            {decodedToken.signature}
                          </JWTValue>
                        </JWTField>
                      </>
                    )}
                  </TokenSectionContent>
                </TokenSection>
              );
            })()}

            {/* 원본 토큰 */}
            <TokenSection>
              <TokenSectionHeader>원본 토큰</TokenSectionHeader>
              <TokenSectionContent>
                <JWTValue style={{ fontSize: '10px', maxHeight: '100px', overflow: 'auto' }}>
                  {selectedToken.token}
                </JWTValue>
              </TokenSectionContent>
            </TokenSection>

            {/* 원본 데이터 */}
            {selectedToken.originalData ? (
              <TokenSection>
                <TokenSectionHeader>원본 사용자 데이터</TokenSectionHeader>
                <TokenSectionContent>
                  <div className="border-t pt-4">
                    <h4 className="font-semibold mb-3">원본 사용자 데이터</h4>
                    <div className="space-y-4">
                      {/* 주요 정보 섹션 */}
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <h5 className="font-medium text-blue-800 mb-2">주요 정보</h5>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div><span className="font-medium">사용자 ID:</span> {selectedToken.originalData.id}</div>
                          <div><span className="font-medium">닉네임:</span> {selectedToken.originalData.nickname}</div>
                          <div><span className="font-medium">태그:</span> {selectedToken.originalData.tag}</div>
                          <div><span className="font-medium">연결된 SNS:</span> {selectedToken.originalData.sns_type || '없음'}</div>
                          <div><span className="font-medium">이메일:</span> {selectedToken.originalData.email || '없음'}</div>
                          <div><span className="font-medium">계정생성날짜:</span> {selectedToken.originalData.date_joined ? new Date(selectedToken.originalData.date_joined).toLocaleDateString('ko-KR') : '없음'}</div>
                          <div><span className="font-medium">생년월일:</span> {selectedToken.originalData.date_of_birth || '없음'}</div>
                          <div>
                            <span className="font-medium">성별:</span> {
                              selectedToken.originalData.gender === 1 ? '남성' : 
                              selectedToken.originalData.gender === 0 ? '여성' : '선택안함'
                            }
                          </div>
                        </div>
                      </div>

                      {/* 팔로우 정보 */}
                      <div className="bg-green-50 p-3 rounded-lg">
                        <h5 className="font-medium text-green-800 mb-2">팔로우 정보</h5>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div><span className="font-medium">팬:</span> {selectedToken.originalData.follower_count?.toLocaleString() || 0}명</div>
                          <div><span className="font-medium">팔로잉:</span> {selectedToken.originalData.following_count?.toLocaleString() || 0}명</div>
                        </div>
                      </div>

                      {/* 스푼 정보 */}
                      <div className="bg-yellow-50 p-3 rounded-lg">
                        <h5 className="font-medium text-yellow-800 mb-2">스푼 정보</h5>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div><span className="font-medium">보유 스푼:</span> {selectedToken.originalData.budget?.present_spoon?.toLocaleString() || 0}</div>
                          <div><span className="font-medium">구매 스푼:</span> {selectedToken.originalData.budget?.purchase_spoon?.toLocaleString() || 0}</div>
                          <div><span className="font-medium">총 사용 스푼:</span> {selectedToken.originalData.budget?.total_exchange_spoon?.toLocaleString() || 0}</div>
                          <div><span className="font-medium">월 결제 금액:</span> {selectedToken.originalData.budget?.monthly_pay_amount?.toLocaleString() || 0}원</div>
                        </div>
                      </div>

                      {/* 계정 상태 */}
                      <div className="bg-purple-50 p-3 rounded-lg">
                        <h5 className="font-medium text-purple-800 mb-2">계정 상태</h5>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div><span className="font-medium">VIP 여부:</span> {selectedToken.originalData.is_vip ? '✓ VIP' : '일반'}</div>
                          <div><span className="font-medium">티어:</span> {selectedToken.originalData.tier || '없음'}</div>
                          <div><span className="font-medium">스푼스태프:</span> {selectedToken.originalData.is_staff ? '✓ 스태프' : '일반 사용자'}</div>
                          <div><span className="font-medium">인증 여부:</span> {selectedToken.originalData.is_verified ? '✓ 인증됨' : '미인증'}</div>
                          <div><span className="font-medium">구독플랜:</span> {selectedToken.originalData.push_settings?.subscription_dj ? '활성' : '비활성'}</div>
                          <div><span className="font-medium">활성 상태:</span> {selectedToken.originalData.is_active ? '활성' : '비활성'}</div>
                        </div>
                      </div>

                      {/* 토큰 정보 */}
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <h5 className="font-medium text-gray-800 mb-2">토큰 정보</h5>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">액세스 토큰:</span>
                            <div className="mt-1">
                              <button
                                onClick={() => setShowFullToken(!showFullToken)}
                                className="text-blue-600 hover:text-blue-800 underline text-xs"
                              >
                                {showFullToken ? '숨기기' : '전체 보기'}
                              </button>
                              <div className="bg-white p-2 rounded border mt-1 font-mono text-xs break-all">
                                {showFullToken ? selectedToken.originalData.token : `${selectedToken.originalData.token?.substring(0, 50)}...`}
                              </div>
                            </div>
                          </div>
                          <div>
                            <span className="font-medium">리프레시 토큰:</span>
                            <div className="bg-white p-2 rounded border mt-1 font-mono text-xs break-all">
                              {selectedToken.originalData.refresh_token || '없음'}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 전체 JSON 데이터 */}
                      <div className="bg-gray-100 p-3 rounded-lg">
                        <h5 className="font-medium text-gray-800 mb-2">전체 JSON 데이터</h5>
                        <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-40">
                          {JSON.stringify(selectedToken.originalData, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                </TokenSectionContent>
              </TokenSection>
            ) : (
              <TokenSection>
                <TokenSectionHeader>원본 사용자 데이터</TokenSectionHeader>
                <TokenSectionContent>
                  <ErrorMessage>
                    ⚠ 원본 데이터가 없습니다. 이 토큰은 확장프로그램의 이전 버전에서 생성되었거나 데이터 저장 중 오류가 발생했을 수 있습니다.
                  </ErrorMessage>
                </TokenSectionContent>
              </TokenSection>
            )}
          </ModalBody>
        )}
        
        <ModalActions>
          <ModalButton 
            variant="secondary" 
            onClick={onClose}
          >
            닫기
          </ModalButton>
          {selectedUser && selectedUser.role !== 'admin' && (
            <ModalButton 
              variant="danger" 
              onClick={() => {
                onDeleteUser(selectedUser.id);
                onClose();
              }}
            >
              계정 삭제
            </ModalButton>
          )}
          {selectedToken && (
            <>
              <ModalButton 
                variant="danger" 
                onClick={() => onDeleteToken(selectedToken.id)}
              >
                토큰 삭제
              </ModalButton>
            </>
          )}
        </ModalActions>
      </ModalContent>
    </Modal>
  );
}; 