.message-detail-list-item-container {
    display: flex;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    min-height: 32px;
    margin-left: 20px;
    margin-right: 50px
}

.message-detail-list-item-container .thumbnail-wrap {
    flex: none;
    position: relative;
    min-width: 36px;
    margin-right: 12px
}

.message-detail-list-item-container .thumbnail-wrap .btn-message,
.message-detail-list-item-container .thumbnail-wrap .btn-profile {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1
}

.message-detail-list-item-container .contents {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1 1;
    font-size: 14px;
    line-height: 18px
}

.message-detail-list-item-container .contents .message-detail-wrap {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    grid-column-gap: .125rem;
    column-gap: .125rem
}

.message-detail-list-item-container .contents .message-detail-wrap.multi-line {
    align-items: flex-start
}

.message-detail-list-item-container .contents .message {
    background-color: #f2f2f2;
    padding: 7px 12px;
    font-size: 14px;
    line-height: 18px;
    border-radius: 16px;
    color: #333
}

.message-detail-list-item-container .contents .message-more-btn {
    width: 24px;
    height: 24px
}

.message-detail-list-item-container .contents .message-more-btn>svg:hover {
    fill: #1a1a1a
}

.message-detail-list-item-container .contents .created {
    margin-top: 6px;
    font-size: 12px;
    line-height: 16px;
    color: grey
}

.message-detail-list-item-container.my {
    flex-direction: row-reverse;
    margin-left: 50px;
    margin-right: 20px
}

.message-detail-list-item-container.my .contents {
    align-items: flex-end
}

.message-detail-list-item-container.my .contents .message-detail-wrap {
    flex-direction: row-reverse
}

.message-detail-list-item-container.my .contents .message {
    color: #fff;
    background-color: #333
}

.message-detail-list-item-container.my .contents .created {
    text-align: right
}

.message-detail-list-item-container:first-child {
    margin-top: 0
}

.message-detail-list-item-container.present .present-box {
    background-color: #ffece5;
    border: 1px solid alive_orange40;
    border-radius: 16px;
    display: flex;
    align-items: center
}

.message-detail-list-item-container.present .present-box .sticker-thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px
}

.message-detail-list-item-container.present .present-box .sticker-thumbnail img {
    width: 100%
}

.message-detail-list-item-container.present .present-box .sticker-detail {
    color: #333;
    font-size: 14px;
    line-height: 18px;
    -webkit-margin-end: 12px;
    margin-inline-end: 12px
}

.message-detail-list-item-container.present .present-box .sticker-detail span {
    color: #ff4100;
    font-weight: 600
}

.app-container.rtl .message-detail-list-item-container {
    margin-left: 50px;
    margin-right: 20px
}

.app-container.rtl .message-detail-list-item-container .thumbnail-wrap {
    margin-left: 12px;
    margin-right: 0
}

.app-container.rtl .message-detail-list-item-container.my {
    margin-left: 20px;
    margin-right: 50px
}

.message-wrap {
    border: 1px solid #e6e6e6;
    border-radius: 16px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    flex: 1 1;
    -webkit-margin-start: 6px;
    margin-inline-start: 6px
}

.message-wrap .input-message {
    flex: 1 1;
    height: 18px;
    padding: 0 16px;
    margin: 6px 0;
    font-size: 14px;
    line-height: 18px;
    color: #4d4d4d;
    resize: none
}

.message-wrap .input-message::-webkit-input-placeholder {
    color: #b3b3b3
}

.message-wrap .input-message:-moz-placeholder,
.message-wrap .input-message::-moz-placeholder {
    color: #b3b3b3
}

.message-wrap .input-message:-ms-input-placeholder {
    color: #b3b3b3
}

.message-wrap .btn-send {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: none;
    overflow: hidden;
    height: 100%;
    padding: 0 16px;
    font-size: 13px;
    color: grey
}

.message-wrap .btn-send.on {
    color: #ff4100
}

.message-wrap.focus {
    border-color: #4d4d4d
}

.direct-message-detail-container {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    transform: translateX(100%);
    transition: transform .5s cubic-bezier(.25, .8, .25, 1);
    will-change: transform;
    z-index: 1
}

.direct-message-detail-container .message-detail-header {
    flex: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 47px;
    padding: 0 14px;
    border-bottom: 1px solid #e6e6e6
}

.direct-message-detail-container .message-detail-header .unread-count {
    width: 10px;
    padding: 1px 4px;
    margin: 0 8px;
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    text-align: center;
    border-radius: 9px;
    color: #fff;
    background-color: #ff4100
}

.direct-message-detail-container .message-detail-header .unread-count.auto-width {
    width: auto
}

.direct-message-detail-container .message-detail-header .left {
    flex: none;
    z-index: 1
}

.direct-message-detail-container .message-detail-header .left .btn-back {
    display: flex;
    align-items: center
}

.direct-message-detail-container .message-detail-header .left .btn-back img {
    width: 24px
}

.direct-message-detail-container .message-detail-header .left .btn-back.hide {
    display: none
}

.direct-message-detail-container .message-detail-header .title {
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    color: #333;
    flex: 1 1
}

.direct-message-detail-container .message-detail-header .title .nickname {
    font-weight: 600;
    align-items: center
}

.direct-message-detail-container .message-detail-header .title .nickname .badge {
    width: 20px;
    margin-right: 4px
}

.direct-message-detail-container .message-detail-header .title .nickname.subscribe {
    line-height: 20px
}

.direct-message-detail-container .message-detail-header .title .tag {
    margin-top: 2px;
    font-size: 10px;
    line-height: 14px;
    color: grey
}

.direct-message-detail-container .message-detail-header .title .tag.subscribe {
    margin-top: 1px
}

.direct-message-detail-container .message-detail-header .title.hide {
    left: auto;
    max-width: 258px;
    text-align: left;
    transform: none
}

.direct-message-detail-container .message-detail-header .right {
    display: flex;
    align-items: center;
    flex: none;
    z-index: 1
}

.direct-message-detail-container .message-detail-header .right .btn-more {
    display: flex
}

.direct-message-detail-container .message-detail-header .right .btn-more img {
    width: 24px
}

.direct-message-detail-container .message-detail-header .right .btn-toggle-dm {
    min-width: 24px;
    margin-left: 11px
}

.direct-message-detail-container .message-detail-header .right .btn-toggle-dm .btn img {
    width: 24px;
    transform: rotate(180deg)
}

.direct-message-detail-container .message-detail-header .right .btn-toggle-dm .btn.on img {
    transform: rotate(0)
}

.direct-message-detail-container .message-detail-header .right .btn-toggle-dm.hide {
    margin: 0
}

.direct-message-detail-container .message-detail-header .btn-open-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.direct-message-detail-container .list-wrap-notice {
    background-color: #f2f2f2;
    color: #333;
    font-size: 12px;
    line-height: 16px;
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    text-align: center;
    padding: 8px 16px
}

.direct-message-detail-container .list-wrap {
    flex: 1 1;
    overflow-y: auto;
    width: 100%;
    position: relative
}

.direct-message-detail-container .list-wrap .list {
    padding: 16px 0 20px
}

.direct-message-detail-container .list-wrap .list .list-item {
    margin-top: 12px
}

.direct-message-detail-container .list-wrap .list .list-item.combo {
    margin-top: 6px
}

.direct-message-detail-container .list-wrap .list .list-item:first-child {
    margin-top: 0
}

.direct-message-detail-container .message-detail-bottom {
    display: flex;
    align-items: center;
    flex: none;
    margin: 0 16px 20px 12px
}

.direct-message-detail-container .message-detail-bottom.multi-line {
    align-items: flex-end
}

.direct-message-detail-container.on {
    transform: translateX(0)
}

.direct-message-detail-container .dm-context-menu {
    border-radius: 1rem;
    background-color: #fff;
    color: #1a1a1a;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    width: 100%
}

.direct-message-detail-container .dm-context-menu>button {
    padding: 1rem;
    width: 100%
}

.app-container.rtl .direct-message-detail-container {
    transform: translateX(-100%)
}

.app-container.rtl .direct-message-detail-container .message-detail-header .left .btn-back img {
    transform: rotate(180deg)
}

.app-container.rtl .direct-message-detail-container .message-detail-header .title .nickname .badge {
    margin-left: 4px;
    margin-right: 0
}

.app-container.rtl .direct-message-detail-container .message-detail-header .title.hide {
    text-align: right
}

.app-container.rtl .direct-message-detail-container .message-detail-header .right .btn-toggle-dm {
    margin-left: 0;
    margin-right: 11px
}

.app-container.rtl .direct-message-detail-container.on {
    transform: translateX(0)
}

@media screen and (max-width:767px) {
    .direct-message-detail-container.webview .message-detail-header {
        min-height: 52px;
        border-bottom: none
    }
    .direct-message-detail-container.webview .message-detail-header .title {
        max-width: 200px
    }
    .direct-message-detail-container.webview .message-detail-header .title .nickname {
        font-size: 16px;
        line-height: 20px
    }
    .direct-message-detail-container.webview .message-detail-header .title .tag {
        margin-top: 2px;
        font-size: 12px;
        line-height: 16px
    }
    .direct-message-detail-container.webview .message-detail-bottom .btn-send .cover {
        display: none
    }
}

.message-list-item-container {
    display: flex;
    position: relative;
    overflow: hidden;
    min-height: 40px;
    padding: 10px 20px;
    content-visibility: auto
}

.message-list-item-container .thumbnail-wrap {
    display: flex;
    align-items: center;
    flex: none;
    position: relative
}

.message-list-item-container .thumbnail-wrap .btn-profile {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1
}

.message-list-item-container .contents {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1 1;
    padding: 0 16px;
    font-size: 14px;
    line-height: 18px;
    color: #333
}

.message-list-item-container .contents .nickname {
    display: flex;
    align-items: center;
    grid-gap: 4px;
    gap: 4px;
    font-weight: 600
}

.message-list-item-container .contents .nickname .badge {
    width: 20px;
    margin-right: 6px
}

.message-list-item-container .contents .nickname.subscribe {
    line-height: 20px
}

.message-list-item-container .contents .text {
    margin-top: 3px
}

.message-list-item-container .contents .text.present {
    color: #ff7a4d;
    font-weight: 600
}

.message-list-item-container .contents .tag {
    margin-top: 3px;
    color: grey
}

.message-list-item-container .contents .tag.subscribe {
    margin-top: 2px
}

.message-list-item-container .created {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    flex: none;
    margin: 4px 8px 0;
    font-size: 10px;
    line-height: 14px;
    color: grey
}

.message-list-item-container .created .count {
    width: 8px;
    padding: 1px 4px;
    margin-top: 3px;
    font-weight: 600;
    text-align: center;
    border-radius: 8px;
    color: #fff;
    background-color: #ff4100
}

.message-list-item-container .created .count.auto-width {
    width: auto
}

.message-list-item-container .btn-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.message-list-item-container:hover {
    background-color: #f5f5f5
}

.message-list-item-container:active {
    background-color: #e0e0e0
}

.message-list-item-container.new {
    border-radius: 12px;
    opacity: 0;
    box-shadow: 0 0 12px 0 rgba(0, 0, 0, .15);
    background-color: hsla(0, 0%, 100%, .95);
    transform: translateX(100%);
    transition: all .3s cubic-bezier(.25, .8, .25, 1)
}

.message-list-item-container.new.on {
    transform: translateX(0);
    opacity: 1
}

.app-container.rtl .message-list-item-container .contents .nickname .badge {
    margin-left: 6px;
    margin-right: 0
}

@media screen and (max-width:767px) {
    .message-list-item-container.webview {
        padding: 7px 16px
    }
    .message-list-item-container.webview .thumbnail-wrap {
        margin-top: 0
    }
    .message-list-item-container.webview .contents {
        padding: 0 12px
    }
    .message-list-item-container.webview .contents .text {
        margin-top: 4px
    }
    .message-list-item-container.webview .contents .tag.subscribe {
        margin-top: 3px
    }
    .message-list-item-container.webview .created {
        margin: 6px 0 0
    }
    .message-list-item-container.webview .created .time {
        font-size: 12px;
        line-height: 16px
    }
    .message-list-item-container.webview .created .count {
        margin-top: 6px
    }
    .message-list-item-container:active,
    .message-list-item-container:hover {
        background-color: transparent
    }
}

.message-room-list-container {
    display: none;
    flex-direction: column;
    flex: 1 1;
    overflow: hidden
}

.message-room-list-container .list-wrap {
    flex: 1 1;
    overflow-y: auto;
    width: 100%
}

.message-room-list-container .list-wrap .list {
    display: flex;
    flex-direction: column;
    padding: 8px 0 32px
}

.message-room-list-container .list-wrap.empty {
    flex: none
}

.message-room-list-container .empty-result {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: 1 1;
    text-align: center
}

.message-room-list-container .empty-result img {
    width: 90px
}

.message-room-list-container .empty-result p {
    margin-top: 20px;
    font-size: 14px;
    line-height: 18px;
    color: grey
}

.message-room-list-container.on {
    display: flex
}

.message-room-list-loader {
    flex: 1 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.message-room-list-loader>img {
    width: 60px
}

@media screen and (max-width:767px) {
    .room-list-container .list-wrap .list {
        padding-top: 8px
    }
}

.message-new-list-container {
    position: fixed;
    top: 80px;
    right: 20px;
    transition: opacity .5s cubic-bezier(.25, .8, .25, 1)
}

.message-new-list-container .message-new-list {
    display: flex;
    flex-direction: column-reverse;
    position: relative;
    width: 360px
}

.message-new-list-container .message-new-list .message-new-list-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    margin-bottom: 6px;
    transition: transform .3s cubic-bezier(.25, .8, .25, 1)
}

.message-new-list-container .message-new-list .message-new-list-item:first-child {
    margin-bottom: 0
}

.message-new-list-container.fade-out {
    opacity: 0
}

.message-user-list-container {
    display: none;
    flex-direction: column;
    flex: 1 1;
    overflow: hidden
}

.message-user-list-container .search-wrap {
    display: flex;
    align-items: center;
    margin: 20px 15px
}

.message-user-list-container .search-wrap .search-field {
    display: flex;
    align-items: center;
    flex: 1 1;
    height: 28px;
    border: 2px solid #e6e6e6;
    border-radius: 16px;
    background-color: #f7f7f7
}

.message-user-list-container .search-wrap .search-field input {
    width: 100%;
    margin-left: 16px;
    font-size: 14px;
    color: #4d4d4d;
    background-color: transparent
}

.message-user-list-container .search-wrap .search-field input::-webkit-input-placeholder {
    color: #d9d9d9
}

.message-user-list-container .search-wrap .search-field input:-moz-placeholder,
.message-user-list-container .search-wrap .search-field input::-moz-placeholder {
    color: #d9d9d9
}

.message-user-list-container .search-wrap .search-field input:-ms-input-placeholder {
    color: #d9d9d9
}

.message-user-list-container .search-wrap .search-field .btn-search-clear {
    display: flex;
    visibility: hidden
}

.message-user-list-container .search-wrap .search-field .btn-search-clear img {
    width: 16px
}

.message-user-list-container .search-wrap .search-field .btn-search-clear:active,
.message-user-list-container .search-wrap .search-field input:focus+.btn-search-clear {
    visibility: visible
}

.message-user-list-container .search-wrap .search-field .btn-search {
    margin: 0 12px
}

.message-user-list-container .search-wrap .search-field .btn-search img {
    width: 24px
}

.message-user-list-container .search-wrap .search-field.on {
    border-color: #d9d9d9
}

.message-user-list-container .search-wrap .btn-search-cancel {
    flex: none;
    margin-left: 14px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #333
}

.message-user-list-container .list-wrap {
    flex: 1 1;
    overflow-y: auto;
    width: 100%
}

.message-user-list-container .list-wrap .search-guide {
    padding: 0 15px;
    font-size: 12px;
    line-height: 16px;
    color: grey
}

.message-user-list-container .list-wrap .list {
    display: flex;
    flex-direction: column;
    padding: 20px 0 32px
}

.message-user-list-container .list-wrap.empty {
    flex: none
}

.message-user-list-container .empty-result {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: 1 1;
    text-align: center
}

.message-user-list-container .empty-result img {
    width: 90px
}

.message-user-list-container .empty-result p {
    margin-top: 20px;
    font-size: 14px;
    line-height: 18px;
    color: grey
}

.message-user-list-container.on {
    display: flex
}

.app-container.rtl .message-user-list-container .search-wrap .search-field input {
    margin-left: 0;
    margin-right: 16px
}

.app-container.rtl .message-user-list-container .search-wrap .btn-search-cancel {
    margin-left: 0;
    margin-right: 14px
}

.direct-message-container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 3;
    -webkit-user-select: none;
    user-select: none
}

.direct-message-container .messenger-wrap {
    position: fixed;
    bottom: 0;
    right: 0
}

.direct-message-container .messenger-wrap .direct-message-wrap {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    right: 40px;
    width: 100vw;
    max-width: 358px;
    height: calc(100vh - 80px);
    max-height: 600px;
    border: 1px solid #e6e6e6;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background-color: #fff;
    transform: translateY(-48px);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, .2);
    transition: transform .5s cubic-bezier(.25, .8, .25, 1)
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    min-height: 47px;
    padding: 0 14px;
    border-bottom: 1px solid #e6e6e6
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .left {
    display: flex;
    align-items: center
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .left .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    color: #1a1a1a
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .left .unread-count {
    width: 10px;
    padding: 1px 4px;
    margin-left: 4px;
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    text-align: center;
    border-radius: 9px;
    color: #fff;
    background-color: #ff4100
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .left .unread-count.auto-width {
    width: auto
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .right .btn-toggle-dm img {
    width: 24px;
    transform: rotate(180deg)
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .right .btn-toggle-dm.on img {
    transform: rotate(0)
}

.direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .btn-open-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.direct-message-container .messenger-wrap .direct-message-wrap.on {
    transform: translateY(-100%)
}

.app-container.rtl .direct-message-container .messenger-wrap {
    left: 0;
    right: auto
}

.app-container.rtl .direct-message-container .messenger-wrap .direct-message-wrap {
    left: 40px;
    right: auto
}

.app-container.rtl .direct-message-container .messenger-wrap .direct-message-wrap .message-list-header .left .unread-count {
    margin-left: auto;
    margin-right: 4px
}

@media screen and (max-width:767px) {
    .direct-message-container {
        display: none
    }
    .direct-message-container.webview {
        display: block
    }
    .direct-message-container.webview .messenger-wrap {
        top: 0;
        bottom: auto
    }
    .direct-message-container.webview .messenger-wrap .direct-message-wrap {
        right: 0;
        max-width: none;
        max-height: none;
        border: none;
        transform: translateY(0);
        transition: none;
        box-shadow: none;
        border-top-left-radius: 0;
        border-top-right-radius: 0
    }
    .direct-message-container.webview .messenger-wrap .direct-message-wrap .message-list-header {
        justify-content: center;
        border-bottom: none;
        min-height: 52px
    }
    .app-container.rtl .direct-message-container.webview .messenger-wrap .direct-message-wrap {
        left: 0
    }
}

/*# sourceMappingURL=53.e27b3eab.chunk.css.map */