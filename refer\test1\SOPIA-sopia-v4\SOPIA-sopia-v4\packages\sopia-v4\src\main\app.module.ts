import { Module } from '@nestjs/common'
import { ELECTRON_WINDOW_DEFAULT_NAME, ElectronModule } from '@doubleshot/nest-electron'
import { BrowserWindow, shell } from 'electron'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import icon from '../../resources/icon.png?asset'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'
import { windowDevtoolStyle } from './utils'
@Module({
  imports: [
    ElectronModule.registerAsync({
      name: [ELECTRON_WINDOW_DEFAULT_NAME],
      useFactory: async () => {
        const mainWindow = new BrowserWindow({
          width: 900,
          height: 670,
          show: false,
          autoHideMenuBar: true,
          ...(process.platform === 'linux' ? { icon } : {}),
          webPreferences: {
            preload: join(__dirname, '../preload/index.js'),
            sandbox: false
          }
        })

        windowDevtoolStyle(mainWindow)

        mainWindow.on('ready-to-show', () => {
          mainWindow.show()
        })
        mainWindow.webContents.setWindowOpenHandler((details) => {
          shell.openExternal(details.url)
          return { action: 'deny' }
        })

        // HMR for renderer base on electron-vite cli.
        // Load the remote URL for development or the local html file for production.
        if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
          mainWindow.webContents.openDevTools()
          mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
        } else {
          mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
        }

        return mainWindow
      }
    })
  ],
  controllers: [AppController],
  providers: [AppService]
})
export class AppModule {}
