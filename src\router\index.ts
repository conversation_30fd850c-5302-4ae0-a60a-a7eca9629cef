import Vue from 'vue';
import VueRouter from 'vue-router';
import bundle from './bundle';
import { authService } from '@/plugins/auth-service';

Vue.use(VueRouter);

export interface RouteConfig {
	name: string;
	path: string;
	component?: any;
	icon?: string;
	children?: RouteConfig[];
	isMenu?: boolean;
	redirect?: string;
	translated?: boolean;
	requireAuth?: boolean;
}

export const routes: RouteConfig[] = [
	{
		name: 'Home',
		path: '/',
		component: () => import('@/views/Home/Index.vue'),
		icon: 'mdi-home',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'UserTab',
		path: '/usertab',
		component: () => import('@/views/UserTab/Index.vue'),
		icon: 'mdi-account-group',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'LotteryTab',
		path: '/lotterytab',
		component: () => import('@/views/LotteryTab/Index.vue'),
		icon: 'mdi-ticket',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'MusicTab',
		path: '/musictab',
		component: () => import('@/views/MusicTab/Index.vue'),
		icon: 'mdi-music-note',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'Roulette',
		path: '/roulette',
		component: () => import('@/views/Roulette/Index.vue'),
		icon: 'mdi-rotate-right',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'ImageEditor',
		path: '/imageeditor',
		component: () => import('@/views/ImageEditor/Index.vue'),
		icon: 'mdi-image-edit',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'Login',
		path: '/login',
		component: () => import('@/views/Login/Index.vue'),
	},
	{
		name: 'Auth',
		path: '/auth',
		component: () => import('@/views/Auth/AuthMain.vue'),
	},
	{
		name: 'Search',
		path: '/search/:type/:query(.*)/',
		component: () => import('@/views/Search/Index.vue'),
		requireAuth: true,
	},
	{
		name: 'User',
		path: '/user/:id/',
		component: () => import('@/views/User/Index.vue'),
		requireAuth: true,
	},
	{
		name: 'Cast',
		path: '/cast/:id/',
		component: () => import('@/views/Cast/Index.vue'),
		requireAuth: true,
	},
	{
		name: 'Command',
		path: '/cmd/:types/',
		component: () => import('@/views/Cmd/Index.vue'),
		isMenu: true,
		icon: 'mdi-powershell',
		requireAuth: true,
		children: [
			{
				name: 'Join',
				path: '/cmd/live_join/',
				component: () => import('@/views/Cmd/Join.vue'),
				icon: 'mdi-door',
				requireAuth: true,
			},
			{
				name: 'Like',
				path: '/cmd/live_like/',
				component: () => import('@/views/Cmd/Like.vue'),
				icon: 'mdi-heart',
				requireAuth: true,
			},
			{
				name: 'Present',
				path: '/cmd/live_present/',
				component: () => import('@/views/Cmd/Present.vue'),
				icon: 'mdi-gift',
				requireAuth: true,
			},
			{
				name: 'Message',
				path: '/cmd/live_message/',
				component: () => import('@/views/Cmd/Message.vue'),
				icon: 'mdi-message-alert',
				requireAuth: true,
			},
		],
	},
	bundle,
	{
		name: 'Setting',
		path: '/setting/',
		component: () => import('@/views/Setting/Index.vue'),
		icon: 'mdi-cog',
		isMenu: true,
		requireAuth: true,
	},
	{
		name: 'default',
		path: '*',
		redirect: '/',
	},
];

const router = new VueRouter({
	mode: 'hash',
	base: process.env.BASE_URL,
	routes,
});

// 네비게이션 가드 추가
router.beforeEach((to, from, next) => {
	// TAMM 인증 상태 확인
	const tammUser = authService.getCurrentUser();
	
	// 기존 Spoon 로그인 상태 확인
	const hasLogonUser = window.$sopia && window.$sopia.logonUser;
	
	// 인증이 필요한 페이지인지 확인
	const requireAuth = to.matched.some(record => record.meta?.requireAuth || (record as any).requireAuth);
	
	// 로그인 관련 페이지들
	const authPages = ['/login', '/auth'];
	const isAuthPage = authPages.includes(to.path);
	
	// 디버그 정보
	console.log('🔍 라우터 가드:', {
		to: to.path,
		tammUser: !!tammUser,
		hasLogonUser: !!hasLogonUser,
		requireAuth,
		isAuthPage
	});
	
	if (requireAuth) {
		// 인증이 필요한 페이지
		if (!tammUser) {
			// TAMM 인증이 없으면 TAMM 인증 페이지로
			console.log('❌ TAMM 인증 필요 - /auth로 리다이렉트');
			next('/auth');
		} else if (!hasLogonUser) {
			// TAMM 인증은 있지만 Spoon 로그인이 없으면 기존 로그인 페이지로
			console.log('⚠️ Spoon 로그인 필요 - /login으로 리다이렉트');
			next('/login');
		} else {
			// 모든 인증이 완료된 경우
			console.log('✅ 모든 인증 완료 - 페이지 접근 허용');
			next();
		}
	} else if (isAuthPage) {
		// 인증 페이지로 가는 경우
		if (tammUser && hasLogonUser) {
			// 모든 인증이 완료된 상태면 홈으로
			console.log('✅ 이미 모든 인증 완료 - 홈으로 리다이렉트');
			next('/');
		} else if (to.path === '/login' && hasLogonUser && !tammUser) {
			// Spoon 로그인만 있고 TAMM 인증이 없으면 TAMM 인증 페이지로
			console.log('⚠️ TAMM 인증 필요 - /auth로 리다이렉트');
			next('/auth');
		} else if (to.path === '/auth' && tammUser && !hasLogonUser) {
			// TAMM 인증만 있고 Spoon 로그인이 없으면 기존 로그인 페이지로
			console.log('⚠️ Spoon 로그인 필요 - /login으로 리다이렉트');
			next('/login');
		} else {
			console.log('🔄 인증 페이지 접근 허용');
			next();
		}
	} else {
		// 인증이 필요하지 않은 페이지
		console.log('🆓 인증 불필요 페이지 - 접근 허용');
		next();
	}
});

export default router;
