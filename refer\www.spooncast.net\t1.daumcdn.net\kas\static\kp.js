/* kakao-pixel-web@1.6.2-release 2024-08-29 14:22:22 */ ! function() {
    try {
        if (!("kakaoPixel" in self)) {
            var e = (function(e) {
                "use strict";
                var t, n, r, o = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {};

                function i(e) {
                    return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e
                }
                var a = function(e, t) {
                        var n = {
                                identity: t
                            },
                            r = e.valueOf;
                        return Object.defineProperty(e, "valueOf", {
                            value: function(e) {
                                return e !== t ? r.apply(this, arguments) : n
                            },
                            writable: !0
                        }), n
                    },
                    u = function() {
                        var e = {};
                        return function(t) {
                            if (("object" != typeof t || null === t) && "function" != typeof t) throw Error("Weakmap-shim: Key must be object");
                            var n = t.valueOf(e);
                            return n && n.identity === e ? n : a(t, e)
                        }
                    },
                    c = i(function() {
                        var e = u();
                        return {
                            get: function(t, n) {
                                var r = e(t);
                                return r.hasOwnProperty("value") ? r.value : n
                            },
                            set: function(t, n) {
                                return e(t).value = n, this
                            },
                            has: function(t) {
                                return "value" in e(t)
                            },
                            delete: function(t) {
                                return delete e(t).value
                            }
                        }
                    }),
                    s = "function" == typeof window.WeakMap ? window.WeakMap : c;

                function l(e, t, n) {
                    if (!t.has(e)) throw TypeError("attempted to " + n + " private field on non-instance");
                    return t.get(e)
                }

                function f(e, t) {
                    var n = l(e, t, "get");
                    return n.get ? n.get.call(e) : n.value
                }

                function v(e, t, n) {
                    ! function(e, t) {
                        if (t.has(e)) throw TypeError("Cannot initialize the same private elements twice on an object")
                    }(e, t), t.set(e, n)
                }

                function d(e, t, n) {
                    var r = l(e, t, "set");
                    return ! function(e, t, n) {
                        if (t.set) t.set.call(e, n);
                        else {
                            if (!t.writable) throw TypeError("attempted to set read only private field");
                            t.value = n
                        }
                    }(e, r, n), n
                }

                function p(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var h = new s,
                    y = new s,
                    b = new s,
                    m = function() {
                        var e, t;

                        function n(e) {
                            var t = e.standardEventTracker,
                                r = e.naverCheckoutEventTracker,
                                o = e.paycoCheckoutEventTracker;
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, n), v(this, h, {
                                writable: !0,
                                value: void 0
                            }), v(this, y, {
                                writable: !0,
                                value: void 0
                            }), v(this, b, {
                                writable: !0,
                                value: void 0
                            }), d(this, h, t), d(this, y, r), d(this, b, o)
                        }
                        return e = [{
                            key: "pageView",
                            value: function(e) {
                                return f(this, h).pageView(e)
                            }
                        }, {
                            key: "search",
                            value: function(e) {
                                return f(this, h).search(e)
                            }
                        }, {
                            key: "viewCart",
                            value: function(e) {
                                return f(this, h).viewCart(e)
                            }
                        }, {
                            key: "viewContent",
                            value: function(e) {
                                return f(this, h).viewContent(e)
                            }
                        }, {
                            key: "purchase",
                            value: function(e, t) {
                                return f(this, h).purchase(e, t)
                            }
                        }, {
                            key: "completeRegistration",
                            value: function(e) {
                                return f(this, h).completeRegistration(e)
                            }
                        }, {
                            key: "signUp",
                            value: function(e) {
                                return f(this, h).signUp(e)
                            }
                        }, {
                            key: "participation",
                            value: function(e) {
                                return f(this, h).participation(e)
                            }
                        }, {
                            key: "addToCart",
                            value: function(e) {
                                return f(this, h).addToCart(e)
                            }
                        }, {
                            key: "addToWishList",
                            value: function(e) {
                                return f(this, h).addToWishList(e)
                            }
                        }, {
                            key: "login",
                            value: function(e) {
                                return f(this, h).login(e)
                            }
                        }, {
                            key: "preparation",
                            value: function(e) {
                                return f(this, h).preparation(e)
                            }
                        }, {
                            key: "tutorial",
                            value: function(e) {
                                return f(this, h).tutorial(e)
                            }
                        }, {
                            key: "missionComplete",
                            value: function(e) {
                                return f(this, h).missionComplete(e)
                            }
                        }, {
                            key: "purchaseNaverCheckout",
                            value: function(e) {
                                f(this, y).purchaseNaverCheckout(e)
                            }
                        }, {
                            key: "trackNaverCheckoutWishLink",
                            value: function(e) {
                                f(this, y).trackNaverCheckoutWishLink(e)
                            }
                        }, {
                            key: "trackNaverCheckoutCartLink",
                            value: function(e) {
                                f(this, y).trackNaverCheckoutCartLink(e)
                            }
                        }, {
                            key: "trackNaverCheckoutTalkLink",
                            value: function(e) {
                                f(this, y).trackNaverCheckoutTalkLink(e)
                            }
                        }, {
                            key: "trackNaverCheckoutPromotionLink",
                            value: function(e) {
                                f(this, y).trackNaverCheckoutPromotionLink(e)
                            }
                        }, {
                            key: "purchasePaycoCheckout",
                            value: function(e) {
                                f(this, b).purchasePaycoCheckout(e)
                            }
                        }, {
                            key: "trackPaycoPromotionCheckoutButton",
                            value: function(e) {
                                f(this, b).trackPaycoPromotionCheckoutButton(e)
                            }
                        }], p(n.prototype, e), t && p(n, t), n
                    }();

                function g(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var k = {
                        kakaoAccountId: "KAKAO_PIXEL_ACCID",
                        encryptedKakaoAccountId: "KAKAO_PIXEL_EACID",
                        idfv: "KAKAO_PIXEL_IDFV",
                        serviceOrigin: "KAKAO_PIXEL_SERVICE_ORIGIN",
                        adId: "KAKAO_PIXEL_ADID",
                        limitAdTrackingEnabled: "KAKAO_PIXEL_ADID_LMT"
                    },
                    w = function() {
                        var e, t;

                        function n(e) {
                            var t, r;
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, n), t = "storage", r = void 0, t in this ? Object.defineProperty(this, t, {
                                value: r,
                                enumerable: !0,
                                configurable: !0,
                                writable: !0
                            }) : this[t] = r, this.storage = e
                        }
                        return e = [{
                            key: "setKakaoAccountId",
                            value: function(e) {
                                return this.storage.setItem(k.kakaoAccountId, e), this
                            }
                        }, {
                            key: "getKakaoAccountId",
                            value: function() {
                                return this.storage.getItem(k.kakaoAccountId)
                            }
                        }, {
                            key: "setEncryptedKakaoAccountId",
                            value: function(e) {
                                return this.storage.setItem(k.encryptedKakaoAccountId, e), this
                            }
                        }, {
                            key: "getEncryptedKakaoAccountId",
                            value: function() {
                                return this.storage.getItem(k.encryptedKakaoAccountId)
                            }
                        }, {
                            key: "setIDFV",
                            value: function(e) {
                                return this.storage.setItem(k.idfv, e), this
                            }
                        }, {
                            key: "getIDFV",
                            value: function() {
                                return this.storage.getItem(k.idfv)
                            }
                        }, {
                            key: "setServiceOrigin",
                            value: function(e) {
                                return this.storage.setItem(k.serviceOrigin, e), this
                            }
                        }, {
                            key: "getServiceOrigin",
                            value: function() {
                                return this.storage.getItem(k.serviceOrigin)
                            }
                        }, {
                            key: "hasServiceOrigin",
                            value: function() {
                                var e = this.getServiceOrigin();
                                return null !== e && e.trim().length > 0
                            }
                        }, {
                            key: "setAdId",
                            value: function(e) {
                                return this.storage.setItem(k.adId, e), this
                            }
                        }, {
                            key: "getAdId",
                            value: function() {
                                return this.storage.getItem(k.adId)
                            }
                        }, {
                            key: "setLimitAdTrackingEnabled",
                            value: function(e) {
                                return this.storage.setItem(k.limitAdTrackingEnabled, e ? "Y" : "N"), this
                            }
                        }, {
                            key: "isLimitAdTrackingEnabled",
                            value: function() {
                                var e = this.storage.getItem(k.limitAdTrackingEnabled);
                                return "Y" === e
                            }
                        }], g(n.prototype, e), t && g(n, t), n
                    }();

                function O(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var S = function() {
                    var e, t;

                    function n() {
                        var e, t;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), e = "storedItems", t = {}, e in this ? Object.defineProperty(this, e, {
                            value: t,
                            enumerable: !0,
                            configurable: !0,
                            writable: !0
                        }) : this[e] = t
                    }
                    return e = [{
                        key: "length",
                        get: function() {
                            return Object.keys(this.storedItems).length
                        }
                    }, {
                        key: "clear",
                        value: function() {
                            this.storedItems = {}
                        }
                    }, {
                        key: "getItem",
                        value: function(e) {
                            var t;
                            return null !== (t = this.storedItems[e]) && void 0 !== t ? t : null
                        }
                    }, {
                        key: "setItem",
                        value: function(e, t) {
                            this.storedItems[e] = t
                        }
                    }, {
                        key: "removeItem",
                        value: function(e) {
                            delete this.storedItems[e]
                        }
                    }, {
                        key: "key",
                        value: function(e) {
                            return Object.keys(this.storedItems)[e]
                        }
                    }], O(n.prototype, e), t && O(n, t), n
                }();

                function C(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function P(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }
                var I = "adfit.storage.test",
                    A = function() {
                        var e, t;

                        function n(e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : function() {
                                return n.defaultStorage
                            };
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, n), P(this, "innerStorage", void 0);
                            try {
                                var r = e(),
                                    o = r.getItem(I);
                                r.removeItem(I), r.setItem(I, null != o ? o : "");
                                var i = r.length;
                                r.key(i - 1), null === o && r.removeItem(I), this.innerStorage = r
                            } catch (e) {
                                this.innerStorage = t()
                            }
                        }
                        return e = [{
                            key: "length",
                            get: function() {
                                return this.innerStorage.length
                            }
                        }, {
                            key: "clear",
                            value: function() {
                                this.innerStorage.clear()
                            }
                        }, {
                            key: "getItem",
                            value: function(e) {
                                return this.innerStorage.getItem(e)
                            }
                        }, {
                            key: "setItem",
                            value: function(e, t) {
                                this.innerStorage.setItem(e, t)
                            }
                        }, {
                            key: "removeItem",
                            value: function(e) {
                                this.innerStorage.removeItem(e)
                            }
                        }, {
                            key: "key",
                            value: function(e) {
                                return this.innerStorage.key(e)
                            }
                        }], C(n.prototype, e), t && C(n, t), n
                    }();
                P(A, "defaultStorage", new S);
                var T = {
                    exports: {}
                };
                T.exports = function() {
                    function e(e) {
                        return "function" == typeof e
                    }
                    var t, n, r, i, a = Array.isArray ? Array.isArray : function(e) {
                            return "[object Array]" === Object.prototype.toString.call(e)
                        },
                        u = 0,
                        c = void 0,
                        s = void 0,
                        l = function(e, t) {
                            b[u] = e, b[u + 1] = t, u += 2, 2 === u && (s ? s(m) : g())
                        },
                        f = "undefined" != typeof window ? window : void 0,
                        v = f || {},
                        d = v.MutationObserver || v.WebKitMutationObserver,
                        p = "undefined" == typeof self && "undefined" != typeof process && "[object process]" === ({}).toString.call(process),
                        h = "undefined" != typeof Uint8ClampedArray && "undefined" != typeof importScripts && "undefined" != typeof MessageChannel;

                    function y() {
                        var e = setTimeout;
                        return function() {
                            return e(m, 1)
                        }
                    }
                    var b = Array(1e3);

                    function m() {
                        for (var e = 0; e < u; e += 2) {
                            var t = b[e],
                                n = b[e + 1];
                            t(n), b[e] = void 0, b[e + 1] = void 0
                        }
                        u = 0
                    }
                    var g = void 0;

                    function k(e, t) {
                        var n = this,
                            r = new this.constructor(S);
                        void 0 === r[O] && L(r);
                        var o = n._state;
                        if (o) {
                            var i = arguments[o - 1];
                            l(function() {
                                return _(o, r, i, n._result)
                            })
                        } else E(n, r, e, t);
                        return r
                    }

                    function w(e) {
                        if (e && "object" == typeof e && e.constructor === this) return e;
                        var t = new this(S);
                        return I(t, e), t
                    }
                    p ? g = function() {
                        return process.nextTick(m)
                    } : d ? (t = 0, n = new d(m), r = document.createTextNode(""), n.observe(r, {
                        characterData: !0
                    }), g = function() {
                        r.data = t = ++t % 2
                    }) : h ? ((i = new MessageChannel).port1.onmessage = m, g = function() {
                        return i.port2.postMessage(0)
                    }) : g = void 0 === f ? function() {
                        try {
                            var e = Function("return this")().require("vertx");
                            return c = e.runOnLoop || e.runOnContext, void 0 !== c ? function() {
                                c(m)
                            } : y()
                        } catch (e) {
                            return y()
                        }
                    }() : y();
                    var O = Math.random().toString(36).substring(2);

                    function S() {}
                    var C = void 0;

                    function P(t, n, r) {
                        n.constructor === t.constructor && r === k && n.constructor.resolve === w ? 1 === n._state ? T(t, n._result) : 2 === n._state ? x(t, n._result) : E(n, void 0, function(e) {
                            return I(t, e)
                        }, function(e) {
                            return x(t, e)
                        }) : void 0 === r ? T(t, n) : e(r) ? l(function(e) {
                            var t = !1,
                                o = function(e, t, n, r) {
                                    try {
                                        e.call(t, n, r)
                                    } catch (e) {
                                        return e
                                    }
                                }(r, n, function(r) {
                                    t || (t = !0, n !== r ? I(e, r) : T(e, r))
                                }, function(n) {
                                    t || (t = !0, x(e, n))
                                }, "Settle: " + (e._label || " unknown promise"));
                            !t && o && (t = !0, x(e, o))
                        }, t) : T(t, n)
                    }

                    function I(e, t) {
                        if (e === t) x(e, TypeError("You cannot resolve a promise with itself"));
                        else if (n = typeof t, null !== t && ("object" === n || "function" === n)) {
                            var n, r = void 0;
                            try {
                                r = t.then
                            } catch (t) {
                                x(e, t);
                                return
                            }
                            P(e, t, r)
                        } else T(e, t)
                    }

                    function A(e) {
                        e._onerror && e._onerror(e._result), j(e)
                    }

                    function T(e, t) {
                        e._state === C && (e._result = t, e._state = 1, 0 !== e._subscribers.length && l(j, e))
                    }

                    function x(e, t) {
                        e._state === C && (e._state = 2, e._result = t, l(A, e))
                    }

                    function E(e, t, n, r) {
                        var o = e._subscribers,
                            i = o.length;
                        e._onerror = null, o[i] = t, o[i + 1] = n, o[i + 2] = r, 0 === i && e._state && l(j, e)
                    }

                    function j(e) {
                        var t = e._subscribers,
                            n = e._state;
                        if (0 !== t.length) {
                            for (var r = void 0, o = void 0, i = e._result, a = 0; a < t.length; a += 3) r = t[a], o = t[a + n], r ? _(n, r, o, i) : o(i);
                            e._subscribers.length = 0
                        }
                    }

                    function _(t, n, r, o) {
                        var i = e(r),
                            a = void 0,
                            u = void 0,
                            c = !0;
                        if (i) {
                            try {
                                a = r(o)
                            } catch (e) {
                                c = !1, u = e
                            }
                            if (n === a) {
                                x(n, TypeError("A promises callback cannot return that same promise."));
                                return
                            }
                        } else a = o;
                        n._state !== C || (i && c ? I(n, a) : !1 === c ? x(n, u) : 1 === t ? T(n, a) : 2 === t && x(n, a))
                    }
                    var D = 0;

                    function L(e) {
                        e[O] = D++, e._state = void 0, e._result = void 0, e._subscribers = []
                    }
                    var R = function() {
                            function e(e, t) {
                                this._instanceConstructor = e, this.promise = new e(S), this.promise[O] || L(this.promise), a(t) ? (this.length = t.length, this._remaining = t.length, this._result = Array(this.length), 0 === this.length ? T(this.promise, this._result) : (this.length = this.length || 0, this._enumerate(t), 0 === this._remaining && T(this.promise, this._result))) : x(this.promise, Error("Array Methods must be provided an Array"))
                            }
                            return e.prototype._enumerate = function(e) {
                                for (var t = 0; this._state === C && t < e.length; t++) this._eachEntry(e[t], t)
                            }, e.prototype._eachEntry = function(e, t) {
                                var n = this._instanceConstructor,
                                    r = n.resolve;
                                if (r === w) {
                                    var o = void 0,
                                        i = void 0,
                                        a = !1;
                                    try {
                                        o = e.then
                                    } catch (e) {
                                        a = !0, i = e
                                    }
                                    if (o === k && e._state !== C) this._settledAt(e._state, t, e._result);
                                    else if ("function" != typeof o) this._remaining--, this._result[t] = e;
                                    else if (n === M) {
                                        var u = new n(S);
                                        a ? x(u, i) : P(u, e, o), this._willSettleAt(u, t)
                                    } else this._willSettleAt(new n(function(t) {
                                        return t(e)
                                    }), t)
                                } else this._willSettleAt(r(e), t)
                            }, e.prototype._settledAt = function(e, t, n) {
                                var r = this.promise;
                                r._state === C && (this._remaining--, 2 === e ? x(r, n) : this._result[t] = n), 0 === this._remaining && T(r, this._result)
                            }, e.prototype._willSettleAt = function(e, t) {
                                var n = this;
                                E(e, void 0, function(e) {
                                    return n._settledAt(1, t, e)
                                }, function(e) {
                                    return n._settledAt(2, t, e)
                                })
                            }, e
                        }(),
                        M = function() {
                            function t(e) {
                                this[O] = D++, this._result = this._state = void 0, this._subscribers = [], S !== e && ("function" != typeof e && function() {
                                    throw TypeError("You must pass a resolver function as the first argument to the promise constructor")
                                }(), this instanceof t ? function(e, t) {
                                    try {
                                        t(function(t) {
                                            I(e, t)
                                        }, function(t) {
                                            x(e, t)
                                        })
                                    } catch (t) {
                                        x(e, t)
                                    }
                                }(this, e) : function() {
                                    throw TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")
                                }())
                            }
                            return t.prototype.catch = function(e) {
                                return this.then(null, e)
                            }, t.prototype.finally = function(t) {
                                var n = this.constructor;
                                return e(t) ? this.then(function(e) {
                                    return n.resolve(t()).then(function() {
                                        return e
                                    })
                                }, function(e) {
                                    return n.resolve(t()).then(function() {
                                        throw e
                                    })
                                }) : this.then(t, t)
                            }, t
                        }();
                    return M.prototype.then = k, M.all = function(e) {
                        return new R(this, e).promise
                    }, M.race = function(e) {
                        var t = this;
                        return new t(a(e) ? function(n, r) {
                            for (var o = e.length, i = 0; i < o; i++) t.resolve(e[i]).then(n, r)
                        } : function(e, t) {
                            return t(TypeError("You must pass an array to race."))
                        })
                    }, M.resolve = w, M.reject = function(e) {
                        var t = new this(S);
                        return x(t, e), t
                    }, M._setScheduler = function(e) {
                        s = e
                    }, M._setAsap = function(e) {
                        l = e
                    }, M._asap = l, M.polyfill = function() {
                        var e = void 0;
                        if (void 0 !== o) e = o;
                        else if ("undefined" != typeof self) e = self;
                        else try {
                            e = Function("return this")()
                        } catch (e) {
                            throw Error("polyfill failed because global object is unavailable in this environment")
                        }
                        var t = e.Promise;
                        if (t) {
                            var n = null;
                            try {
                                n = Object.prototype.toString.call(t.resolve())
                            } catch (e) {}
                            if ("[object Promise]" === n && !t.cast) return
                        }
                        e.Promise = M
                    }, M.Promise = M, M
                }();
                var x = T.exports,
                    E = ["AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AUD", "AWG", "AZN", "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BOV", "BRL", "BSD", "BTN", "BWP", "BYR", "BZD", "CAD", "CDF", "CHE", "CHF", "CHW", "CLF", "CLP", "CNY", "COP", "COU", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF", "DKK", "DOP", "DZD", "EGP", "ERN", "ETB", "EUR", "FJD", "FKP", "GBP", "GEL", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL", "HRK", "HTG", "HUF", "IDR", "ILS", "INR", "IQD", "IRR", "ISK", "JMD", "JOD", "JPY", "KES", "KGS", "KHR", "KMF", "KPW", "KRW", "KWD", "KYD", "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LTL", "LVL", "LYD", "MAD", "MDL", "MGA", "MKD", "MMK", "MNT", "MOP", "MRO", "MUR", "MVR", "MWK", "MXN", "MXV", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "NZD", "OMR", "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD", "RUB", "RWF", "SAR", "SBD", "SCR", "SDG", "SEK", "SGD", "SHP", "SLL", "SOS", "SRD", "SSP", "STD", "SYP", "SZL", "THB", "TJS", "TMT", "TND", "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX", "USD", "USN", "USS", "UYI", "UYU", "UZS", "VEF", "VND", "VUV", "WST", "XAF", "XAG", "XAU", "XBA", "XBB", "XBC", "XBD", "XCD", "XDR", "XOF", "XPD", "XPF", "XPT", "XTS", "XXX", "YER", "ZAR", "ZMW"];

                function j(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function _(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function D(e) {
                    return function(e) {
                        if (Array.isArray(e)) return j(e)
                    }(e) || function(e) {
                        if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
                    }(e) || function(e, t) {
                        if (e) {
                            if ("string" == typeof e) return j(e, t);
                            var n = Object.prototype.toString.call(e).slice(8, -1);
                            if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                            if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return j(e, t)
                        }
                    }(e) || function() {
                        throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                    }()
                }
                var L = function() {
                    var e, t;

                    function n() {
                        var e, t;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), e = "listeners", t = {}, e in this ? Object.defineProperty(this, e, {
                            value: t,
                            enumerable: !0,
                            configurable: !0,
                            writable: !0
                        }) : this[e] = t
                    }
                    return e = [{
                        key: "addListener",
                        value: function(e, t) {
                            var n = this;
                            if (e in this.listeners) {
                                var r = this.listeners[e];
                                "function" == typeof r ? r !== t && (this.listeners[e] = [r, t]) : 0 > r.indexOf(t) && r.push(t)
                            } else this.listeners[e] = t;
                            return function() {
                                return n.removeListener(e, t)
                            }
                        }
                    }, {
                        key: "removeListener",
                        value: function(e, t) {
                            if (e in this.listeners) {
                                var n = this.listeners[e];
                                if ("function" == typeof n) n === t && delete this.listeners[e];
                                else {
                                    var r = n.indexOf(t);
                                    r >= 0 && n.splice(r, 1)
                                }
                            }
                        }
                    }, {
                        key: "removeAllListeners",
                        value: function(e) {
                            var t = this;
                            e ? delete this.listeners[e] : Object.keys(this.listeners).forEach(function(e) {
                                delete t.listeners[e]
                            })
                        }
                    }, {
                        key: "emit",
                        value: function(e) {
                            for (var t = arguments.length, n = Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
                            var o = this;
                            if (e in this.listeners) {
                                var i, a = this.listeners[e];
                                if ("function" == typeof a) a.call.apply(a, [this].concat(D(n)));
                                else if (a.forEach(function(e) {
                                        try {
                                            e.call.apply(e, [o].concat(D(n)))
                                        } catch (e) {
                                            i = e
                                        }
                                    }), i) throw i
                            }
                        }
                    }], _(n.prototype, e), t && _(n, t), n
                }();

                function R(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function M() {
                    for (var e = arguments.length, t = Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                    try {
                        (window.console.warn || window.console.log).apply(this, function(e) {
                            if (Array.isArray(e)) return R(e)
                        }(t) || function(e) {
                            if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
                        }(t) || function(e, t) {
                            if (e) {
                                if ("string" == typeof e) return R(e, t);
                                var n = Object.prototype.toString.call(e).slice(8, -1);
                                if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                                if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return R(e, t)
                            }
                        }(t) || function() {
                            throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                        }())
                    } catch (e) {}
                }

                function N(e) {
                    if (void 0 === e) throw ReferenceError("this hasn't been initialised - super() hasn't been called");
                    return e
                }

                function K(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }

                function F(e) {
                    return function() {
                        var t = this,
                            n = arguments;
                        return new x.Promise(function(r, o) {
                            var i = e.apply(t, n);

                            function a(e) {
                                K(i, r, o, a, u, "next", e)
                            }

                            function u(e) {
                                K(i, r, o, a, u, "throw", e)
                            }
                            a(void 0)
                        })
                    }
                }

                function B(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function U(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }

                function V(e) {
                    return (V = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                        return e.__proto__ || Object.getPrototypeOf(e)
                    })(e)
                }

                function H(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = null != arguments[t] ? arguments[t] : {},
                            r = Object.keys(n);
                        "function" == typeof Object.getOwnPropertySymbols && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) {
                            return Object.getOwnPropertyDescriptor(n, e).enumerable
                        }))), r.forEach(function(t) {
                            U(e, t, n[t])
                        })
                    }
                    return e
                }

                function X(e, t) {
                    return (X = Object.setPrototypeOf || function(e, t) {
                        return e.__proto__ = t, e
                    })(e, t)
                }

                function G(e, t) {
                    var n, r, o, i, a = {
                        label: 0,
                        sent: function() {
                            if (1 & o[0]) throw o[1];
                            return o[1]
                        },
                        trys: [],
                        ops: []
                    };
                    return i = {
                        next: u(0),
                        throw: u(1),
                        return: u(2)
                    }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                        return this
                    }), i;

                    function u(i) {
                        return function(u) {
                            return function(i) {
                                if (n) throw TypeError("Generator is already executing.");
                                for (; a;) try {
                                    if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                    switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                        case 0:
                                        case 1:
                                            o = i;
                                            break;
                                        case 4:
                                            return a.label++, {
                                                value: i[1],
                                                done: !1
                                            };
                                        case 5:
                                            a.label++, r = i[1], i = [0];
                                            continue;
                                        case 7:
                                            i = a.ops.pop(), a.trys.pop();
                                            continue;
                                        default:
                                            if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                a = 0;
                                                continue
                                            }
                                            if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                a.label = i[1];
                                                break
                                            }
                                            if (6 === i[0] && a.label < o[1]) {
                                                a.label = o[1], o = i;
                                                break
                                            }
                                            if (o && a.label < o[2]) {
                                                a.label = o[2], a.ops.push(i);
                                                break
                                            }
                                            o[2] && a.ops.pop(), a.trys.pop();
                                            continue
                                    }
                                    i = t.call(e, a)
                                } catch (e) {
                                    i = [6, e], r = 0
                                } finally {
                                    n = o = 0
                                }
                                if (5 & i[0]) throw i[1];
                                return {
                                    value: i[0] ? i[1] : void 0,
                                    done: !0
                                }
                            }([i, u])
                        }
                    }
                }
                var W = function(e) {
                    ! function(e, t) {
                        if ("function" != typeof t && null !== t) throw TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, {
                            constructor: {
                                value: e,
                                writable: !0,
                                configurable: !0
                            }
                        }), t && X(e, t)
                    }(i, e);
                    var t, n, r, o = (t = function() {
                        if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham) return !1;
                        if ("function" == typeof Proxy) return !0;
                        try {
                            return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {})), !0
                        } catch (e) {
                            return !1
                        }
                    }(), function() {
                        var e, n, r = V(i);
                        if (t) {
                            var o = V(this).constructor;
                            n = Reflect.construct(r, arguments, o)
                        } else n = r.apply(this, arguments);
                        return (e = n) && ("object" == (e && "undefined" != typeof Symbol && e.constructor === Symbol ? "symbol" : typeof e) || "function" == typeof e) ? e : N(this)
                    });

                    function i(e) {
                        var t;
                        return ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, i), t = o.call(this), U(N(t), "eventTracker", void 0), t.eventTracker = e, t
                    }
                    return n = [{
                        key: "pageView",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("PageView", e), [4, t.eventTracker.track("PageView", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "search",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            if (t.emitCall("Search", e), "object" != typeof e) return [3, 2];
                                            return [4, t.eventTracker.track("Search", {
                                                tag: e.tag || void 0,
                                                search_string: e.keyword || void 0
                                            })];
                                        case 1:
                                        case 3:
                                            return n.sent(), [2];
                                        case 2:
                                            return [4, t.eventTracker.track("Search", {
                                                tag: e || void 0
                                            })]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "viewCart",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("ViewCart", e), [4, t.eventTracker.track("ViewCart", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "viewContent",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            if (t.emitCall("ViewContent", e), "object" != typeof e) return [3, 2];
                                            return [4, t.eventTracker.track("ViewContent", {
                                                tag: e.tag || void 0,
                                                content_id: e.id || void 0
                                            })];
                                        case 1:
                                        case 3:
                                            return n.sent(), [2];
                                        case 2:
                                            return [4, t.eventTracker.track("ViewContent", {
                                                tag: e || void 0
                                            })]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "purchase",
                        value: function(e, t) {
                            var n = this;
                            return F(function() {
                                var r, o, i;
                                return G(this, function(a) {
                                    switch (a.label) {
                                        case 0:
                                            if (n.emitCall("Purchase", e, t), "object" != typeof e) return [3, 2];
                                            if (r = H({}, e), void 0 === r.currency) r.currency = "KRW";
                                            else {
                                                var u;
                                                r.currency && (u = r.currency, !(E.indexOf(u.toUpperCase()) > -1)) && M("Invalid currency: ".concat(r.currency))
                                            }
                                            if (r.products)
                                                for (o = 0; o < r.products.length; ++o)(i = r.products[o]).id && "string" != typeof i.id && (i.id = String(i.id));
                                            return r.tag = t || e.tag || void 0, [4, n.eventTracker.track("Purchase", r)];
                                        case 1:
                                        case 3:
                                            return a.sent(), [2];
                                        case 2:
                                            return [4, n.eventTracker.track("Purchase", {
                                                tag: t || e || void 0
                                            })]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "completeRegistration",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("CompleteRegistration", e), [4, t.eventTracker.track("CompleteRegistration", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "signUp",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("SignUp", e), [4, t.eventTracker.track("SignUp", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "participation",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("Participation", e), [4, t.eventTracker.track("Participation", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "addToCart",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            if (t.emitCall("AddToCart", e), "object" != typeof e) return [3, 2];
                                            return [4, t.eventTracker.track("AddToCart", {
                                                tag: e.tag || void 0,
                                                content_id: e.id || void 0
                                            })];
                                        case 1:
                                        case 3:
                                            return n.sent(), [2];
                                        case 2:
                                            return [4, t.eventTracker.track("AddToCart", {
                                                tag: e || void 0
                                            })]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "addToWishList",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            if (t.emitCall("AddToWishList", e), "object" != typeof e) return [3, 2];
                                            return [4, t.eventTracker.track("AddToWishList", {
                                                tag: e.tag || void 0,
                                                content_id: e.id || void 0
                                            })];
                                        case 1:
                                        case 3:
                                            return n.sent(), [2];
                                        case 2:
                                            return [4, t.eventTracker.track("AddToWishList", {
                                                tag: e || void 0
                                            })]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "login",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("Login", e), [4, t.eventTracker.track("Login", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "preparation",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("Preparation", e), [4, t.eventTracker.track("Preparation", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "tutorial",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("Tutorial", e), [4, t.eventTracker.track("Tutorial", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "missionComplete",
                        value: function(e) {
                            var t = this;
                            return F(function() {
                                return G(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t.emitCall("MissionComplete", e), [4, t.eventTracker.track("MissionComplete", {
                                                tag: e || void 0
                                            })];
                                        case 1:
                                            return n.sent(), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "emitCall",
                        value: function(e, t, n) {
                            try {
                                if ("object" == typeof t) {
                                    var r, o;
                                    this.emit("eventCall", e, (r = H({}, t), o = {
                                        tag: n || t.tag || void 0
                                    }, o = null != o ? o : {}, Object.getOwnPropertyDescriptors ? Object.defineProperties(r, Object.getOwnPropertyDescriptors(o)) : (function(e, t) {
                                        var n = Object.keys(e);
                                        if (Object.getOwnPropertySymbols) {
                                            var r = Object.getOwnPropertySymbols(e);
                                            n.push.apply(n, r)
                                        }
                                        return n
                                    })(Object(o)).forEach(function(e) {
                                        Object.defineProperty(r, e, Object.getOwnPropertyDescriptor(o, e))
                                    }), r))
                                } else this.emit("eventCall", e, {
                                    tag: n || t || void 0
                                })
                            } catch (e) {}
                        }
                    }], B(i.prototype, n), r && B(i, r), i
                }(L);

                function Y(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function q(e) {
                    for (var t = {}, n = e.replace(/^.*\?/, "").split("&"), r = 0; r < n.length; ++r) {
                        var o = n[r];
                        try {
                            var i, a = (i = o.split("=").map(decodeURIComponent), function(e) {
                                    if (Array.isArray(e)) return e
                                }(i) || function(e, t) {
                                    var n, r, o = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                                    if (null != o) {
                                        var i = [],
                                            a = !0,
                                            u = !1;
                                        try {
                                            for (o = o.call(e); !(a = (n = o.next()).done) && (i.push(n.value), !t || i.length !== t); a = !0);
                                        } catch (e) {
                                            u = !0, r = e
                                        } finally {
                                            try {
                                                a || null == o.return || o.return()
                                            } finally {
                                                if (u) throw r
                                            }
                                        }
                                        return i
                                    }
                                }(i, 2) || function(e, t) {
                                    if (e) {
                                        if ("string" == typeof e) return Y(e, t);
                                        var n = Object.prototype.toString.call(e).slice(8, -1);
                                        if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                                        if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return Y(e, t)
                                    }
                                }(i, 2) || function() {
                                    throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                }()),
                                u = a[0],
                                c = a[1];
                            c && (t[u] = c)
                        } catch (e) {}
                    }
                    return t
                }

                function J(e, t) {
                    for (var n = [], r = 0; r < t.length; r++) e(t[r], r, t) && n.push(t[r]);
                    return n
                }
                var $ = /^\?|&$/g;

                function Z(e) {
                    return J(function(t) {
                        return null !== e[t] && void 0 !== e[t]
                    }, Object.keys(e)).map(function(t) {
                        return "".concat(encodeURIComponent(t), "=").concat(encodeURIComponent(String(e[t])))
                    }).join("&")
                }

                function z(e, t) {
                    var n = !(arguments.length > 2) || void 0 === arguments[2] || arguments[2];
                    if (!t) return e;
                    var r = document.createElement("a");
                    r.href = e;
                    var o = 0 === r.pathname.indexOf("/") ? r.pathname : "/".concat(r.pathname),
                        i = "".concat(r.protocol, "//").concat(r.host).concat(o);
                    if (n) {
                        var a = Z(function(e) {
                            for (var t = 1; t < arguments.length; t++) {
                                var n = null != arguments[t] ? arguments[t] : {},
                                    r = Object.keys(n);
                                "function" == typeof Object.getOwnPropertySymbols && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) {
                                    return Object.getOwnPropertyDescriptor(n, e).enumerable
                                }))), r.forEach(function(t) {
                                    var r;
                                    r = n[t], t in e ? Object.defineProperty(e, t, {
                                        value: r,
                                        enumerable: !0,
                                        configurable: !0,
                                        writable: !0
                                    }) : e[t] = r
                                })
                            }
                            return e
                        }({}, q(r.search), t));
                        return "".concat(i, "?").concat(a)
                    }
                    var u = Z(t),
                        c = r.search.replace($, "");
                    return c ? "".concat(i, "?").concat(c, "&").concat(u) : "".concat(i, "?").concat(u)
                }
                var Q = window.fetch || function(e, t) {
                    return t = t || {}, new x.Promise(function(n, r) {
                        var o = new XMLHttpRequest,
                            i = [],
                            a = [],
                            u = {},
                            c = function() {
                                return {
                                    ok: 2 == (o.status / 100 | 0),
                                    statusText: o.statusText,
                                    status: o.status,
                                    url: o.responseURL,
                                    text: function() {
                                        return x.Promise.resolve(o.responseText)
                                    },
                                    json: function() {
                                        return x.Promise.resolve(o.responseText).then(JSON.parse)
                                    },
                                    blob: function() {
                                        return x.Promise.resolve(new Blob([o.response]))
                                    },
                                    clone: c,
                                    headers: {
                                        keys: function() {
                                            return i
                                        },
                                        entries: function() {
                                            return a
                                        },
                                        get: function(e) {
                                            return u[e.toLowerCase()]
                                        },
                                        has: function(e) {
                                            return e.toLowerCase() in u
                                        }
                                    }
                                }
                            };
                        for (var s in o.open(t.method || "get", e, !0), o.onload = function() {
                                o.getAllResponseHeaders().replace(/^(.*?):[^\S\n]*([\s\S]*?)$/gm, function(e, t, n) {
                                    i.push(t = t.toLowerCase()), a.push([t, n]), u[t] = u[t] ? u[t] + "," + n : n
                                }), n(c())
                            }, o.onerror = r, o.withCredentials = "include" == t.credentials, t.headers) o.setRequestHeader(s, t.headers[s]);
                        o.send(t.body || null)
                    })
                };

                function ee(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }
                var et = "https://acid-api.ds.kakao.com/acid";

                function en() {
                    var e;
                    return e = function(e) {
                        var t, n, r, o, i, a, u, c, s;
                        return function(e, t) {
                            var n, r, o, i, a = {
                                label: 0,
                                sent: function() {
                                    if (1 & o[0]) throw o[1];
                                    return o[1]
                                },
                                trys: [],
                                ops: []
                            };
                            return i = {
                                next: u(0),
                                throw: u(1),
                                return: u(2)
                            }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                return this
                            }), i;

                            function u(i) {
                                return function(u) {
                                    return function(i) {
                                        if (n) throw TypeError("Generator is already executing.");
                                        for (; a;) try {
                                            if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                            switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                                case 0:
                                                case 1:
                                                    o = i;
                                                    break;
                                                case 4:
                                                    return a.label++, {
                                                        value: i[1],
                                                        done: !1
                                                    };
                                                case 5:
                                                    a.label++, r = i[1], i = [0];
                                                    continue;
                                                case 7:
                                                    i = a.ops.pop(), a.trys.pop();
                                                    continue;
                                                default:
                                                    if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                        a = 0;
                                                        continue
                                                    }
                                                    if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                        a.label = i[1];
                                                        break
                                                    }
                                                    if (6 === i[0] && a.label < o[1]) {
                                                        a.label = o[1], o = i;
                                                        break
                                                    }
                                                    if (o && a.label < o[2]) {
                                                        a.label = o[2], a.ops.push(i);
                                                        break
                                                    }
                                                    o[2] && a.ops.pop(), a.trys.pop();
                                                    continue
                                            }
                                            i = t.call(e, a)
                                        } catch (e) {
                                            i = [6, e], r = 0
                                        } finally {
                                            n = o = 0
                                        }
                                        if (5 & i[0]) throw i[1];
                                        return {
                                            value: i[0] ? i[1] : void 0,
                                            done: !0
                                        }
                                    }([i, u])
                                }
                            }
                        }(this, function(l) {
                            switch (l.label) {
                                case 0:
                                    if (t = e.appId, n = e.appKey, r = e.appUserId, o = e.accessToken, !t && !n || !r && !o) return [2, null];
                                    i = r ? {
                                        appUserId: r
                                    } : {
                                        accessToken: o
                                    }, a = n ? "".concat(et, "/appKey/").concat(n) : "".concat(et, "/appId/").concat(t), l.label = 1;
                                case 1:
                                    return l.trys.push([1, 4, , 5]), [4, Q(z(a, i), {
                                        credentials: "include"
                                    })];
                                case 2:
                                    return u = l.sent(), [4, u.json()];
                                case 3:
                                    return c = l.sent().eacid, s = void 0 === c ? null : c, [2, s];
                                case 4:
                                    return l.sent(), [3, 5];
                                case 5:
                                    return [2, null]
                            }
                        })
                    }, (en = function() {
                        var t = this,
                            n = arguments;
                        return new x.Promise(function(r, o) {
                            var i = e.apply(t, n);

                            function a(e) {
                                ee(i, r, o, a, u, "next", e)
                            }

                            function u(e) {
                                ee(i, r, o, a, u, "throw", e)
                            }
                            a(void 0)
                        })
                    }).apply(this, arguments)
                }

                function er(e) {
                    var t, n = !1;
                    return function() {
                        for (var r = arguments.length, o = Array(r), i = 0; i < r; i++) o[i] = arguments[i];
                        return n || (n = !0, t = e.apply(this, o)), t
                    }
                }

                function eo(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function ei(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }

                function ea(e) {
                    return function(e) {
                        if (Array.isArray(e)) return eo(e)
                    }(e) || function(e) {
                        if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
                    }(e) || eu(e) || function() {
                        throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                    }()
                }

                function eu(e, t) {
                    if (e) {
                        if ("string" == typeof e) return eo(e, t);
                        var n = Object.prototype.toString.call(e).slice(8, -1);
                        if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                        if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return eo(e, t)
                    }
                }

                function ec() {
                    var e;
                    return e = function(e, t) {
                        var n, r, o, i, a, u, c, s, l, f, v, d, p, h, y, b, m, g, k, w, O, S, C;
                        return function(e, t) {
                            var n, r, o, i, a = {
                                label: 0,
                                sent: function() {
                                    if (1 & o[0]) throw o[1];
                                    return o[1]
                                },
                                trys: [],
                                ops: []
                            };
                            return i = {
                                next: u(0),
                                throw: u(1),
                                return: u(2)
                            }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                return this
                            }), i;

                            function u(i) {
                                return function(u) {
                                    return function(i) {
                                        if (n) throw TypeError("Generator is already executing.");
                                        for (; a;) try {
                                            if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                            switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                                case 0:
                                                case 1:
                                                    o = i;
                                                    break;
                                                case 4:
                                                    return a.label++, {
                                                        value: i[1],
                                                        done: !1
                                                    };
                                                case 5:
                                                    a.label++, r = i[1], i = [0];
                                                    continue;
                                                case 7:
                                                    i = a.ops.pop(), a.trys.pop();
                                                    continue;
                                                default:
                                                    if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                        a = 0;
                                                        continue
                                                    }
                                                    if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                        a.label = i[1];
                                                        break
                                                    }
                                                    if (6 === i[0] && a.label < o[1]) {
                                                        a.label = o[1], o = i;
                                                        break
                                                    }
                                                    if (o && a.label < o[2]) {
                                                        a.label = o[2], a.ops.push(i);
                                                        break
                                                    }
                                                    o[2] && a.ops.pop(), a.trys.pop();
                                                    continue
                                            }
                                            i = t.call(e, a)
                                        } catch (e) {
                                            i = [6, e], r = 0
                                        } finally {
                                            n = o = 0
                                        }
                                        if (5 & i[0]) throw i[1];
                                        return {
                                            value: i[0] ? i[1] : void 0,
                                            done: !0
                                        }
                                    }([i, u])
                                }
                            }
                        }(this, function(P) {
                            switch (P.label) {
                                case 0:
                                    if (o = /adfit/i.test(e), i = /kakaotalk/i.test(e), a = /iphone|ipad/i.test(e), u = /android/i.test(e), c = /crios/i.test(e), s = /daumapps/i.test(e), l = /bot|googlebot|crawler|spider|robot|crawling|yeti/i.test(e), f = o || i || a || u || c || s || !!(null === (n = t) || void 0 === n ? void 0 : n.mobile), v = "pc", a ? v = "ios" : u ? v = "android" : f && (v = "unknown"), d = {
                                            isAdFitInAppBrowser: o,
                                            isKakaoTalkApp: i,
                                            isIOS: a,
                                            isAndroid: u,
                                            isIOSChrome: c,
                                            isDaumApp: s,
                                            isMobile: f,
                                            isBot: l,
                                            deviceType: v,
                                            browser: void 0,
                                            browserVersion: void 0,
                                            os: void 0,
                                            osVersion: void 0,
                                            model: void 0
                                        }, !(null === (r = t) || void 0 === r ? void 0 : r.getHighEntropyValues)) return [3, 2];
                                    return [4, t.getHighEntropyValues(["fullVersionList", "platformVersion", "model"])];
                                case 1:
                                    var I, A, T;
                                    return y = (h = null !== (p = P.sent()) && void 0 !== p ? p : {}).fullVersionList, b = h.platformVersion, m = h.model, g = h.platform, I = ea(J(function(e) {
                                        return /chrome|opera|edge/i.test(e.brand)
                                    }, null != y ? y : [])).concat(ea(J(function(e) {
                                        return /chromium/i.test(e.brand)
                                    }, null != y ? y : [])), [{
                                        brand: void 0,
                                        version: void 0
                                    }]), w = (k = (function(e) {
                                        if (Array.isArray(e)) return e
                                    }(I) || function(e, t) {
                                        var n, r, o = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                                        if (null != o) {
                                            var i = [],
                                                a = !0,
                                                u = !1;
                                            try {
                                                for (o = o.call(e); !(a = (n = o.next()).done) && (i.push(n.value), !t || i.length !== t); a = !0);
                                            } catch (e) {
                                                u = !0, r = e
                                            } finally {
                                                try {
                                                    a || null == o.return || o.return()
                                                } finally {
                                                    if (u) throw r
                                                }
                                            }
                                            return i
                                        }
                                    }(I, 1) || eu(I, 1) || function() {
                                        throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                    }())[0]).brand, O = k.version, S = g || t.platform || void 0, C = b || void 0, [2, (A = function(e) {
                                        for (var t = 1; t < arguments.length; t++) {
                                            var n = null != arguments[t] ? arguments[t] : {},
                                                r = Object.keys(n);
                                            "function" == typeof Object.getOwnPropertySymbols && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) {
                                                return Object.getOwnPropertyDescriptor(n, e).enumerable
                                            }))), r.forEach(function(t) {
                                                var r;
                                                r = n[t], t in e ? Object.defineProperty(e, t, {
                                                    value: r,
                                                    enumerable: !0,
                                                    configurable: !0,
                                                    writable: !0
                                                }) : e[t] = r
                                            })
                                        }
                                        return e
                                    }({}, d), T = {
                                        browser: w,
                                        browserVersion: O,
                                        os: S,
                                        osVersion: C,
                                        model: m || void 0,
                                        isBot: l || !!m && /spider/i.test(m)
                                    }, T = null != T ? T : {}, Object.getOwnPropertyDescriptors ? Object.defineProperties(A, Object.getOwnPropertyDescriptors(T)) : (function(e, t) {
                                        var n = Object.keys(e);
                                        if (Object.getOwnPropertySymbols) {
                                            var r = Object.getOwnPropertySymbols(e);
                                            n.push.apply(n, r)
                                        }
                                        return n
                                    })(Object(T)).forEach(function(e) {
                                        Object.defineProperty(A, e, Object.getOwnPropertyDescriptor(T, e))
                                    }), A)];
                                case 2:
                                    return [2, d]
                            }
                        })
                    }, (ec = function() {
                        var t = this,
                            n = arguments;
                        return new x.Promise(function(r, o) {
                            var i = e.apply(t, n);

                            function a(e) {
                                ei(i, r, o, a, u, "next", e)
                            }

                            function u(e) {
                                ei(i, r, o, a, u, "throw", e)
                            }
                            a(void 0)
                        })
                    }).apply(this, arguments)
                }
                var es = er(function() {
                        return function(e, t) {
                            return ec.apply(this, arguments)
                        }(window.navigator.userAgent, window.navigator.userAgentData)
                    }),
                    el = new x.Promise(function(e) {
                        var t = function() {
                            "complete" === document.readyState && e()
                        };
                        document.addEventListener("readystatechange", t), t()
                    }),
                    ef = "1.6.2",
                    ev = "kakao-pixel-web",
                    ed = {
                        exports: {}
                    };
                ed.exports = function(e) {
                    function t(r) {
                        if (n[r]) return n[r].exports;
                        var o = n[r] = {
                            exports: {},
                            id: r,
                            loaded: !1
                        };
                        return e[r].call(o.exports, o, o.exports, t), o.loaded = !0, o.exports
                    }
                    var n = {};
                    return t.m = e, t.c = n, t.p = "", t(0)
                }([function(e, t, n) {
                    function r(e) {
                        this.name = "JackdawError", this.message = e
                    }

                    function i(e, t) {
                        function n(e, t) {
                            t = t || {};
                            var n, r, o = (r = /^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/.exec(e)) ? ((n = {
                                protocol: r[1],
                                user: r[2],
                                pass: r[3] || "",
                                host: r[4],
                                port: r[5] || "",
                                projectId: r[6]
                            }).endpoint = n.protocol + "://" + n.host + (n.port ? ":" + n.port : "") + "/api/" + n.projectId + "/store/?sentry_version=7&sentry_key=" + n.user + (n.pass ? "&sentry_secret=" + n.pass : ""), n) : "";
                            return x = y(x, {
                                project: o.projectId
                            }), (x = y(x, t || {})).maxBreadcrumbs = Math.max(0, Math.min(x.maxBreadcrumbs, 100)), T = o.endpoint, E = x.transport = t.transport || p, this
                        }

                        function o(e) {
                            return "[object DOMError]" === Object.prototype.toString.call(e)
                        }

                        function l(e, t) {
                            x.debug && console.log(t)
                        }

                        function f() {
                            return I && m() - A < I
                        }

                        function v(e, t, n) {
                            if (f()) return void(t && t(new r("Should back off")));
                            if (b = e, C && (b.stacktrace || C.stacktrace ? d(b.stacktrace, C.stacktrace) : b.exception || C.exception ? (o = b.exception, i = C.exception, a = o, u = i, !(!!a ^ !!u) && (o = o.values[0], i = i.values[0], o.type === i.type && o.value === i.value && (c = o.stacktrace, s = i.stacktrace, void 0 !== c || void 0 !== s) && d(o.stacktrace, i.stacktrace))) : b.fingerprint || C.fingerprint ? !!(b.fingerprint && C.fingerprint) && h(b.fingerprint) === h(C.fingerprint) : (v = b.message, p = C.message, !(!v && !p) && !(v && !p || !v && p) && v === p))) return l("warn", "Jackdaw dropped repeat event: "), void(t && t(new r("Dropped repeat event")));
                            (e = y(x, e)).tag = e.tag || {}, e.extra = e.extra || {}, e.extra["session:duration"] = m() - w, O && O.length > 0 && (e.breadcrumbs = {
                                values: [].slice.call(O, 0)
                            }), S = e.event_id = "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g, function(e) {
                                var t = 16 * Math.random() | 0;
                                return ("x" === e ? t : 3 & t | 8).toString(16)
                            }), C = e, l("debug", "Jackdaw about to send:");
                            var o, i, a, u, c, s, v, p, b, g = P = e.exception && e.exception.values[0];
                            k.addBreadcrumb({
                                category: "sentry",
                                message: g ? (g.type ? g.type + ": " : "") + g.value : e.message,
                                event_id: S,
                                level: e.level || "error"
                            });
                            try {
                                n({
                                    url: T,
                                    data: e,
                                    onSuccess: function() {
                                        I = 0, A = null, t && t()
                                    },
                                    onError: function(e) {
                                        l("error", "Jackdaw transport failed to send: ", e), e.request && function(e) {
                                            if (f()) return void l("warn", "Should back off");
                                            var t, n = e.status;
                                            if (400 === n || 401 === n || 429 === n) {
                                                try {
                                                    t = e.getResponseHeader("Retry-After"), t = 1e3 * parseInt(t, 10)
                                                } catch (e) {}
                                                I = t || 2 * I || 1e3, A = m()
                                            }
                                        }(e.request), t && t(e)
                                    }
                                })
                            } catch (e) {
                                t && t(e)
                            }
                        }

                        function d(e, t) {
                            if (!!e ^ !!t) return !1;
                            var n = e.frames,
                                r = t.frames;
                            if (void 0 === n || void 0 === r || n.length !== r.length) return !1;
                            for (var o, i, a = 0; a < n.length; a++)
                                if (o = n[a], i = r[a], o.filename !== i.filename || o.lineno !== i.lineno || o.colno !== i.colno || o.function !== i.function) return !1;
                            return !0
                        }

                        function p(e) {
                            var t = e.url,
                                n = u.XMLHttpRequest && new u.XMLHttpRequest;
                            if (!n) return !1;
                            var o = "withCredentials" in n;
                            if (!o && void 0 === u.XDomainRequest) return !1;
                            o ? n.onreadystatechange = function() {
                                if (4 !== n.readyState);
                                else if (200 === n.status) e.onSuccess && e.onSuccess();
                                else if (e.onError) {
                                    var t = new r("Transport error code: " + n.status);
                                    t.request = n, e.onError(t)
                                }
                            } : (n = new u.XDomainRequest, t = t.replace(/^https?:/, ""), e.onSuccess && (n.onload = e.onSuccess), e.onError && (n.onerror = function() {
                                var t = new r("Transport error code: XDomainRequest");
                                t.request = n, e.onError(t)
                            })), n.open("POST", t, !0), n.send(h(e.data))
                        }

                        function h(e) {
                            return (JSON.stringify || function e(t) {
                                var n = void 0 === t ? "undefined" : a(t);
                                if ("object" !== n || null === t) return "string" === n && (t = '"' + t + '"'), String(t);
                                var r, o, i = [],
                                    u = t && t.constructor === Array;
                                for (r in t) o = t[r], n = void 0 === o ? "undefined" : a(o), "string" === n ? o = '"' + o + '"' : "object" === n && null !== o && (o = e(o)), i.push((u ? "" : '"' + r + '":') + String(o));
                                return (u ? "[" : "{") + String(i) + (u ? "]" : "}")
                            })(e)
                        }

                        function y(e, t) {
                            return t && b(t, function(t, n) {
                                e[t] = n
                            }), e
                        }

                        function b(e, t) {
                            var n, r;
                            if ("function" == typeof t) {
                                if (e.length) {
                                    if (r = e.length)
                                        for (n = 0; n < r; n++) t.call(null, n, e[n])
                                } else
                                    for (n in e) Object.prototype.hasOwnProperty.call(e, n) && t.call(null, n, e[n])
                            }
                        }

                        function m() {
                            return +new Date
                        }
                        if (this instanceof i == !1) return new i(e, t);
                        if (!e) throw new r("All I want is a public DSN string");
                        var g = i._instanceMap = i._instanceMap || {};
                        if (g[e]) return t && g[e].config(e, t), g[e];
                        g[e] = this;
                        var k = this,
                            w = m(),
                            O = [],
                            S = null,
                            C = null,
                            P = null,
                            I = 0,
                            A = null,
                            T = null,
                            x = {
                                logger: "javascript",
                                platform: "javascript",
                                request: {
                                    headers: {
                                        "User-Agent": s.userAgent
                                    },
                                    url: c.location.href,
                                    referrer: c.referrer
                                },
                                sdk: {
                                    name: "@kakao/jackdaw-js",
                                    version: "1.0.5"
                                },
                                level: "error",
                                maxBreadcrumbs: 100,
                                debug: !1
                            },
                            E = p;
                        this.config = n, this.addBreadcrumb = function(e) {
                            var t = y({
                                timestamp: m() / 1e3
                            }, e);
                            return O.push(t), O.length > x.maxBreadcrumbs && O.splice(0, O.length - x.maxBreadcrumbs), this
                        }, this.captureMessage = function(e, t, n) {
                            var o = E;
                            if (!e) throw new r("Need something to send message");
                            return t && "function" == typeof t.transport && (o = t.transport, delete t.transport), v(y({
                                message: e + ""
                            }, t || {}), n, o), this
                        }, this.captureException = function(e, t, n) {
                            var i, a, u, s, l, f, d, p, h, m = E;
                            if (!e) throw new r("Need something to send exception or error");
                            if (t && "function" == typeof t.transport && (m = t.transport, delete t.transport), e.error && (e = e.error), o(e) || (i = e, "[object DOMException]" === Object.prototype.toString.call(i))) {
                                var g = e.name || (o(e) ? "DOMError" : "DOMException"),
                                    k = e.message ? g + ": " + e.message : g;
                                return this.captureMessage(k, y(t, {
                                    stacktrace: !0,
                                    trimHeadFrames: t.trimHeadFrames + 1
                                }))
                            }
                            P = e, (t = y({
                                trimHeadFrames: 0
                            }, t || {})).level = t.level || "error";
                            var w = function e(t, n) {
                                    function r() {
                                        try {
                                            return c.location.href
                                        } catch (e) {}
                                        return ""
                                    }
                                    var o = null;
                                    n = null == n ? 0 : +n;
                                    try {
                                        if (o = function(e) {
                                                if (void 0 !== e.stack && e.stack) {
                                                    for (var t, n, o, i = /^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[a-z]:|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i, a = /^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx(?:-web)|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i, u = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|moz-extension).*?:\/.*?|\[native code\]|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i, c = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i, s = /\((\S*)(?::(\d+))(?::(\d+))\)/, l = e.stack.split("\n"), f = [], v = 0, d = l.length; v < d; ++v) {
                                                        if (n = i.exec(l[v])) {
                                                            var p = n[2] && 0 === n[2].indexOf("native");
                                                            n[2] && 0 === n[2].indexOf("eval") && (t = s.exec(n[2])) && (n[2] = t[1], n[3] = t[2], n[4] = t[3]), o = {
                                                                url: p ? null : n[2],
                                                                func: n[1] || "?",
                                                                args: p ? [n[2]] : [],
                                                                line: n[3] ? +n[3] : null,
                                                                column: n[4] ? +n[4] : null
                                                            }
                                                        } else if (n = a.exec(l[v])) o = {
                                                            url: n[2],
                                                            func: n[1] || "?",
                                                            args: [],
                                                            line: +n[3],
                                                            column: n[4] ? +n[4] : null
                                                        };
                                                        else {
                                                            if (!(n = u.exec(l[v]))) continue;
                                                            n[3] && n[3].indexOf(" > eval") > -1 && (t = c.exec(n[3])) ? (n[3] = t[1], n[4] = t[2], n[5] = null) : 0 !== v || n[5] || void 0 === e.columnNumber || (f[0].column = e.columnNumber + 1), o = {
                                                                url: n[3],
                                                                func: n[1] || "?",
                                                                args: n[2] ? n[2].split(",") : [],
                                                                line: n[4] ? +n[4] : null,
                                                                column: n[5] ? +n[5] : null
                                                            }
                                                        }!o.func && o.line && (o.func = "?"), f.push(o)
                                                    }
                                                    return f.length ? {
                                                        name: e.name,
                                                        message: e.message,
                                                        url: r(),
                                                        stack: f
                                                    } : null
                                                }
                                            }(t)) return o
                                    } catch (e) {}
                                    try {
                                        if (o = function t(n, o) {
                                                for (var i, a, u = /function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i, c = [], s = {}, l = !1, f = t.caller; f && !l; f = f.caller)
                                                    if (f !== e) {
                                                        if (a = {
                                                                url: null,
                                                                func: "?",
                                                                line: null,
                                                                column: null
                                                            }, f.name ? a.func = f.name : (i = u.exec(f.toString())) && (a.func = i[1]), void 0 === a.func) try {
                                                            a.func = i.input.substring(0, i.input.indexOf("{"))
                                                        } catch (e) {}
                                                        s["" + f] ? l = !0 : s["" + f] = !0, c.push(a)
                                                    }
                                                o && c.splice(0, o);
                                                var v = {
                                                    name: n.name,
                                                    message: n.message,
                                                    url: r(),
                                                    stack: c
                                                };
                                                return function(e, t, n, r) {
                                                    var o = {
                                                        url: t,
                                                        line: n
                                                    };
                                                    if (o.url && o.line) {
                                                        if (e.incomplete = !1, o.func || (o.func = "?"), e.stack.length > 0 && e.stack[0].url === o.url) {
                                                            if (e.stack[0].line === o.line) return 0;
                                                            if (!e.stack[0].line && e.stack[0].func === o.func) return e.stack[0].line = o.line, 0
                                                        }
                                                        return e.stack.unshift(o), e.partial = !0
                                                    }
                                                    e.incomplete = !0
                                                }(v, n.sourceURL || n.fileName, n.line || n.lineNumber, n.message || n.description), v
                                            }(t, n + 1)) return o
                                    } catch (e) {}
                                    return {
                                        name: t.name,
                                        message: t.message,
                                        url: r()
                                    }
                                }(e),
                                O = function(e, t) {
                                    var n = [];
                                    if (e.stack && e.stack.length && (b(e.stack, function(t, r) {
                                            var o, i, a = (o = e.url, i = {
                                                filename: r.url,
                                                lineno: r.line,
                                                colno: r.column,
                                                function: r.func || "?"
                                            }, r.url || (i.filename = o), i);
                                            a && n.push(a)
                                        }), t && t.trimHeadFrames))
                                        for (var r = 0; r < t.trimHeadFrames && r < n.length; r++) n[r].in_app = !1;
                                    return n = n.slice(0, 50)
                                }(w);
                            return v((a = w.name, u = w.message, s = w.url, l = w.lineno, f = t, O && O.length ? (s = O[0].filename || s, O.reverse(), d = {
                                frames: O
                            }) : s && (d = {
                                frames: [{
                                    filename: s,
                                    lineno: l,
                                    in_app: !0
                                }]
                            }), h = (p = y({
                                exception: {
                                    values: [{
                                        type: a,
                                        value: u,
                                        stacktrace: d
                                    }]
                                }
                            }, f)).exception.values[0], null == h.type && "" === h.value && (h.value = "Unrecoverable error caught"), p), n, m), this
                        }, this.lastException = function() {
                            return P
                        }, this.lastData = function() {
                            return C
                        }, this.lastEventId = function() {
                            return S
                        }, n(e, t)
                    }
                    var a = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                            return typeof e
                        } : function(e) {
                            return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                        },
                        u = "undefined" != typeof window ? window : void 0 !== o ? o : "undefined" != typeof self ? self : {},
                        c = u.document,
                        s = u.navigator;
                    u.Jackdaw || (u.Jackdaw = i), r.prototype = Error(), r.prototype.constructor = r, e.exports = i
                }]);
                var ep = ed.exports,
                    eh = i(ep);

                function ey(e) {
                    if (void 0 === e) throw ReferenceError("this hasn't been initialised - super() hasn't been called");
                    return e
                }

                function eb(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }

                function em(e, t) {
                    if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                }

                function eg(e, t, n) {
                    return (eg = eA() ? Reflect.construct : function(e, t, n) {
                        var r = [null];
                        r.push.apply(r, t);
                        var o = new(Function.bind.apply(e, r));
                        return n && eP(o, n.prototype), o
                    }).apply(null, arguments)
                }

                function ek(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function ew(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }

                function eO(e) {
                    return (eO = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                        return e.__proto__ || Object.getPrototypeOf(e)
                    })(e)
                }

                function eS(e, t) {
                    if ("function" != typeof t && null !== t) throw TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, {
                        constructor: {
                            value: e,
                            writable: !0,
                            configurable: !0
                        }
                    }), t && eP(e, t)
                }

                function eC(e, t) {
                    return null != t && "undefined" != typeof Symbol && t[Symbol.hasInstance] ? !!t[Symbol.hasInstance](e) : e instanceof t
                }

                function eP(e, t) {
                    return (eP = Object.setPrototypeOf || function(e, t) {
                        return e.__proto__ = t, e
                    })(e, t)
                }

                function eI(e) {
                    var t = "function" == typeof Map ? new Map : void 0;
                    return (eI = function(e) {
                        if (null === e || -1 === Function.toString.call(e).indexOf("[native code]")) return e;
                        if ("function" != typeof e) throw TypeError("Super expression must either be null or a function");
                        if (void 0 !== t) {
                            if (t.has(e)) return t.get(e);
                            t.set(e, n)
                        }

                        function n() {
                            return eg(e, arguments, eO(this).constructor)
                        }
                        return n.prototype = Object.create(e.prototype, {
                            constructor: {
                                value: n,
                                enumerable: !1,
                                writable: !0,
                                configurable: !0
                            }
                        }), eP(n, e)
                    })(e)
                }

                function eA() {
                    if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham) return !1;
                    if ("function" == typeof Proxy) return !0;
                    try {
                        return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {})), !0
                    } catch (e) {
                        return !1
                    }
                }

                function eT(e) {
                    var t = eA();
                    return function() {
                        var n, r, o = eO(e);
                        if (t) {
                            var i = eO(this).constructor;
                            r = Reflect.construct(o, arguments, i)
                        } else r = o.apply(this, arguments);
                        return (n = r) && ("object" == (n && "undefined" != typeof Symbol && n.constructor === Symbol ? "symbol" : typeof n) || "function" == typeof n) ? n : ey(this)
                    }
                }
                var ex = function(e) {
                    eS(o, e);
                    var t, n, r = eT(o);

                    function o(e, t) {
                        var n;
                        em(this, o), n = r.call(this, e), ew(ey(n), "name", "PixelError"), ew(ey(n), "detail", void 0);
                        try {
                            "function" == typeof Error.captureStackTrace ? Error.captureStackTrace(ey(n), n.constructor) : n.stack || (n.stack = Error(e).stack)
                        } catch (e) {}
                        return n.detail = t, n
                    }
                    return t = [{
                        key: "capture",
                        value: function(e) {
                            return o.capture(this, e)
                        }
                    }], n = [{
                        key: "capture",
                        value: function(e, t) {
                            return o.captureBy(o.getSentryInstance(), e, t)
                        }
                    }, {
                        key: "captureBy",
                        value: function(e, t) {
                            var n, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                            return (n = function() {
                                var n, i;
                                return function(e, t) {
                                    var n, r, o, i, a = {
                                        label: 0,
                                        sent: function() {
                                            if (1 & o[0]) throw o[1];
                                            return o[1]
                                        },
                                        trys: [],
                                        ops: []
                                    };
                                    return i = {
                                        next: u(0),
                                        throw: u(1),
                                        return: u(2)
                                    }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                        return this
                                    }), i;

                                    function u(i) {
                                        return function(u) {
                                            return function(i) {
                                                if (n) throw TypeError("Generator is already executing.");
                                                for (; a;) try {
                                                    if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                                    switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                                        case 0:
                                                        case 1:
                                                            o = i;
                                                            break;
                                                        case 4:
                                                            return a.label++, {
                                                                value: i[1],
                                                                done: !1
                                                            };
                                                        case 5:
                                                            a.label++, r = i[1], i = [0];
                                                            continue;
                                                        case 7:
                                                            i = a.ops.pop(), a.trys.pop();
                                                            continue;
                                                        default:
                                                            if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                                a = 0;
                                                                continue
                                                            }
                                                            if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                                a.label = i[1];
                                                                break
                                                            }
                                                            if (6 === i[0] && a.label < o[1]) {
                                                                a.label = o[1], o = i;
                                                                break
                                                            }
                                                            if (o && a.label < o[2]) {
                                                                a.label = o[2], a.ops.push(i);
                                                                break
                                                            }
                                                            o[2] && a.ops.pop(), a.trys.pop();
                                                            continue
                                                    }
                                                    i = t.call(e, a)
                                                } catch (e) {
                                                    i = [6, e], r = 0
                                                } finally {
                                                    n = o = 0
                                                }
                                                if (5 & i[0]) throw i[1];
                                                return {
                                                    value: i[0] ? i[1] : void 0,
                                                    done: !0
                                                }
                                            }([i, u])
                                        }
                                    }
                                }(this, function(a) {
                                    switch (a.label) {
                                        case 0:
                                            return [4, es()];
                                        case 1:
                                            if (a.sent().isBot) return [2];
                                            if (!eC(t, Error)) return o.captureBy(e, new o("Non-Error exception", {
                                                value: JSON.stringify(t)
                                            }), r), [2];
                                            return i = {
                                                trackId: (n = function(e) {
                                                    for (var t = 1; t < arguments.length; t++) {
                                                        var n = null != arguments[t] ? arguments[t] : {},
                                                            r = Object.keys(n);
                                                        "function" == typeof Object.getOwnPropertySymbols && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) {
                                                            return Object.getOwnPropertyDescriptor(n, e).enumerable
                                                        }))), r.forEach(function(t) {
                                                            ew(e, t, n[t])
                                                        })
                                                    }
                                                    return e
                                                }({}, r, eC(t, o) ? t.detail : {})).trackId,
                                                eventCode: n.eventCode
                                            }, e.captureException(t, {
                                                tags: i,
                                                extra: n
                                            }), [2]
                                    }
                                })
                            }, function() {
                                var e = this,
                                    t = arguments;
                                return new x.Promise(function(r, o) {
                                    var i = n.apply(e, t);

                                    function a(e) {
                                        eb(i, r, o, a, u, "next", e)
                                    }

                                    function u(e) {
                                        eb(i, r, o, a, u, "throw", e)
                                    }
                                    a(void 0)
                                })
                            })()
                        }
                    }], t && ek(o.prototype, t), n && ek(o, n), o
                }(eI(Error));
                ew(ex, "getSentryInstance", er(function() {
                    return eh("https://".concat("974753e2fca1433689ce92827ec28203", "@").concat("aem-kakao-collector.onkakao.net", "/").concat("3307"), {
                        release: "v".concat(ef),
                        environment: "release"
                    })
                }));
                var eE = function(e) {
                    eS(n, e);
                    var t = eT(n);

                    function n() {
                        var e;
                        return em(this, n), e = t.apply(this, arguments), ew(ey(e), "name", "PixelRequestError"), e
                    }
                    return n
                }(ex);

                function ej(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function e_(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function eD(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }(t = n || (n = {})).SUPPORTED = "adfit:3PC:supported", t.UNSUPPORTED = "adfit:3PC:unsupported", t.PARTITIONED = "adfit:3PC:partitioned";
                var eL = "https://t1.daumcdn.net/kas/static/third-party/cookie/ct2.html",
                    eR = (r = {}, eD(r, n.SUPPORTED, "Y"), eD(r, n.UNSUPPORTED, "N"), eD(r, n.PARTITIONED, "P"), r),
                    eM = function() {
                        var e, t;

                        function r() {
                            var e = this;
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, r), eD(this, "pendingCheckPromise", void 0), eD(this, "resolvePendingCheck", void 0), eD(this, "checkTimeoutTimerId", void 0), eD(this, "handleMessage", function(t) {
                                var n, o = t.data;
                                r.isThirdPartyCookieSupportStatusMessage(o) && (null === (n = e.resolvePendingCheck) || void 0 === n || n.call(e, eR[o]), e.clearCheck())
                            }), eD(this, "handleTimeout", function() {
                                var t;
                                null === (t = e.resolvePendingCheck) || void 0 === t || t.call(e, void 0), e.clearCheck()
                            })
                        }
                        return e = [{
                            key: "getThirdPartyCookieSupportStatus",
                            value: function(e) {
                                return this.pendingCheckPromise || (this.pendingCheckPromise = this.startCheck(e)), this.pendingCheckPromise
                            }
                        }, {
                            key: "startCheck",
                            value: function(e) {
                                var t, n = this;
                                return /iP(hone|ad|od)/i.test(window.navigator.userAgent) && (!/Safari/i.test(window.navigator.userAgent) || "webkit" in window && "object" == typeof window.webkit && null !== window.webkit && "messageHandlers" in window.webkit || (t = ["Mozilla", "AppleWebKit", "Safari", "Mobile", "Version", "Mobile", "CriOS", "FxiOS", "OPT", "EdgiOS", "Whale"], (function() {
                                    var e = window.navigator.userAgent.split(" ").map(function(e) {
                                            return e.trim()
                                        }),
                                        t = [],
                                        n = !1,
                                        r = !0,
                                        o = !1,
                                        i = void 0;
                                    try {
                                        for (var a, u = e[Symbol.iterator](); !(r = (a = u.next()).done); r = !0) {
                                            var c, s = a.value;
                                            if (0 !== s.length) {
                                                if (n) {
                                                    ")" === s[s.length - 1] && (n = !1);
                                                    continue
                                                }
                                                if ("(" === s[0]) {
                                                    n = !0;
                                                    continue
                                                }
                                                var l = (c = s.split("/"), function(e) {
                                                    if (Array.isArray(e)) return e
                                                }(c) || function(e, t) {
                                                    var n, r, o = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                                                    if (null != o) {
                                                        var i = [],
                                                            a = !0,
                                                            u = !1;
                                                        try {
                                                            for (o = o.call(e); !(a = (n = o.next()).done) && (i.push(n.value), !t || i.length !== t); a = !0);
                                                        } catch (e) {
                                                            u = !0, r = e
                                                        } finally {
                                                            try {
                                                                a || null == o.return || o.return()
                                                            } finally {
                                                                if (u) throw r
                                                            }
                                                        }
                                                        return i
                                                    }
                                                }(c, 1) || function(e, t) {
                                                    if (e) {
                                                        if ("string" == typeof e) return ej(e, t);
                                                        var n = Object.prototype.toString.call(e).slice(8, -1);
                                                        if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                                                        if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return ej(e, t)
                                                    }
                                                }(c, 1) || function() {
                                                    throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                                }())[0];
                                                t.push(l)
                                            }
                                        }
                                    } catch (e) {
                                        o = !0, i = e
                                    } finally {
                                        try {
                                            r || null == u.return || u.return()
                                        } finally {
                                            if (o) throw i
                                        }
                                    }
                                    return t
                                })().some(function(e) {
                                    return -1 === t.indexOf(e)
                                }))) ? x.Promise.resolve("N") : new x.Promise(function(t) {
                                    e && (n.checkTimeoutTimerId = window.setTimeout(n.handleTimeout, e)), window.addEventListener("message", n.handleMessage), n.resolvePendingCheck = t, r.appendCheckFrame()
                                })
                            }
                        }, {
                            key: "clearCheck",
                            value: function() {
                                r.removeCheckFrame(), window.removeEventListener("message", this.handleMessage), this.checkTimeoutTimerId && window.clearTimeout(this.checkTimeoutTimerId), this.checkTimeoutTimerId = void 0, this.resolvePendingCheck = void 0
                            }
                        }], t = [{
                            key: "getCheckFrame",
                            value: function() {
                                return document.body.querySelector('iframe[src="'.concat(eL, '"]'))
                            }
                        }, {
                            key: "appendCheckFrame",
                            value: function() {
                                try {
                                    if (!document.body) {
                                        window.addEventListener("DOMContentLoaded", this.appendCheckFrame);
                                        return
                                    }
                                    if (this.getCheckFrame()) return;
                                    var e = document.createElement("iframe");
                                    e.style.display = "none", e.src = eL, document.body.appendChild(e)
                                } catch (e) {}
                            }
                        }, {
                            key: "removeCheckFrame",
                            value: function() {
                                try {
                                    var e, t = this.getCheckFrame();
                                    if (!t) return;
                                    null === (e = t.parentNode) || void 0 === e || e.removeChild(t)
                                } catch (e) {}
                            }
                        }, {
                            key: "isThirdPartyCookieSupportStatusMessage",
                            value: function(e) {
                                return Object.values(n).includes(e)
                            }
                        }], e && e_(r.prototype, e), t && e_(r, t), r
                    }();

                function eN(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function eK(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }

                function eF(e) {
                    return function() {
                        var t = this,
                            n = arguments;
                        return new x.Promise(function(r, o) {
                            var i = e.apply(t, n);

                            function a(e) {
                                eK(i, r, o, a, u, "next", e)
                            }

                            function u(e) {
                                eK(i, r, o, a, u, "throw", e)
                            }
                            a(void 0)
                        })
                    }
                }

                function eB(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function eU(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }

                function eV(e, t) {
                    var n, r, o, i, a = {
                        label: 0,
                        sent: function() {
                            if (1 & o[0]) throw o[1];
                            return o[1]
                        },
                        trys: [],
                        ops: []
                    };
                    return i = {
                        next: u(0),
                        throw: u(1),
                        return: u(2)
                    }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                        return this
                    }), i;

                    function u(i) {
                        return function(u) {
                            return function(i) {
                                if (n) throw TypeError("Generator is already executing.");
                                for (; a;) try {
                                    if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                    switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                        case 0:
                                        case 1:
                                            o = i;
                                            break;
                                        case 4:
                                            return a.label++, {
                                                value: i[1],
                                                done: !1
                                            };
                                        case 5:
                                            a.label++, r = i[1], i = [0];
                                            continue;
                                        case 7:
                                            i = a.ops.pop(), a.trys.pop();
                                            continue;
                                        default:
                                            if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                a = 0;
                                                continue
                                            }
                                            if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                a.label = i[1];
                                                break
                                            }
                                            if (6 === i[0] && a.label < o[1]) {
                                                a.label = o[1], o = i;
                                                break
                                            }
                                            if (o && a.label < o[2]) {
                                                a.label = o[2], a.ops.push(i);
                                                break
                                            }
                                            o[2] && a.ops.pop(), a.trys.pop();
                                            continue
                                    }
                                    i = t.call(e, a)
                                } catch (e) {
                                    i = [6, e], r = 0
                                } finally {
                                    n = o = 0
                                }
                                if (5 & i[0]) throw i[1];
                                return {
                                    value: i[0] ? i[1] : void 0,
                                    done: !0
                                }
                            }([i, u])
                        }
                    }
                }
                eD(eM, "getInstance", er(function() {
                    return new eM
                }));
                var eH = function() {
                    var e, t;

                    function n(e, t, r, o) {
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), eU(this, "trackId", void 0), eU(this, "globalDataStorage", void 0), eU(this, "aidStorage", void 0), eU(this, "adFitConversionDataService", void 0), eU(this, "thirdPartyCookieSupportStatusChecker", eM.getInstance()), this.trackId = e, this.globalDataStorage = t, this.aidStorage = r, this.adFitConversionDataService = o
                    }
                    return e = [{
                        key: "track",
                        value: function(e, t) {
                            var n = this;
                            return eF(function() {
                                var r, o, i, a, u, c, s, l, f, v, d, p, h;
                                return eV(this, function(y) {
                                    switch (y.label) {
                                        case 0:
                                            return y.trys.push([0, 8, , 9]), [4, es()];
                                        case 1:
                                            if (o = (r = y.sent()).isAdFitInAppBrowser, i = r.isKakaoTalkApp, a = r.isIOS, r.isBot) return [2];
                                            if (!((o || i) && a)) return [3, 3];
                                            return [4, el];
                                        case 2:
                                            y.sent(), y.label = 3;
                                        case 3:
                                            return [4, n.createConversionData(e, t)];
                                        case 4:
                                            return u = y.sent(), c = window.location.hostname.indexOf(".kakao.com") > -1 ? "https://bc.ds.kakao.com/bc" : "https://bc.ad.daum.net/bc", s = z(c, {
                                                d: JSON.stringify(u)
                                            }), l = n.createHeaders(), [4, (function(e) {
                                                var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                                                    n = t.timeout,
                                                    r = void 0 === n ? 2e3 : n,
                                                    o = t.headers;
                                                return new x.Promise(function(t, n) {
                                                    var i = 0;
                                                    r > 0 && (i = window.setTimeout(function() {
                                                        i = 0, n(Error("Fetch request timed out"))
                                                    }, r)), Q(e, {
                                                        credentials: "include",
                                                        headers: o
                                                    }).then(t).catch(n).finally(function() {
                                                        i > 0 && (window.clearTimeout(i), i = 0)
                                                    })
                                                })
                                            })(s, {
                                                headers: l
                                            }).catch(function(e) {
                                                var t;
                                                throw new eE((t = Error, null != t && "undefined" != typeof Symbol && t[Symbol.hasInstance] ? !!t[Symbol.hasInstance](e) : e instanceof t) ? e.message : "Fetch request has failed", {
                                                    error: e,
                                                    conversionData: u,
                                                    url: s,
                                                    headers: l
                                                })
                                            })];
                                        case 5:
                                            if ((f = y.sent()).ok) return [3, 7];
                                            return v = ex.bind, d = [void 0, "Fetch response was not ok"], p = {
                                                statusCode: f.status,
                                                statusText: f.statusText
                                            }, [4, f.text().catch(function() {})];
                                        case 6:
                                            throw new(v.apply(ex, d.concat([(p.responseBody = y.sent(), p.conversionData = u, p.url = s, p.headers = l, p)])));
                                        case 7:
                                            return n.storeAid(f), [3, 9];
                                        case 8:
                                            return h = y.sent(), ex.capture(h, {
                                                trackId: n.trackId,
                                                eventCode: e,
                                                eventParameters: t
                                            }), [3, 9];
                                        case 9:
                                            return [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "createConversionData",
                        value: function(e, t) {
                            var n = this;
                            return eF(function() {
                                var r, o, i, a, u, c, s, l, f, v, d, p, h, y, b, m, g, k, w, O, S, C, P, I, A, T, x, E, j, _, D, L, R, M, N, K, F;
                                return eV(this, function(B) {
                                    switch (B.label) {
                                        case 0:
                                            if (l = !(s = n.globalDataStorage.isLimitAdTrackingEnabled()) && (null === (r = n.globalDataStorage.getAdId()) || void 0 === r ? void 0 : r.trim()) || void 0, f = (null === (o = n.globalDataStorage.getKakaoAccountId()) || void 0 === o ? void 0 : o.trim()) || void 0, !(v = function() {
                                                    try {
                                                        if (!window.Kakao || !window.Kakao.Auth || !window.Kakao.Auth.getAppKey || !window.Kakao.Auth.getAccessToken || window.Kakao.isInitialized && !window.Kakao.isInitialized()) return null;
                                                        var e = window.Kakao.Auth.getAppKey(),
                                                            t = window.Kakao.Auth.getAccessToken();
                                                        if (e) return {
                                                            appKey: e,
                                                            accessToken: t
                                                        }
                                                    } catch (e) {}
                                                    return null
                                                }())) return [3, 2];
                                            return [4, function(e) {
                                                return en.apply(this, arguments)
                                            }(v)];
                                        case 1:
                                            return p = B.sent(), [3, 3];
                                        case 2:
                                            p = n.globalDataStorage.getEncryptedKakaoAccountId(), B.label = 3;
                                        case 3:
                                            return d = p || void 0, [4, n.adFitConversionDataService.getAdFitConversionData()];
                                        case 4:
                                            return y = (h = B.sent()).etxId, b = h.surl, m = h.kclid, g = (null === (i = n.globalDataStorage.getIDFV()) || void 0 === i ? void 0 : i.trim()) || void 0, k = null === (a = v) || void 0 === a ? void 0 : a.appKey, w = null === (u = v) || void 0 === u ? void 0 : u.accessToken, O = s ? "Y" : "N", S = (null === (c = n.globalDataStorage.getServiceOrigin()) || void 0 === c ? void 0 : c.trim()) || void 0, C = document.domain, P = "WEB", I = ef, A = window.location.href, T = b || document.referrer || void 0, x = window.location !== window.parent.location, E = window.navigator.doNotTrack ? "Y" : "N", [4, es()];
                                        case 5:
                                            return _ = (j = B.sent()).deviceType, D = j.browser, L = j.browserVersion, R = j.os, M = j.osVersion, N = j.model, K = j.isMobile ? "Y" : "N", [4, n.getTpc()];
                                        case 6:
                                            return F = B.sent(), [2, {
                                                track_id: n.trackId,
                                                event_code: e,
                                                params: t,
                                                ad_id: l,
                                                accid: f,
                                                eacid: d,
                                                etx_id: y,
                                                kclid: m,
                                                props: {
                                                    idfv: g,
                                                    app_key: k,
                                                    app_access_token: w,
                                                    lmt: O,
                                                    service_origin: S
                                                },
                                                site: {
                                                    identifier: C
                                                },
                                                sdk: {
                                                    type: P,
                                                    version: I,
                                                    tpc: F
                                                },
                                                page: {
                                                    url: A,
                                                    ref_url: T,
                                                    is_frame_env: x
                                                },
                                                device: {
                                                    dnt: E,
                                                    device_type: _,
                                                    is_mobile: K,
                                                    browser: D,
                                                    browser_version: L,
                                                    os: R,
                                                    os_version: M,
                                                    model: N
                                                }
                                            }]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "createHeaders",
                        value: function() {
                            var e, t = {},
                                r = null === (e = this.aidStorage) || void 0 === e ? void 0 : e.get();
                            if (r) {
                                var o = r.aid,
                                    i = r.aidTs;
                                t["x-kakao-aid"] = n.createAidHeader(o, i)
                            }
                            return t
                        }
                    }, {
                        key: "storeAid",
                        value: function(e) {
                            var t = e.headers.get("x-kakao-aid");
                            if (t) {
                                var r = n.parseAidHeader(t);
                                if (r) {
                                    var o = r.aid,
                                        i = r.aidTs;
                                    this.aidStorage.set(o, i)
                                }
                            }
                        }
                    }, {
                        key: "getTpc",
                        value: function() {
                            var e = this;
                            return eF(function() {
                                return eV(this, function(t) {
                                    switch (t.label) {
                                        case 0:
                                            return t.trys.push([0, 2, , 3]), [4, e.thirdPartyCookieSupportStatusChecker.getThirdPartyCookieSupportStatus(500)];
                                        case 1:
                                            return [2, t.sent()];
                                        case 2:
                                            return t.sent(), [2, void 0];
                                        case 3:
                                            return [2]
                                    }
                                })
                            })()
                        }
                    }], t = [{
                        key: "parseAidHeader",
                        value: function(e) {
                            var t = {};
                            return (e.split(",").forEach(function(e) {
                                var n, r = (n = e.split("="), function(e) {
                                        if (Array.isArray(e)) return e
                                    }(n) || function(e, t) {
                                        var n, r, o = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                                        if (null != o) {
                                            var i = [],
                                                a = !0,
                                                u = !1;
                                            try {
                                                for (o = o.call(e); !(a = (n = o.next()).done) && (i.push(n.value), !t || i.length !== t); a = !0);
                                            } catch (e) {
                                                u = !0, r = e
                                            } finally {
                                                try {
                                                    a || null == o.return || o.return()
                                                } finally {
                                                    if (u) throw r
                                                }
                                            }
                                            return i
                                        }
                                    }(n, 2) || function(e, t) {
                                        if (e) {
                                            if ("string" == typeof e) return eN(e, t);
                                            var n = Object.prototype.toString.call(e).slice(8, -1);
                                            if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                                            if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return eN(e, t)
                                        }
                                    }(n, 2) || function() {
                                        throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                    }()),
                                    o = r[0],
                                    i = r[1];
                                t[o.trim()] = i.trim()
                            }), "aid" in t && "aid_ts" in t) ? {
                                aid: t.aid,
                                aidTs: t.aid_ts
                            } : null
                        }
                    }, {
                        key: "createAidHeader",
                        value: function(e, t) {
                            return "aid=".concat(e, ",aid_ts=").concat(t)
                        }
                    }], e && eB(n.prototype, e), t && eB(n, t), n
                }();

                function eX(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function eG(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }
                var eW = function() {
                    var e, t;

                    function n(e, t) {
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), eG(this, "naverCheckoutObserver", void 0), eG(this, "kakaoPixelEventTracker", void 0), this.naverCheckoutObserver = e, this.kakaoPixelEventTracker = t
                    }
                    return e = [{
                        key: "purchaseNaverCheckout",
                        value: function(e) {
                            var t = this;
                            this.addListener("buyLinkClick", function() {
                                var n = "function" == typeof e ? e() : e;
                                n && (n.total_price || n.total_quantity) && t.kakaoPixelEventTracker.purchase(n, "npay_purchase")
                            })
                        }
                    }, {
                        key: "trackNaverCheckoutWishLink",
                        value: function(e) {
                            this.addListener("wishLinkClick", e)
                        }
                    }, {
                        key: "trackNaverCheckoutCartLink",
                        value: function(e) {
                            this.addListener("cartLinkClick", e)
                        }
                    }, {
                        key: "trackNaverCheckoutTalkLink",
                        value: function(e) {
                            this.addListener("talkLinkClick", e)
                        }
                    }, {
                        key: "trackNaverCheckoutPromotionLink",
                        value: function(e) {
                            this.addListener("promotionLinkClick", e)
                        }
                    }, {
                        key: "addListener",
                        value: function(e, t) {
                            this.naverCheckoutObserver.addListener(e, function() {
                                try {
                                    t()
                                } catch (e) {}
                            })
                        }
                    }], eX(n.prototype, e), t && eX(n, t), n
                }();

                function eY(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function eq(e) {
                    return (eq = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                        return e.__proto__ || Object.getPrototypeOf(e)
                    })(e)
                }

                function eJ(e, t) {
                    return t && ("object" == (t && "undefined" != typeof Symbol && t.constructor === Symbol ? "symbol" : typeof t) || "function" == typeof t) ? t : function(e) {
                        if (void 0 === e) throw ReferenceError("this hasn't been initialised - super() hasn't been called");
                        return e
                    }(e)
                }

                function e$(e, t) {
                    return (e$ = Object.setPrototypeOf || function(e, t) {
                        return e.__proto__ = t, e
                    })(e, t)
                }
                var eZ = {
                        buyLink: "[id^=NPAY_BUY_LINK_IDNC_ID_]",
                        wishLink: "[id^=NPAY_WISH_LINK_IDNC_ID_]",
                        cartLink: "[id^=NPAY_CART_LINK_IDNC_ID_]",
                        talkLink: "[id^=NPAY_TALK_LINK_IDNC_ID_]",
                        promotionLink: "[id^=NPAY_PROMOTION_IDNC_ID]"
                    },
                    ez = function(e) {
                        ! function(e, t) {
                            if ("function" != typeof t && null !== t) throw TypeError("Super expression must either be null or a function");
                            e.prototype = Object.create(t && t.prototype, {
                                constructor: {
                                    value: e,
                                    writable: !0,
                                    configurable: !0
                                }
                            }), t && e$(e, t)
                        }(i, e);
                        var t, n, r, o = (t = function() {
                            if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham) return !1;
                            if ("function" == typeof Proxy) return !0;
                            try {
                                return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {})), !0
                            } catch (e) {
                                return !1
                            }
                        }(), function() {
                            var e, n = eq(i);
                            if (t) {
                                var r = eq(this).constructor;
                                e = Reflect.construct(n, arguments, r)
                            } else e = n.apply(this, arguments);
                            return eJ(this, e)
                        });

                        function i() {
                            var e, t, n;
                            return (! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, i), e = o.call(this), (null === (t = window.nhn) || void 0 === t ? void 0 : t.CheckoutButton) || (null === (n = window.naver) || void 0 === n ? void 0 : n.NaverPayButton)) ? (e.bindClickEvent("buyLink"), e.bindClickEvent("cartLink"), e.bindClickEvent("talkLink"), e.bindClickEvent("wishLink"), e.bindClickEvent("promotionLink"), e) : eJ(e)
                        }
                        return n = [{
                            key: "bindClickEvent",
                            value: function(e) {
                                var t, n = this,
                                    r = document.querySelector(eZ[e]);
                                if (t = HTMLElement, null != t && "undefined" != typeof Symbol && t[Symbol.hasInstance] ? !!t[Symbol.hasInstance](r) : r instanceof t) {
                                    var o = r.onclick;
                                    r.onclick = function(t) {
                                        try {
                                            n.emit("".concat(e, "Click"))
                                        } catch (e) {}
                                        null == o || o.call(r, t)
                                    }
                                }
                            }
                        }], eY(i.prototype, n), r && eY(i, r), i
                    }(L);

                function eQ(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function e0(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }
                var e1 = function() {
                    var e, t;

                    function n(e, t) {
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), e0(this, "paycoCheckoutObserver", void 0), e0(this, "kakaoPixelEventTracker", void 0), this.paycoCheckoutObserver = e, this.kakaoPixelEventTracker = t
                    }
                    return e = [{
                        key: "purchasePaycoCheckout",
                        value: function(e) {
                            var t = this;
                            this.addListener("checkoutButtonClick", function() {
                                var n;
                                t.kakaoPixelEventTracker.purchase(null !== (n = "function" == typeof e ? e() : e) && void 0 !== n ? n : {}, "payco_purchase")
                            })
                        }
                    }, {
                        key: "trackPaycoPromotionCheckoutButton",
                        value: function(e) {
                            this.addListener("promotionCheckoutButtonClick", e)
                        }
                    }, {
                        key: "addListener",
                        value: function(e, t) {
                            this.paycoCheckoutObserver.addListener(e, function() {
                                try {
                                    t()
                                } catch (e) {}
                            })
                        }
                    }], eQ(n.prototype, e), t && eQ(n, t), n
                }();

                function e2(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function e3(e) {
                    return (e3 = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) {
                        return e.__proto__ || Object.getPrototypeOf(e)
                    })(e)
                }

                function e4(e, t) {
                    return t && ("object" == (t && "undefined" != typeof Symbol && t.constructor === Symbol ? "symbol" : typeof t) || "function" == typeof t) ? t : function(e) {
                        if (void 0 === e) throw ReferenceError("this hasn't been initialised - super() hasn't been called");
                        return e
                    }(e)
                }

                function e6(e, t) {
                    return (e6 = Object.setPrototypeOf || function(e, t) {
                        return e.__proto__ = t, e
                    })(e, t)
                }
                var e5 = {
                        checkoutButton: "[id^=payco_btn_]",
                        promotionCheckoutButton: "[id^=pco_ev_link_]"
                    },
                    e8 = function(e) {
                        ! function(e, t) {
                            if ("function" != typeof t && null !== t) throw TypeError("Super expression must either be null or a function");
                            e.prototype = Object.create(t && t.prototype, {
                                constructor: {
                                    value: e,
                                    writable: !0,
                                    configurable: !0
                                }
                            }), t && e6(e, t)
                        }(i, e);
                        var t, n, r, o = (t = function() {
                            if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham) return !1;
                            if ("function" == typeof Proxy) return !0;
                            try {
                                return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {})), !0
                            } catch (e) {
                                return !1
                            }
                        }(), function() {
                            var e, n = e3(i);
                            if (t) {
                                var r = e3(this).constructor;
                                e = Reflect.construct(n, arguments, r)
                            } else e = n.apply(this, arguments);
                            return e4(this, e)
                        });

                        function i() {
                            var e;
                            return (! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, i), e = o.call(this), void 0 !== window.Payco && "Button" in window.Payco) ? (e.bindClickEvent("checkoutButton"), e.bindClickEvent("promotionCheckoutButton"), e) : e4(e)
                        }
                        return n = [{
                            key: "bindClickEvent",
                            value: function(e) {
                                var t, n = this,
                                    r = document.querySelector(e5[e]);
                                if (t = HTMLElement, null != t && "undefined" != typeof Symbol && t[Symbol.hasInstance] ? !!t[Symbol.hasInstance](r) : r instanceof t) {
                                    var o = r.onclick;
                                    r.onclick = function(t) {
                                        try {
                                            n.emit("".concat(e, "Click"))
                                        } catch (e) {}
                                        null == o || o.call(r, t)
                                    }
                                }
                            }
                        }], e2(i.prototype, n), r && e2(i, r), i
                    }(L);

                function e7(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var e9 = function() {
                    var e, t;

                    function n(e) {
                        var t, r;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), t = "storage", r = void 0, t in this ? Object.defineProperty(this, t, {
                            value: r,
                            enumerable: !0,
                            configurable: !0,
                            writable: !0
                        }) : this[t] = r, this.storage = e
                    }
                    return e = [{
                        key: "setItem",
                        value: function(e, t, n) {
                            this.storage.setItem(e, JSON.stringify({
                                value: t,
                                expiry: n
                            }))
                        }
                    }, {
                        key: "getItem",
                        value: function(e) {
                            var t = this.storage.getItem(e);
                            if (null === t) return null;
                            var r = n.parseItem(t);
                            return null === r ? null : r.expiry <= Date.now() ? (this.removeItem(e), null) : r.value
                        }
                    }, {
                        key: "removeItem",
                        value: function(e) {
                            this.storage.removeItem(e)
                        }
                    }], t = [{
                        key: "isExpiringStorageItem",
                        value: function(e) {
                            return "object" == typeof e && null !== e && "expiry" in e && "number" == typeof e.expiry && "value" in e && "string" == typeof e.value
                        }
                    }, {
                        key: "parseItem",
                        value: function(e) {
                            try {
                                var t = JSON.parse(e);
                                if (!this.isExpiringStorageItem(t)) return null;
                                return t
                            } catch (e) {
                                return null
                            }
                        }
                    }], e && e7(n.prototype, e), t && e7(n, t), n
                }();

                function te(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var tt = "KAKAO_PIXEL_ETXID",
                    tn = function() {
                        var e, t;

                        function n(e) {
                            var t, r;
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, n), t = "storage", r = void 0, t in this ? Object.defineProperty(this, t, {
                                value: r,
                                enumerable: !0,
                                configurable: !0,
                                writable: !0
                            }) : this[t] = r, this.storage = e
                        }
                        return e = [{
                            key: "get",
                            value: function() {
                                var e = this.storage.getItem(tt);
                                return "string" != typeof e && null !== e ? (this.remove(), null) : e
                            }
                        }, {
                            key: "set",
                            value: function(e) {
                                this.storage.setItem(tt, e, Date.now() + 6048e5)
                            }
                        }, {
                            key: "remove",
                            value: function() {
                                this.storage.removeItem(tt)
                            }
                        }], te(n.prototype, e), t && te(n, t), n
                    }();

                function tr(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var to = "KAKAO_PIXEL_AID",
                    ti = function() {
                        var e, t;

                        function n(e) {
                            var t, r;
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, n), t = "storage", r = void 0, t in this ? Object.defineProperty(this, t, {
                                value: r,
                                enumerable: !0,
                                configurable: !0,
                                writable: !0
                            }) : this[t] = r, this.storage = e
                        }
                        return e = [{
                            key: "set",
                            value: function(e, t) {
                                var n = Date.now() + 31536e6;
                                this.storage.setItem(to, JSON.stringify({
                                    aid: e,
                                    aidTs: t
                                }), n)
                            }
                        }, {
                            key: "get",
                            value: function() {
                                var e = this.storage.getItem(to);
                                if (null === e) return null;
                                try {
                                    var t = JSON.parse(e);
                                    if (!n.isAidItem(t)) return this.storage.removeItem(to), null;
                                    return t
                                } catch (e) {}
                                return this.storage.removeItem(to), null
                            }
                        }], t = [{
                            key: "isAidItem",
                            value: function(e) {
                                return "object" == typeof e && null !== e && "aid" in e && "string" == typeof e.aid && "aidTs" in e && "string" == typeof e.aidTs
                            }
                        }], e && tr(n.prototype, e), t && tr(n, t), n
                    }(),
                    ta = "[".concat(ev, "]: 트랙 ID는 문자열로 입력해야 합니다."),
                    tu = "[".concat(ev, "]: Invalid advertiser id");

                function tc(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function ts(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }
                var tl = function() {
                    var e, t;

                    function n(e) {
                        var t = this;
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), ts(this, "messageBus", void 0), ts(this, "events", {}), this.messageBus = e, this.messageBus.addEventListener("message", function(e) {
                            t.handleMessage(e.data)
                        })
                    }
                    return e = [{
                        key: "addEventCall",
                        value: function(e) {
                            var t = e.eventCode,
                                n = e.trackId,
                                r = e.parameters,
                                o = Date.now(),
                                i = {
                                    eventCode: t,
                                    trackId: n,
                                    parameters: r,
                                    timestamp: o,
                                    pageUrl: window.location.href,
                                    id: "".concat(Math.random()).concat(o)
                                };
                            this.addEvent(i), this.sendEventMessage(i)
                        }
                    }, {
                        key: "addTrack",
                        value: function(e) {
                            this.events[e] || (this.events[e] = [], this.sendTrackMessage(e))
                        }
                    }, {
                        key: "addEvent",
                        value: function(e) {
                            var t = this.events[e.trackId];
                            if (!t) {
                                this.events[e.trackId] = [e];
                                return
                            }
                            t.push(e)
                        }
                    }, {
                        key: "sendEventMessage",
                        value: function(e) {
                            this.messageBus.postMessage({
                                type: "kakaoPixel:event",
                                event: e
                            }, "*")
                        }
                    }, {
                        key: "sendTrackMessage",
                        value: function(e) {
                            this.messageBus.postMessage({
                                type: "kakaoPixel:track",
                                trackId: e
                            }, "*")
                        }
                    }, {
                        key: "sendEventsMessage",
                        value: function() {
                            var e = {
                                type: "kakaoPixel:events",
                                events: this.events
                            };
                            this.messageBus.postMessage(e, "*")
                        }
                    }, {
                        key: "handleMessage",
                        value: function(e) {
                            "object" == typeof e && null !== e && "type" in e && "kakaoPixel:requestEvents" === e.type && this.sendEventsMessage()
                        }
                    }], tc(n.prototype, e), t && tc(n, t), n
                }();

                function tf(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }

                function tv(e) {
                    return function() {
                        var t = this,
                            n = arguments;
                        return new x.Promise(function(r, o) {
                            var i = e.apply(t, n);

                            function a(e) {
                                tf(i, r, o, a, u, "next", e)
                            }

                            function u(e) {
                                tf(i, r, o, a, u, "throw", e)
                            }
                            a(void 0)
                        })
                    }
                }

                function td(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }

                function tp(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }

                function th(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = null != arguments[t] ? arguments[t] : {},
                            r = Object.keys(n);
                        "function" == typeof Object.getOwnPropertySymbols && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) {
                            return Object.getOwnPropertyDescriptor(n, e).enumerable
                        }))), r.forEach(function(t) {
                            tp(e, t, n[t])
                        })
                    }
                    return e
                }

                function ty(e, t) {
                    var n, r, o, i, a = {
                        label: 0,
                        sent: function() {
                            if (1 & o[0]) throw o[1];
                            return o[1]
                        },
                        trys: [],
                        ops: []
                    };
                    return i = {
                        next: u(0),
                        throw: u(1),
                        return: u(2)
                    }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                        return this
                    }), i;

                    function u(i) {
                        return function(u) {
                            return function(i) {
                                if (n) throw TypeError("Generator is already executing.");
                                for (; a;) try {
                                    if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                    switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                        case 0:
                                        case 1:
                                            o = i;
                                            break;
                                        case 4:
                                            return a.label++, {
                                                value: i[1],
                                                done: !1
                                            };
                                        case 5:
                                            a.label++, r = i[1], i = [0];
                                            continue;
                                        case 7:
                                            i = a.ops.pop(), a.trys.pop();
                                            continue;
                                        default:
                                            if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                a = 0;
                                                continue
                                            }
                                            if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                a.label = i[1];
                                                break
                                            }
                                            if (6 === i[0] && a.label < o[1]) {
                                                a.label = o[1], o = i;
                                                break
                                            }
                                            if (o && a.label < o[2]) {
                                                a.label = o[2], a.ops.push(i);
                                                break
                                            }
                                            o[2] && a.ops.pop(), a.trys.pop();
                                            continue
                                    }
                                    i = t.call(e, a)
                                } catch (e) {
                                    i = [6, e], r = 0
                                } finally {
                                    n = o = 0
                                }
                                if (5 & i[0]) throw i[1];
                                return {
                                    value: i[0] ? i[1] : void 0,
                                    done: !0
                                }
                            }([i, u])
                        }
                    }
                }
                var tb = function() {
                    var e, t;

                    function n(e, t, r) {
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n), tp(this, "adFitConversionDataRepository", void 0), tp(this, "etxIdStorage", void 0), tp(this, "kclidStorage", void 0), this.etxIdStorage = t, this.adFitConversionDataRepository = e, this.kclidStorage = r
                    }
                    return e = [{
                        key: "storeAdFitConversionData",
                        value: function() {
                            var e = this;
                            return tv(function() {
                                var t, n, r;
                                return ty(this, function(o) {
                                    switch (o.label) {
                                        case 0:
                                            return [4, e.adFitConversionDataRepository.getAdFitConversionData()];
                                        case 1:
                                            return n = (t = o.sent()).etxId, r = t.kclid, n && e.etxIdStorage.set(n), r && e.kclidStorage.set(r), [2]
                                    }
                                })
                            })()
                        }
                    }, {
                        key: "getAdFitConversionData",
                        value: function() {
                            var e = this;
                            return tv(function() {
                                var t;
                                return ty(this, function(n) {
                                    switch (n.label) {
                                        case 0:
                                            return t = [{
                                                etxId: e.etxIdStorage.get() || void 0,
                                                kclid: e.kclidStorage.get() || void 0
                                            }], [4, e.adFitConversionDataRepository.getAdFitConversionData()];
                                        case 1:
                                            return [2, th.apply(void 0, t.concat([n.sent()]))]
                                    }
                                })
                            })()
                        }
                    }], td(n.prototype, e), t && td(n, t), n
                }();

                function tm(e, t, n, r, o, i, a) {
                    try {
                        var u = e[i](a),
                            c = u.value
                    } catch (e) {
                        n(e);
                        return
                    }
                    u.done ? t(c) : x.Promise.resolve(c).then(r, o)
                }

                function tg(e, t, n) {
                    return t in e ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0
                    }) : e[t] = n, e
                }

                function tk(e) {
                    for (var t = 1; t < arguments.length; t++) {
                        var n = null != arguments[t] ? arguments[t] : {},
                            r = Object.keys(n);
                        "function" == typeof Object.getOwnPropertySymbols && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) {
                            return Object.getOwnPropertyDescriptor(n, e).enumerable
                        }))), r.forEach(function(t) {
                            tg(e, t, n[t])
                        })
                    }
                    return e
                }
                var tw = JSON.stringify({
                        from: "adfit",
                        type: "adfit:requestConversionData"
                    }),
                    tO = "_kakaoPixelEtxId",
                    tS = function() {
                        var e;

                        function t() {
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, t), tg(this, "getAdFitConversionData", er(t.getAdFitConversionData))
                        }
                        return e = [{
                                key: "getKclid",
                                value: function() {
                                    return q(location.href).kclid
                                }
                            }, {
                                key: "getAndroidKakaoTalkInAppBrowserConversionData",
                                value: function() {
                                    if (tO in window) {
                                        var e = window[tO];
                                        if ("string" == typeof e) return {
                                            etxId: e
                                        }
                                    }
                                }
                            }, {
                                key: "getAdFitConversionDataFromReferrer",
                                value: function() {
                                    var e = q(document.referrer);
                                    if (Object.keys(e).length > 0) return {
                                        etxId: e.etxId,
                                        surl: e.surl
                                    }
                                }
                            }, {
                                key: "getAdFitConversionDataFromOpener",
                                value: function() {
                                    var e;
                                    if (null === (e = window.opener) || void 0 === e ? void 0 : e.postMessage) return new x.Promise(function(e) {
                                        function t(n) {
                                            try {
                                                var r = JSON.parse(n.data),
                                                    o = r.type,
                                                    i = r.detail;
                                                if ("adfit:conversionData" !== o) return;
                                                if (window.removeEventListener("message", t), !i || "object" != typeof i) {
                                                    e(void 0);
                                                    return
                                                }
                                                e(i)
                                            } catch (e) {}
                                        }
                                        window.addEventListener("message", t), window.opener.postMessage(tw, "*"), setTimeout(function() {
                                            window.removeEventListener("message", t), e(void 0)
                                        }, 1e3)
                                    })
                                }
                            }, {
                                key: "getAdFitConversionData",
                                value: function() {
                                    var e;
                                    return (e = function() {
                                        var e, n, r;
                                        return function(e, t) {
                                            var n, r, o, i, a = {
                                                label: 0,
                                                sent: function() {
                                                    if (1 & o[0]) throw o[1];
                                                    return o[1]
                                                },
                                                trys: [],
                                                ops: []
                                            };
                                            return i = {
                                                next: u(0),
                                                throw: u(1),
                                                return: u(2)
                                            }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                                return this
                                            }), i;

                                            function u(i) {
                                                return function(u) {
                                                    return function(i) {
                                                        if (n) throw TypeError("Generator is already executing.");
                                                        for (; a;) try {
                                                            if (n = 1, r && (o = 2 & i[0] ? r.return : i[0] ? r.throw || ((o = r.return) && o.call(r), 0) : r.next) && !(o = o.call(r, i[1])).done) return o;
                                                            switch (r = 0, o && (i = [2 & i[0], o.value]), i[0]) {
                                                                case 0:
                                                                case 1:
                                                                    o = i;
                                                                    break;
                                                                case 4:
                                                                    return a.label++, {
                                                                        value: i[1],
                                                                        done: !1
                                                                    };
                                                                case 5:
                                                                    a.label++, r = i[1], i = [0];
                                                                    continue;
                                                                case 7:
                                                                    i = a.ops.pop(), a.trys.pop();
                                                                    continue;
                                                                default:
                                                                    if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                                                                        a = 0;
                                                                        continue
                                                                    }
                                                                    if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                                                                        a.label = i[1];
                                                                        break
                                                                    }
                                                                    if (6 === i[0] && a.label < o[1]) {
                                                                        a.label = o[1], o = i;
                                                                        break
                                                                    }
                                                                    if (o && a.label < o[2]) {
                                                                        a.label = o[2], a.ops.push(i);
                                                                        break
                                                                    }
                                                                    o[2] && a.ops.pop(), a.trys.pop();
                                                                    continue
                                                            }
                                                            i = t.call(e, a)
                                                        } catch (e) {
                                                            i = [6, e], r = 0
                                                        } finally {
                                                            n = o = 0
                                                        }
                                                        if (5 & i[0]) throw i[1];
                                                        return {
                                                            value: i[0] ? i[1] : void 0,
                                                            done: !0
                                                        }
                                                    }([i, u])
                                                }
                                            }
                                        }(this, function(o) {
                                            switch (o.label) {
                                                case 0:
                                                    if (o.trys.push([0, 3, , 4]), e = t.getKclid(), n = [{}], r = t.getAndroidKakaoTalkInAppBrowserConversionData() || t.getAdFitConversionDataFromReferrer()) return [3, 2];
                                                    return [4, t.getAdFitConversionDataFromOpener()];
                                                case 1:
                                                    r = o.sent(), o.label = 2;
                                                case 2:
                                                    return [2, tk.apply(void 0, n.concat([r || {}, e ? {
                                                        kclid: e
                                                    } : {}]))];
                                                case 3:
                                                    return o.sent(), [3, 4];
                                                case 4:
                                                    return [2, {}]
                                            }
                                        })
                                    }, function() {
                                        var t = this,
                                            n = arguments;
                                        return new x.Promise(function(r, o) {
                                            var i = e.apply(t, n);

                                            function a(e) {
                                                tm(i, r, o, a, u, "next", e)
                                            }

                                            function u(e) {
                                                tm(i, r, o, a, u, "throw", e)
                                            }
                                            a(void 0)
                                        })
                                    })()
                                }
                            }],
                            function(e, t) {
                                for (var n = 0; n < t.length; n++) {
                                    var r = t[n];
                                    r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                                }
                            }(t, e), t
                    }();

                function tC(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function tP(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var tI = function() {
                    var e, t;

                    function n() {
                        ! function(e, t) {
                            if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                        }(this, n)
                    }
                    return e = [{
                        key: "getItem",
                        value: function(e) {
                            try {
                                var t = document.cookie;
                                if (!t) return null;
                                var n = t.split(";"),
                                    r = !0,
                                    o = !1,
                                    i = void 0;
                                try {
                                    for (var a, u = n[Symbol.iterator](); !(r = (a = u.next()).done); r = !0) {
                                        var c, s = (c = a.value.split("="), function(e) {
                                                if (Array.isArray(e)) return e
                                            }(c) || function(e, t) {
                                                var n, r, o = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                                                if (null != o) {
                                                    var i = [],
                                                        a = !0,
                                                        u = !1;
                                                    try {
                                                        for (o = o.call(e); !(a = (n = o.next()).done) && (i.push(n.value), !t || i.length !== t); a = !0);
                                                    } catch (e) {
                                                        u = !0, r = e
                                                    } finally {
                                                        try {
                                                            a || null == o.return || o.return()
                                                        } finally {
                                                            if (u) throw r
                                                        }
                                                    }
                                                    return i
                                                }
                                            }(c, 2) || function(e, t) {
                                                if (e) {
                                                    if ("string" == typeof e) return tC(e, t);
                                                    var n = Object.prototype.toString.call(e).slice(8, -1);
                                                    if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                                                    if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return tC(e, t)
                                                }
                                            }(c, 2) || function() {
                                                throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                            }()),
                                            l = s[0],
                                            f = s[1];
                                        if (l.trim() === e) return f.trim()
                                    }
                                } catch (e) {
                                    o = !0, i = e
                                } finally {
                                    try {
                                        r || null == u.return || u.return()
                                    } finally {
                                        if (o) throw i
                                    }
                                }
                            } catch (e) {}
                            return null
                        }
                    }, {
                        key: "setItem",
                        value: function(e, t, n) {
                            if ("number" == typeof n) {
                                this.setItem(e, t, {
                                    expires: n
                                });
                                return
                            }
                            try {
                                var r = ["".concat(e, "=").concat(t)];
                                if ((null == n ? void 0 : n.maxAge) && r.push("Max-Age=".concat(n.maxAge)), null == n ? void 0 : n.expires) {
                                    var o = new Date(n.expires).toUTCString();
                                    r.push("Expires=".concat(o))
                                }(null == n ? void 0 : n.path) && r.push("Path=".concat(n.path)), (null == n ? void 0 : n.domain) && r.push("Domain=".concat(n.domain)), (null == n ? void 0 : n.secure) && r.push("Secure"), document.cookie = r.join(";")
                            } catch (e) {}
                        }
                    }, {
                        key: "removeItem",
                        value: function(e) {
                            this.setItem(e, "", {
                                expires: -1
                            })
                        }
                    }], tP(n.prototype, e), t && tP(n, t), n
                }();

                function tA(e, t) {
                    (null == t || t > e.length) && (t = e.length);
                    for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
                    return r
                }

                function tT(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r)
                    }
                }
                var tx = "_kclid",
                    tE = function() {
                        var e, t;

                        function n(e) {
                            var t, r;
                            ! function(e, t) {
                                if (!(e instanceof t)) throw TypeError("Cannot call a class as a function")
                            }(this, n), t = "storage", r = void 0, t in this ? Object.defineProperty(this, t, {
                                value: r,
                                enumerable: !0,
                                configurable: !0,
                                writable: !0
                            }) : this[t] = r, this.storage = e
                        }
                        return e = [{
                            key: "get",
                            value: function() {
                                var e, t = this.storage.getItem(tx);
                                if (!t) return null;
                                var n = (e = t.split("!"), function(e) {
                                    if (Array.isArray(e)) return e
                                }(e) || function(e, t) {
                                    var n, r, o = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
                                    if (null != o) {
                                        var i = [],
                                            a = !0,
                                            u = !1;
                                        try {
                                            for (o = o.call(e); !(a = (n = o.next()).done) && (i.push(n.value), !t || i.length !== t); a = !0);
                                        } catch (e) {
                                            u = !0, r = e
                                        } finally {
                                            try {
                                                a || null == o.return || o.return()
                                            } finally {
                                                if (u) throw r
                                            }
                                        }
                                        return i
                                    }
                                }(e, 3) || function(e, t) {
                                    if (e) {
                                        if ("string" == typeof e) return tA(e, t);
                                        var n = Object.prototype.toString.call(e).slice(8, -1);
                                        if ("Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n) return Array.from(n);
                                        if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return tA(e, t)
                                    }
                                }(e, 3) || function() {
                                    throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                                }())[2];
                                return null != n ? n : null
                            }
                        }, {
                            key: "set",
                            value: function(e) {
                                this.storage.setItem(tx, "kclid!".concat(Date.now(), "!").concat(e), Date.now() + 7776e6)
                            }
                        }, {
                            key: "remove",
                            value: function() {
                                this.storage.removeItem(tx)
                            }
                        }], tT(n.prototype, e), t && tT(n, t), n
                    }();
                return e.entry = function() {
                    try {
                        var e, t, n, r, o, i, a, u, c, s, l, f, v, d;
                        return {
                            module: (r = {}, o = new w(new A(function() {
                                return window.sessionStorage
                            })), i = new A(function() {
                                return window.localStorage
                            }), a = new e9(i), u = new tn(a), c = new ti(a), s = new tE(new tI), l = new tl(window), f = new tS, (v = new tb(f, u, s)).storeAdFitConversionData(), (d = function(n) {
                                try {
                                    if ("number" == typeof n) M(ta);
                                    else if ("string" != typeof n || !(n.trim().length > 0)) throw new ex(tu);
                                    if (n in r) return r[n];
                                    e || (e = new ez), t || (t = new e8);
                                    var i = new eH(String(n), o, c, v),
                                        a = new W(i),
                                        u = new eW(e, a),
                                        s = new e1(t, a),
                                        f = new m({
                                            standardEventTracker: a,
                                            naverCheckoutEventTracker: u,
                                            paycoCheckoutEventTracker: s
                                        });
                                    return r[n] = f, l.addTrack(n), a.addListener("eventCall", function(e, t) {
                                        l.addEventCall({
                                            eventCode: e,
                                            trackId: n,
                                            parameters: t
                                        })
                                    }), f
                                } catch (e) {
                                    throw ex.capture(e, {
                                        trackId: n
                                    }), e
                                }
                            }).setKakaoAccountId = function(e) {
                                return o.setKakaoAccountId(e), d
                            }, d.getKakaoAccountId = function() {
                                return o.getKakaoAccountId()
                            }, d.setEncryptedKakaoAccountId = function(e) {
                                return o.setEncryptedKakaoAccountId(e), d
                            }, d.getEncryptedKakaoAccountId = function() {
                                return o.getEncryptedKakaoAccountId()
                            }, d.setIDFV = function(e) {
                                return o.setIDFV(e), d
                            }, d.getIDFV = function() {
                                return o.getIDFV()
                            }, d.setServiceOrigin = function(e) {
                                return o.setServiceOrigin(e), d
                            }, d.getServiceOrigin = function() {
                                return o.getServiceOrigin()
                            }, d.hasServiceOrigin = function() {
                                return o.hasServiceOrigin()
                            }, d.setAdId = function(e) {
                                return o.setAdId(e), d
                            }, d.getAdId = function() {
                                return o.getAdId()
                            }, d.setLimitAdTrackingEnabled = function(e) {
                                return o.setLimitAdTrackingEnabled(e), d
                            }, d.isLimitAdTrackingEnabled = function() {
                                return o.isLimitAdTrackingEnabled()
                            }, d.getBidId = function() {
                                return u.get()
                            }, d.getAid = function() {
                                var e;
                                return null !== (n = null === (e = c.get()) || void 0 === e ? void 0 : e.aid) && void 0 !== n ? n : null
                            }, d)
                        }
                    } catch (e) {
                        throw ex.capture(e), e
                    }
                }, e
            })({}).entry();
            self.kakaoPixel = e.module
        }
    } catch (e) {}
}();