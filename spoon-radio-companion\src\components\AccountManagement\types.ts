export interface User {
  id: string;
  displayName: string;
  email: string;
  password: string;
  joinDate: string;
  lastLogin: string;
  status: 'active' | 'inactive' | 'suspended';
  role: 'admin' | 'user';
  currentIP?: string; // 현재 사용자의 IP 주소
}

export interface SpoonTokenData {
  id: string; // IP 주소
  token: string;
  refreshToken?: string;
  username: string;
  userId: string;
  timestamp: number;
  userIP: string;
  user: {
    id: string;
    nickname: string;
    tag: string;
    profile_url: string;
  };
  linkedUserId?: string;
  linkedAt?: any;
  createdAt?: any;
  lastUpdated?: any;
  originalData?: any; // 확장프로그램에서 가져온 원본 사용자 데이터
}

export interface JWTDecoded {
  header: any;
  payload: any;
  signature: string;
  isValid: boolean;
  isExpired: boolean;
  error?: string;
}

export interface UnifiedData {
  id: string;
  type: 'user' | 'token';
  userAccount?: User;
  spoonToken?: SpoonTokenData;
  spoonTokens?: SpoonTokenData[]; // 연결된 토큰들 (관리자용)
  connectionStatus: 'linked' | 'unlinked';
  searchableText: string;
  profileUrl: string; // 표시용 프로필 URL
  displayName: string; // 표시용 이름
  subtitle: string; // 표시용 부제목
  lastActivity: string; // 마지막 활동 시간
} 