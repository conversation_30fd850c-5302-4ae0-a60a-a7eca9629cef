import React from 'react'
import { useTranslation } from 'react-i18next'

export const PrivacyPolicy: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <p className="text-gray-700 dark:text-gray-300 mb-6">{t('signup.privacy.intro')}</p>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.privacy.collection.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          {Object.values(t('signup.privacy.collection.items', { returnObjects: true })).map(
            (item: string, index: number) => (
              <li key={index}>{item}</li>
            )
          )}
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.privacy.purpose.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          {Object.values(t('signup.privacy.purpose.items', { returnObjects: true })).map(
            (item: string, index: number) => (
              <li key={index}>{item}</li>
            )
          )}
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.privacy.retention.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          {Object.values(t('signup.privacy.retention.items', { returnObjects: true })).map(
            (item: string, index: number) => (
              <li key={index}>{item}</li>
            )
          )}
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.privacy.notice.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-2">
          {t('signup.privacy.notice.content')}
        </p>
        <p className="text-gray-700 dark:text-gray-300">{t('signup.privacy.notice.activity')}</p>
      </section>

      <p className="text-sm text-gray-500 dark:text-gray-400 mt-6">
        {t('signup.privacy.effectiveDate')}
      </p>
    </div>
  )
}
