import { HttpClient } from './http.client'
import { Country, CountryNumber, CountryType } from '../const/country.const'
import { ApiUrls } from '../struct/api.struct'
import { SpoonCountry } from '../struct/spoon.struct'
import { ApiClient } from './api.client'
import { SignInRequestData } from '../dto/auth.dto'
import { SnsValueType } from '../const/sns-type.const'

export class Spoon {
  public urls!: ApiUrls
  private httpClient!: HttpClient
  public api!: ApiClient
  public token!: string
  public refreshToken!: string

  constructor(public country: CountryType = Country.KOREA) {
    this.httpClient = new HttpClient('https://www.spooncast.net/')
  }

  async initUrlsInfo() {
    this.urls = await this.httpClient.request<ApiUrls>(
      `/config/api/${this.country}.json`,
      {
        method: 'GET',
        params: {
          ts: Date.now()
        }
      },
      ApiUrls
    )
    if (!this.urls.authUrl) {
      this.urls.authUrl = `https://${this.country}-auth.spooncast.net`
    }
  }

  async getClientCountry() {
    const response = await this.httpClient.request<SpoonCountry>(
      '/country',
      {
        method: 'GET'
      },
      SpoonCountry
    )
    this.country = response.code.toLowerCase() as CountryType
  }

  async init() {
    await this.getClientCountry()
    await this.initUrlsInfo()
    this.api = new ApiClient(new HttpClient(this.urls.api))
  }

  async initToken(sns_id: string | number, password: string, sns_type: SnsValueType) {
    const code = CountryNumber[this.country.toUpperCase() as keyof typeof CountryNumber]
    const res = await this.httpClient.request<{ data: { jwt: string; refreshToken: string } }>(
      `${this.urls.authUrl}/tokens/`,
      {
        method: 'POST',
        body: {
          auth_data: {
            act_type: sns_type,
            password,
            msisdn: Number(code + sns_id)
          },
          device_unique_id: this.httpClient.userAgent.toLowerCase()
        }
      }
    )
    this.token = res.data.jwt
    this.refreshToken = res.data.refreshToken
    this.setToken(this.token)
  }

  setToken(token: string) {
    this.api.instance.httpClient.appendBaseConfig({
      headers: {
        Authorization: `Bearer ${token}`
      }
    })
  }
}
