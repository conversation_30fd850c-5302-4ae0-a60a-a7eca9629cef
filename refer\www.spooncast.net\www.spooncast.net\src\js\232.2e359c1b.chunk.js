(window["webpackJsonpspooncast-web-next"] = window["webpackJsonpspooncast-web-next"] || []).push([
    [232], {
        2565: function(e, t, n) {
            "use strict";
            n.r(t);
            var a, s, r, c, o, i, l = n(0),
                u = n.n(l),
                b = n(6),
                m = n(69),
                p = n(5),
                d = n(16),
                j = n(4);
            const O = j.d.div(a || (a = Object(p.a)(["\n  ", "\n  width:100%;\n\n  position: fixed;\n  bottom: 88px;\n  left: 0;\n\n  z-index: 20;\n  text-align: center;\n  cursor: default;\n  pointer-events: none;\n\n  ", "\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("center", "center")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object(j.c)(s || (s = Object(p.a)(["\n      bottom: 64px;\n    "]))))
                }),
                f = j.d.div(r || (r = Object(p.a)(["\n  min-width: 420px;\n  padding: 15px;\n  border: 2px solid ", ";\n  border-radius: 4px;\n  background-color: ", ";\n  opacity: 0;\n  transform: translateY(25%);\n  transition: opacity 0.5s cubic-bezier(0.25, 0.8, 0.25, 1),\n    transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);\n\n  ", "\n\n  // toast가 보이면 transition 제거\n  ", "\n\n    ", "\n"])), d.a.light.primary.deep_alive_orange_70, e => {
                    let {
                        theme: t
                    } = e;
                    return t.colors.fill.primary.pressed
                }, e => {
                    let {
                        $on: t
                    } = e;
                    return t && Object(j.c)(c || (c = Object(p.a)(["\n      opacity: 1;\n      transform: translateY(0);\n    "])))
                }, e => {
                    let {
                        $isToastVisible: t
                    } = e;
                    return t && Object(j.c)(o || (o = Object(p.a)(["\n      transition: none;\n    "])))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object(j.c)(i || (i = Object(p.a)(["\n      flex: 1;\n      min-width: auto;\n      margin: 0 ", ";\n    "])), e => {
                        let {
                            theme: t
                        } = e;
                        return t.layout.spacing.s
                    }))
                });
            var g = n(93);
            const w = () => {
                const e = Object(b.a)(),
                    {
                        toastMessage: t,
                        callback: n
                    } = Object(b.b)(e => ({
                        toastMessage: e.toast.message,
                        callback: e.toast.callback
                    })),
                    [a, s] = Object(l.useState)(!1),
                    [r, c] = Object(l.useState)(""),
                    o = Object(l.useRef)(null),
                    i = Object(l.useRef)(null),
                    u = () => {
                        o.current && (clearTimeout(o.current), o.current = null)
                    },
                    m = () => {
                        i.current && (clearTimeout(i.current), i.current = null)
                    },
                    p = Object(l.useCallback)(() => {
                        u(), m(), o.current = setTimeout(() => {
                            e(Object(g.closeToast)()), u()
                        }, 3500)
                    }, [e]),
                    d = Object(l.useCallback)(() => {
                        u(), m(), i.current && (i.current = setTimeout(() => {
                            c(""), m()
                        }, 500))
                    }, []),
                    j = Object(l.useCallback)(() => {
                        n && n();
                        c(t), s(!1), p()
                    }, [n, p, t]);
                return Object(l.useEffect)(() => {
                    if (t) return s(!0), void j();
                    d()
                }, [d, j, t]), {
                    message: r,
                    isToastVisible: a
                }
            };
            t.default = () => {
                const {
                    toastMessage: e
                } = Object(b.b)(e => ({
                    toastMessage: e.toast.message
                })), {
                    message: t,
                    isToastVisible: n
                } = w();
                return u.a.createElement(O, null, u.a.createElement(f, {
                    $on: !(!e || !t),
                    $isToastVisible: n
                }, u.a.createElement(m.a, {
                    value: t,
                    dangerouslySetInnerHTML: {
                        __html: t
                    },
                    variant: "s400",
                    asElement: "p",
                    color: "white"
                })))
            }
        }
    }
]);
//# sourceMappingURL=232.2e359c1b.chunk.js.map