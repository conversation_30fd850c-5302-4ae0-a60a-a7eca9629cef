﻿import { dataMigration } from './github-to-firebase-migration';
import { firebaseAuthObserver } from './firebase-auth-observer';
import { setPersistence, browserLocalPersistence } from 'firebase/auth';
import { auth } from '../main';

/**
 * Firebase 초기화 및 마이그레이션 클래스
 */
export class FirebaseInitializer {
  private static initialized = false;
  
  /**
   * Firebase 관련 모든 초기화 작업을 수행합니다.
   */
  public static async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    try {
      console.log('🔄 Firebase 초기화 시작...');
      
      // 1. Firebase 인증 지속성 설정
      await setPersistence(auth, browserLocalPersistence);
      console.log('✅ Firebase 인증 지속성 설정 완료');
      
      // 2. 인증 관찰자 초기화
      await firebaseAuthObserver.initialize();
      console.log('✅ Firebase 인증 관찰자 초기화 완료');
      
      // 3. 보안 규칙 안내 (콘솔에만 출력)
      console.log('🔒 Firebase 보안 규칙 안내:');
      dataMigration.printFirestoreSecurityRules();
      
      this.initialized = true;
      console.log('✅ Firebase 초기화 완료');
    } catch (error) {
      console.error('❌ Firebase 초기화 오류:', error);
    }
  }
  
  /**
   * 기본 관리자 계정을 Firebase로 마이그레이션합니다.
   * @param adminPassword 관리자 계정의 임시 비밀번호
   */
  public static async migrateAdminAccount(adminPassword: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 관리자 계정 마이그레이션 시작...');
      const result = await dataMigration.migrateAdminUser(adminPassword);
      return result;
    } catch (error: any) {
      console.error('❌ 관리자 계정 마이그레이션 오류:', error);
      return { success: false, message: error.message || '알 수 없는 오류가 발생했습니다.' };
    }
  }
  
  /**
   * 승인된 모든 사용자를 Firebase로 마이그레이션합니다.
   * @param defaultPassword 사용자 계정의 임시 비밀번호
   */
  public static async migrateAllUsers(defaultPassword: string): Promise<{ success: boolean; message: string; count: number }> {
    try {
      console.log('🔄 모든 사용자 마이그레이션 시작...');
      const result = await dataMigration.migrateAllApprovedUsers(defaultPassword);
      return { 
        success: result.success, 
        message: result.message, 
        count: result.migratedCount 
      };
    } catch (error: any) {
      console.error('❌ 사용자 마이그레이션 오류:', error);
      return { 
        success: false, 
        message: error.message || '알 수 없는 오류가 발생했습니다.', 
        count: 0 
      };
    }
  }
}

// 앱 시작 시 Firebase 초기화
FirebaseInitializer.initialize().catch(console.error);