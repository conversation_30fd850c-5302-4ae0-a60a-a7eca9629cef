<template>
  <v-card class="mx-auto mt-10 modern-card" max-width="450" elevation="12">
    <div class="card-header">
      <div class="header-icon">
        <v-icon size="32" color="white">mdi-account-plus-outline</v-icon>
      </div>
      <h2 class="header-title">회원가입</h2>
      <p class="header-subtitle">새로운 계정을 만들어보세요</p>
      
      <!-- 뒤로가기 버튼 -->
      <v-btn
        @click="$emit('switch-to-login')"
        icon
        color="white"
        class="back-button"
        title="로그인으로 돌아가기"
      >
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
    </div>
    
    <v-card-text class="pt-8 px-8">
      <v-form ref="form" v-model="valid" lazy-validation @submit.prevent="handleRegister">
        <div class="input-group">
          <v-text-field
            v-model="form.name"
            :rules="nameRules"
            label="이름"
            prepend-inner-icon="mdi-account-outline"
            solo
            rounded
            required
            class="modern-input"
            background-color="rgba(0,0,0,0.03)"
            flat
          ></v-text-field>
        </div>

        <div class="input-group">
          <v-text-field
            v-model="form.email"
            :rules="emailRules"
            label="이메일"
            prepend-inner-icon="mdi-email-outline"
            solo
            rounded
            required
            class="modern-input"
            background-color="rgba(0,0,0,0.03)"
            flat
          ></v-text-field>
        </div>

        <div class="input-group">
          <v-text-field
            v-model="form.password"
            :rules="passwordRules"
            label="비밀번호"
            prepend-inner-icon="mdi-lock-outline"
            :type="showPassword ? 'text' : 'password'"
            :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append="showPassword = !showPassword"
            solo
            rounded
            required
            class="modern-input"
            background-color="rgba(0,0,0,0.03)"
            flat
          ></v-text-field>
        </div>

        <div class="input-group">
          <v-text-field
            v-model="form.confirmPassword"
            :rules="confirmPasswordRules"
            label="비밀번호 확인"
            prepend-inner-icon="mdi-lock-check-outline"
            :type="showConfirmPassword ? 'text' : 'password'"
            :append-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append="showConfirmPassword = !showConfirmPassword"
            solo
            rounded
            required
            class="modern-input"
            background-color="rgba(0,0,0,0.03)"
            flat
          ></v-text-field>
        </div>

        <v-fade-transition>
          <v-alert
            v-if="message"
            :type="messageType"
            dense
            rounded
            class="mb-6 modern-alert"
          >
            {{ message }}
          </v-alert>
        </v-fade-transition>
      </v-form>
    </v-card-text>

    <v-card-actions class="px-8 pb-8">
      <v-btn
        :disabled="!valid || loading"
        :loading="loading"
        @click="handleRegister"
        color="primary"
        x-large
        rounded
        block
        class="modern-button elevation-2"
        elevation="0"
      >
        <v-icon left>mdi-account-plus</v-icon>
        회원가입 요청
      </v-btn>
    </v-card-actions>

    <v-divider class="mx-6"></v-divider>

    <v-card-actions class="px-8 pt-4 pb-8">
      <v-btn
        @click="$emit('switch-to-login')"
        color="grey"
        text
        rounded
        block
        class="modern-link-button"
      >
        이미 계정이 있으신가요? 로그인
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { authService } from '@/plugins/auth-service';

@Component
export default class Register extends Vue {
  public valid = false;
  public loading = false;
  public showPassword = false;
  public showConfirmPassword = false;
  public message = '';
  public messageType: 'success' | 'error' | 'warning' | 'info' = 'info';

  public form = {
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  };

  public nameRules = [
    (v: string) => !!v || '이름을 입력해주세요.',
    (v: string) => (v && v.length >= 2) || '이름은 2글자 이상이어야 합니다.'
  ];

  public emailRules = [
    (v: string) => !!v || '이메일을 입력해주세요.',
    (v: string) => /.+@.+\..+/.test(v) || '올바른 이메일 형식이 아닙니다.'
  ];

  public passwordRules = [
    (v: string) => !!v || '비밀번호를 입력해주세요.',
    (v: string) => (v && v.length >= 6) || '비밀번호는 6글자 이상이어야 합니다.',
    (v: string) => /^(?=.*[a-zA-Z])(?=.*\d)/.test(v) || '영문과 숫자를 조합해주세요.'
  ];

  public confirmPasswordRules = [
    (v: string) => !!v || '비밀번호 확인을 입력해주세요.',
    (v: string) => v === this.form.password || '비밀번호가 일치하지 않습니다.'
  ];

  public async handleRegister() {
    if (!(this.$refs.form as any).validate()) {
      return;
    }

    this.loading = true;
    this.message = '';

    try {
      await authService.register(this.form.email, this.form.password, this.form.name);
      this.messageType = 'success';
      this.message = '회원가입 요청이 성공적으로 전송되었습니다. 관리자 승인을 기다려주세요.';
      
      // 성공 시 폼 초기화
      this.form = {
        name: '',
        email: '',
        password: '',
        confirmPassword: ''
      };
      (this.$refs.form as any).resetValidation();

      // 5초 후 로그인 화면으로 전환
      setTimeout(() => {
        this.$emit('switch-to-login');
      }, 3000);
    } catch (error: any) {
      console.error('회원가입 오류:', error);
      this.messageType = 'error';
      if (error.code === 'auth/email-already-in-use') {
        this.message = '이미 사용 중인 이메일 주소입니다.';
      } else if (error.code === 'auth/weak-password') {
        this.message = '비밀번호는 6자 이상이어야 합니다.';
      } else {
        this.message = `회원가입 실패: ${error.message}`;
      }
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
.modern-card {
  border-radius: 24px !important;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px 24px 24px;
  text-align: center;
  color: white;
  position: relative;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px;
  letter-spacing: -0.5px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

/* 뒤로가기 버튼 스타일 */
.back-button {
  position: absolute !important;
  top: 16px !important;
  left: 16px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.05) !important;
}

.input-group {
  margin-bottom: 20px;
}

.modern-input {
  margin-bottom: 0 !important;
}

.modern-input .v-input__slot {
  border-radius: 50px !important;
  min-height: 56px !important;
  padding: 0 20px !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-input .v-input__slot:hover {
  border-color: rgba(102, 126, 234, 0.3) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.modern-input.v-input--is-focused .v-input__slot {
  border-color: #667eea !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(102, 126, 234, 0.15) !important;
}

.modern-input .v-text-field__details {
  padding: 8px 20px 0 !important;
}

.modern-button {
  height: 56px !important;
  border-radius: 28px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.modern-button:active {
  transform: translateY(0);
}

.modern-link-button {
  height: 48px !important;
  border-radius: 24px !important;
  color: #666 !important;
  font-weight: 500 !important;
  text-transform: none !important;
  transition: all 0.3s ease;
}

.modern-link-button:hover {
  background: rgba(102, 126, 234, 0.05) !important;
  color: #667eea !important;
}

.modern-alert {
  border-radius: 16px !important;
  border: none !important;
  backdrop-filter: blur(10px);
}

.v-divider {
  opacity: 0.3;
}

/* 반응형 디자인 */
@media (max-width: 600px) {
  .modern-card {
    margin: 20px 16px !important;
    max-width: none !important;
  }
  
  .card-header {
    padding: 24px 16px 20px;
  }
  
  .header-title {
    font-size: 20px;
  }
  
  .v-card-text {
    padding: 24px 20px !important;
  }
  
  .v-card-actions {
    padding: 0 20px 24px !important;
  }
}

/* 애니메이션 */
.modern-card {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-group {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

.input-group:nth-child(1) { animation-delay: 0.1s; }
.input-group:nth-child(2) { animation-delay: 0.2s; }
.input-group:nth-child(3) { animation-delay: 0.3s; }
.input-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 