<!--
 * Index.vue
 * Created on Sat Jul 18 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
-->
<template>
	<!-- S: Login Dialog -->
	<div style="position: relative;">
		<v-row class="ma-0" style="height: 100vh;">
			<!-- <v-col cols="6" class="blue-grey lighten-5"  style="height: 100%;">
				<v-row style="height: 100%;" align="center">
					<v-col cols="12" class="text-center">
						<img @click="upEGG" alt="" src="../../assets/sopia-sd.png" style="width: 100%">
					</v-col>
				</v-row>
			</v-col> -->
			<v-col cols="8" lg="6" offset="2" offset-lg="3" align="center" style="position: relative">
				<v-scroll-x-reverse-transition>
					<login-spoon v-if="spoonShow" @logon="spoonLogon"/>
				</v-scroll-x-reverse-transition>
			</v-col>
			<v-col cols="12" class="text-center">
			</v-col>
		</v-row>
	</div>
	<!-- E: Login Dialog -->
</template>
<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator';
import { UserDto } from '@sopia-bot/api-dto';
import { LogonUser } from '@sopia-bot/core';

import LoginSpoon from '@/views/Login/LoginSpoonNew.vue';
import GlobalMixins from '@/plugins/mixins';
const { ipcRenderer } = window.require('electron');

@Component({
	components: {
		LoginSpoon,
	},
})
export default class Login extends Mixins(GlobalMixins) {

	@Prop(Boolean) public value!: boolean;
	public sopiaShow: boolean = false;
	public spoonShow: boolean = true;

	public sopiaUser: UserDto = {
		id: 'auto',
		user_id: Date.now(),
		name: 'Auto User',
		gender: 'M',
		spoon_id: '0'
	} as unknown as UserDto;
	public countEGG: number = 0;
	public dialog: boolean = false;

	public upEGG() {
		this.countEGG += 1;
		if ( this.countEGG > 5 ) {
			this.countEGG = 0;
			ipcRenderer.send('open-dev-tools');
		}
	}

	public created() {
		this.$evt.$on('login:skip-sopia-login', (user: UserDto) => {
			this.sopiaUser = user;
		});
		const sopia = this.$cfg.get('auth.sopia');
		if ( sopia ) {
			this.$evt.$emit('login:skip-sopia-login', sopia);
		}
	}

	public beforeUnmount() {
		this.$evt.$off('login:skip-sopia-login');
	}

	public async spoonLogon(user: LogonUser) {
		this.$logger.info('Spoon login user', user);
		this.sopiaUser.spoon_id = user.id.toString();
		this.sopiaUser.name = user.tag;
		this.sopiaUser.gender = user.gender;

		try {
			await this.$api.setUserInfo(this.sopiaUser);
		} catch {
			await this.$swal({
				icon: 'error',
				title: this.$t('error'),
				html: this.$t('app.login.unauthorized-logout'),
			});
			window.logout();
			return;
		}

		if ( +this.sopiaUser.spoon_id !== user.id ) {
			await this.$swal({
				icon: 'warning',
				title: this.$t('msg.alert'),
				html: this.$t('app.login.error.diff_id'),
				confirmButtonText: this.$t('confirm'),
			});
			return;
		}

		const { id, token, refresh_token } = this.$sopia.logonUser;
		this.$cfg.set('auth.spoon.id', id);
		this.$cfg.set('auth.spoon.token', token);
		this.$cfg.set('auth.spoon.refresh_token', refresh_token);
		this.$cfg.set('auth.sopia', this.sopiaUser);
		this.$cfg.save();
		console.log('스푼 로그인 완료 - 인증 정보 저장됨:', id, token);

		this.loginSpoon(this.$sopia.logonUser);
	}

	public loginSpoon(user: LogonUser) {
		this.$emit('input', false);
		this.$evt.$emit('user', user);
		this.$store.commit('user', user);
		this.sopiaShow = false;
		this.spoonShow = false;

		this.$api.activityLog('logon');
		
		console.log('로그인 성공 - 홈 화면으로 이동');
		this.$router.push('/');
	}

	public removeAppCfg() {
		this.$cfg.delete('auth');
		this.$cfg.save();
		setTimeout(() => {
			window.location.reload();
		}, 100);
	}

}
</script>
