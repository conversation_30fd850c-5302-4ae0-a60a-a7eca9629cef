<template>
	<v-dialog v-model="dialog" max-width="800" persistent>
		<v-card class="compose-card" elevation="24">
			<!-- 헤더 섹션 -->
			<div class="compose-header">
				<div class="header-content">
					<div class="header-icon">
						<v-icon size="32" color="white">mdi-email-edit</v-icon>
					</div>
					<div class="header-text">
						<h2 class="header-title">편지 쓰기</h2>
						<p class="header-subtitle">관리자에게 메시지를 보내세요</p>
					</div>
				</div>
				<v-btn 
					icon 
					large 
					color="white" 
					@click="close" 
					class="close-button"
					elevation="2"
				>
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</div>

			<v-divider class="divider-gradient"></v-divider>

			<!-- 메인 콘텐츠 -->
			<v-card-text class="main-content">
				<!-- 작성 팁 -->
				<v-alert 
					color="info" 
					border="left" 
					elevation="2" 
					colored-border
					class="writing-tip"
				>
					<div class="tip-content">
						<v-icon color="info" class="tip-icon">mdi-lightbulb-outline</v-icon>
						<div class="tip-text">
							<h4>작성 팁</h4>
							<p>명확하고 구체적으로 작성하시면 더 빠른 답변을 받을 수 있습니다.</p>
						</div>
					</div>
				</v-alert>

				<!-- 메시지 폼 -->
				<v-card class="form-card" elevation="8">
					<v-card-text class="form-content">
						<v-form ref="messageForm" v-model="valid" lazy-validation>
							<!-- 받는 사람 -->
							<div class="recipient-section">
								<div class="recipient-card">
									<v-avatar size="48" color="primary">
										<v-icon color="white">mdi-account-tie</v-icon>
									</v-avatar>
									<div class="recipient-info">
										<h4>관리자</h4>
										<p>시스템 관리자에게 메시지를 전송합니다.</p>
									</div>
								</div>
							</div>

							<!-- 제목 입력 -->
							<div class="form-field">
								<v-text-field
									v-model="form.subject"
									:rules="subjectRules"
									label="제목"
									counter="100"
									maxlength="100"
									required
									outlined
									dense
									class="custom-field"
									prepend-inner-icon="mdi-format-title"
								></v-text-field>
							</div>

							<!-- 내용 입력 -->
							<div class="form-field">
								<v-textarea
									v-model="form.content"
									:rules="contentRules"
									label="내용"
									counter="1000"
									maxlength="1000"
									rows="8"
									required
									outlined
									auto-grow
									class="custom-field"
									prepend-inner-icon="mdi-text"
								></v-textarea>
							</div>
						</v-form>
					</v-card-text>
				</v-card>

				<!-- 액션 버튼 -->
				<div class="action-section">
					<v-btn 
						large 
						outlined 
						color="grey" 
						@click="close" 
						class="action-btn cancel-btn"
						:disabled="isSending"
					>
						<v-icon left>mdi-close</v-icon>
						취소
					</v-btn>

					<v-btn 
						large 
						color="primary" 
						@click="sendMessage" 
						:loading="isSending"
						:disabled="!valid || isSending"
						class="action-btn send-btn"
						elevation="2"
					>
						<v-icon left>mdi-send</v-icon>
						편지 보내기
					</v-btn>
				</div>
			</v-card-text>
		</v-card>
	</v-dialog>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { mailboxService } from '@/plugins/mailbox-service';

@Component
export default class MessageCompose extends Mixins(GlobalMixins) {
	@Prop({ type: Boolean, default: false }) value!: boolean;

	public valid: boolean = false;
	public isSending: boolean = false;
	public form = {
		subject: '',
		content: ''
	};

	public subjectRules = [
		(v: string) => !!v || '제목을 입력해주세요.',
		(v: string) => (v && v.length <= 100) || '제목은 100자 이하로 입력해주세요.'
	];

	public contentRules = [
		(v: string) => !!v || '내용을 입력해주세요.',
		(v: string) => (v && v.length <= 1000) || '내용은 1000자 이하로 입력해주세요.'
	];

	public get dialog() {
		return this.value;
	}

	public set dialog(value: boolean) {
		this.$emit('input', value);
	}

	public close() {
		this.dialog = false;
		this.resetForm();
	}

	public resetForm() {
		this.form = {
			subject: '',
			content: ''
		};
		this.valid = false;
		if (this.$refs.messageForm) {
			(this.$refs.messageForm as any).resetValidation();
		}
	}

	public async sendMessage() {
		if (!this.valid) {
			(this.$refs.messageForm as any).validate();
			return;
		}

		this.isSending = true;
		try {
			// 편지 전송 시뮬레이션 (실제 구현 시 mailboxService.sendMessage 구현 필요)
			await new Promise(resolve => setTimeout(resolve, 1000));
			
			this.showSuccess('편지가 성공적으로 전송되었습니다.');
			this.$emit('message-sent');
			this.close();
		} catch (error) {
			console.error('편지 전송 오류:', error);
			this.showError('편지 전송 중 오류가 발생했습니다.');
		} finally {
			this.isSending = false;
		}
	}

	private showSuccess(message: string) {
		this.$emit('success', message);
	}

	private showError(message: string) {
		this.$emit('error', message);
	}
}
</script>

<style scoped>
.compose-card {
	border-radius: 20px !important;
	overflow: hidden;
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 헤더 스타일 */
.compose-header {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	padding: 24px 32px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	overflow: hidden;
}

.compose-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pen" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M10,30 L20,20 L30,30 L20,40 Z M25,15 L35,25" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></pattern></defs><rect width="100" height="100" fill="url(%23pen)"/></svg>');
	opacity: 0.3;
}

.header-content {
	display: flex;
	align-items: center;
	z-index: 1;
	position: relative;
}

.header-icon {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16px;
	padding: 16px;
	margin-right: 20px;
	backdrop-filter: blur(10px);
}

.header-text {
	color: white;
}

.header-title {
	font-size: 28px;
	font-weight: 700;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
	font-size: 16px;
	margin: 4px 0 0 0;
	opacity: 0.9;
	font-weight: 400;
}

.close-button {
	z-index: 1;
	position: relative;
	background: rgba(255, 255, 255, 0.2) !important;
	backdrop-filter: blur(10px);
}

.divider-gradient {
	height: 4px !important;
	background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
	border: none !important;
}

/* 메인 콘텐츠 */
.main-content {
	padding: 32px !important;
	background: #f8fafc;
}

/* 작성 팁 */
.writing-tip {
	margin-bottom: 24px;
	border-radius: 16px !important;
	background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
}

.tip-content {
	display: flex;
	align-items: center;
}

.tip-icon {
	margin-right: 16px;
}

.tip-text h4 {
	margin: 0 0 8px 0;
	font-weight: 600;
	color: #1565c0;
}

.tip-text p {
	margin: 0;
	color: #424242;
	font-size: 14px;
}

/* 폼 카드 */
.form-card {
	border-radius: 20px !important;
	background: white;
	margin-bottom: 24px;
}

.form-content {
	padding: 32px !important;
}

/* 받는 사람 섹션 */
.recipient-section {
	margin-bottom: 24px;
}

.recipient-card {
	display: flex;
	align-items: center;
	padding: 20px;
	background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
	border-radius: 16px;
	border: 2px solid #e8f5e8;
}

.recipient-info {
	margin-left: 16px;
}

.recipient-info h4 {
	margin: 0 0 4px 0;
	font-weight: 600;
	color: #2d3748;
}

.recipient-info p {
	margin: 0;
	color: #718096;
	font-size: 14px;
}

/* 폼 필드 */
.form-field {
	margin-bottom: 24px;
}

.custom-field {
	border-radius: 12px;
}

.custom-field >>> .v-text-field__details {
	margin-top: 8px;
}

.custom-field >>> .v-input__control {
	min-height: 56px;
}

.custom-field >>> .v-input__slot {
	border-radius: 12px !important;
}

/* 액션 섹션 */
.action-section {
	display: flex;
	justify-content: flex-end;
	gap: 16px;
	padding-top: 16px;
	border-top: 1px solid #e2e8f0;
}

.action-btn {
	border-radius: 12px !important;
	text-transform: none !important;
	font-weight: 600;
	padding: 0 32px !important;
	height: 48px !important;
}

.cancel-btn {
	background: white !important;
	border: 2px solid #e2e8f0 !important;
	color: #718096 !important;
}

.cancel-btn:hover {
	background: #f7fafc !important;
	border-color: #cbd5e0 !important;
}

.send-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.send-btn:hover {
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6) !important;
	transform: translateY(-2px);
}

/* 반응형 디자인 */
@media (max-width: 768px) {
	.compose-header {
		padding: 20px;
		flex-direction: column;
		text-align: center;
	}

	.header-content {
		margin-bottom: 16px;
	}

	.main-content {
		padding: 20px !important;
	}

	.form-content {
		padding: 20px !important;
	}

	.action-section {
		flex-direction: column;
		gap: 12px;
	}

	.action-btn {
		width: 100%;
	}

	.recipient-card {
		flex-direction: column;
		text-align: center;
	}

	.recipient-info {
		margin-left: 0;
		margin-top: 16px;
	}
}

/* 애니메이션 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.form-card {
	animation: fadeInUp 0.4s ease-out;
}

.writing-tip {
	animation: fadeInUp 0.3s ease-out;
}

.action-section {
	animation: fadeInUp 0.5s ease-out;
}
</style> 