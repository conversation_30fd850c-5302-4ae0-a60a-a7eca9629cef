<template>
  <v-container fluid class="auth-container">
    <v-row justify="center" align="center" class="fill-height">
      <v-col cols="12" md="8" lg="6">
        <!-- 로그인/회원가입 모드 전환 -->
        <v-slide-x-transition mode="out-in">
          <tamm-login
            v-if="mode === 'login'"
            @switch-to-register="mode = 'register'"
            @login-success="handleLoginSuccess"
            key="login"
          />
          <register
            v-else
            @switch-to-login="mode = 'login'"
            key="register"
          />
        </v-slide-x-transition>

        <!-- 도움말 및 정보 -->
        <div class="text-center mt-8">
          <v-row>
            <v-col cols="12" sm="6">
              <div class="info-card approval-card">
                <div class="info-icon-container">
                  <div class="info-icon">
                    <v-icon color="white" size="24">mdi-account-clock</v-icon>
                  </div>
                </div>
                <div class="info-content">
                  <h3 class="info-title">회원가입 후 관리자 승인이 필요합니다</h3>
                  <p class="info-description">승인까지 시간이 소요될 수 있습니다.</p>
                </div>
                <div class="info-decoration"></div>
              </div>
            </v-col>
            <v-col cols="12" sm="6">
              <div class="info-card security-card">
                <div class="info-icon-container">
                  <div class="info-icon">
                    <v-icon color="white" size="24">mdi-shield-lock</v-icon>
                  </div>
                </div>
                <div class="info-content">
                  <h3 class="info-title">안전한 로컬 인증</h3>
                  <p class="info-description">서버 없이 안전하게 관리됩니다.</p>
                </div>
                <div class="info-decoration"></div>
              </div>
            </v-col>
          </v-row>
        </div>

        <!-- 버전 정보 -->
        <div class="text-center mt-8">
          <div class="version-badge">
            <v-icon left color="white" size="16">mdi-tag-outline</v-icon>
            <span class="version-text">Version {{ version }}</span>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- 배경 패턴 -->
    <div class="background-pattern"></div>
  </v-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import TammLogin from './TammLogin.vue';
import Register from './Register.vue';
import { User } from '@/plugins/auth-service';

@Component({
  components: {
    TammLogin,
    Register
  }
})
export default class AuthMain extends Vue {
  public mode: 'login' | 'register' = 'login';
  public version = '1.0.0';

  public mounted() {
    // 패키지 버전 정보 가져오기 (선택사항)
    try {
      const packageInfo = require('../../../package.json');
      this.version = packageInfo.version || '1.0.0';
    } catch {
      this.version = '1.0.0';
    }
  }

  public handleLoginSuccess(user: User) {
    // 로그인 성공 시 메인 애플리케이션으로 이동
    console.log('✅ TAMM 사용자 로그인 성공:', user);
    
    // 성공 메시지 표시
    this.$swal.fire({
      icon: 'success',
      title: '로그인 성공!',
      text: '기존 Spoon 로그인 페이지로 이동합니다.',
      timer: 2000,
      showConfirmButton: false
    });
    
    // 2초 후 기존 로그인 페이지로 이동
    setTimeout(() => {
      this.$router.push('/login').catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          console.error('라우팅 오류:', err);
        }
      });
    }, 2000);
    
    // 기존 로직으로 연결 (Spoon 로그인 등)
    this.$emit('auth-success', user);
  }
}
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-image: 
    radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, white 2px, transparent 2px);
  background-size: 40px 40px;
  background-position: 0 0, 20px 20px;
  pointer-events: none;
}

.v-avatar {
  border: 4px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.display-1 {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.v-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.v-chip {
  background: rgba(255, 255, 255, 0.9) !important;
}

/* 반응형 디자인 */
@media (max-width: 600px) {
  .display-1 {
    font-size: 2rem !important;
  }
  
  .v-avatar {
    width: 80px !important;
    height: 80px !important;
  }
  
  .info-card {
    margin: 10px 0 !important;
  }
  
  .info-title {
    font-size: 16px !important;
  }
}

/* 새로운 도움말 카드 스타일 */
.info-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 24px 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 120px;
  cursor: pointer;
}

.info-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.info-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 2;
}

.info-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.approval-card .info-icon {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.security-card .info-icon {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.info-card:hover .info-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.info-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.info-title {
  font-size: 18px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.info-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.info-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  opacity: 0.1;
  transition: all 0.3s ease;
}

.approval-card .info-decoration {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.security-card .info-decoration {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.info-card:hover .info-decoration {
  transform: scale(1.2);
  opacity: 0.2;
}

/* 버전 뱃지 스타일 */
.version-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.version-badge:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.version-text {
  color: white;
  font-size: 13px;
  font-weight: 500;
  margin-left: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 애니메이션 */
.v-slide-x-transition-enter-active,
.v-slide-x-transition-leave-active {
  transition: transform 0.3s ease-in-out;
}

.v-slide-x-transition-enter {
  transform: translateX(100%);
}

.v-slide-x-transition-leave-to {
  transform: translateX(-100%);
}
</style> 