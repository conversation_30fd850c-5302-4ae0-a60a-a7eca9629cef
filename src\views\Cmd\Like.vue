<!--
 * Like.vue
 * Created on Fri Nov 27 2020
 *
 * Copyright (c) Raravel. Licensed under the MIT License.
-->
<template>
  <v-card flat class="mt-4">
    <v-card-text>
      <v-textarea
        v-model="liveLike"
        outlined
        hide-details
        auto-grow
        rows="4"
        counter="500"
        :label="$t('cmd.message')"
        :placeholder="$t('cmd.message-input')"
        class="rounded-lg"
        background-color="grey lighten-5">
      </v-textarea>
      
      <v-alert
        v-if="liveLike.length > 0"
        class="mt-4 rounded-lg"
        color="purple lighten-5"
        border="left"
        elevation="1"
        icon="mdi-information-outline">
        {{ $t('cmd.preview') || '미리보기' }}: <strong>{{ liveLike }}</strong>
      </v-alert>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import CfgLite from '@/plugins/cfg-lite-ipc';

@Component
export default class CmdLike extends Mixins(GlobalMixins) {
	public liveLike: string = '';

	public cfgPath: string = this.$path('userData', 'cmd.cfg');
	public cfg: CfgLite = new CfgLite(this.cfgPath);

	public async mounted() {
		this.liveLike = this.cfg.get('live_like') || '';
		this.$evt.$on('cmd:save', () => {
			this.cfg.set('live_like', this.liveLike);
			this.cfg.save();
		});
	}

	public beforeUnmount() {
		this.$evt.$off('cmd:save');
	}
}
</script>

<style scoped>
.v-textarea >>> .v-input__slot {
  min-height: 120px;
}
</style>
