<!--
 * FanRankingModal.vue
 * Created on 2025
 *
 * 팬 랭킹을 표시하는 모달 컴포넌트
-->
<template>
	<v-dialog
		v-model="isOpen"
		max-width="500px"
		content-class="fan-ranking-modal"
		@click:outside="closeModal"
	>
		<v-card class="fan-ranking-card">
			<!-- 헤더 -->
			<v-card-title class="d-flex justify-space-between align-center pa-4 pb-2">
				<h3 class="text-h6 custom-font">{{ userNickname }}님의 팬 랭킹</h3>
				<v-btn
					icon
					small
					@click="closeModal"
					class="close-btn"
				>
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</v-card-title>
			
			<v-divider></v-divider>
			
			<!-- 팬 목록 -->
			<v-card-text class="pa-0">
				<div 
					class="fan-list-container"
					ref="fanListContainer"
					@scroll="handleScroll"
				>
					<!-- 로딩 상태 -->
					<div v-if="loading && fanList.length === 0" class="d-flex justify-center align-center pa-6">
						<v-progress-circular 
							indeterminate 
							color="purple" 
							size="32"
						></v-progress-circular>
						<span class="ml-3 text-subtitle-1 grey--text">팬 목록 로딩 중...</span>
					</div>
					
					<!-- 팬 목록 -->
					<div v-else-if="fanList.length > 0">
						<div 
							v-for="(fan, index) in fanList" 
							:key="fan.user.id" 
							class="fan-item d-flex align-center pa-3"
							@click="goToUserPage(fan.user.id)"
						>
							<!-- 순위 -->
							<div class="rank-number mr-3">
								<span 
									class="rank-text"
									:class="{
										'rank-gold': index === 0,
										'rank-silver': index === 1,
										'rank-bronze': index === 2
									}"
								>
									{{ index + 1 }}
								</span>
							</div>
							
							<!-- 프로필 이미지 -->
							<v-avatar 
								size="50" 
								class="mr-3"
								:class="{
									'gold-border': index === 0,
									'silver-border': index === 1,
									'bronze-border': index === 2
								}"
							>
								<v-img :src="fan.user.profile_url" cover></v-img>
							</v-avatar>
							
							<!-- 사용자 정보 -->
							<div class="flex-grow-1">
								<p class="fan-nickname mb-1 custom-font">{{ fan.user.nickname }}</p>
								<p class="fan-tag text-caption grey--text mb-0">@{{ fan.user.tag }}</p>
							</div>
							
							<!-- 스푼 개수 또는 팔로워 수 -->
							<div class="count-info d-flex align-center">
								<!-- 스푼 개수가 있는 경우 (내 팬 목록) -->
								<template v-if="fan.total_spoon !== undefined">
									<v-icon color="#FFD700" size="16" class="mr-1">mdi-silverware-spoon</v-icon>
									<span class="text-subtitle-2 font-weight-medium">{{ formatSpoonCount(fan.total_spoon) }}</span>
								</template>
								<!-- 스푼 개수가 없는 경우 (다른 사람의 팬 목록) -->
								<template v-else>
									<v-icon color="#1976D2" size="16" class="mr-1">mdi-account-group</v-icon>
									<span class="text-subtitle-2 font-weight-medium">{{ formatFollowerCount(fan.user.follower_count) }}</span>
								</template>
							</div>
						</div>
						
						<!-- 추가 로딩 -->
						<div v-if="loadingMore" class="d-flex justify-center align-center pa-4">
							<v-progress-circular 
								indeterminate 
								color="purple" 
								size="24"
							></v-progress-circular>
							<span class="ml-2 text-caption grey--text">더 많은 팬 로딩 중...</span>
						</div>
						
						<!-- 모든 팬 로드 완료 -->
						<div v-if="!hasMore && !loadingMore" class="text-center pa-3">
							<span class="text-caption grey--text">모든 팬을 로드했습니다</span>
						</div>
					</div>
					
					<!-- 팬이 없는 경우 -->
					<div v-else-if="!loading" class="d-flex justify-center align-center pa-6">
						<div class="text-center">
							<v-icon size="48" color="grey lighten-1" class="mb-2">mdi-account-group-outline</v-icon>
							<p class="text-subtitle-1 grey--text">팬이 없습니다</p>
						</div>
					</div>
				</div>
			</v-card-text>
		</v-card>
	</v-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';

interface FanData {
	user: {
		id: number;
		nickname: string;
		tag: string;
		profile_url: string;
		follower_count: number;
		following_count: number;
		follow_status: number;
	};
	total_spoon?: number;
	user_plan_level: any;
}

@Component({})
export default class FanRankingModal extends Mixins(GlobalMixins) {
	@Prop({ type: Boolean, default: false })
	readonly value!: boolean;
	
	@Prop({ type: Number, required: true })
	readonly userId!: number;
	
	@Prop({ type: String, default: '' })
	readonly userNickname!: string;

	public fanList: FanData[] = [];
	public loading: boolean = false;
	public loadingMore: boolean = false;
	public hasMore: boolean = true;
	public nextCursor: string = '';
	
	get isOpen(): boolean {
		return this.value;
	}
	
	set isOpen(value: boolean) {
		this.$emit('input', value);
	}

	@Watch('value')
	onValueChanged(newValue: boolean) {
		if (newValue) {
			this.loadFanList();
		} else {
			this.resetData();
		}
	}

	public closeModal() {
		this.isOpen = false;
	}

	public resetData() {
		this.fanList = [];
		this.loading = false;
		this.loadingMore = false;
		this.hasMore = true;
		this.nextCursor = '';
	}

	public async loadFanList(loadMore: boolean = false) {
		if (this.loading || (loadMore && this.loadingMore) || (loadMore && !this.hasMore)) {
			return;
		}

		if (loadMore) {
			this.loadingMore = true;
		} else {
			this.loading = true;
			this.fanList = [];
		}

		try {
			let url = `/users/${this.userId}/top_fan/`;
			if (loadMore && this.nextCursor) {
				// cursor에서 필요한 부분만 추출
				const urlObj = new URL(this.nextCursor);
				const cursor = urlObj.searchParams.get('cursor');
				if (cursor) {
					url += `?cursor=${cursor}`;
				}
			}

			const response = await this.$sopia.api.request({
				url,
				method: 'GET',
			});

			if (response && response.res && response.res.status_code === 200) {
				const data = response.res;
				
				if (loadMore) {
					this.fanList.push(...(data.results as FanData[]));
				} else {
					this.fanList = data.results as FanData[];
				}
				
				this.hasMore = !!data.next;
				this.nextCursor = data.next || '';
			}
		} catch (error) {
			console.error('팬 목록 로드 실패:', error);
		} finally {
			this.loading = false;
			this.loadingMore = false;
		}
	}

	public handleScroll(event: Event) {
		const target = event.target as HTMLElement;
		const scrollTop = target.scrollTop;
		const scrollHeight = target.scrollHeight;
		const clientHeight = target.clientHeight;
		
		// 스크롤이 하단 근처에 도달했을 때 추가 로드
		if (scrollTop + clientHeight >= scrollHeight - 50) {
			if (this.hasMore && !this.loadingMore) {
				this.loadFanList(true);
			}
		}
	}

	public goToUserPage(userId: number) {
		this.closeModal();
		this.$router.push(`/user/${userId}`);
	}

	public formatSpoonCount(count: number): string {
		// 천 단위 구분자(콤마)를 사용한 정확한 숫자 표시
		return count.toLocaleString();
	}

	public formatFollowerCount(count: number): string {
		// 천 단위 구분자(콤마)를 사용한 정확한 숫자 표시
		return count.toLocaleString();
	}
}
</script>

<style scoped>
.fan-ranking-modal .v-card {
	border-radius: 16px !important;
}

.fan-ranking-card {
	max-height: 70vh;
	overflow: hidden;
}

.fan-list-container {
	max-height: 50vh;
	overflow-y: auto;
	padding: 0;
}

.fan-item {
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.fan-item:hover {
	background-color: #f8f9fa;
}

.fan-item:last-child {
	border-bottom: none;
}

.rank-number {
	width: 30px;
	text-align: center;
}

.rank-text {
	font-weight: 700;
	font-size: 1rem;
}

.rank-gold {
	color: #FFD700;
	text-shadow: 0 0 4px rgba(255, 215, 0, 0.5);
}

.rank-silver {
	color: #C0C0C0;
	text-shadow: 0 0 4px rgba(192, 192, 192, 0.5);
}

.rank-bronze {
	color: #CD7F32;
	text-shadow: 0 0 4px rgba(205, 127, 50, 0.5);
}

.fan-nickname {
	font-size: 0.95rem;
	font-weight: bold;
	color: black;
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.fan-tag {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.count-info {
	min-width: 80px;
	justify-content: flex-end;
}

/* 팬 랭킹 테두리 */
.gold-border {
	border: 3px solid #FFD700 !important;
}

.silver-border {
	border: 3px solid #C0C0C0 !important;
}

.bronze-border {
	border: 3px solid #CD7F32 !important;
}

.close-btn {
	color: #666 !important;
}

/* 전역 폰트 적용 */
.custom-font {
	font-family: 'Pretendard', 'SUIT', 'Noto Sans KR', sans-serif !important;
	letter-spacing: -0.02em;
}

/* 스크롤바 스타일 */
.fan-list-container::-webkit-scrollbar {
	width: 4px;
}

.fan-list-container::-webkit-scrollbar-track {
	background: transparent;
}

.fan-list-container::-webkit-scrollbar-thumb {
	background-color: rgba(100, 100, 100, 0.2);
	border-radius: 4px;
}

.fan-list-container::-webkit-scrollbar-thumb:hover {
	background-color: rgba(100, 100, 100, 0.4);
}
</style> 