<template>
	<v-navigation-drawer
		app
		floating
		v-model="sidebarOpen"
		:mini-variant="!sidebarOpen"
		mini-variant-width="80"
		width="280"
		class="modern-sidebar"
		style="margin-top: 48px; height: calc(100vh - 48px); z-index: 5;">
		
		<!-- 사이드바 헤더 -->
		<div class="sidebar-header" v-if="sidebarOpen">
			<div class="sidebar-logo">
				<img src="@/assets/icon128.png" width="40" height="40" class="logo-image">
				<div class="logo-text">
					<h3 class="app-title">TAMM</h3>
					<p class="app-subtitle">스푼라디오 매니저</p>
				</div>
			</div>
		</div>

		<!-- 메뉴 아이템들 -->
		<v-list class="sidebar-menu-list" :class="{ 'mini-menu': !sidebarOpen }">
			<side-menu-item
				v-for="(menu) of menuItems"
				:key="menu.href"
				:label="menu.label"
				:active="menu.isActive(menu.href)"
				:icon="menu.icon"
				:openNew="menu.openNew"
				:active-icon="menu.activeIcon"
				:href="menu.href"
				:expanded="sidebarOpen"
				@click="closeOnNavigate"></side-menu-item>
		</v-list>

		<!-- 사이드바 푸터 -->
		<div class="sidebar-footer" v-if="sidebarOpen">
			<div class="footer-content">
				<v-icon small color="#8B5FBF">mdi-heart</v-icon>
				<span class="footer-text">TAMM v{{ version }}</span>
			</div>
		</div>
	</v-navigation-drawer>
</template>
<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import SideMenuItem from './SideMenuItem.vue';
import pkg from '../../../package.json';

@Component({
	components: {
		SideMenuItem,
	},
})
export default class SideMenu extends Mixins(GlobalMixins) {
	@Prop({ type: Boolean, default: false }) open!: boolean;

	// 내부 상태로 관리
	public sidebarOpen: boolean = false;

	public get version() {
		return pkg.version;
	}

	public menuItems: any[] = [
		{
			href: '/',
			label: this.$t('page.Home'),
			icon: 'mdi-home-outline',
			activeIcon: 'mdi-home',
			isActive: this.isActive.bind(this),
		},
		{
			href: '/usertab',
			label: this.$t('page.UserTab'),
			icon: 'mdi-account-group-outline',
			activeIcon: 'mdi-account-group',
			isActive: this.isActive.bind(this),
		},
		{
			href: '/lotterytab',
			label: this.$t('page.LotteryTab'),
			icon: 'mdi-ticket-outline',
			activeIcon: 'mdi-ticket',
			isActive: this.isActive.bind(this),
		},
		{
			href: '/musictab',
			label: this.$t('page.MusicTab'),
			icon: 'mdi-music-note-outline',
			activeIcon: 'mdi-music-note',
			isActive: this.isActive.bind(this),
		},
		{
			href: '/roulette',
			label: this.$t('page.Roulette'),
			icon: 'mdi-rotate-right-variant',
			activeIcon: 'mdi-rotate-right',
			isActive: this.isActive.bind(this),
		},
		{
			href: '/imageeditor',
			label: this.$t('page.ImageEditor'),
			icon: 'mdi-image-edit-outline',
			activeIcon: 'mdi-image-edit',
			isActive: this.isActive.bind(this),
		},
		{
			href: '/cmd/live_join/',
			label: this.$t('page.Command'),
			icon: 'mdi-robot-happy-outline',
			activeIcon: 'mdi-robot-happy',
			isActive: () => {
				return this.$route.path.startsWith('/cmd/');
			},
		},
		{
			href: '/setting',
			label: this.$t('page.Setting'),
			icon: 'mdi-cog-outline',
			activeIcon: 'mdi-cog',
			isActive: () => {
				return this.$route.path.startsWith('/setting');
			},
		},
	];

	public isActive(href: string): boolean {
		return this.$route.path === href;
	}

	public closeOnNavigate() {
		// 네비게이션 시 사이드바 닫기
		this.sidebarOpen = false;
	}

	public mounted() {
		// 전역 이벤트 버스를 통해 사이드바 토글 이벤트 수신
		this.$evt.$on('toggle-sidebar', this.toggleSidebar);
	}

	public beforeDestroy() {
		// 컴포넌트 제거 시 이벤트 리스너 정리
		this.$evt.$off('toggle-sidebar', this.toggleSidebar);
	}

	public toggleSidebar() {
		console.log('사이드바 토글 이벤트 수신');
		this.sidebarOpen = !this.sidebarOpen;
		this.$emit('input', this.sidebarOpen);
	}
}
</script>
<style>
.modern-sidebar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
	backdrop-filter: blur(20px);
	border-right: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-sidebar .v-navigation-drawer__content {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.sidebar-header {
	padding: 24px 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
}

.sidebar-logo {
	display: flex;
	align-items: center;
	gap: 12px;
}

.logo-image {
	border-radius: 12px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.logo-text {
	flex: 1;
}

.app-title {
	color: white;
	font-size: 1.5rem;
	font-weight: 700;
	margin: 0;
	letter-spacing: 0.5px;
}

.app-subtitle {
	color: rgba(255, 255, 255, 0.7);
	font-size: 0.75rem;
	margin: 2px 0 0 0;
	font-weight: 400;
}

.sidebar-menu-list {
	flex: 1;
	padding: 16px 12px;
	background: transparent !important;
}

.sidebar-menu-list.mini-menu {
	padding: 16px 8px;
}

.sidebar-footer {
	padding: 16px 20px;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
}

.footer-content {
	display: flex;
	align-items: center;
	gap: 8px;
	justify-content: center;
}

.footer-text {
	color: rgba(255, 255, 255, 0.7);
	font-size: 0.75rem;
	font-weight: 500;
}

/* 스크롤바 스타일링 */
.modern-sidebar ::-webkit-scrollbar {
	width: 6px;
}

.modern-sidebar ::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 3px;
}

.modern-sidebar ::-webkit-scrollbar-thumb {
	background: rgba(255, 255, 255, 0.3);
	border-radius: 3px;
}

.modern-sidebar ::-webkit-scrollbar-thumb:hover {
	background: rgba(255, 255, 255, 0.5);
}
</style>
