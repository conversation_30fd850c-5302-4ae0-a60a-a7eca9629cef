import React, { useState } from 'react';
import styled from 'styled-components';

const ChatContainer = styled.div`
  width: 300px;
  background-color: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const ChatHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
`;

const ChatTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const Message = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 8px;
`;

const MessageAvatar = styled.div`
  width: 24px;
  height: 24px;
  background-color: #ff4100;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: bold;
  flex-shrink: 0;
`;

const MessageContent = styled.div`
  flex: 1;
`;

const MessageAuthor = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 2px;
`;

const MessageText = styled.div`
  font-size: 14px;
  color: #333;
  line-height: 1.4;
`;

const MessageTime = styled.div`
  font-size: 10px;
  color: #999;
  margin-top: 2px;
`;

const ChatInput = styled.div`
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
`;

const InputContainer = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const Input = styled.input`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  
  &:focus {
    border-color: #ff4100;
  }
`;

const SendButton = styled.button`
  background-color: #ff4100;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #e63900;
  }
`;

const Chat: React.FC = () => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    {
      id: 1,
      author: '청취자1',
      text: '안녕하세요!',
      time: '오후 2:30'
    },
    {
      id: 2,
      author: '청취자2',
      text: '오늘 방송 너무 좋네요',
      time: '오후 2:31'
    },
    {
      id: 3,
      author: '청취자3',
      text: '음악 선곡 센스가 좋아요',
      time: '오후 2:32'
    },
    {
      id: 4,
      author: '청취자4',
      text: '계속 들을게요!',
      time: '오후 2:33'
    }
  ]);

  const handleSendMessage = () => {
    if (message.trim()) {
      const newMessage = {
        id: messages.length + 1,
        author: '나',
        text: message,
        time: new Date().toLocaleTimeString('ko-KR', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      };
      setMessages([...messages, newMessage]);
      setMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <ChatContainer>
      <ChatHeader>
        <ChatTitle>실시간 채팅</ChatTitle>
      </ChatHeader>
      <ChatMessages>
        {messages.map(msg => (
          <Message key={msg.id}>
            <MessageAvatar>{msg.author[0]}</MessageAvatar>
            <MessageContent>
              <MessageAuthor>{msg.author}</MessageAuthor>
              <MessageText>{msg.text}</MessageText>
              <MessageTime>{msg.time}</MessageTime>
            </MessageContent>
          </Message>
        ))}
      </ChatMessages>
      <ChatInput>
        <InputContainer>
          <Input
            type="text"
            placeholder="메시지를 입력하세요..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <SendButton onClick={handleSendMessage}>전송</SendButton>
        </InputContainer>
      </ChatInput>
    </ChatContainer>
  );
};

export default Chat; 