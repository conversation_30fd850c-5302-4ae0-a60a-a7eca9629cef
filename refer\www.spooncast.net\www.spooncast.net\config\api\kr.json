{"api": "https://kr-api.spooncast.net/", "commonsApi": "https://kr-conf.spooncast.net/v2/commons/", "cdn": "https://kr-cdn.spooncast.net/", "socket": "wss://kr-heimdallr.spooncast.net/", "singApi": "https://kr-ssm.spooncast.net", "singSocket": "wss://kr-ssm.spooncast.net", "stickerApiUrl": "https://static.spooncast.net/kr/stickers/index.json", "badgeApiUrl": "https://static.spooncast.net/kr/badge/badge_styles_manifest.json", "signatureStickerApiUrl": "https://static.spooncast.net/kr/stickers/signature/OOOO/index.json", "textDonationUrl": "https://static.spooncast.net/kr/stickers/index_donation.json", "itemStoreApiUrl": "https://kr-store-api.spooncast.net/v1/store/", "gwApi": "https://kr-gw.spooncast.net/", "spoonSupportApi": "https://kr-spoon-support.spooncast.net/"}