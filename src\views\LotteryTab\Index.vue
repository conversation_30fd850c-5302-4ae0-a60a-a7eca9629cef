<!--
 * Index.vue - Lottery Tab
 * Created on 2025
 *
 * Copyright (c) tamm-v1. Licensed under the MIT License.
-->
<template>
  <v-main class="pa-0 ma-0" style="min-height: 100vh; height: 100%; overflow: hidden;">
    <v-container class="pa-0 ma-0 fill-height" fluid>
      <!-- 제목 -->
      <v-card flat class="fill-height pa-0 ma-0" style="width: 100%; overflow: hidden;"
             tile>
        <v-card-title class="text-center py-4 justify-center">
          <h1 class="text-h5 font-weight-bold">복권 관리</h1>
        </v-card-title>
        
        <v-card-text class="text-center pa-0 pb-2">
          <v-text-field
            v-model="searchQuery"
            append-icon="mdi-magnify"
            label="유저 검색"
            dense
            hide-details
            rounded
            outlined
            class="mx-auto"
            style="max-width: 400px;"
          ></v-text-field>
          
          <!-- 추가 기능 바 -->
          <div class="d-flex align-center mt-3 mx-auto justify-center" style="max-width: 500px;">
            <v-text-field
              v-model="giveAmount"
              label="지급 수량"
              type="number"
              min="0"
              dense
              hide-details
              outlined
              rounded
              class="mr-2"
              style="max-width: 120px;"
            ></v-text-field>
            
            <v-btn small color="primary" class="mx-1 rounded-pill" @click="selectAllActiveUsers">
              <v-icon small left>mdi-select-all</v-icon>
              전체
            </v-btn>
            
            <v-btn small color="info" class="mx-1 rounded-pill" @click="clearAllSelections">
              <v-icon small left>mdi-checkbox-marked-outline</v-icon>
              <template v-if="selectedUsers.length === 0">선택</template>
              <template v-else>({{ selectedUsers.length }}명)</template>
            </v-btn>
            
            <v-btn small color="success" class="mx-1 rounded-pill" @click="giveLotteryToSelected" :disabled="selectedUsers.length === 0">
              <v-icon small left>mdi-ticket-confirmation</v-icon>
              지급
            </v-btn>
          </div>
        </v-card-text>
        
        <v-card-text class="pa-3 fill-height" style="overflow: hidden; display: flex; flex-direction: column;">
          <!-- 유저 목록 -->
          <div class="user-list scrollable-container" style="flex: 1; overflow-y: auto; margin-bottom: 0; padding-bottom: 0;">
            <template v-if="userData.length > 0">
              <div class="user-grid">
                <v-card 
                  v-for="(user, index) in userData" 
                  :key="index"
                  class="user-item"
                  :class="{'selected-card': isUserSelected(user.tag)}"
                  @click="toggleUserSelection(user.tag)"
                >
                  <v-card-text class="py-3">
                    <div class="user-header d-flex align-center">
                      <div>
                        <span class="user-name">{{ user.nickname }}</span>
                        <span class="user-id ml-1">@{{ user.tag }}</span>
                      </div>
                      <v-chip color="primary" small class="ml-auto level-chip">Lv.{{ user.level }}</v-chip>
                    </div>
                    
                    <div class="user-stats mt-3 grey--text text--darken-2">
                      <div class="d-flex justify-space-between mb-2">
                        <div class="stat-item">
                          <span class="stat-label">복권:</span>
                          <span class="stat-value">{{ user.spoon ? user.spoon[2] : 0 }}</span>
                        </div>
                        
                        <div class="stat-item">
                          <span class="stat-label">보유금:</span>
                          <span class="stat-value">{{ user.money ? user.money.toLocaleString() : 0 }}원</span>
                        </div>
                      </div>
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </template>
            <v-card v-else class="pa-4 text-center">
              <v-icon large color="grey lighten-1">mdi-account-search</v-icon>
              <p class="mt-2 grey--text">검색 결과가 없습니다.</p>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-container>
    
    <!-- 결과 알림 snackbar -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
      bottom
      :style="{ left: '50%', transform: 'translateX(-50%)' }"
    >
      {{ snackbar.text }}
    </v-snackbar>
  </v-main>
</template>

<script lang="ts">
import { Component, Mixins, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import userDataManager, { UserData } from '@/plugins/user-data-ipc';
import chatCommandHandler from '@/plugins/chat-command-handler';
import { v4 as uuidv4 } from 'uuid';

// 브라우저 환경과 Electron 환경 구분
const isElectron = window && window.process && window.process.type;

@Component
export default class LotteryTab extends Mixins(GlobalMixins) {
  // 검색 관련
  searchQuery = '';
  
  // 지급 관련
  giveAmount = 1;
  
  // 선택된 유저 태그 목록
  selectedUsers: string[] = [];
  
  // 알림 메시지
  snackbar = {
    show: false,
    text: '',
    color: 'success'
  };
  
  // DJ 태그 정보 
  djTag = '';
  
  // 데이터 로딩 상태
  loading: boolean = true;
  
  created() {
    console.log('LotteryTab 컴포넌트 created');
    // DJ 정보 가져오기
    this.getDjInfo();
    
    // 데이터 즉시 로드 (비동기 실행)
    this.initializeData();
    
    // 타이틀바 검색창과 프로필 숨기기를 위한 이벤트
    if (window && (window as any).sopia && (window as any).sopia.events) {
      (window as any).sopia.events.emit('hide-titlebar-elements', true);
      
      // 채팅 이벤트 처리 등록
      (window as any).sopia.events.on('live:chat', (data: any) => this.handleChatMessage(data));
    }
    
    // ChatCommandHandler에 DJ 태그 정보 설정 및 메시지 전송 콜백 설정
    if (chatCommandHandler) {
      chatCommandHandler.setDjTag(this.djTag);
      chatCommandHandler.setSendMessageCallback(this.sendChatMessage.bind(this));
    }
  }
  
  // 채팅 메시지 전송 함수
  sendChatMessage(message: string): void {
    try {
      if (window && (window as any).sopia) {
        const sopia = (window as any).sopia;
        
        // 방송 중인지 확인
        if (sopia.store && sopia.store.state && sopia.store.state.live && sopia.store.state.live.id) {
          const liveId = sopia.store.state.live.id;
          
          // 메시지 전송
          if (sopia.api && sopia.api.lives && typeof sopia.api.lives.sendChat === 'function') {
            sopia.api.lives.sendChat(liveId, message)
              .then(() => console.log('메시지 전송 성공:', message))
              .catch((err: any) => console.error('메시지 전송 실패:', err));
          } else {
            console.warn('메시지 전송 API를 찾을 수 없습니다.');
          }
        } else {
          console.warn('현재 방송 중이 아니거나 방송 ID를 찾을 수 없습니다.');
        }
      }
    } catch (error) {
      console.error('메시지 전송 중 오류 발생:', error);
    }
  }
  
  // 사용자 선택 토글
  toggleUserSelection(tag: string) {
    const index = this.selectedUsers.indexOf(tag);
    if (index === -1) {
      // 유저가 선택되지 않았으면 선택 목록에 추가
      this.selectedUsers.push(tag);
    } else {
      // 이미 선택되어 있으면 선택 목록에서 제거
      this.selectedUsers.splice(index, 1);
    }
    console.log(`유저 선택 상태 변경: ${tag}, 현재 선택된 사용자 수: ${this.selectedUsers.length}`);
  }
  
  // 유저 선택 상태 확인
  isUserSelected(tag: string): boolean {
    return this.selectedUsers.includes(tag);
  }
  
  // 선택된 유저에게 복권 지급
  async giveLotteryToSelected() {
    try {
      // 입력값 검증
      const amount = parseInt(this.giveAmount.toString());
      if (isNaN(amount) || amount <= 0) {
        this.showSnackbar('지급할 복권 수량은 1 이상이어야 합니다.', 'error');
        return;
      }
      
      if (this.selectedUsers.length === 0) {
        this.showSnackbar('선택된 유저가 없습니다.', 'warning');
        return;
      }
      
      // 현재 모든 유저 데이터 가져오기
      const allUsers = userDataManager.getAllUsers();
      
      // 선택된 유저에게 복권 지급 처리
      let successCount = 0;
      let failCount = 0;
      let successUsers: string[] = []; // 복권 지급 성공한 유저 닉네임 저장
      
      for (const tag of this.selectedUsers) {
        // 태그로 유저 찾기
        const user = allUsers.find(u => u.tag === tag);
        if (!user) {
          console.error(`유저를 찾을 수 없음: ${tag}`);
          failCount++;
          continue;
        }
        
        try {
          // spoon 배열이 없으면 초기화
          if (!user.spoon) {
            user.spoon = [0, 0, 0];
          }
          
          // 복권 수량 증가 (spoon 배열의 세 번째 요소)
          user.spoon[2] = (user.spoon[2] || 0) + amount;
          
          // 유저 데이터 업데이트
          await userDataManager.updateUser(user);
          console.log(`복권 ${amount}개가 유저 ${user.nickname}에게 지급됨, 현재 복권: ${user.spoon[2]}개`);
          successCount++;
          successUsers.push(user.nickname);
        } catch (error) {
          console.error(`유저 ${user.nickname} 복권 지급 중 오류:`, error);
          failCount++;
        }
      }
      
      // 데이터 다시 로드
      await userDataManager.loadData();
      
      // UI 갱신을 위한 작업
      const originalQuery = this.searchQuery;
      this.searchQuery = originalQuery + ' ';
      await this.$nextTick();
      this.searchQuery = originalQuery;
      
      // 결과 표시
      if (successCount > 0 && failCount === 0) {
        this.showSnackbar(`${successCount}명의 유저에게 복권 ${amount}개가 지급되었습니다.`, 'success');
      } else if (successCount > 0 && failCount > 0) {
        this.showSnackbar(`${successCount}명 지급 성공, ${failCount}명 실패했습니다.`, 'warning');
      } else {
        this.showSnackbar('복권 지급에 실패했습니다.', 'error');
      }
      
      // 채팅창에 알림 메시지 전송
      if (successCount > 0) {
        try {
          // DJ 닉네임 가져오기
          let djNickname = '운영자';
          if (window && (window as any).sopia && (window as any).sopia.store) {
            const sopiaStore = (window as any).sopia.store;
            if (sopiaStore.state && sopiaStore.state.auth && sopiaStore.state.auth.user) {
              djNickname = sopiaStore.state.auth.user.nickname || '운영자';
            }
          }
          
          // 채팅 메시지 구성
          let chatMessage = '';
          
          if (this.selectedUsers.length <= 5) {
            // 유저 수가 적을 경우 모든 유저 닉네임 표시
            chatMessage = `[복권 지급] ${djNickname}님이 ${successUsers.join(', ')}님에게 복권 ${amount}개를 지급했습니다.`;
          } else {
            // 유저 수가 많을 경우 총합만 표시
            chatMessage = `[복권 지급] ${djNickname}님이 ${successCount}명의 유저에게 복권 ${amount}개씩 지급했습니다.`;
          }
          
          // 1. 직접 메시지 전송 시도
          this.sendChatMessage(chatMessage);
          
          // 2. chatCommandHandler로 메시지 전송 (기존 방식)
          if (chatCommandHandler && typeof chatCommandHandler.sendMessage === 'function') {
            chatCommandHandler.sendMessage(chatMessage);
            console.log('채팅창에 복권 지급 알림 메시지 전송 완료');
          } else {
            console.warn('chatCommandHandler가 없거나 sendMessage 메서드를 사용할 수 없습니다.');
          }
        } catch (error) {
          console.error('채팅 메시지 전송 중 오류 발생:', error);
        }
      }
      
      // 강제 UI 갱신
      this.$forceUpdate();
      
      // 선택된 유저들 모두 선택 해제
      this.selectedUsers = [];
      console.log('복권 지급 후 유저 선택 초기화 완료');
      
    } catch (error) {
      console.error('복권 지급 중 오류 발생:', error);
      this.showSnackbar('복권 지급 처리 중 오류가 발생했습니다.', 'error');
    }
  }
  
  // 알림 메시지 표시
  showSnackbar(text: string, color: string = 'success') {
    this.snackbar.text = text;
    this.snackbar.color = color;
    this.snackbar.show = true;
  }
  
  // 선택된 유저들을 모두 해제하는 메서드
  clearAllSelections() {
    // 선택된 유저가 있을 때만 동작
    if (this.selectedUsers.length > 0) {
      this.selectedUsers = [];
      console.log('모든 유저 선택 해제');
    }
  }
  
  // 현재 접속 중인 모든 시청자 선택
  async selectAllActiveUsers() {
    try {
      this.showSnackbar('현재 접속 중인 시청자 목록을 가져오는 중...', 'info');
      
      // 현재 방송 ID 가져오기
      let liveId = '';
      if (window && (window as any).sopia && (window as any).sopia.store) {
        const sopiaStore = (window as any).sopia.store;
        if (sopiaStore.state && sopiaStore.state.live && sopiaStore.state.live.id) {
          liveId = sopiaStore.state.live.id;
        }
      }
      
      if (!liveId) {
        this.showSnackbar('현재 방송 정보를 찾을 수 없습니다. 방송 중이 아닌 것 같습니다.', 'error');
        return;
      }
      
      // 시청자 목록 가져오기
      const listeners = await this.getAllListeners(liveId);
      
      if (!listeners || listeners.length === 0) {
        this.showSnackbar('현재 접속 중인 시청자가 없습니다.', 'warning');
        return;
      }
      
      // 시청자들을 selectedUsers에 추가
      this.selectedUsers = [];
      const allUsers = userDataManager.getAllUsers();
      let foundCount = 0;
      
      for (const listener of listeners) {
        // 시청자가 유저 데이터에 있는지 확인
        const user = allUsers.find(u => u.tag === listener.tag);
        
        if (user) {
          this.selectedUsers.push(user.tag);
          foundCount++;
        }
      }
      
      if (foundCount > 0) {
        this.showSnackbar(`현재 접속 중인 ${foundCount}명의 시청자를 선택했습니다.`, 'success');
      } else {
        this.showSnackbar('현재 접속 중인 시청자 중 등록된 유저가 없습니다.', 'warning');
      }
    } catch (error) {
      console.error('시청자 목록 가져오기 중 오류 발생:', error);
      this.showSnackbar('시청자 목록을 가져오는데 실패했습니다.', 'error');
    }
  }
  
  // 현재 방송의 모든 시청자 목록 가져오기
  async getAllListeners(liveId: string): Promise<any[]> {
    try {
      let members: any[] = [];
      
      // Sopia API를 통해 시청자 목록 가져오기
      if (window && (window as any).sopia && (window as any).sopia.api) {
        const sopiaApi = (window as any).sopia.api;
        
        if (sopiaApi.lives && typeof sopiaApi.lives.listeners === 'function') {
          let req = await sopiaApi.lives.listeners(liveId);
          let res = req.res;
          
          if (res && res.results) {
            members = members.concat(res.results);
            
            // 다음 페이지가 있으면 계속 가져오기
            while (res.next) {
              res = await req.next();
              if (res && res.results) {
                members = members.concat(res.results);
              }
            }
          }
        }
      }
      
      console.log(`현재 방송에 접속 중인 시청자 수: ${members.length}명`);
      return members;
    } catch (error) {
      console.error('시청자 목록 가져오기 중 오류 발생:', error);
      throw error;
    }
  }
  
  // 데이터 초기 로드 (전체 초기화)
  async initializeData() {
    try {
      this.loading = true;
      console.log('데이터 초기화 시작');
      
      // UserDataManager 무조건 초기화
      await userDataManager.init(this.djTag);
      console.log('UserDataManager 초기화 완료');
      
      // 강제로 데이터 로드
      await userDataManager.loadData();
      console.log('초기 데이터 로드 완료');
      
      // 즉시 UI 업데이트 트리거
      await this.$nextTick();
      this.$forceUpdate();
      
      // 데이터 로드 후 유저 수 확인 로깅
      const users = userDataManager.getAllUsers();
      console.log(`초기화 후 로드된 유저 수: ${users.length}명`);
      
      // 추가 지연 UI 업데이트 (타이밍 이슈 해결)
      setTimeout(() => {
        this.$forceUpdate();
        console.log('지연된 초기 UI 갱신 완료');
        this.loading = false;
      }, 100);
    } catch (error) {
      console.error('데이터 초기화 중 오류 발생:', error);
      this.loading = false;
    }
  }
  
  // 컴포넌트 마운트 시 이벤트 리스너 설정
  mounted() {
    console.log('LotteryTab 컴포넌트 mounted');
    
    // Vue Router 이벤트 리스너 추가
    if (this.$router) {
      // 현재 경로가 LotteryTab인 경우 데이터 로드 (앱 시작 시 첫 화면이 LotteryTab인 경우)
      if (this.$route && this.$route.name === 'LotteryTab') {
        console.log('현재 경로가 LotteryTab, 즉시 데이터 로드');
        // 약간의 지연 후 데이터 로드 (DOM 렌더링 후)
        setTimeout(() => {
          this.loadUserData();
        }, 10);
      }
      
      this.$router.afterEach((to) => {
        if (to.name === 'LotteryTab') {
          console.log('라우터 변경 감지: LotteryTab 페이지로 이동');
          // 약간의 지연 후 데이터 로드 (라우터 전환 완료 후)
          setTimeout(() => {
            this.loadUserData();
          }, 50);
        }
      });
    }
    
    // 전역 이벤트 리스너 추가
    window.addEventListener('focus', this.onWindowFocus);
    
    // 새로고침 감지 처리
    if (window && window.performance) {
      // 페이지 로드 타입 확인 (reload, navigate, back_forward)
      const navEntries = window.performance.getEntriesByType('navigation');
      if (navEntries.length > 0 && (navEntries[0] as any).type === 'reload') {
        console.log('페이지 새로고침 감지');
        // 새로고침 시 약간의 지연 후 데이터 로드
        setTimeout(() => {
          this.loadUserData();
          console.log('새로고침 후 데이터 로드 완료');
        }, 100);
      }
    }
  }
  
  // 컴포넌트 제거 시 이벤트 리스너 정리
  beforeDestroy() {
    console.log('LotteryTab 컴포넌트 beforeDestroy');
    
    // 윈도우 이벤트 리스너 제거
    window.removeEventListener('focus', this.onWindowFocus);
    
    // Vue 이벤트 리스너 제거
    if (this.$root) {
      this.$root.$off('route-changed');
    }
    
    // 소피아 이벤트 리스너 제거 및 타이틀바 요소 복원
    if (window && (window as any).sopia && (window as any).sopia.events) {
      (window as any).sopia.events.off('route-changed');
      (window as any).sopia.events.off('live:chat', this.handleChatMessage);
      (window as any).sopia.events.emit('hide-titlebar-elements', false);
    }
  }
  
  // 윈도우 포커스 이벤트 핸들러
  onWindowFocus() {
    if (this.$route.name === 'LotteryTab') {
      console.log('윈도우 포커스 감지: 데이터 리로드');
      this.loadUserData();
    }
  }
  
  // 컴포넌트 활성화 시 데이터 리로드
  activated() {
    console.log('LotteryTab 컴포넌트 activated');
    // 활성화 시 데이터 즉시 로드
    this.loadUserData();
    
    // 타이틀바 검색창과 프로필 숨기기
    if (window && (window as any).sopia && (window as any).sopia.events) {
      (window as any).sopia.events.emit('hide-titlebar-elements', true);
    }
  }
  
  // 컴포넌트 비활성화 시 타이틀바 요소 복원
  deactivated() {
    // 타이틀바 검색창과 프로필 복원
    if (window && (window as any).sopia && (window as any).sopia.events) {
      (window as any).sopia.events.emit('hide-titlebar-elements', false);
    }
  }

  // DJ 정보 가져오기
  getDjInfo() {
    try {
      // Tamm 앱의 인증 정보 가져오기
      const sopia = (window as any).sopia;
      if (sopia && sopia.store && sopia.store.state && sopia.store.state.auth) {
        const auth = sopia.store.state.auth;
        if (auth.user && auth.user.tag) {
          this.djTag = auth.user.tag;
          console.log('로그인된 DJ 태그:', this.djTag);
          return;
        }
      }
      
      // 대체 방법: 로컬 스토어에서 가져오기
      const vuexStore = this.$store;
      if (vuexStore && vuexStore.state && vuexStore.state.auth) {
        const auth = vuexStore.state.auth;
        if (auth.user && auth.user.tag) {
          this.djTag = auth.user.tag;
          console.log('로컬 저장소에서 DJ 태그 가져옴:', this.djTag);
          return;
        }
      }
      
      // 기본값 설정
      this.djTag = 'bxd';
      console.log('기본 DJ 태그 사용:', this.djTag);
    } catch (err) {
      console.error('DJ 정보를 가져오는 중 오류 발생:', err);
      this.djTag = 'bxd';
    }
  }
  
  // 기본 사용자 데이터
  defaultUserData(): UserData {
    return {
      nickname: '',
      tag: '',
      last_attend: Date.now(),
      level: 1,
      point: 0,
      attend_count: 0,
      heart_count: 0,
      chat_count: 0,
      is_double: false,
      spoon: [0, 0, 0],
      money: 0
    };
  }

  async loadUserData() {
    try {
      console.log('loadUserData 호출: 유저 데이터 로딩 시작');
      this.loading = true;
      
      // UserDataManager 무조건 초기화 진행
      try {
        await userDataManager.init(this.djTag);
        console.log('UserDataManager 초기화 완료');
      } catch (initError) {
        console.error('UserDataManager 오류:', initError);
      }
      
      // 강제로 데이터 로드
      await userDataManager.loadData();
      
      // 데이터가 로드되었는지 확인
      const users = userDataManager.getAllUsers();
      console.log(`로드된 유저 수: ${users.length}명`);
      
      // UI 갱신을 위한 다중 방법 적용
      
      // 1. 검색어 쿼리 변경으로 반응성 트리거
      const originalQuery = this.searchQuery;
      this.searchQuery = originalQuery + ' ';
      await this.$nextTick();
      this.searchQuery = originalQuery;
      
      // 2. 강제 UI 갱신
      this.$forceUpdate();
      
      // 3. 지연 후 다시 한번 강제 갱신 (타이밍 이슈 대응)
      setTimeout(() => {
        this.$forceUpdate();
        this.loading = false;
        console.log('지연된 UI 강제 갱신 완료');
      }, 50);
      
      return true;
    } catch (error) {
      console.error('유저 데이터 로드 중 오류 발생:', error);
      
      // 오류 알림 표시
      if (this.$store && this.$store.commit) {
        this.$store.commit('setSnackbar', {
          text: '유저 데이터를 불러오는 데 실패했습니다.',
          color: 'error'
        });
      }
      
      return false;
    } finally {
      // loading 상태는 지연된 갱신에서 처리하므로 여기서는 설정하지 않음
    }
  }
  
  // 현재 표시할 사용자 목록 (컴포넌트 라이프사이클 내에서 항상 최신 상태 유지)
  get userData() {
    // 항상 최신 데이터를 가져오기 위해 직접 호출
    let users;
    try {
      users = this.searchQuery 
        ? userDataManager.searchUsers(this.searchQuery) 
        : userDataManager.getAllUsers();
    } catch (error) {
      console.error('userData getter 오류:', error);
      users = [];
    }
    
    // 유저 수 확인
    if (!users || users.length === 0) {
      // 데이터가 없는 경우 재시도
      if (!this.loading && !this.searchQuery) {
        // 데이터 로드 중이 아니고 검색 중이 아닌 경우에만 재시도
        console.log('userData getter: 데이터 없음, 지연 재시도');
        setTimeout(() => {
          this.loadUserData();
        }, 100);
      }
      return [];
    }
    
    console.log('userData getter 호출됨, 유저 수:', users.length);
    
    // 복권 보유량 기준으로 내림차순 정렬 (복권이 많은 사용자가 먼저 표시됨)
    return users.sort((a, b) => {
      const lotteryA = a.spoon ? a.spoon[2] : 0;
      const lotteryB = b.spoon ? b.spoon[2] : 0;
      return lotteryB - lotteryA;
    });
  }
  
  // 채팅 메시지 처리
  async handleChatMessage(data: any) {
    try {
      // chatCommandHandler로 전달
      await chatCommandHandler.handleChatMessage(data);
      
      // 명령어가 "!내정보 생성"인 경우 데이터 목록 갱신
      if (data?.message?.trim().toLowerCase() === '!내정보 생성') {
        // 약간의 지연 후 데이터 리로드 (유저 생성 처리 시간 고려)
        setTimeout(() => {
          this.loadUserData();
        }, 1000);
      }
    } catch (error) {
      console.error('채팅 메시지 처리 중 오류:', error);
    }
  }
}
</script>

<style scoped>
/* 입력 필드 숫자 증가/감소 화살표 제거 - 향상된 버전 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  margin: 0 !important;
  display: none !important;
}

input[type="number"] {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
}

.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 0;
}

.user-item {
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.user-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.selected-card {
  box-shadow: 0 0 0 2px var(--v-primary-base) !important;
  border: 2px solid #9c27b0 !important;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
}

.user-id {
  font-size: 12px;
  color: #666;
}

.level-chip {
  font-size: 11px;
  height: 22px;
}

.user-stats {
  font-size: 13px;
}

.stat-item {
  display: flex;
  width: 48%;
  justify-content: space-between;
  align-items: center;
}

.stat-item.wide {
  width: 100%;
}

.stat-label {
  color: #666;
  min-width: 60px;
  margin-right: 4px;
}

.stat-value {
  color: #000;
  font-weight: 700;
  text-align: left;
  min-width: 80px;
}

.user-action-btn {
  font-size: 10px;
}

.search-field {
  max-width: 500px;
  margin: 0 auto;
}

.scrollable-container {
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  min-height: calc(100vh - 140px);
  height: calc(100vh - 140px);
  max-height: calc(100vh - 140px);
}

/* 스크롤바 스타일링 */
.scrollable-container::-webkit-scrollbar {
  width: 6px;
}

.scrollable-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.scrollable-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

.scrollable-container::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* 유저 수정 다이얼로그 스타일 */
.user-edit-dialog .v-sheet {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease;
}

.user-edit-dialog .v-sheet:hover {
  border-color: rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-edit-dialog .subtitle-1 {
  color: var(--v-primary-base);
  font-size: 14px;
  letter-spacing: 0.5px;
}

.user-edit-dialog .v-text-field--filled > .v-input__control > .v-input__slot {
  background: rgba(0, 0, 0, 0.03);
}

.user-edit-dialog .v-input__prepend-inner {
  margin-top: 10px !important;
  opacity: 0.6;
}

.user-edit-dialog .v-btn {
  letter-spacing: 0.5px;
  text-transform: none;
  font-weight: 500;
}

.user-edit-dialog .v-card__title {
  letter-spacing: 0.5px;
}

.user-edit-dialog .v-date-picker-table .v-btn.v-btn--active {
  color: white !important;
}

/* 유저 수정 다이얼로그 스타일 */
.user-edit-dialog .v-card {
  border-radius: 28px !important;
  overflow: hidden;
}

.user-edit-dialog .section-container {
  margin-bottom: 10px;
  padding: 8px;
  padding-bottom: 1px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.02);
}

.user-edit-dialog .subtitle-1 {
  color: var(--v-primary-base);
  font-size: 14px;
  letter-spacing: 0.5px;
  margin-left: 10px;
  margin-bottom: 4px;
}

.user-edit-dialog .v-input__prepend-inner {
  margin-top: 10px !important;
  opacity: 0.6;
}

.user-edit-dialog .v-btn {
  letter-spacing: 0.5px;
  text-transform: none;
  font-weight: 500;
}

.user-edit-dialog .v-card__title {
  letter-spacing: 0.5px;
}

.user-edit-dialog .v-date-picker-table .v-btn.v-btn--active {
  color: white !important;
}

/* 캡슐형 컨테이너 스타일 */
.capsule-container {
  position: relative;
  padding-top: 18px;
  margin-bottom: 4px;
}

.capsule-label {
  position: absolute;
  top: 0;
  left: 12px;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  z-index: 1;
}

/* 레벨 및 통계 정보 섹션 내 행 간격 줄이기 */
.section-container .v-row {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  margin-left: -4px !important;
  margin-right: -4px !important;
}

.section-container .v-col {
  padding: 0 4px !important;
}

/* 컴팩트 폼 스타일 */
.compact-form {
  margin: 0;
  padding: 0;
}

/* 다이얼로그 내용 여백 줄이기 */
.user-edit-dialog .v-card__text {
  padding-top: 12px !important;
}

/* 모바일 환경에서 다이얼로그 높이 조정 */
@media (max-width: 600px) {
  .user-edit-dialog .v-card__text {
    max-height: calc(100vh - 200px);
  }
}

/* 버튼 스타일 */
.rounded-pill {
  border-radius: 50px !important;
  padding-left: 20px !important;
  padding-right: 20px !important;
}

/* 캡슐형 입력 필드 스타일 */
.capsule-input .v-input__slot {
  border-radius: 50px !important;
  transition: all 0.3s ease;
  background-color: #f5f5f5 !important;
}

.capsule-input.v-input--is-focused .v-input__slot {
  border-color: var(--v-primary-base) !important;
  box-shadow: 0 0 0 2px rgba(156, 39, 176, 0.2) !important;
  background-color: #ffffff !important;
}

.capsule-input.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 38px;
}

.capsule-input .v-label {
  font-size: 13px;
}

/* 입력 필드 포커스 효과 */
.capsule-input:hover .v-input__slot {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* 수정 다이얼로그 스크롤 처리 */
.user-edit-dialog .v-card__text {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}
</style> 