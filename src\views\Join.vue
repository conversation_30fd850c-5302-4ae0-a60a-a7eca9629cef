<!--
 * Join.vue
 * Created on Fri Nov 27 2020
 *
 * Copyright (c) Raravel. Licensed under the MIT License.
-->
<template>
	<v-card flat class="mt-4">
		<v-card-text>
			<v-textarea
				v-model="liveJoin"
				outlined
				hide-details
				auto-grow
				rows="4"
				counter="500"
				:label="$t('cmd.message')"
				:placeholder="$t('cmd.message-input')"
				class="rounded-lg"
				background-color="grey lighten-5">
				<template v-slot:prepend-inner>
					<v-icon color="purple lighten-1" class="mt-1">mdi-login</v-icon>
				</template>
			</v-textarea>
			
			<v-alert
				v-if="liveJoin.length > 0"
				class="mt-4 rounded-lg"
				color="purple lighten-5"
				border="left"
				elevation="1"
				icon="mdi-information-outline">
				{{ $t('cmd.preview') || '미리보기' }}: <strong>{{ liveJoin }}</strong>
			</v-alert>
		</v-card-text>
	</v-card>
</template>

<script lang="ts">
// ... existing code ...
</script>

<style scoped>
.v-textarea >>> .v-input__slot {
  min-height: 120px;
}
</style> 