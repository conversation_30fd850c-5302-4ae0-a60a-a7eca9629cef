<!--
 * Index.vue - User Tab
 * Created on 2023
 *
 * Copyright (c) tamm-v1. Licensed under the MIT License.
-->
<template>
  <v-main class="pa-0 ma-0" style="min-height: 100vh; height: 100%; overflow: hidden;">
    <v-container class="pa-0 ma-0 fill-height" fluid>
      <!-- 제목 -->
      <v-card flat class="fill-height pa-0 ma-0" style="width: 100%; overflow: hidden;"
             tile>
        <v-card-title class="text-center py-4 justify-center">
          <h1 class="text-h5 font-weight-bold">유저 관리</h1>
        </v-card-title>
        
        <v-card-text class="text-center pa-0 pb-2">
          
          
          <v-text-field
            v-model="searchQuery"
            append-icon="mdi-magnify"
            label="유저 검색"
            dense
            hide-details
            rounded
            outlined
            class="mx-auto"
            style="max-width: 400px;"
          ></v-text-field>
        </v-card-text>
        
        <v-card-text class="pa-3 fill-height" style="overflow: hidden; display: flex; flex-direction: column;">
          <!-- 유저 목록 -->
          <div class="user-list scrollable-container" style="flex: 1; overflow-y: auto; margin-bottom: 0; padding-bottom: 0;">
            <template v-if="userData.length > 0">
              <div class="user-grid">
                <v-card 
                  v-for="(user, index) in userData" 
                  :key="index"
                  class="user-item"
                  :class="{'active-card': activeUserTag === user.tag}"
                  @click="toggleUserActions(user.tag)"
                >
                  <v-card-text class="py-3">
                    <div class="user-header d-flex align-center">
                      <div>
                        <span class="user-name">{{ user.nickname }}</span>
                        <span class="user-id ml-1">@{{ user.tag }}</span>
                      </div>
                      <v-chip color="primary" small class="ml-auto level-chip">Lv.{{ user.level }}</v-chip>
                    </div>
                    
                    <div class="user-stats mt-2 grey--text text--darken-2">
                      <div>
                        <div class="d-flex justify-space-between mb-1">
                          <span class="caption">경험치: {{ user.point }} / {{ user.level * 100 }}</span>
                          <span class="caption">{{ calculateExpPercentage(user.point, user.level) }}%</span>
                        </div>
                        <v-progress-linear
                          :value="calculateExpPercentage(user.point, user.level)"
                          height="8"
                          rounded
                          color="primary"
                          class="mb-2"
                        ></v-progress-linear>
                      </div>
                      <div class="d-flex justify-space-between mb-1">
                        <div class="stat-item">
                          <span class="stat-label">채팅수:</span> 
                          <span class="stat-value">{{ user.chat_count }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">출석수:</span>
                          <span class="stat-value">{{ user.attend_count }}</span>
                        </div>
                      </div>
                      <div class="d-flex justify-space-between mb-1">
                        <div class="stat-item">
                          <span class="stat-label">하트수:</span>
                          <span class="stat-value">{{ user.heart_count }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">복권:</span>
                          <span class="stat-value">{{ user.spoon ? user.spoon[2] : 0 }}</span>
                        </div>
                      </div>
                      <div class="d-flex justify-space-between mb-1">
                        <div class="stat-item">
                          <span class="stat-label">보유금:</span>
                          <span class="stat-value">{{ user.money ? user.money.toLocaleString() : 0 }}원</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">최근출석:</span>
                          <span 
                            class="stat-value date-value" 
                            :class="getAttendanceClass(user.last_attend)"
                          >
                            {{ formatDate(user.last_attend) }}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="text-right mt-2" v-show="activeUserTag === user.tag">
                      <v-btn x-small color="primary" class="mr-2 user-action-btn" @click.stop="editUser(user)">
                        <v-icon x-small left>mdi-pencil</v-icon>
                        수정
                      </v-btn>
                      <v-btn x-small color="error" class="user-action-btn" @click.stop="confirmDeleteUser(user)">
                        <v-icon x-small left>mdi-delete</v-icon>
                        삭제
                      </v-btn>
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </template>
            <v-card v-else class="pa-4 text-center">
              <v-icon large color="grey lighten-1">mdi-account-search</v-icon>
              <p class="mt-2 grey--text">검색 결과가 없습니다.</p>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-container>

    <!-- 사용자 수정/추가 다이얼로그 -->
    <v-dialog v-model="editDialog" max-width="550px" content-class="user-edit-dialog">
      <v-card class="rounded-xl">
        <v-card-title class="headline py-4 primary white--text rounded-t-xl">
          <v-icon left color="white">{{ isNewUser ? 'mdi-account-plus' : 'mdi-account-edit' }}</v-icon>
          {{ isNewUser ? '신규 유저 추가' : '유저 정보 수정' }}
        </v-card-title>
        <v-card-text class="pt-3 px-4 overflow-hidden">
          <v-form ref="form" class="compact-form">
            <!-- 기본 정보 섹션 -->
            <div class="section-container mb-3">
              <span class="subtitle-1 font-weight-bold">기본 정보</span>
              
              <!-- 닉네임 필드 -->
              <div class="capsule-container mb-2">
                <div class="capsule-label">닉네임</div>
                <v-text-field
                  v-model="editedUser.nickname"
                  outlined
                  rounded
                  dense
                  hide-details="auto"
                  prepend-inner-icon="mdi-account"
                  class="capsule-input"
                  required
                ></v-text-field>
              </div>
              
              <!-- 고유 태그 필드 -->
              <div class="capsule-container mb-0">
                <div class="capsule-label">고유 태그</div>
                <v-text-field
                  v-model="editedUser.tag"
                  outlined
                  rounded
                  dense
                  hide-details="auto"
                  prepend-inner-icon="mdi-tag"
                  class="capsule-input"
                  required
                  :disabled="!isNewUser"
                ></v-text-field>
              </div>
            </div>

            <!-- 레벨 및 스탯 섹션 -->
            <div class="section-container mb-4">
              <span class="subtitle-1 font-weight-bold">레벨 및 경험치</span>
              <v-row class="mt-2">
                <v-col cols="6">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">레벨</div>
                    <v-text-field
                      v-model.number="editedUser.level"
                      type="number"
                      min="1"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      prepend-inner-icon="mdi-chevron-triple-up"
                      class="capsule-input"
                      required
                    ></v-text-field>
                  </div>
                </v-col>
                <v-col cols="6">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">경험치</div>
                    <v-text-field
                      v-model.number="editedUser.point"
                      type="number"
                      min="0"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      prepend-inner-icon="mdi-star"
                      class="capsule-input"
                      required
                    ></v-text-field>
                  </div>
                </v-col>
              </v-row>
            </div>

            <!-- 통계 정보 섹션 -->
            <div class="section-container mb-4">
              <span class="subtitle-1 font-weight-bold">통계 정보</span>
              <v-row class="mt-2">
                <v-col cols="6">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">출석 수</div>
                    <v-text-field
                      v-model.number="editedUser.attend_count"
                      type="number"
                      min="0"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      prepend-inner-icon="mdi-calendar-check"
                      class="capsule-input"
                    ></v-text-field>
                  </div>
                </v-col>
                <v-col cols="6">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">하트 수</div>
                    <v-text-field
                      v-model.number="editedUser.heart_count"
                      type="number"
                      min="0"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      prepend-inner-icon="mdi-heart"
                      class="capsule-input"
                    ></v-text-field>
                  </div>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">채팅 수</div>
                    <v-text-field
                      v-model.number="editedUser.chat_count"
                      type="number"
                      min="0"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      prepend-inner-icon="mdi-chat"
                      class="capsule-input"
                    ></v-text-field>
                  </div>
                </v-col>
                <v-col cols="6" v-if="editedUser.spoon">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">복권 수</div>
                    <v-text-field
                      v-model.number="editedUser.spoon[2]"
                      type="number"
                      min="0"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      prepend-inner-icon="mdi-ticket"
                      class="capsule-input"
                    ></v-text-field>
                  </div>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <div class="capsule-container mb-2">
                    <div class="capsule-label">보유금</div>
                    <v-text-field
                      v-model.number="editedUser.money"
                      type="number"
                      min="0"
                      outlined
                      rounded
                      dense
                      hide-details="auto"
                      suffix="원"
                      prepend-inner-icon="mdi-cash"
                      class="capsule-input"
                    ></v-text-field>
                  </div>
                </v-col>
              </v-row>
            </div>

            <!-- 날짜 정보 섹션 -->
            <div class="section-container">
              <span class="subtitle-1 font-weight-bold">날짜 정보</span>
              <v-row class="mt-2">
                <v-col cols="12">
                  <div class="capsule-container">
                    <div class="capsule-label">최근 출석일</div>
                    <v-menu
                      v-model="dateMenu"
                      :close-on-content-click="false"
                      transition="scale-transition"
                      offset-y
                      min-width="290px"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          v-model="pickerDate"
                          outlined
                          rounded
                          dense
                          hide-details="auto"
                          prepend-inner-icon="mdi-calendar"
                          class="capsule-input"
                          readonly
                          v-bind="attrs"
                          v-on="on"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="pickerDate"
                        @input="updateLastAttendDate"
                        color="primary"
                      ></v-date-picker>
                    </v-menu>
                  </div>
                </v-col>
              </v-row>
            </div>
          </v-form>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions class="py-3 px-4">
          <v-spacer></v-spacer>
          <v-btn color="grey darken-1" text @click="editDialog = false" class="rounded-pill">
            <v-icon left>mdi-close</v-icon>
            취소
          </v-btn>
          <v-btn color="primary" @click="saveUser" :elevation="2" class="rounded-pill">
            <v-icon left>mdi-content-save</v-icon>
            저장
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 삭제 확인 다이얼로그 -->
    <v-dialog v-model="deleteDialog" max-width="400px">
      <v-card class="rounded-xl">
        <v-card-title class="headline error white--text rounded-t-xl">
          <v-icon left color="white">mdi-delete-alert</v-icon>
          유저 삭제
        </v-card-title>
        <v-card-text class="pt-4">
          <v-alert type="warning" text dense>
            {{ userToDelete ? userToDelete.nickname : '' }} 유저를 정말 삭제하시겠습니까?<br>
            이 작업은 되돌릴 수 없습니다.
          </v-alert>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions class="py-3">
          <v-spacer></v-spacer>
          <v-btn color="grey darken-1" text @click="deleteDialog = false" class="rounded-pill">
            <v-icon left>mdi-close</v-icon>
            취소
          </v-btn>
          <v-btn color="error" @click="deleteUser" class="rounded-pill">
            <v-icon left>mdi-delete</v-icon>
            삭제
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-main>
</template>

<script lang="ts">
import { Component, Mixins, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import userDataManager, { UserData } from '@/plugins/user-data-ipc';
import chatCommandHandler from '@/plugins/chat-command-handler';
import { v4 as uuidv4 } from 'uuid';

// 브라우저 환경과 Electron 환경 구분
const isElectron = window && window.process && window.process.type;

@Component
export default class UserTab extends Mixins(GlobalMixins) {
  // 검색 관련
  searchQuery = '';
  
  // DJ 태그 정보 
  djTag = '';
  
  // 활성화된 사용자 태그 (버튼 표시용)
  activeUserTag = '';
  
  // 다이얼로그 관련
  editDialog = false;
  deleteDialog = false;
  isNewUser = false;
  editedUser: UserData = this.defaultUserData();
  userToDelete: UserData | null = null;
  
  // 날짜 픽커 관련
  dateMenu = false;
  pickerDate = '';
  
  // 데이터 로딩 상태
  loading: boolean = true;
  
  created() {
    console.log('UserTab 컴포넌트 created');
    // DJ 정보 가져오기
    this.getDjInfo();
    
    // 이벤트 리스너 등록
    this.registerChatEventListener();
    
    // 데이터 즉시 로드 (비동기 실행)
    this.initializeData();
  }
  
  // 데이터 초기 로드 (전체 초기화)
  async initializeData() {
    try {
      this.loading = true;
      console.log('데이터 초기화 시작');
      
      // UserDataManager 무조건 초기화
      await userDataManager.init(this.djTag);
      console.log('UserDataManager 초기화 완료');
      
      // 강제로 데이터 로드
      await userDataManager.loadData();
      console.log('초기 데이터 로드 완료');
      
      // 즉시 UI 업데이트 트리거
      await this.$nextTick();
      this.$forceUpdate();
      
      // 데이터 로드 후 유저 수 확인 로깅
      const users = userDataManager.getAllUsers();
      console.log(`초기화 후 로드된 유저 수: ${users.length}명`);
      
      // 추가 지연 UI 업데이트 (타이밍 이슈 해결)
      setTimeout(() => {
        this.$forceUpdate();
        console.log('지연된 초기 UI 갱신 완료');
        this.loading = false;
      }, 100);
    } catch (error) {
      console.error('데이터 초기화 중 오류 발생:', error);
      this.loading = false;
    }
  }
  
  // 컴포넌트 마운트 시 이벤트 리스너 설정
  mounted() {
    console.log('UserTab 컴포넌트 mounted');
    
    // Vue Router 이벤트 리스너 추가
    if (this.$router) {
      // 현재 경로가 UserTab인 경우 데이터 로드 (앱 시작 시 첫 화면이 UserTab인 경우)
      if (this.$route && this.$route.name === 'UserTab') {
        console.log('현재 경로가 UserTab, 즉시 데이터 로드');
        // 약간의 지연 후 데이터 로드 (DOM 렌더링 후)
        setTimeout(() => {
          this.loadUserData();
        }, 10);
      }
      
      this.$router.afterEach((to) => {
        if (to.name === 'UserTab') {
          console.log('라우터 변경 감지: UserTab 페이지로 이동');
          // 약간의 지연 후 데이터 로드 (라우터 전환 완료 후)
          setTimeout(() => {
            this.loadUserData();
          }, 50);
        }
      });
    }
    
    // 전역 이벤트 리스너 추가
    window.addEventListener('focus', this.onWindowFocus);
    
    // 새로고침 감지 처리
    if (window && window.performance) {
      // 페이지 로드 타입 확인 (reload, navigate, back_forward)
      const navEntries = window.performance.getEntriesByType('navigation');
      if (navEntries.length > 0 && (navEntries[0] as any).type === 'reload') {
        console.log('페이지 새로고침 감지');
        // 새로고침 시 약간의 지연 후 데이터 로드
        setTimeout(() => {
          this.loadUserData();
          console.log('새로고침 후 데이터 로드 완료');
        }, 100);
      }
    }
  }
  
  // 윈도우 포커스 이벤트 핸들러
  onWindowFocus() {
    if (this.$route.name === 'UserTab') {
      console.log('윈도우 포커스 감지: 데이터 리로드');
      this.loadUserData();
    }
  }
  
  // 컴포넌트 활성화 시 데이터 리로드
  activated() {
    console.log('UserTab 컴포넌트 activated');
    // 활성화 시 데이터 즉시 로드
    this.loadUserData();
  }
  
  // 컴포넌트 제거 시 이벤트 리스너 정리
  beforeDestroy() {
    console.log('UserTab 컴포넌트 beforeDestroy');
    
    // 이벤트 리스너 제거
    this.unregisterChatEventListener();
    
    // 윈도우 이벤트 리스너 제거
    window.removeEventListener('focus', this.onWindowFocus);
    
    // Vue 이벤트 리스너 제거
    if (this.$root) {
      this.$root.$off('route-changed');
    }
    
    // 소피아 이벤트 리스너 제거
    if (window && (window as any).sopia && (window as any).sopia.events) {
      (window as any).sopia.events.off('route-changed');
    }
  }

  // DJ 정보 가져오기
  getDjInfo() {
    try {
      // Tamm 앱의 인증 정보 가져오기
      const sopia = (window as any).sopia;
      if (sopia && sopia.store && sopia.store.state && sopia.store.state.auth) {
        const auth = sopia.store.state.auth;
        if (auth.user && auth.user.tag) {
          this.djTag = auth.user.tag;
          // 채팅 명령어 핸들러에 DJ 태그 설정
          chatCommandHandler.setDjTag(this.djTag);
          console.log('로그인된 DJ 태그:', this.djTag);
          return;
        }
      }
      
      // 대체 방법: 로컬 스토어에서 가져오기
      const vuexStore = this.$store;
      if (vuexStore && vuexStore.state && vuexStore.state.auth) {
        const auth = vuexStore.state.auth;
        if (auth.user && auth.user.tag) {
          this.djTag = auth.user.tag;
          // 채팅 명령어 핸들러에 DJ 태그 설정
          chatCommandHandler.setDjTag(this.djTag);
          console.log('로컬 저장소에서 DJ 태그 가져옴:', this.djTag);
          return;
        }
      }
      
      // 기본값 설정
      this.djTag = 'bxd';
      // 채팅 명령어 핸들러에 DJ 태그 설정
      chatCommandHandler.setDjTag(this.djTag);
      console.log('기본 DJ 태그 사용:', this.djTag);
    } catch (err) {
      console.error('DJ 정보를 가져오는 중 오류 발생:', err);
      this.djTag = 'bxd';
      chatCommandHandler.setDjTag(this.djTag);
    }
  }
  
  // 채팅 이벤트 리스너 등록
  registerChatEventListener() {
    try {
      // Electron 환경에서만 실행
      if (!isElectron) {
        console.log('브라우저 환경에서는 채팅 이벤트 리스너가 비활성화됩니다.');
        return;
      }
      
      // Sopia 이벤트 시스템 접근
      const sopia = (window as any).sopia;
      if (sopia && sopia.events) {
        // 채팅 메시지 이벤트 리스너 등록
        sopia.events.on('live:chat', (data: any) => this.handleChatMessage(data));
        sopia.events.on('live:like', (data: any) => this.handleLikeEvent(data));
        
        // 채팅 메시지 전송 콜백 설정
        chatCommandHandler.setSendMessageCallback((message: string) => this.sendChatMessage(message));
        
        console.log('채팅 이벤트 리스너 등록 완료');
      }
    } catch (err) {
      console.error('채팅 이벤트 리스너 등록 중 오류 발생:', err);
    }
  }
  
  // 채팅 이벤트 리스너 제거
  unregisterChatEventListener() {
    try {
      // Electron 환경에서만 실행
      if (!isElectron) return;
      
      const sopia = (window as any).sopia;
      if (sopia && sopia.events) {
        // 이벤트 리스너 제거
        sopia.events.off('live:chat', this.handleChatMessage);
        sopia.events.off('live:like', this.handleLikeEvent);
      }
    } catch (err) {
      console.error('채팅 이벤트 리스너 제거 중 오류 발생:', err);
    }
  }
  
  // 채팅 메시지 처리
  async handleChatMessage(data: any) {
    try {
      // 채팅 명령어 핸들러로 처리 위임
      await chatCommandHandler.handleChatMessage(data);
      
      // 유저 목록 UI 갱신 (필요시)
      if (this.$route.name === 'UserTab') {
        setTimeout(() => {
          this.loadUserData();
        }, 300); // 약간의 지연을 두고 UI 갱신
      }
    } catch (err) {
      console.error('채팅 메시지 처리 중 오류 발생:', err);
    }
  }
  
  // 좋아요 이벤트 처리
  async handleLikeEvent(data: any) {
    try {
      // 채팅 명령어 핸들러로 처리 위임
      await chatCommandHandler.handleLikeEvent(data);
      
      // 유저 목록 UI 갱신 (필요시)
      if (this.$route.name === 'UserTab') {
        setTimeout(() => {
          this.loadUserData();
        }, 300); // 약간의 지연을 두고 UI 갱신
      }
    } catch (err) {
      console.error('좋아요 이벤트 처리 중 오류 발생:', err);
    }
  }
  
  // 유저 레벨 계산
  calculateUserLevel(user: UserData) {
    try {
      // 새로운 레벨 계산 로직: 경험치가 (현재 레벨 * 100)을 넘으면 레벨업
      const requiredExp = user.level * 100;
      
      if (user.point >= requiredExp) {
        // 레벨업
        user.level += 1;
        console.log(`${user.nickname} 레벨업! ${user.level-1} -> ${user.level}`);
      }
    } catch (err) {
      console.error('유저 레벨 계산 중 오류 발생:', err);
    }
  }
  
  // 경험치 퍼센트 계산 (프로그레스 바용)
  calculateExpPercentage(exp: number, level: number): number {
    const requiredExp = level * 100;
    return Math.min(Math.floor((exp / requiredExp) * 100), 100);
  }
  
  // 새 유저 생성
  createNewUser(author: any): UserData {
    return {
      nickname: author.nickname || '',
      tag: author.tag || '',
      last_attend: Date.now(),
      level: 1,
      point: 0,
      attend_count: 0,
      heart_count: 0,
      chat_count: 0,
      is_double: false,
      spoon: [0, 0, 0],
      money: 0
    };
  }
  
  // 채팅 메시지 전송
  sendChatMessage(message: string) {
    try {
      if (!isElectron) {
        console.log('브라우저 환경에서는 채팅 메시지 전송이 비활성화됩니다:', message);
        return;
      }
      
      const sopia = (window as any).sopia;
      if (sopia && sopia.chat && typeof sopia.chat.send === 'function') {
        sopia.chat.send(message);
        console.log('채팅 메시지 전송:', message);
      }
    } catch (err) {
      console.error('채팅 메시지 전송 중 오류 발생:', err);
    }
  }
  
  // 유저 액션 버튼 토글
  toggleUserActions(tag: string) {
    if (this.activeUserTag === tag) {
      this.activeUserTag = '';
    } else {
      this.activeUserTag = tag;
    }
  }
  
  // 레벨에 따른 색상 반환
  getLevelColor(level: number): string {
    if (level >= 100) return 'red darken-1';
    if (level >= 50) return 'orange';
    if (level >= 30) return 'amber';
    if (level >= 20) return 'light-green';
    if (level >= 10) return 'green';
    return 'primary'; }
  
  // 기본 사용자 데이터
  defaultUserData(): UserData {
    return {
      nickname: '',
      tag: '',
      last_attend: Date.now(),
      level: 1,
      point: 0,
      attend_count: 0,
      heart_count: 0,
      chat_count: 0,
      is_double: false,
      spoon: [0, 0, 0],
      money: 0
    };
  }
  
  // 타임스탬프를 날짜 형식으로 변환
  formatDate(timestamp: number): string {
    if (!timestamp) return '없음';
    
    try {
      const date = new Date(timestamp);
      
      // 유효한 날짜인지 확인
      if (isNaN(date.getTime())) {
        return '없음';
      }
      
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
    } catch (e) {
      console.error('날짜 변환 오류:', e);
      return '없음';
    }
  }
  
  // 신규 사용자 추가 다이얼로그 열기
  openAddUserDialog() {
    this.isNewUser = true;
    this.editedUser = this.defaultUserData();
    this.editDialog = true;
  }
  
  // 사용자 수정
  editUser(user: UserData) {
    this.isNewUser = false;
    this.editedUser = JSON.parse(JSON.stringify(user)); // Deep copy
    
    // 날짜 피커 초기화
    if (user.last_attend) {
      this.pickerDate = this.formatDate(user.last_attend);
    } else {
      this.pickerDate = this.formatDate(Date.now());
    }
    
    this.editDialog = true;
  }
  
  // 날짜 선택 시 last_attend 업데이트
  updateLastAttendDate() {
    try {
      const selectedDate = new Date(this.pickerDate);
      this.editedUser.last_attend = selectedDate.getTime();
      this.dateMenu = false;
    } catch (err) {
      console.error('출석일 업데이트 중 오류 발생:', err);
    }
  }
  
  // 사용자 저장
  async saveUser() {
    try {
      if (this.isNewUser) {
        // 신규 사용자 추가
        await userDataManager.addUser(this.editedUser);
        console.log('새 사용자 추가됨:', this.editedUser.nickname);
      } else {
        // 기존 사용자 업데이트
        await userDataManager.updateUser(this.editedUser);
        console.log('사용자 정보 업데이트됨:', this.editedUser.nickname);
      }
      
      // 데이터 다시 로드
      await userDataManager.loadData();
      
      // 데이터 변경 트리거를 위한 검색 쿼리 약간 변경 후 원복
      const originalQuery = this.searchQuery;
      this.searchQuery = originalQuery + ' ';
      await this.$nextTick();
      this.searchQuery = originalQuery;
      
      // 강제 UI 업데이트
      setTimeout(() => {
        this.$forceUpdate();
        console.log('사용자 저장 완료 및 UI 갱신됨');
      }, 100);
      
      // 다이얼로그 닫기
      this.editDialog = false;
    } catch (error) {
      console.error('사용자 저장 중 오류 발생:', error);
      alert('사용자 정보 저장에 실패했습니다.');
    }
  }
  
  // 사용자 삭제 확인
  confirmDeleteUser(user: UserData) {
    this.userToDelete = user;
    this.deleteDialog = true;
  }
  
  // 사용자 삭제
  async deleteUser() {
    try {
      if (!this.userToDelete) return;
      
      // UserDataManager를 통해 사용자 삭제
      await userDataManager.deleteUser(this.userToDelete.nickname);
      console.log('사용자 삭제됨:', this.userToDelete.nickname);
      
      // 데이터 다시 로드
      await userDataManager.loadData();
      
      // 데이터 변경 트리거를 위한 검색 쿼리 약간 변경 후 원복
      const originalQuery = this.searchQuery;
      this.searchQuery = originalQuery + ' ';
      await this.$nextTick();
      this.searchQuery = originalQuery;
      
      // 강제 UI 업데이트
      setTimeout(() => {
        this.$forceUpdate();
        console.log('사용자 삭제 완료 및 UI 갱신됨');
      }, 100);
      
      // 다이얼로그 닫기
      this.deleteDialog = false;
      this.userToDelete = null;
    } catch (error) {
      console.error('사용자 삭제 중 오류 발생:', error);
      alert('사용자 삭제에 실패했습니다.');
    }
  }

  async loadUserData() {
    try {
      console.log('loadUserData 호출: 유저 데이터 로딩 시작');
      this.loading = true;
      
      // UserDataManager 무조건 초기화 진행
      try {
        await userDataManager.init(this.djTag);
        console.log('UserDataManager 초기화 완료');
      } catch (initError) {
        console.error('UserDataManager 초기화 오류:', initError);
      }
      
      // 강제로 데이터 로드
      await userDataManager.loadData();
      
      // 데이터가 로드되었는지 확인
      const users = userDataManager.getAllUsers();
      console.log(`로드된 유저 수: ${users.length}명`);
      
      // UI 갱신을 위한 다중 방법 적용
      
      // 1. 검색어 쿼리 변경으로 반응성 트리거
      const originalQuery = this.searchQuery;
      this.searchQuery = originalQuery + ' ';
      await this.$nextTick();
      this.searchQuery = originalQuery;
      
      // 2. 강제 UI 갱신
      this.$forceUpdate();
      
      // 3. 지연 후 다시 한번 강제 갱신 (타이밍 이슈 대응)
      setTimeout(() => {
        this.$forceUpdate();
        this.loading = false;
        console.log('지연된 UI 강제 갱신 완료');
      }, 50);
      
      return true;
    } catch (error) {
      console.error('유저 데이터 로드 중 오류 발생:', error);
      
      // 오류 알림 표시
      if (this.$store && this.$store.commit) {
        this.$store.commit('setSnackbar', {
          text: '유저 데이터를 불러오는 데 실패했습니다.',
          color: 'error'
        });
      }
      
      return false;
    } finally {
      // loading 상태는 지연된 갱신에서 처리하므로 여기서는 설정하지 않음
    }
  }
  
  // 현재 표시할 사용자 목록 (컴포넌트 라이프사이클 내에서 항상 최신 상태 유지)
  get userData() {
    // 항상 최신 데이터를 가져오기 위해 직접 호출
    let users;
    try {
      users = this.searchQuery 
        ? userDataManager.searchUsers(this.searchQuery) 
        : userDataManager.getAllUsers();
    } catch (error) {
      console.error('userData getter 오류:', error);
      users = [];
    }
    
    // 유저 수 확인
    if (!users || users.length === 0) {
      // 데이터가 없는 경우 재시도
      if (!this.loading && !this.searchQuery) {
        // 데이터 로드 중이 아니고 검색 중이 아닌 경우에만 재시도
        console.log('userData getter: 데이터 없음, 지연 재시도');
        setTimeout(() => {
          this.loadUserData();
        }, 100);
      }
      return [];
    }
    
    console.log('userData getter 호출됨, 유저 수:', users.length);
    
    // 최근 출석일 기준으로 내림차순 정렬 (가장 최근에 출석한 사용자가 먼저 표시됨)
    return users.sort((a, b) => {
      const dateA = a.last_attend || 0;
      const dateB = b.last_attend || 0;
      return dateB - dateA;
    });
  }

  getAttendanceClass(last_attend: number): string {
    if (!last_attend) return '';
    
    const today = new Date().getTime();
    const lastAttendDate = new Date(last_attend).getTime();
    const diffInDays = Math.floor((today - lastAttendDate) / (1000 * 3600 * 24));

    if (diffInDays < 2) {
      return 'today'; // 2일 이내 (초록색 + 반짝이는 효과)
    } else if (diffInDays < 7) {
      return 'recent'; // 7일 이내 (초록색)
    } else if (diffInDays < 14) {
      return 'week'; // 14일 이내 (파란색)
    } else if (diffInDays < 20) {
      return 'older'; // 20일 이내 (주황색)
    } else {
      return 'danger'; // 20일 이후 (빨간색)
    }
  }

  isWithinDays(timestamp: number, days: number): boolean {
    const today = new Date().getTime();
    const lastAttendDate = new Date(timestamp).getTime();
    const diffInDays = Math.floor((today - lastAttendDate) / (1000 * 3600 * 24));
    return diffInDays <= days;
  }
}
</script>

<style scoped>
/* 입력 필드 숫자 증가/감소 화살표 제거 - 향상된 버전 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  margin: 0 !important;
  display: none !important;
}

input[type="number"] {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
}

/* Vuetify 특정 입력필드 화살표 제거 */
.v-text-field input[type="number"]::-webkit-inner-spin-button,
.v-text-field input[type="number"]::-webkit-outer-spin-button,
.capsule-input input[type="number"]::-webkit-inner-spin-button,
.capsule-input input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  margin: 0 !important;
  display: none !important;
}

.v-text-field input[type="number"],
.capsule-input input[type="number"] {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
}

.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 0;
}

.user-item {
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.user-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.active-card {
  box-shadow: 0 0 0 2px var(--v-primary-base) !important;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
}

.user-id {
  font-size: 12px;
  color: #666;
}

.level-chip {
  font-size: 11px;
  height: 22px;
}

.user-stats {
  font-size: 13px;
}

.stat-item {
  display: flex;
  width: 48%;
  justify-content: space-between;
  align-items: center;
}

.full-width {
  width: 100%;
}

.stat-label {
  color: #666;
  min-width: 60px;
  margin-right: 4px;
}

.stat-value {
  color: #000;
  font-weight: 700;
  text-align: left;
  min-width: 370px;
}

.date-value {
  font-size: 12px;
  font-weight: 600;
}

/* 최근 출석 날짜 스타일 */
.today {
  color: #9c27b0 !important; /* 보라색 */
  font-weight: 800;
  animation: glow-purple 1.5s ease-in-out infinite alternate;
}

@keyframes glow-purple {
  from {
    text-shadow: 0 0 5px rgba(156, 39, 176, 0.5);
  }
  to {
    text-shadow: 0 0 10px rgba(156, 39, 176, 0.8);
  }
}

.yesterday {
  color: #2E7D32 !important; /* 초록색 */
  font-weight: 800;
}

.recent {
  color: #388E3C !important; /* 약간 밝은 초록색 */
}

.week {
  color: #1976D2 !important; /* 파란색 */
}

.older {
  color: #ff9800 !important; /* 주황색 */
}

.danger {
  color: #f44336 !important; /* 빨간색 */
}

/* 별 반짝임 효과 */
.sparkle {
  animation: sparkle-animation 1.5s infinite ease-in-out;
}

@keyframes sparkle-animation {
  0% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
}

.user-action-btn {
  font-size: 10px;
}

.search-field {
  max-width: 500px;
  margin: 0 auto;
}

.scrollable-container {
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  min-height: calc(100vh - 140px);
  height: calc(100vh - 140px);
  max-height: calc(100vh - 140px);
}

/* 스크롤바 스타일링 */
.scrollable-container::-webkit-scrollbar {
  width: 6px;
}

.scrollable-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.scrollable-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

.scrollable-container::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.stat-item .stat-value {
  font-weight: bold;
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
  }
  to {
    text-shadow: 0 0 10px rgba(76, 175, 80, 0.8);
  }
}

/* 유저 수정 다이얼로그 스타일 */
.user-edit-dialog .v-sheet {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease;
}

.user-edit-dialog .v-sheet:hover {
  border-color: rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-edit-dialog .subtitle-1 {
  color: var(--v-primary-base);
  font-size: 14px;
  letter-spacing: 0.5px;
}

.user-edit-dialog .v-text-field--filled > .v-input__control > .v-input__slot {
  background: rgba(0, 0, 0, 0.03);
}

.user-edit-dialog .v-input__prepend-inner {
  margin-top: 10px !important;
  opacity: 0.6;
}

.user-edit-dialog .v-btn {
  letter-spacing: 0.5px;
  text-transform: none;
  font-weight: 500;
}

.user-edit-dialog .v-card__title {
  letter-spacing: 0.5px;
}

.user-edit-dialog .v-date-picker-table .v-btn.v-btn--active {
  color: white !important;
}

/* 유저 수정 다이얼로그 스타일 */
.user-edit-dialog .v-card {
  border-radius: 28px !important;
  overflow: hidden;
}

.user-edit-dialog .section-container {
  margin-bottom: 10px;
  padding: 8px;
  padding-bottom: 1px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.02);
}

.user-edit-dialog .subtitle-1 {
  color: var(--v-primary-base);
  font-size: 14px;
  letter-spacing: 0.5px;
  margin-left: 10px;
  margin-bottom: 4px;
}

.user-edit-dialog .v-input__prepend-inner {
  margin-top: 10px !important;
  opacity: 0.6;
}

.user-edit-dialog .v-btn {
  letter-spacing: 0.5px;
  text-transform: none;
  font-weight: 500;
}

.user-edit-dialog .v-card__title {
  letter-spacing: 0.5px;
}

.user-edit-dialog .v-date-picker-table .v-btn.v-btn--active {
  color: white !important;
}

/* 캡슐형 컨테이너 스타일 */
.capsule-container {
  position: relative;
  padding-top: 18px;
  margin-bottom: 4px;
}

.capsule-label {
  position: absolute;
  top: 0;
  left: 12px;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  z-index: 1;
}

/* 레벨 및 통계 정보 섹션 내 행 간격 줄이기 */
.section-container .v-row {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  margin-left: -4px !important;
  margin-right: -4px !important;
}

.section-container .v-col {
  padding: 0 4px !important;
}

/* 컴팩트 폼 스타일 */
.compact-form {
  margin: 0;
  padding: 0;
}

/* 다이얼로그 내용 여백 줄이기 */
.user-edit-dialog .v-card__text {
  padding-top: 12px !important;
}

/* 모바일 환경에서 다이얼로그 높이 조정 */
@media (max-width: 600px) {
  .user-edit-dialog .v-card__text {
    max-height: calc(100vh - 200px);
  }
}

/* 버튼 스타일 */
.rounded-pill {
  border-radius: 50px !important;
  padding-left: 20px !important;
  padding-right: 20px !important;
}

/* 캡슐형 입력 필드 스타일 */
.capsule-input .v-input__slot {
  border-radius: 50px !important;
  transition: all 0.3s ease;
  background-color: #f5f5f5 !important;
}

.capsule-input.v-input--is-focused .v-input__slot {
  border-color: var(--v-primary-base) !important;
  box-shadow: 0 0 0 2px rgba(156, 39, 176, 0.2) !important;
  background-color: #ffffff !important;
}

.capsule-input.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 38px;
}

.capsule-input .v-label {
  font-size: 13px;
}

/* 입력 필드 포커스 효과 */
.capsule-input:hover .v-input__slot {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* 수정 다이얼로그 스크롤 처리 */
.user-edit-dialog .v-card__text {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}
</style>