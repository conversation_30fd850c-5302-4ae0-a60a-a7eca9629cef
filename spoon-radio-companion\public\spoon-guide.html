<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Last-Modified" content="0">
    <meta name="cache-control" content="no-cache">
    <meta name="expires" content="0">
    <meta name="pragma" content="no-cache">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
    <title>스푼 연동 가이드 v2.7 - Firebase 내장</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        .logo {
            font-size: 48px;
            margin-bottom: 16px;
        }
        .title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 32px;
            line-height: 1.5;
        }
        .step {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            text-align: left;
            border-left: 4px solid #7c3aed;
        }
        .step-number {
            background: #7c3aed;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            margin-right: 12px;
        }
        .step-content {
            display: inline-block;
            vertical-align: top;
            width: calc(100% - 40px);
        }
        .step-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }
        .step-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }
        .btn {
            background: #7c3aed;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 8px 0;
            transition: all 0.2s ease;
        }
        .btn:hover {
            background: #6d28d9;
            transform: translateY(-2px);
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #5a6268;
        }
        .status {
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-weight: 500;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
        }
        .extension-icon {
            width: 32px;
            height: 32px;
            background: #7c3aed;
            border-radius: 6px;
            display: inline-block;
            margin: 0 8px;
            vertical-align: middle;
            position: relative;
        }
        .extension-icon::before {
            content: "🥄";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
        }
        .hidden {
            display: none;
        }
        .download-link {
            color: #7c3aed;
            text-decoration: none;
            font-weight: 600;
        }
        .download-link:hover {
            text-decoration: underline;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 16px 0;
            color: #856404;
            font-size: 14px;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🥄 Tamm 스푼 연동 가이드 <span style="background: #10b981; color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600; margin-left: 8px;">v2.7</span><span style="background: #f59e0b; color: white; padding: 4px 8px; border-radius: 8px; font-size: 11px; font-weight: 600; margin-left: 4px;">Firebase 내장</span></h1>
        <p class="subtitle">Chrome 확장프로그램을 설치하고 스푼캐스트에 연동하세요<br>
        <small style="color: #10b981; font-weight: 600;">🔧 Firebase SDK 내장으로 연동 문제 완전 해결</small></p>

        <div class="step">
            <span class="step-number">1</span>
            <div class="step-content">
                <div class="step-title">확장프로그램 다운로드</div>
                <div class="step-desc">
                    아래 버튼을 클릭하여 Tamm 스푼 연동 확장프로그램을 다운로드하세요.<br>
                    <span style="color: #dc3545; font-weight: 600;">⚠️ 중요: ZIP 파일을 반드시 압축 해제하세요!</span>
                </div>
            </div>
        </div>

        <div class="step">
            <span class="step-number">2</span>
            <div class="step-content">
                <div class="step-title">압축 해제</div>
                <div class="step-desc">
                    <span style="color: #dc3545; font-weight: 600;">🔓 필수 단계:</span> 다운로드된 ZIP 파일을 마우스 우클릭 → "압축 풀기" 또는 "Extract All"을 선택하여 폴더로 압축 해제하세요.<br>
                    <span style="color: #28a745; font-weight: 600;">💡 팁: 바탕화면이나 다운로드 폴더에 압축을 풀어두면 찾기 쉽습니다</span>
                </div>
            </div>
        </div>

        <div class="step">
            <span class="step-number">3</span>
            <div class="step-content">
                <div class="step-title">Chrome 확장프로그램 페이지 열기</div>
                <div class="step-desc">
                    Chrome 주소창에 아래 주소를 입력하세요:<br>
                    <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin: 8px 0; font-family: monospace; font-size: 14px; border: 1px solid #dee2e6;">
                        chrome://extensions/
                    </div>
                    <button class="btn" onclick="copyExtensionsUrl()" style="padding: 8px 16px; font-size: 14px; margin: 4px 0;">
                        📋 주소 복사하기
                    </button>
                    <span style="color: #28a745; font-weight: 600;">💡 팁: 복사 후 주소창에 붙여넣기(Ctrl+V)하세요</span>
                </div>
            </div>
        </div>

        <div class="step">
            <span class="step-number">4</span>
            <div class="step-content">
                <div class="step-title">개발자 모드 활성화</div>
                <div class="step-desc">
                    Chrome 확장프로그램 페이지에서 우상단의 "개발자 모드" 토글을 켜주세요
                </div>
            </div>
        </div>

        <div class="step">
            <span class="step-number">5</span>
            <div class="step-content">
                <div class="step-title">확장프로그램 설치</div>
                <div class="step-desc">
                    "압축해제된 확장 프로그램을 로드합니다" 버튼을 클릭하고, 압축 해제한 폴더를 선택하세요
                </div>
            </div>
        </div>

        <div class="step">
            <span class="step-number">6</span>
            <div class="step-content">
                <div class="step-title">스푼캐스트 로그인</div>
                <div class="step-desc">
                    아래 버튼을 클릭하여 스푼캐스트에 접속하고 로그인하세요.<br>
                    <span style="color: #7c3aed; font-weight: 600;">🔑 중요: 반드시 로그인을 완료해야 합니다!</span>
                </div>
            </div>
        </div>

        <div class="step">
            <span class="step-number">7</span>
            <div class="step-content">
                <div class="step-title">TAMM 연동하기</div>
                <div class="step-desc">
                    스푼캐스트 로그인 후:<br>
                    1. Chrome 브라우저 우상단의 <strong>확장프로그램 아이콘(퍼즐 모양)</strong>을 클릭<br>
                    2. <strong>"Tamm 스푼 연동"</strong> 확장프로그램을 클릭<br>
                    3. <strong>"🔗 TAMM에 연동하기"</strong> 버튼을 클릭<br>
                    <span style="color: #28a745; font-weight: 600;">✅ 연동 완료! 이제 Tamm 앱에서 스푼 기능을 사용할 수 있습니다.</span>
                </div>
            </div>
        </div>

        <div class="buttons">
            <button class="btn" onclick="downloadExtension()">
                📦 확장프로그램 다운로드
            </button>
            <button class="btn secondary" onclick="openSpooncast()">
                🥄 스푼캐스트로 이동
            </button>
            <button class="btn secondary" onclick="window.location.reload()">
                🔄 설치 완료 후 새로고침
            </button>
        </div>
    </div>

    <script>
        // 강력한 캐시 무효화 및 버전 체크
        (function() {
            const CURRENT_VERSION = '2.4';
            const VERSION_KEY = 'spoon_guide_version';
            
            // 즉시 모든 캐시 클리어 시도
            if ('caches' in window) {
                caches.keys().then(names => {
                    console.log('🗑️ 발견된 캐시:', names);
                    return Promise.all(names.map(name => {
                        console.log('🗑️ 캐시 삭제:', name);
                        return caches.delete(name);
                    }));
                }).then(() => {
                    console.log('✅ 모든 캐시 삭제 완료');
                });
            }
            
            // Service Worker 완전 제거
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    registrations.forEach(registration => {
                        console.log('🗑️ Service Worker 제거:', registration);
                        registration.unregister();
                    });
                });
            }
            
            // URL 파라미터에서 버전 확인
            const urlParams = new URLSearchParams(window.location.search);
            const urlVersion = urlParams.get('v');
            
            console.log(`🔄 가이드 페이지 로드 - 현재 버전: ${CURRENT_VERSION}, URL 버전: ${urlVersion}`);
            
            // 저장된 버전과 다르면 강제 새로고침
            const savedVersion = localStorage.getItem(VERSION_KEY);
            if (savedVersion && savedVersion !== CURRENT_VERSION) {
                console.log(`🔄 버전 불일치 감지: ${savedVersion} → ${CURRENT_VERSION}, 즉시 새로고침`);
                localStorage.setItem(VERSION_KEY, CURRENT_VERSION);
                
                // 즉시 강제 새로고침 (location.reload 대신 location.replace 사용)
                const newUrl = window.location.pathname + '?v=' + CURRENT_VERSION + '&t=' + Date.now() + '&force=1';
                window.location.replace(newUrl);
                return;
            }
            
            // 현재 버전 저장
            localStorage.setItem(VERSION_KEY, CURRENT_VERSION);
            
            // 페이지 제목에 실제 로드된 버전 표시
            document.title = `스푼 연동 가이드 v${CURRENT_VERSION}`;
            
        })();
        
        // 기존 스크립트
        window.addEventListener('DOMContentLoaded', function() {
            // 확장프로그램 설치 여부 확인 (개선된 방식)
            function checkExtensionInstalled() {
                return new Promise((resolve) => {
                    console.log('🔍 확장프로그램 감지 시작');
                    
                    let responded = false;
                    
                    // 방법 1: DOM 마커 확인 (즉시)
                    const checkDOMMarker = () => {
                        const marker = document.querySelector('[data-tamm-extension]');
                        if (marker) {
                            console.log('✅ DOM 마커로 확장프로그램 감지됨:', marker);
                            if (!responded) {
                                responded = true;
                                resolve(true);
                            }
                            return true;
                        }
                        return false;
                    };
                    
                    // 즉시 DOM 마커 확인
                    if (checkDOMMarker()) return;
                    
                    // 방법 2: postMessage를 통한 감지
                    const messageListener = (event) => {
                        if (event.data.type === 'EXTENSION_RESPONSE') {
                            console.log('✅ postMessage로 확장프로그램 감지됨');
                            if (!responded) {
                                responded = true;
                                window.removeEventListener('message', messageListener);
                                resolve(true);
                            }
                        }
                    };
                    
                    window.addEventListener('message', messageListener);
                    
                    // postMessage 전송
                    console.log('📡 확장프로그램에 감지 메시지 전송');
                    window.postMessage({ type: 'CHECK_EXTENSION' }, '*');
                    
                    // 더 자주 DOM 마커 확인 (0.2초, 0.5초, 1초, 2초, 3초)
                    const checkIntervals = [200, 500, 1000, 2000, 3000];
                    checkIntervals.forEach(delay => {
                        setTimeout(() => {
                            if (!responded && checkDOMMarker()) return;
                        }, delay);
                    });
                    
                    // 5초 후 응답이 없으면 미설치로 판단 (시간 연장)
                    setTimeout(() => {
                        if (!responded) {
                            console.log('❌ 확장프로그램 감지 실패 - 미설치로 판단');
                            console.log('🔍 DOM 마커 최종 확인:', document.querySelector('[data-tamm-extension]'));
                            window.removeEventListener('message', messageListener);
                            resolve(false);
                        }
                    }, 5000);
                });
            }
            
            // 페이지 로드 시 확장프로그램 확인
            window.addEventListener('load', async () => {
                console.log('📄 가이드 페이지 로드됨');
                
                const isInstalled = await checkExtensionInstalled();
                
                // document.getElementById('checking-extension').classList.add('hidden'); // This element is removed
                
                if (isInstalled) {
                    console.log('✅ 확장프로그램 설치됨 - 사용 가이드 표시');
                    // document.getElementById('extension-installed').classList.remove('hidden'); // This element is removed
                } else {
                    console.log('❌ 확장프로그램 미설치 - 설치 가이드 표시');
                    // document.getElementById('extension-not-installed').classList.remove('hidden'); // This element is removed
                }
            });
            
            // 전역 함수들을 window 객체에 등록
            window.openSpooncast = function() {
                window.open('https://www.spooncast.net/kr', '_blank');
            };
            
            window.downloadExtension = function() {
                // 확장프로그램 폴더를 ZIP으로 다운로드
                const link = document.createElement('a');
                link.href = '/spoon-extension-v7-fixed.zip';
                link.download = 'spoon-extension-v7-fixed.zip';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                console.log('📦 확장프로그램 다운로드 시작 (v2.7 Firebase 내장)');
                
                // 다운로드 후 안내 메시지
                setTimeout(() => {
                    alert(`다운로드가 완료되었습니다! (v2.7 Firebase 내장)

다음 단계:
1. 다운로드된 ZIP 파일을 압축 해제하세요
2. Chrome 확장프로그램 페이지를 열어주세요
3. 개발자 모드를 켜주세요
4. "압축해제된 확장 프로그램을 로드합니다" 클릭
5. 압축 해제한 폴더를 선택하세요
6. 설치 완료 후 이 페이지를 새로고침해주세요

🔧 Firebase SDK 내장:
- 외부 CDN 의존성 제거
- 연동 안정성 대폭 향상
- 디버깅 정보 강화

📋 문제 발생 시:
- Chrome 개발자 도구(F12)를 열고 Console 탭에서 오류 메시지를 확인해주세요`);
                }, 1000);
            };
            
            window.copyExtensionsUrl = function() {
                const url = 'chrome://extensions/';
                
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(() => {
                        alert('✅ 주소가 클립보드에 복사되었습니다!\n\nChrome 주소창에 붙여넣기(Ctrl+V) 후 Enter를 눌러주세요.');
                        console.log('✅ 클립보드에 복사됨:', url);
                    }).catch(() => {
                        // 클립보드 복사 실패시 수동 복사 안내
                        prompt('아래 주소를 복사하여 Chrome 주소창에 붙여넣어주세요:', url);
                    });
                } else {
                    // 클립보드 API 지원하지 않는 경우
                    prompt('아래 주소를 복사하여 Chrome 주소창에 붙여넣어주세요:', url);
                }
            };
            
            window.openExtensionsPage = function() {
                // 사용자에게 직접 안내
                const message = `Chrome 확장프로그램 페이지 열기:

1. Chrome 주소창에 다음을 입력하세요:
   chrome://extensions/

2. 또는 Chrome 메뉴(⋮) → 더보기 도구 → 확장프로그램

위의 "📋 주소 복사하기" 버튼을 사용하면 더 편리합니다!`;
                
                alert(message);
            };

            window.openExtensionPopup = function() {
                // 확장프로그램 사용법 안내
                alert(`확장프로그램을 사용하는 방법:

1. 브라우저 우상단의 확장프로그램 아이콘 영역을 찾아주세요
2. 퍼즐 조각 모양 아이콘을 클릭하세요
3. "Tamm 스푼 연동" 확장프로그램을 찾아 클릭하세요
4. 또는 주소창 옆의 🥄 아이콘을 직접 클릭하세요

스푼캐스트에 로그인된 상태에서 확장프로그램을 실행해주세요!`);
                
                // 스푼캐스트가 열려있지 않다면 열기
                if (!window.spoonTabOpened) {
                    window.openSpooncast();
                    window.spoonTabOpened = true;
                }
            };
        });
    </script>
</body>
</html> 