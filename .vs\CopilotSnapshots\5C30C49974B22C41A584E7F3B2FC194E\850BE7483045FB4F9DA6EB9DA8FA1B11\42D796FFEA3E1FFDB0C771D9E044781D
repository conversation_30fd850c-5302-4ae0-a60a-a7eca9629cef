﻿import { db, auth } from '../main';
import { doc, setDoc, getDoc, collection, getDocs, query, where } from 'firebase/firestore';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import * as fs from 'fs';
import * as path from 'path';
import { getAppPath } from '@/plugins/ipc-renderer';

/**
 * GitHub에서 Firebase로 사용자 데이터를 마이그레이션하는 유틸리티 클래스
 */
export class DataMigration {
  private readonly LOCAL_JSON_PATH = path.join(getAppPath('userData'), 'data', 'approved-users.json');

  /**
   * 승인된 사용자 데이터를 로컬 JSON 파일에서 가져옵니다.
   */
  private async getLocalApprovedUsers(): Promise<any> {
    try {
      // 현재 디렉토리의 src/data/approved-users.json 파일에서 기본 관리자 정보 가져오기
      const defaultAdminData = require('../data/approved-users.json');
      
      // 로컬 저장소에서 사용자 데이터 가져오기 (없으면 기본값 사용)
      if (fs.existsSync(this.LOCAL_JSON_PATH)) {
        const localData = JSON.parse(fs.readFileSync(this.LOCAL_JSON_PATH, 'utf-8'));
        return localData;
      } else {
        return defaultAdminData;
      }
    } catch (error) {
      console.error('로컬 사용자 데이터 로드 오류:', error);
      return { approved_users: [], pending_users: [] };
    }
  }

  /**
   * Firestore에 사용자가 이미 존재하는지 확인합니다.
   */
  private async userExistsInFirestore(email: string): Promise<boolean> {
    try {
      const userQuery = query(collection(db, 'users'), where('email', '==', email));
      const querySnapshot = await getDocs(userQuery);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('Firestore 사용자 확인 오류:', error);
      return false;
    }
  }

  /**
   * GitHub에서 Firebase로 관리자 계정을 마이그레이션합니다.
   * @param adminPassword 관리자 계정의 임시 비밀번호
   */
  public async migrateAdminUser(adminPassword: string): Promise<{ success: boolean, message: string }> {
    try {
      const localData = await this.getLocalApprovedUsers();
      const adminUser = localData.approved_users.find((user: any) => user.role === 'admin');
      
      if (!adminUser) {
        return { success: false, message: '관리자 계정을 찾을 수 없습니다.' };
      }

      // 이미 Firebase에 존재하는지 확인
      const exists = await this.userExistsInFirestore(adminUser.email);
      if (exists) {
        return { success: true, message: '관리자 계정이 이미 Firebase에 존재합니다.' };
      }

      // Firebase Authentication에 관리자 사용자 생성
      const userCredential = await createUserWithEmailAndPassword(auth, adminUser.email, adminPassword);
      const user = userCredential.user;

      // Firestore에 관리자 정보 저장
      await setDoc(doc(db, 'users', user.uid), {
        email: adminUser.email,
        name: adminUser.name || '관리자',
        role: 'admin',
        isApproved: true,
        createdAt: new Date(adminUser.approved_at || Date.now()),
      });

      return { success: true, message: '관리자 계정이 Firebase로 성공적으로 마이그레이션되었습니다.' };
    } catch (error: any) {
      console.error('관리자 마이그레이션 오류:', error);
      return { success: false, message: `관리자 마이그레이션 실패: ${error.message}` };
    }
  }

  /**
   * GitHub에서 승인된 모든 사용자를 Firebase로 마이그레이션합니다.
   * @param defaultPassword 사용자 계정의 임시 비밀번호
   */
  public async migrateAllApprovedUsers(defaultPassword: string): Promise<{ success: boolean, message: string, migratedCount: number }> {
    try {
      const localData = await this.getLocalApprovedUsers();
      const approvedUsers = localData.approved_users || [];
      
      if (approvedUsers.length === 0) {
        return { success: true, message: '마이그레이션할 사용자가 없습니다.', migratedCount: 0 };
      }

      let migratedCount = 0;

      for (const user of approvedUsers) {
        try {
          // 이미 Firebase에 존재하는지 확인
          const exists = await this.userExistsInFirestore(user.email);
          if (exists) {
            console.log(`사용자 ${user.email}은(는) 이미 Firebase에 존재합니다.`);
            continue;
          }

          // Firebase Authentication에 사용자 생성
          const userCredential = await createUserWithEmailAndPassword(auth, user.email, defaultPassword);
          const firebaseUser = userCredential.user;

          // Firestore에 사용자 정보 저장
          await setDoc(doc(db, 'users', firebaseUser.uid), {
            email: user.email,
            name: user.name || '사용자',
            role: user.role || 'user',
            isApproved: true,
            createdAt: new Date(user.approved_at || Date.now()),
          });

          migratedCount++;
        } catch (error) {
          console.error(`사용자 ${user.email} 마이그레이션 오류:`, error);
          // 개별 사용자 오류는 무시하고 계속 진행
        }
      }

      return { 
        success: true, 
        message: `${migratedCount}명의 사용자가 Firebase로 성공적으로 마이그레이션되었습니다.`, 
        migratedCount 
      };
    } catch (error: any) {
      console.error('사용자 마이그레이션 오류:', error);
      return { success: false, message: `사용자 마이그레이션 실패: ${error.message}`, migratedCount: 0 };
    }
  }

  /**
   * Firebase Firestore 보안 규칙을 콘솔에 출력합니다. (참고용)
   */
  public printFirestoreSecurityRules(): void {
    const securityRules = `
// Firestore 보안 규칙 (Firebase 콘솔에 복사하세요)
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 인증된 사용자만 접근 가능
    match /users/{userId} {
      // 사용자는 자신의 문서만 읽을 수 있음
      allow read: if request.auth != null && 
                    (request.auth.uid == userId || 
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      
      // 사용자는 자신의 문서만 업데이트 가능 (role과 isApproved 필드 제외)
      allow update: if request.auth != null && 
                      request.auth.uid == userId &&
                      !request.resource.data.diff(resource.data).affectedKeys().hasAny(['role', 'isApproved']);
      
      // 관리자는 모든 사용자 문서를 읽고 쓸 수 있음
      allow write: if request.auth != null && 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // 다른 컬렉션에 대한 규칙
    match /{document=**} {
      allow read, write: if request.auth != null && 
                            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isApproved == true;
    }
  }
}`;
    console.log(securityRules);
  }
}

// 싱글톤 인스턴스로 내보냅니다.
export const dataMigration = new DataMigration();