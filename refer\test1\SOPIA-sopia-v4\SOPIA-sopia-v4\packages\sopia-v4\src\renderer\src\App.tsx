import { Routes, Route } from 'react-router-dom'
import { ProtectedRoute } from './components/ProtectedRoute'
import LoginPage from './pages/login'
import SignupPage from './pages/signup'
import HomePage from './pages/home'
import { useThemeStore } from './stores/themeStore'
import { ThemeToggle } from './components/ThemeToggle'

export default function App() {
  const theme = useThemeStore((state) => state.theme)

  return (
    <div className={theme}>
      <ThemeToggle />
      <Routes>
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <LoginPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/signup"
          element={
            <ProtectedRoute requireAuth={false}>
              <SignupPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <HomePage />
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}
