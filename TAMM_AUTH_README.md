# TAMM V1 회원가입 승인 시스템

## 🚀 개요

TAMM V1에 서버 없이 동작하는 회원가입 승인 시스템이 추가되었습니다. 관리자가 수동으로 회원을 승인하는 방식으로 앱 사용을 제어할 수 있습니다.

## 📋 기능

- ✅ **서버리스 인증**: 별도의 서버 없이 로컬 파일 기반으로 동작
- 🔒 **승인 기반 시스템**: 관리자 승인 후에만 로그인 가능
- 👥 **관리자 패널**: 대기 중인 회원 목록 확인 및 승인 처리
- 🎨 **현대적 UI**: 아름다운 그라디언트와 애니메이션
- 📱 **반응형 디자인**: 다양한 화면 크기 지원
- 💾 **로그인 정보 저장**: ID/PW 저장 기능으로 편리한 로그인

## 🛠️ 설치된 컴포넌트

### 1. 인증 서비스 (`src/plugins/auth-service.ts`)
- 회원가입, 로그인, 승인 처리 로직
- 로컬 JSON 파일 기반 데이터 저장
- GitHub API 연동 옵션 (선택사항)

### 2. UI 컴포넌트
- `src/views/Auth/AuthMain.vue`: 메인 인증 화면
- `src/views/Auth/Register.vue`: 회원가입 폼
- `src/views/Auth/TammLogin.vue`: 로그인 + 관리자 패널

### 3. 데이터 파일
- `src/data/approved-users.json`: 승인된 사용자 목록

## 🔧 사용법

### 일반 사용자

1. **회원가입**
   - 앱 실행 시 자동으로 인증 화면 표시
   - 이름, 이메일, 비밀번호 입력
   - "회원가입 요청" 버튼 클릭
   - 관리자 승인 대기

2. **로그인**
   - 승인 완료 후 이메일/비밀번호로 로그인
   - "로그인 정보 저장" 체크 시 다음 로그인에서 자동 입력
   - 성공 시 기존 Spoon 로그인 화면으로 이동

### 관리자

1. **관리자 계정 설정**
   ```json
   // src/data/approved-users.json에서 기본 관리자 계정 확인
   {
     "approved_users": [
       {
         "id": "admin",
         "email": "<EMAIL>", // 변경 필요
         "name": "관리자",
         "role": "admin"
       }
     ]
   }
   ```

2. **회원 승인 처리**
   - 관리자 계정으로 로그인
   - 로그인 화면 하단에 "관리자 패널" 표시
   - 대기 중인 회원 목록 확인
   - "승인" 버튼 클릭하여 승인 처리

## ⚙️ 설정

### 1. 관리자 계정 변경
```json
// src/data/approved-users.json
{
  "approved_users": [
    {
      "id": "admin",
      "email": "<EMAIL>",
      "name": "당신의 이름",
      "password": "hashed_password", // 해시된 비밀번호
      "role": "admin",
      "approved_at": "2024-01-01T00:00:00.000Z",
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pending_users": []
}
```

### 2. GitHub 연동 (선택사항)
```typescript
// src/plugins/auth-service.ts에서 설정
private readonly GITHUB_REPO = 'your-username/tamm-users';
private readonly GITHUB_TOKEN = process.env.GITHUB_TOKEN;
```

## 🔐 보안 고려사항

1. **비밀번호 해싱**: 현재 SHA256 사용 (프로덕션에서는 bcrypt 권장)
2. **로컬 저장**: 사용자 데이터가 로컬 파일에 저장됨
3. **관리자 권한**: 관리자 계정 보안에 특별한 주의 필요

## 🚦 인증 플로우

```
사용자 앱 실행
       ↓
TAMM 인증 확인
       ↓
┌─── 인증 없음 → 회원가입/로그인 화면
│     ↓
│   회원가입 요청
│     ↓
│   관리자 승인 대기
│     ↓
│   승인 완료 → TAMM 로그인
│     ↓
└── TAMM 인증 완료
       ↓
    Spoon 로그인
       ↓
    메인 앱 사용
```

## 🐛 문제 해결

### 로그인이 안 되는 경우
1. 관리자가 승인했는지 확인
2. 이메일/비밀번호 정확성 확인
3. 로그 콘솔에서 오류 메시지 확인

### 관리자 패널이 안 보이는 경우
1. 관리자 계정으로 로그인했는지 확인
2. approved-users.json에서 role이 "admin"인지 확인

### 데이터 초기화가 필요한 경우
```bash
# 설정 파일 삭제 (주의: 모든 데이터 삭제됨)
rm ~/.tamm-auth.cfg
```

## 📝 추가 개발 사항

향후 추가할 수 있는 기능들:

1. **이메일 알림**: 승인 완료 시 이메일 전송
2. **일괄 승인**: 여러 사용자 동시 승인
3. **사용자 관리**: 승인된 사용자 비활성화/삭제
4. **감사 로그**: 모든 인증 활동 기록
5. **백업/복원**: 사용자 데이터 백업 기능

## 📞 지원

문제가 발생하거나 도움이 필요한 경우 개발자에게 문의하세요.

---

**TAMM V1 회원가입 승인 시스템** - 서버 없이 안전하게 사용자 관리하기 