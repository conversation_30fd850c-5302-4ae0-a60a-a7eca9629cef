{"name": "sopia-v4", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev --watch", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@doubleshot/nest-electron": "^0.2.8", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@heroui/react": "^2.7.5", "@nestjs/common": "^11.0.12", "@nestjs/core": "^11.0.12", "@nestjs/microservices": "^11.0.12", "class-validator": "^0.14.1", "framer-motion": "^12.6.2", "i18next": "^24.2.3", "marked": "^15.0.7", "react-i18next": "^15.4.1", "react-router-dom": "^7.4.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "zustand": "^5.0.3"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.13.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.8.1", "autoprefixer": "^10.4.17", "electron": "^35.0.3", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.23.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "postcss": "^8.4.35", "prettier": "^3.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.1", "typescript": "^5.8.2", "vite": "^6.2.3", "vite-tsconfig-paths": "^5.1.4"}}