rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 'users' 컬렉션에 대한 규칙
    match /users/{userId} {
      // 사용자 자신의 문서 읽기/쓰기 허용
      allow read, create, update: if request.auth != null && request.auth.uid == userId;
      
      // 관리자 이메일에 대한 모든 권한 허용
      allow read, write, create, update, delete: if request.auth != null && request.auth.token.email == '<EMAIL>';
      
      // 모든 로그인 사용자에게 읽기 권한 허용 (승인 대기 목록 조회용)
      allow read: if request.auth != null;
      
      // 모든 로그인 사용자에게 업데이트 권한 허용 (승인 처리용)
      allow update: if request.auth != null;
      
      // 모든 로그인 사용자에게 삭제 권한 허용 (거절 처리용)
      allow delete: if request.auth != null;
      
      // 회원가입 시 새 사용자 생성 허용
      allow create: if request.auth != null;
    }

    // 'spoon_tokens' 컬렉션에 대한 규칙
    match /spoon_tokens/{tokenId} {
      // 모든 작업 허용 (단순화)
      allow read, write, create, update, delete: if true;
    }
    
    // 전체 문서에 대한 읽기 권한 허용 (개발 편의성)
    match /{document=**} {
      allow read: if true;
    }
  }
} 