import path from 'path';
import { app, BrowserWindow , ipcMain, IpcMainEvent, dialog } from 'electron';
import { ChildProcess, execSync, spawn } from 'child_process';
import { install as npmInstall, InstallItem, InstallOptions } from 'npkgi';
import express, { Application } from 'express';
import cors from 'cors';
import { Server as HttpServer } from 'http';
import axios from 'axios';
import Module, { createRequire } from 'module';
import { bun, bunx } from './bun';
import AdmZip from 'adm-zip';

import CfgLite from 'cfg-lite';
import fs from 'fs';
import vm from 'vm';
import pkg from '../../package.json';
import { registerStpApp } from './stp-protocol';
export const USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36';

const isDevelopment = process.env.NODE_ENV !== 'production';

type PathType = 'home' | 'appData' | 'userData' | 'temp' | 'exe' | 'module' | 'desktop' | 'documents' | 'downloads' | 'music' | 'pictures' | 'videos' | 'recent' | 'logs' | 'crashDumps';
const CfgList: Record<string, any> = {};
const getPath = (type: PathType, ...args: string[]) => path.resolve(app.getPath(type), ...args);
const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

// 사용자 데이터 캐시 관리를 위한 맵
const userDataMap = new Map<string, any>();

export function registerIpcHandler() {
	ipcMain.on('cfg-lite', (evt: IpcMainEvent, prop: string, file: string, ...args: any) => {
		const key = file;
		let rtn: any = null;
		console.log(`cfg-lite: prop=${prop},file=${file},argc=${args.length}, args=${args.join()}`);
		try {
			if ( prop === 'new' ) {
				const tmp = new CfgLite(file, args[0]);
				CfgList[key] = tmp;
				rtn = true;
			} else {
				if ( typeof CfgList[key][prop] === 'function' ) {
					rtn = CfgList[key][prop](...args);
				} else {
					rtn = CfgList[key][prop];
				}
			}
			evt.returnValue = rtn;
		} catch(err: any) {
			console.log('cfg-lite: Cannot open cfg file.', file, err.message);
			evt.returnValue = false;
		}
	});

	ipcMain.on('zip:create', (evt: IpcMainEvent, src: string, dst: string) => {
		console.log('zip:create', src, dst);
		try {
			const zip = new AdmZip();
			zip.addLocalFolder(src);
			zip.writeZip(dst);
			evt.returnValue = true;
		} catch (err) {
			console.error(err);
			evt.returnValue = false;
		}
	});

	ipcMain.on('zip:uncompress-buffer', (evt: IpcMainEvent, b64str: string, dst: string) => {
		console.log('zip:uncompress-buffer', dst);
		try {
			const zip = new AdmZip(Buffer.from(b64str, 'base64'));
			zip.extractAllTo(dst, true);
			evt.returnValue = true;
		} catch (err) {
			console.error(err);
			evt.returnValue = false;
		}
	});

	ipcMain.on('isdev', (evt: IpcMainEvent) => {
		evt.returnValue = isDevelopment;
	});

	ipcMain.on('app:get-path', (evt: IpcMainEvent, type: string) => {
		evt.returnValue = app.getPath(type as any);
	});

	const buildTime = (time: Date): string => {
		const yyyy = time.getFullYear();
		const mm = (time.getMonth() + 1).toString().padStart(2, '0');
		const dd = (time.getDate()).toString().padStart(2, '0');

		const hh = time.getHours().toString().padStart(2, '0');
		const MM = time.getMinutes().toString().padStart(2, '0');
		const ss = time.getSeconds().toString().padStart(2, '0');

		return `${yyyy}${mm}${dd}-${hh}${MM}${ss}`;
	};
	const startTime = buildTime(new Date());
	ipcMain.on('start-time', (evt: IpcMainEvent, type: string) => {
		evt.returnValue = startTime;
	});

	function pickProgram(list: string[]) {
		for ( const item of list ) {
			if ( fs.existsSync(item) ) {
				return item;
			}
		}
		return '';
	}

	function getChromePathWindows() {
		try {
			const chromePath = execSync('reg query "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths\\chrome.exe" /ve', { encoding: 'utf-8' });
			const chromePathMatch = chromePath.match(/\s*REG_SZ\s*(.*?)\s*$/i);
		
			if (chromePathMatch && chromePathMatch[1]) {
				const chromeInstallPath = chromePathMatch[1];
				return chromeInstallPath;
			} else {
				console.log('Chrome path not found in the registry.');
			}
		} catch (error) {
			console.error('An error occurred:', error);
		}
		return '';
	}

	function getChromeProcessWindows() {
		try {
			const stdbuf = execSync('wmic process where "name like \'%chrome.exe%\'" get processid,workingsetsize');
			const stdout = stdbuf.toString('utf-8');
			
			const processes = stdout.trim().split('\n').slice(1); // 헤더 행 제거
		
			if (processes.length === 0) {
				console.log('실행 중인 크롬 브라우저 프로세스가 없습니다.');
				return [];
			} else {
				console.log('실행 중인 크롬 브라우저 프로세스:');
				const pids = processes.filter(line => line.trim()).map(line => {
					const [pid, memory] = line.trim().split(/\s+/);
					console.log(`- PID: ${pid}, 메모리 사용량: ${(parseInt(memory) / 1024).toFixed(2)} MB`);
					return pid;
				});
				return pids;
			}
		} catch (err) {
			console.error('프로세스 정보 가져오기 실패:', err);
		}
		return [];
	}

	function isChromeRunning() {
		switch ( process.platform ) {
			case 'win32':
				const pids = getChromeProcessWindows();
				console.log(`Chrome 실행 확인: ${pids.length > 0 ? '실행 중' : '실행 중 아님'}`);
				return pids.length > 0;
			default:
				return false;
		}
	}

	// Chrome 실행 여부 확인 핸들러 추가
	ipcMain.handle('is-chrome-running', async (evt) => {
		return isChromeRunning();
	});

	// Chrome 강제 종료 핸들러 추가
	ipcMain.handle('close-chrome', async (evt) => {
		try {
			if (process.platform === 'win32') {
				console.log('Chrome 종료 명령 실행 중...');
				// 빠른 종료를 위해 다양한 방법으로 Chrome 종료 시도
				try {
					execSync('taskkill /f /im chrome.exe', { stdio: 'ignore' });
				} catch (e) {
					// 무시하고 계속 진행
				}
				
				// 최소한의 대기 시간 (100ms)
				await sleep(100);
				
				// 더 강력한 종료 방법 시도
				try {
					execSync('wmic process where name="chrome.exe" delete', { stdio: 'ignore' });
				} catch (e) {
					// 무시하고 계속 진행
				}
				
				return true; // 항상 성공 반환 (비동기적으로 종료 중일 수 있음)
			}
			return true;
		} catch (err) {
			console.error('Chrome 종료 중 오류 발생:', err);
			return true; // 오류가 발생해도 계속 진행
		}
	});

	function checkExtVersion(version: string) {
		if ( fs.existsSync(getPath('userData', `ext-${version}`)) ) {
			return true;
		}
		if ( fs.existsSync(getPath('userData', 'login-ext')) ) {
			fs.rmSync(getPath('userData', 'login-ext'), { recursive: true });
		}
		fs.writeFileSync(getPath('userData', `ext-${version}`), '');
		return true;
	}

	let expressServer: HttpServer|null = null;
	ipcMain.handle('ext-login-open', async (evt, url: string) => {
		try {
			// install extension
			let executablePath = '';
			switch ( process.platform ) {
				case 'darwin':
					executablePath = pickProgram([
						'/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
					]);
					break;
				case 'win32':
					executablePath = pickProgram([
						getChromePathWindows(),
						`C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe`,
						`C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe`,
					]);
					break;
			}
			
			if ( !executablePath ) {
				console.log('Can not find Chrome exe file');
				return {
					success: false,
					status: '101',
					message: '크롬 실행 파일을 찾을 수 없습니다.'
				};
			}

			// Chrome 실행 여부 확인
			const isRunning = isChromeRunning();
			if (isRunning) {
				console.log('Chrome is already running, need confirmation');
				return {
					success: false,
					status: '102',
					message: 'Chrome이 이미 실행 중입니다. 먼저 종료해야 합니다.'
				};
			}

			checkExtVersion(pkg.version);

			const extensionPath = getPath('userData', 'login-ext');
			if ( !fs.existsSync(extensionPath) ) {
				console.log('로컬 확장프로그램 복사 중...');
				try {
					// 앱 실행 경로 기준으로 login-ext 폴더 경로를 여러 경로에서 시도
					const possiblePaths = [
						// 개발 환경
						path.join(app.getAppPath(), 'trash', 'login-ext'),
						// 프로덕션 환경 (실행 파일 기준)
						path.join(path.dirname(app.getPath('exe')), 'trash', 'login-ext'),
						// 프로덕션 환경 (리소스 경로)
						path.join(process.resourcesPath, 'trash', 'login-ext'),
						// 상대 경로 시도
						path.join(process.cwd(), 'trash', 'login-ext'),
						// 앱 경로에서 한 단계 위로
						path.join(app.getAppPath(), '..', 'trash', 'login-ext')
					];
					
					// 가능한 경로에서 login-ext 찾기
					let localExtPath = '';
					for (const testPath of possiblePaths) {
						console.log('확장프로그램 경로 시도:', testPath);
						if (fs.existsSync(testPath)) {
							localExtPath = testPath;
							console.log('확장프로그램 경로 찾음:', localExtPath);
							break;
						}
					}
					
					// trash/login-ext 폴더가 존재하는지 확인
					if (localExtPath && fs.existsSync(localExtPath)) {
						// 디렉토리가 없으면 생성
						if (!fs.existsSync(extensionPath)) {
							fs.mkdirSync(extensionPath, { recursive: true });
						}
						
						// 파일 복사 함수
						const copyDir = (src: string, dest: string) => {
							const entries = fs.readdirSync(src, { withFileTypes: true });
							
							for (const entry of entries) {
								const srcPath = path.join(src, entry.name);
								const destPath = path.join(dest, entry.name);
								
								if (entry.isDirectory()) {
									if (!fs.existsSync(destPath)) {
										fs.mkdirSync(destPath, { recursive: true });
									}
									copyDir(srcPath, destPath);
								} else {
									fs.copyFileSync(srcPath, destPath);
								}
							}
						};
						
						// 폴더 복사 실행
						copyDir(localExtPath, extensionPath);
						console.log('로컬 확장프로그램 복사 완료');
					} else {
						console.log('로컬 확장프로그램 폴더를 찾을 수 없습니다. 원격에서 다운로드합니다.');
						const res = await axios.get('https://sopia-v3.s3.ap-northeast-2.amazonaws.com/sopia-login-ext.zip', {
							responseType: 'arraybuffer',
						});
						const buf = res.data;
						const archive = new AdmZip(buf);
						archive.extractAllTo(extensionPath);
					}
				} catch (err) {
					console.error('확장프로그램 복사 오류:', err);
					// 오류 발생 시 원래의 다운로드 방식으로 폴백
					console.log('원격에서 다운로드합니다.');
					const res = await axios.get('https://sopia-v3.s3.ap-northeast-2.amazonaws.com/sopia-login-ext.zip', {
						responseType: 'arraybuffer',
					});
					const buf = res.data;
					const archive = new AdmZip(buf);
					archive.extractAllTo(extensionPath);
				}
			}

			const callback = new Promise((resolve, reject) => {
				if ( expressServer ) {
					expressServer.close();
					expressServer = null;
				}
				let proc: ChildProcess|null = null;
				const app = express();
				app.use(cors())
				app.use(express.json());
				app.post('/spoon-login', (req, res) => {
					console.log('body', req.body);
					res.json({});
					proc?.kill();
					resolve(req.body);
				});
				expressServer = app.listen(19595, () => {
					console.log('express listen', 19595);

					const url = 'https://www.spooncast.net/kr';
					proc = spawn(executablePath, [
						`--disable-extensions-except=${extensionPath}`,
						`--load-extension=${extensionPath}`,
						`${url}`
					]);
				});
			});

			const userInfo = await callback;
			expressServer?.close();
			expressServer = null;
			return {
				success: true,
				status: '100',
				data: userInfo,
			};
		} catch (err: any) {
			console.error(err);
			return {
				success: false,
				status: '999',
				error: err.message
			};
		}
	});

	ipcMain.on('open-chrome', (event, url: string) => {
		// install extension
		let executablePath = '';
		switch ( process.platform ) {
			case 'darwin':
				executablePath = pickProgram([
					'/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
				]);
				break;
			case 'win32':
				executablePath = pickProgram([
					getChromePathWindows(),
					`C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe`,
					`C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe`,
				]);
				break;
		}
		const proc = spawn(executablePath, [
			`${url}`
		]);
	})

	ipcMain.handle('open-dialog', async (event, options: any) => {
		return await dialog.showOpenDialog(options);
	});

	ipcMain.handle('npm:install', async (event, packages: InstallItem[], options: InstallOptions) => {
		return await npmInstall(packages, options);
	});

	ipcMain.handle('bun:install', async (event, pkgPath: string) => {
		const bunExec = await bun(['install', '--production'], {
			cwd: pkgPath,
		});

		const postscript = path.join(pkgPath, 'postscript.js');
		if ( fs.existsSync(postscript) ) {
			const scriptText = fs.readFileSync(postscript, 'utf-8');
			const script = new vm.Script(scriptText);
			const moduleObj: { exports: any } = { exports: {} };
			const bundleRequire = createRequire(postscript);
			const context = {
				require: function(name) {
					try {
						return bundleRequire(name);
					} catch {
						return __non_webpack_require__(name);
					}
				},
				Buffer,
				__dirname: path.dirname(postscript),
				__filename: postscript,
				__pkgdir: pkgPath,
				module: moduleObj,
				process,
				exports: moduleObj.exports,
				console,
				bun,
				bunx,
			};
			vm.createContext(context);
			try {
				script.runInContext(context, {
					displayErrors: true,
				});
			} catch(err) {
				console.error(err);
				return false;
			}
		}
		
		return true;
	});

	const readDirectory = (dir: string, cb: (...args: any) => any, oriDir?: string) => {
		if ( !oriDir ) {
			oriDir = dir;
			dir = '';
		}

		const target = path.resolve(oriDir, dir);
		const items = fs.readdirSync(target);
		items.forEach((item: string) => {
			const t = path.resolve(target, item);
			const st = path.join(dir, item).replace(/\\/g, '/');
			const stat = fs.statSync(t);
			cb(st, stat.isDirectory());
			if ( stat.isDirectory() ) {
				readDirectory(st, cb, oriDir);
			}
		});
	};

	ipcMain.on('package:create', (evt: IpcMainEvent, src: string, dst: string) => {
		console.log('package:create', src, dst);
		try {
			const pkg = JSON.parse(fs.readFileSync(path.join(src, 'package.json'), 'utf8'));
			let ignore: string[] = [];
			if ( pkg.sopia ) {
				ignore = (pkg?.sopia?.['ignore:upload'] || []).map((i: string) => path.join(src, i));
			}

			const zip = new AdmZip();
			readDirectory(src, (p: string, isDir: boolean) => {
				if ( !isDir ) {
					const fullPath = path.join(src, p);
					if ( ignore.includes(fullPath) ) {
						return;
					}
					zip.addLocalFile(fullPath, path.dirname(p));
				}
			});

			zip.writeZip(dst);
			evt.returnValue = true;
		} catch (err) {
			console.error(err);
			evt.returnValue = false;
		}
	});

	ipcMain.on('package:uncompress-buffer', (evt: IpcMainEvent, b64str: string, dst: string) => {
		console.log('package:uncompress-buffer', dst);

		if ( !fs.existsSync(dst) ) {
			fs.mkdirSync(dst);
		}

		try {
			const zip = new AdmZip(Buffer.from(b64str, 'base64'));
			const pkgEntry = zip.getEntry('package.json');
			if ( !pkgEntry ) {
				return false;
			}

			const pkg = JSON.parse(pkgEntry.getData().toString('utf8'));
			const ignore = (pkg?.sopia?.['ignore:fetch'] || []).map((i: string) => path.join(dst, i));
			console.log(`package:uncompress-buffer: ignoring list ${ignore.join(',')}`);

			zip.getEntries().forEach((entry) => {
				const target = path.join(dst, entry.entryName);
				if ( fs.existsSync(target) ) {
					if ( ignore.includes(target) ) {
						return;
					}
				}
				const dirname = path.dirname(target);
				if ( !fs.existsSync(dirname) ) {
					fs.mkdirSync(dirname, { recursive: true });
				}
				zip.extractEntryTo(entry, dirname, false, true);
			});

			evt.returnValue = true;
		} catch (err) {
			console.error(err);
			evt.returnValue = false;
		}
	});

	ipcMain.on('app:quit', (evt: IpcMainEvent) => {
		app.quit();
	});

	ipcMain.handle('stp:regist', async (evt, domain: string, targetFile: string, packageDir: string) => {
		if ( fs.existsSync(targetFile) ) {
			const scriptText = fs.readFileSync(targetFile, 'utf-8');
			console.log('targetFile', targetFile);
			const script = new vm.Script(scriptText);
			const moduleObj: { exports: any } = { exports: {} };
			const bundleRequire = createRequire(targetFile);
			const context = {
				require: function(name) {
					try {
						return bundleRequire(name);
					} catch {
						return __non_webpack_require__(name);
					}
				},
				Buffer,
				__dirname: path.dirname(targetFile),
				__filename: targetFile,
				__pkgdir: packageDir,
				module: moduleObj,
				process,
				exports: moduleObj.exports,
				console,
				bun,
				bunx,
			};
			vm.createContext(context);
			try {
				script.runInContext(context, {
					displayErrors: true,
				});
				let app = context.module.exports?.default ?? context.module.exports;
				registerStpApp(domain, app as Application);
				return {
					success: true,
				};
			} catch(err) {
				console.error(err);
				return {
					success: false,
					detail: err,
				};
			}
		} else {
			console.log('Can not find ', targetFile);
		}
	});

	ipcMain.handle('open-bundle-manager', async (evt) => {
		const exePath = process.execPath;
		const exeDir = path.dirname(exePath);
		const exeFile = path.join(exeDir, 'SopiaBundleManager.exe');
		spawn(exeFile, ['--mode', 'bundle-manager'], {
			detached: true,
			stdio: 'ignore',
			shell: true,
			windowsHide: true
		});
		return true;
	});

	// 사용자 데이터 로드 핸들러
	ipcMain.handle('user-data:load', async (event, djTag = 'default') => {
		try {
			// fan_level 번들 경로 설정 - 이 경로만 사용
			const fanLevelPath = path.join(app.getPath('userData'), 'bundles', 'fan_level', 'data');
			
			// fan_level 데이터 경로가 없으면 생성
			if (!fs.existsSync(fanLevelPath)) {
				fs.mkdirSync(fanLevelPath, { recursive: true });
				console.log('fan_level 데이터 디렉토리 생성됨:', fanLevelPath);
			}
			
			// fan_level 번들의 데이터 파일 경로
			const fanLevelFile = path.join(fanLevelPath, `${djTag}.json`);
			console.log('fan_level 데이터 파일 로드 경로:', fanLevelFile);
			
			// 파일이 존재하는지 확인
			if (fs.existsSync(fanLevelFile)) {
				// 파일에서 데이터 로드
				const data = fs.readFileSync(fanLevelFile, 'utf8');
				const userData = JSON.parse(data);
				console.log(`fan_level 사용자 데이터 로드 성공: ${userData.user_info ? userData.user_info.length : 0}명`);
				return userData;
			} else {
				// 파일이 없는 경우 빈 객체 반환
				console.log('사용자 데이터 파일이 없어 빈 객체를 반환합니다.');
				return { user_info: [] };
			}
		} catch (error) {
			console.error('사용자 데이터 로드 중 오류 발생:', error);
			return { user_info: [] };
		}
	});

	// 사용자 데이터 저장 핸들러
	ipcMain.handle('user-data:save', async (event, userData, djTag = 'default') => {
		try {
			// fan_level 번들 경로 설정 - 이 경로만 사용
			const fanLevelPath = path.join(app.getPath('userData'), 'bundles', 'fan_level', 'data');
			
			// 디렉토리 생성 (필요한 경우)
			if (!fs.existsSync(fanLevelPath)) {
				fs.mkdirSync(fanLevelPath, { recursive: true });
				console.log('fan_level 데이터 디렉토리 생성됨:', fanLevelPath);
			}
			
			// fan_level 번들의 데이터 파일 경로
			const fanLevelFile = path.join(fanLevelPath, `${djTag}.json`);
			
			// 데이터를 JSON 문자열로 변환하여 파일에 저장
			fs.writeFileSync(fanLevelFile, JSON.stringify(userData, null, 2), 'utf8');
			
			console.log(`사용자 데이터 저장 성공: ${userData.user_info ? userData.user_info.length : 0}명`);
			console.log(`저장 경로: ${fanLevelFile}`);
			
			return true;
		} catch (error) {
			console.error('사용자 데이터 저장 중 오류 발생:', error);
			return false;
		}
	});

	// 사용자 데이터 삭제 핸들러
	ipcMain.handle('user-data:delete', async (event, userIndex, djTag = 'default') => {
		try {
			console.log(`user-data:delete 요청됨: ${djTag}, 인덱스 ${userIndex}`);
			
			// fan_level 번들 경로 - 이 경로만 사용
			const fanLevelPath = path.join(app.getPath('userData'), 'bundles', 'fan_level', 'data');
			const fanLevelFile = path.join(fanLevelPath, `${djTag}.json`);
			
			// 파일 존재 여부 확인
			if (!fs.existsSync(fanLevelFile)) {
				console.log('삭제할 사용자 데이터 파일이 존재하지 않습니다:', fanLevelFile);
				return { success: false, error: '데이터 파일이 존재하지 않습니다.' };
			}
			
			// 데이터 읽기
			let userData = JSON.parse(fs.readFileSync(fanLevelFile, 'utf8'));
			
			// 인덱스 유효성 검사
			if (userIndex < 0 || userIndex >= userData.length) {
				return { success: false, error: '유효하지 않은 사용자 인덱스입니다.' };
			}
			
			// 사용자 삭제
			userData.splice(userIndex, 1);
			
			// 파일에 업데이트된 데이터 저장
			fs.writeFileSync(fanLevelFile, JSON.stringify(userData, null, 2), 'utf8');
			
			// 캐시 업데이트
			userDataMap.set(djTag, userData);
			
			console.log(`사용자 삭제 완료. 남은 사용자: ${userData.length}명`);
			return { success: true, userData };
		} catch (error: any) {
			console.error('사용자 삭제 중 오류 발생:', error);
			return { success: false, error: error.message || String(error) };
		}
	});
};