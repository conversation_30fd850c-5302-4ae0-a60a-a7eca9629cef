{"defaultSeverity": "warning", "extends": ["tslint:recommended"], "linterOptions": {"exclude": ["node_modules/**", "package.json"]}, "rules": {"indent": [true, "tabs", 4], "interface-name": false, "no-consecutive-blank-lines": false, "object-literal-sort-keys": false, "ordered-imports": false, "quotemark": [true, "single"], "comment-format": {"severity": "warning"}, "no-console": false, "only-arrow-functions": false, "one-variable-per-declaration": false, "object-literal-shorthand": [true, {"method": "always"}], "no-bitwise": false, "no-string-literal": false}}