<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>타므</title>
    <script src="tailwindcss.js"></script>
    <style type="text/tailwindcss">
        .btn {
            @apply py-2.5 px-5 mb-2 text-sm font-medium text-white focus:outline-none bg-purple-600 rounded-lg border border-purple-200 hover:bg-purple-800 hover:text-white focus:z-10 focus:ring-4 focus:ring-purple-300 transition-all duration-200 shadow-md;
        }
        body {
            @apply bg-gray-50;
        }
        .container {
            @apply p-4 rounded-lg shadow-sm bg-white border border-gray-100;
        }
        .app-title {
            @apply text-lg font-semibold text-purple-700 mb-4;
        }
    </style>
</head>
<body>
    <div class="flex items-center justify-center" style="height: 200px; width: 200px;">
        <div id="login-page" class="hidden container text-center">
            <h2 class="app-title">계정 연결</h2>
            <button class="btn flex items-center mx-auto" id="login-btn">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd" />
                </svg>
                연결
            </button>
        </div>
        <div id="not-login" class="hidden container">
            <h3 class="text-red-600 font-medium">로그인 정보를 찾을 수 없습니다.</h3>
            <p class="text-gray-600">로그인 후 다시 시도해주세요.</p>
        </div>
        <div id="wrong-page" class="hidden container">
            <h3 class="text-red-600 font-medium">이런. 여기는 제 집이 아닙니다.</h3>
            <p class="text-gray-600">스푼라디오 페이지에서 다시 시도해주세요.</p>
        </div>
    </div>
    <script src="popup.js"></script>
</body>
</html>