@echo off
echo ========================================
echo Tamm 스푼 연동 웹앱 빌드 및 배포 시작
echo ========================================
echo.

echo [1/3] 현재 디렉토리 확인...
cd /d "%~dp0"
echo 현재 위치: %CD%
echo.

echo [2/3] React 앱 빌드 중...
echo npm run build 실행...
call npm run build
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 빌드 실패! 오류를 확인하세요.
    pause
    exit /b 1
)
echo ✅ 빌드 완료!
echo.

echo [3/3] Firebase 호스팅 배포 중...
echo firebase deploy --only hosting 실행...
call firebase deploy --only hosting
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 배포 실패! Firebase 설정을 확인하세요.
    pause
    exit /b 1
)
echo.

echo ========================================
echo 🎉 배포 완료!
echo ========================================
echo 웹앱이 성공적으로 배포되었습니다.
echo.
echo Firebase 콘솔에서 확인하세요:
echo https://console.firebase.google.com/
echo.
pause 