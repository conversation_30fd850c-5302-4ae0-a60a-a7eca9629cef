(window["webpackJsonpspooncast-web-next"] = window["webpackJsonpspooncast-web-next"] || []).push([
    [278], {
        2489: function(t, e, n) {
            "use strict";
            n.r(e);
            var r = {
                    log: "log",
                    debug: "debug",
                    info: "info",
                    warn: "warn",
                    error: "error"
                },
                i = console,
                o = {};
            Object.keys(r).forEach((function(t) {
                o[t] = i[t]
            }));
            var a = "Datadog Browser SDK:",
                s = {
                    debug: o.debug.bind(i, a),
                    log: o.log.bind(i, a),
                    info: o.info.bind(i, a),
                    warn: o.warn.bind(i, a),
                    error: o.error.bind(i, a)
                },
                u = "https://docs.datadoghq.com";

            function c(t, e) {
                return function() {
                    for (var n = [], r = 0; r < arguments.length; r++) n[r] = arguments[r];
                    try {
                        return t.apply(void 0, n)
                    } catch (t) {
                        s.error(e, t)
                    }
                }
            }
            var l, d = function(t, e, n) {
                    if (n || 2 === arguments.length)
                        for (var r, i = 0, o = e.length; i < o; i++) !r && i in e || (r || (r = Array.prototype.slice.call(e, 0, i)), r[i] = e[i]);
                    return t.concat(r || Array.prototype.slice.call(e))
                },
                f = !1;

            function p(t) {
                f = t
            }

            function v(t) {
                return function() {
                    return h(t, this, arguments)
                }
            }

            function h(t, e, n) {
                try {
                    return t.apply(e, n)
                } catch (t) {
                    if (m(t), l) try {
                        l(t)
                    } catch (t) {
                        m(t)
                    }
                }
            }

            function m() {
                for (var t = [], e = 0; e < arguments.length; e++) t[e] = arguments[e];
                f && s.error.apply(s, d(["[MONITOR]"], t, !1))
            }

            function g(t, e) {
                return -1 !== t.indexOf(e)
            }

            function _(t) {
                if (Array.from) return Array.from(t);
                var e = [];
                if (t instanceof Set) t.forEach((function(t) {
                    return e.push(t)
                }));
                else
                    for (var n = 0; n < t.length; n++) e.push(t[n]);
                return e
            }

            function y(t, e) {
                for (var n = 0; n < t.length; n += 1) {
                    var r = t[n];
                    if (e(r, n)) return r
                }
            }

            function b(t) {
                return Object.keys(t).map((function(e) {
                    return t[e]
                }))
            }

            function w(t) {
                return Object.keys(t).map((function(e) {
                    return [e, t[e]]
                }))
            }

            function S(t, e) {
                return t.slice(0, e.length) === e
            }

            function E(t) {
                for (var e = [], n = 1; n < arguments.length; n++) e[n - 1] = arguments[n];
                return e.forEach((function(e) {
                    for (var n in e) Object.prototype.hasOwnProperty.call(e, n) && (t[n] = e[n])
                })), t
            }

            function C() {
                if ("object" === typeof globalThis) return globalThis;
                Object.defineProperty(Object.prototype, "_dd_temp_", {
                    get: function() {
                        return this
                    },
                    configurable: !0
                });
                var t = _dd_temp_;
                return delete Object.prototype._dd_temp_, "object" !== typeof t && (t = "object" === typeof self ? self : "object" === typeof window ? window : {}), t
            }
            var T = /[^\u0000-\u007F]/;

            function k(t) {
                return T.test(t) ? void 0 !== window.TextEncoder ? (new TextEncoder).encode(t).length : new Blob([t]).size : t.length
            }

            function x(t, e) {
                var n, r = C();
                return r.Zone && "function" === typeof r.Zone.__symbol__ && (n = t[r.Zone.__symbol__(e)]), n || (n = t[e]), n
            }

            function A(t, e) {
                return x(C(), "setTimeout")(v(t), e)
            }

            function R(t) {
                x(C(), "clearTimeout")(t)
            }

            function I(t, e) {
                return x(C(), "setInterval")(v(t), e)
            }

            function O(t) {
                x(C(), "clearInterval")(t)
            }

            function N(t, e, n) {
                var r, i, o = !n || void 0 === n.leading || n.leading,
                    a = !n || void 0 === n.trailing || n.trailing,
                    s = !1;
                return {
                    throttled: function() {
                        for (var n = [], u = 0; u < arguments.length; u++) n[u] = arguments[u];
                        s ? r = n : (o ? t.apply(void 0, n) : r = n, s = !0, i = A((function() {
                            a && r && t.apply(void 0, r), s = !1, r = void 0
                        }), e))
                    },
                    cancel: function() {
                        R(i), s = !1, r = void 0
                    }
                }
            }

            function L() {}

            function M(t, e, n) {
                if ("object" !== typeof t || null === t) return JSON.stringify(t);
                var r = U(Object.prototype),
                    i = U(Array.prototype),
                    o = U(Object.getPrototypeOf(t)),
                    a = U(t);
                try {
                    return JSON.stringify(t, e, n)
                } catch (t) {
                    return "<error: unable to serialize object>"
                } finally {
                    r(), i(), o(), a()
                }
            }

            function U(t) {
                var e = t,
                    n = e.toJSON;
                return n ? (delete e.toJSON, function() {
                    e.toJSON = n
                }) : L
            }

            function D(t) {
                return E({}, t)
            }

            function P(t, e) {
                return Object.keys(t).some((function(n) {
                    return t[n] === e
                }))
            }

            function z(t) {
                return 0 === Object.keys(t).length
            }

            function B(t) {
                void 0 === t && (t = 2);
                var e = new Map,
                    n = !1;

                function r(r) {
                    if (void 0 === r && (r = 0), !n && 0 !== t) {
                        var i = 2 === t ? 3072 : 16384,
                            o = r;
                        e.forEach((function(t) {
                            o += t.getBytesCount()
                        })), o > i && (! function(t) {
                            s.warn("Customer data exceeds the recommended ".concat(t / 1024, "KiB threshold. More details: ").concat(u, "/real_user_monitoring/browser/troubleshooting/#customer-data-exceeds-the-recommended-threshold-warning"))
                        }(i), n = !0)
                    }
                }
                return {
                    createDetachedTracker: function() {
                        var t = F((function() {
                            return r(t.getBytesCount())
                        }));
                        return t
                    },
                    getOrCreateTracker: function(t) {
                        return e.has(t) || e.set(t, F(r)), e.get(t)
                    },
                    setCompressionStatus: function(e) {
                        0 === t && (t = e, r())
                    },
                    getCompressionStatus: function() {
                        return t
                    },
                    stop: function() {
                        e.forEach((function(t) {
                            return t.stop()
                        })), e.clear()
                    }
                }
            }

            function F(t) {
                var e = 0,
                    n = N((function(n) {
                        e = k(M(n)), t()
                    }), 200),
                    r = n.throttled,
                    i = n.cancel,
                    o = function() {
                        i(), e = 0
                    };
                return {
                    updateCustomerData: function(t) {
                        z(t) ? o() : r(t)
                    },
                    resetCustomerData: o,
                    getBytesCount: function() {
                        return e
                    },
                    stop: function() {
                        i()
                    }
                }
            }

            function V(t) {
                return null === t ? "null" : Array.isArray(t) ? "array" : typeof t
            }

            function H(t, e, n) {
                if (void 0 === n && (n = function() {
                        if ("undefined" !== typeof WeakSet) {
                            var t = new WeakSet;
                            return {
                                hasAlreadyBeenSeen: function(e) {
                                    var n = t.has(e);
                                    return n || t.add(e), n
                                }
                            }
                        }
                        var e = [];
                        return {
                            hasAlreadyBeenSeen: function(t) {
                                var n = e.indexOf(t) >= 0;
                                return n || e.push(t), n
                            }
                        }
                    }()), void 0 === e) return t;
                if ("object" !== typeof e || null === e) return e;
                if (e instanceof Date) return new Date(e.getTime());
                if (e instanceof RegExp) {
                    var r = e.flags || [e.global ? "g" : "", e.ignoreCase ? "i" : "", e.multiline ? "m" : "", e.sticky ? "y" : "", e.unicode ? "u" : ""].join("");
                    return new RegExp(e.source, r)
                }
                if (!n.hasAlreadyBeenSeen(e)) {
                    if (Array.isArray(e)) {
                        for (var i = Array.isArray(t) ? t : [], o = 0; o < e.length; ++o) i[o] = H(i[o], e[o], n);
                        return i
                    }
                    var a = "object" === V(t) ? t : {};
                    for (var s in e) Object.prototype.hasOwnProperty.call(e, s) && (a[s] = H(a[s], e[s], n));
                    return a
                }
            }

            function q(t) {
                return H(void 0, t)
            }

            function G() {
                for (var t, e = [], n = 0; n < arguments.length; n++) e[n] = arguments[n];
                for (var r = 0, i = e; r < i.length; r++) {
                    var o = i[r];
                    void 0 !== o && null !== o && (t = H(t, o))
                }
                return t
            }

            function j(t, e) {
                var n;
                void 0 === e && (e = 225280);
                var r = U(Object.prototype),
                    i = U(Array.prototype),
                    o = [],
                    a = new WeakMap,
                    s = Z(t, "$", void 0, o, a),
                    u = (null === (n = JSON.stringify(s)) || void 0 === n ? void 0 : n.length) || 0;
                if (!(u > e)) {
                    for (; o.length > 0 && u < e;) {
                        var c = o.shift(),
                            l = 0;
                        if (Array.isArray(c.source))
                            for (var d = 0; d < c.source.length; d++) {
                                if (u += void 0 !== (f = Z(c.source[d], c.path, d, o, a)) ? JSON.stringify(f).length : 4, u += l, l = 1, u > e) {
                                    K(e, "truncated", t);
                                    break
                                }
                                c.target[d] = f
                            } else
                                for (var d in c.source)
                                    if (Object.prototype.hasOwnProperty.call(c.source, d)) {
                                        var f;
                                        if (void 0 !== (f = Z(c.source[d], c.path, d, o, a)) && (u += JSON.stringify(f).length + l + d.length + 3, l = 1), u > e) {
                                            K(e, "truncated", t);
                                            break
                                        }
                                        c.target[d] = f
                                    }
                    }
                    return r(), i(), s
                }
                K(e, "discarded", t)
            }

            function Z(t, e, n, r, i) {
                var o = function(t) {
                    var e = t;
                    if (e && "function" === typeof e.toJSON) try {
                        return e.toJSON()
                    } catch (t) {}
                    return t
                }(t);
                if (!o || "object" !== typeof o) return function(t) {
                    if ("bigint" === typeof t) return "[BigInt] ".concat(t.toString());
                    if ("function" === typeof t) return "[Function] ".concat(t.name || "unknown");
                    if ("symbol" === typeof t) return "[Symbol] ".concat(t.description || t.toString());
                    return t
                }(o);
                var a = function(t) {
                    try {
                        if (t instanceof Event) return {
                            isTrusted: t.isTrusted
                        };
                        var e = Object.prototype.toString.call(t).match(/\[object (.*)\]/);
                        if (e && e[1]) return "[".concat(e[1], "]")
                    } catch (t) {}
                    return "[Unserializable]"
                }(o);
                if ("[Object]" !== a && "[Array]" !== a && "[Error]" !== a) return a;
                var s = t;
                if (i.has(s)) return "[Reference seen at ".concat(i.get(s), "]");
                var u = void 0 !== n ? "".concat(e, ".").concat(n) : e,
                    c = Array.isArray(o) ? [] : {};
                return i.set(s, u), r.push({
                    source: o,
                    target: c,
                    path: u
                }), c
            }

            function K(t, e, n) {
                s.warn("The data provided has been ".concat(e, " as it is over the limit of ").concat(t, " characters:"), n)
            }
            var W = function() {
                function t(t) {
                    this.onFirstSubscribe = t, this.observers = []
                }
                return t.prototype.subscribe = function(t) {
                    var e = this;
                    return !this.observers.length && this.onFirstSubscribe && (this.onLastUnsubscribe = this.onFirstSubscribe(this) || void 0), this.observers.push(t), {
                        unsubscribe: function() {
                            e.observers = e.observers.filter((function(e) {
                                return t !== e
                            })), !e.observers.length && e.onLastUnsubscribe && e.onLastUnsubscribe()
                        }
                    }
                }, t.prototype.notify = function(t) {
                    this.observers.forEach((function(e) {
                        return e(t)
                    }))
                }, t
            }();

            function Y() {
                for (var t = [], e = 0; e < arguments.length; e++) t[e] = arguments[e];
                return new W((function(e) {
                    var n = t.map((function(t) {
                        return t.subscribe((function(t) {
                            return e.notify(t)
                        }))
                    }));
                    return function() {
                        return n.forEach((function(t) {
                            return t.unsubscribe()
                        }))
                    }
                }))
            }

            function X(t) {
                var e = {},
                    n = new W,
                    r = {
                        getContext: function() {
                            return q(e)
                        },
                        setContext: function(i) {
                            "object" === V(i) ? (e = j(i), t.updateCustomerData(e)) : r.clearContext(), n.notify()
                        },
                        setContextProperty: function(r, i) {
                            e[r] = j(i), t.updateCustomerData(e), n.notify()
                        },
                        removeContextProperty: function(r) {
                            delete e[r], t.updateCustomerData(e), n.notify()
                        },
                        clearContext: function() {
                            e = {}, t.resetCustomerData(), n.notify()
                        },
                        changeObservable: n
                    };
                return r
            }
            var J, $ = {
                GRANTED: "granted",
                NOT_GRANTED: "not-granted"
            };
            ! function(t) {
                t.WRITABLE_RESOURCE_GRAPHQL = "writable_resource_graphql", t.CUSTOM_VITALS = "custom_vitals", t.TOLERANT_RESOURCE_TIMINGS = "tolerant_resource_timings", t.MICRO_FRONTEND = "micro_frontend"
            }(J || (J = {}));
            var Q = new Set;

            function tt(t) {
                Array.isArray(t) && t.filter((function(t) {
                    return P(J, t)
                })).forEach((function(t) {
                    Q.add(t)
                }))
            }

            function et(t) {
                return Q.has(t)
            }

            function nt(t) {
                return 0 !== t && 100 * Math.random() <= t
            }

            function rt(t, e) {
                return +t.toFixed(e)
            }

            function it(t) {
                return ot(t) && t >= 0 && t <= 100
            }

            function ot(t) {
                return "number" === typeof t
            }
            var at;

            function st(t) {
                return {
                    relative: t,
                    timeStamp: ct(t)
                }
            }

            function ut(t) {
                return {
                    relative: _t(t),
                    timeStamp: t
                }
            }

            function ct(t) {
                var e = dt() - performance.now();
                return e > yt() ? Math.round(gt(e, t)) : function(t) {
                    return Math.round(gt(yt(), t))
                }(t)
            }

            function lt(t) {
                return ot(t) ? rt(1e6 * t, 0) : t
            }

            function dt() {
                return (new Date).getTime()
            }

            function ft() {
                return dt()
            }

            function pt() {
                return performance.now()
            }

            function vt() {
                return {
                    relative: pt(),
                    timeStamp: ft()
                }
            }

            function ht() {
                return {
                    relative: 0,
                    timeStamp: yt()
                }
            }

            function mt(t, e) {
                return e - t
            }

            function gt(t, e) {
                return t + e
            }

            function _t(t) {
                return t - yt()
            }

            function yt() {
                return void 0 === at && (at = performance.timing.navigationStart), at
            }

            function bt(t) {
                var e = [],
                    n = At(t, "stack"),
                    r = String(t);
                return n && S(n, r) && (n = n.slice(r.length)), n && n.split("\n").forEach((function(t) {
                    var n = function(t) {
                        var e = St.exec(t);
                        if (!e) return;
                        var n = e[2] && 0 === e[2].indexOf("native"),
                            r = e[2] && 0 === e[2].indexOf("eval"),
                            i = Et.exec(e[2]);
                        r && i && (e[2] = i[1], e[3] = i[2], e[4] = i[3]);
                        return {
                            args: n ? [e[2]] : [],
                            column: e[4] ? +e[4] : void 0,
                            func: e[1] || "?",
                            line: e[3] ? +e[3] : void 0,
                            url: n ? void 0 : e[2]
                        }
                    }(t) || function(t) {
                        var e = Ct.exec(t);
                        if (!e) return;
                        return {
                            args: [],
                            column: e[3] ? +e[3] : void 0,
                            func: "?",
                            line: e[2] ? +e[2] : void 0,
                            url: e[1]
                        }
                    }(t) || function(t) {
                        var e = Tt.exec(t);
                        if (!e) return;
                        return {
                            args: [],
                            column: e[4] ? +e[4] : void 0,
                            func: e[1] || "?",
                            line: +e[3],
                            url: e[2]
                        }
                    }(t) || function(t) {
                        var e = kt.exec(t);
                        if (!e) return;
                        var n = e[3] && e[3].indexOf(" > eval") > -1,
                            r = xt.exec(e[3]);
                        n && r && (e[3] = r[1], e[4] = r[2], e[5] = void 0);
                        return {
                            args: e[2] ? e[2].split(",") : [],
                            column: e[5] ? +e[5] : void 0,
                            func: e[1] || "?",
                            line: e[4] ? +e[4] : void 0,
                            url: e[3]
                        }
                    }(t);
                    n && (!n.func && n.line && (n.func = "?"), e.push(n))
                })), {
                    message: At(t, "message"),
                    name: At(t, "name"),
                    stack: e
                }
            }
            var wt = "((?:file|https?|blob|chrome-extension|native|eval|webpack|snippet|<anonymous>|\\w+\\.|\\/).*?)",
                St = new RegExp("^\\s*at (.*?) ?\\(".concat(wt).concat("(?::(\\d+))", "?").concat("(?::(\\d+))", "?\\)?\\s*$"), "i"),
                Et = new RegExp("\\((\\S*)".concat("(?::(\\d+))").concat("(?::(\\d+))", "\\)"));
            var Ct = new RegExp("^\\s*at ?".concat(wt).concat("(?::(\\d+))", "?").concat("(?::(\\d+))", "??\\s*$"), "i");
            var Tt = /^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;
            var kt = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|capacitor|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,
                xt = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i;

            function At(t, e) {
                if ("object" === typeof t && t && e in t) {
                    var n = t[e];
                    return "string" === typeof n ? n : void 0
                }
            }

            function Rt(t, e, n, r) {
                var i = [{
                        url: e,
                        column: r,
                        line: n
                    }],
                    o = function(t) {
                        var e, n, r;
                        "[object String]" === {}.toString.call(t) && (e = It.exec(t), n = e[1], r = e[2]);
                        return {
                            name: n,
                            message: r
                        }
                    }(t);
                return {
                    name: o.name,
                    message: o.message,
                    stack: i
                }
            }
            var It = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?([\s\S]*)$/;

            function Ot() {
                var t, e = new Error;
                if (!e.stack) try {
                    throw e
                } catch (t) {}
                return h((function() {
                    var n = bt(e);
                    n.stack = n.stack.slice(2), t = Nt(n)
                })), t
            }

            function Nt(t) {
                var e = Lt(t);
                return t.stack.forEach((function(t) {
                    var n = "?" === t.func ? "<anonymous>" : t.func,
                        r = t.args && t.args.length > 0 ? "(".concat(t.args.join(", "), ")") : "",
                        i = t.line ? ":".concat(t.line) : "",
                        o = t.line && t.column ? ":".concat(t.column) : "";
                    e += "\n  at ".concat(n).concat(r, " @ ").concat(t.url).concat(i).concat(o)
                })), e
            }

            function Lt(t) {
                return "".concat(t.name || "Error", ": ").concat(t.message)
            }

            function Mt(t) {
                var e = t.stackTrace,
                    n = t.originalError,
                    r = t.handlingStack,
                    i = t.startClocks,
                    o = t.nonErrorPrefix,
                    a = t.source,
                    s = t.handling,
                    u = n instanceof Error,
                    c = function(t, e, n, r) {
                        return (null === t || void 0 === t ? void 0 : t.message) && (null === t || void 0 === t ? void 0 : t.name) ? t.message : e ? "Empty message" : "".concat(n, " ").concat(M(j(r)))
                    }(e, u, o, n),
                    l = function(t, e) {
                        if (void 0 === e) return !1;
                        if (t) return !0;
                        return e.stack.length > 0 && (e.stack.length > 1 || void 0 !== e.stack[0].url)
                    }(u, e) ? Nt(e) : "No stack, consider using an instance of Error",
                    d = u ? Dt(n, a) : void 0;
                return {
                    startClocks: i,
                    source: a,
                    handling: s,
                    handlingStack: r,
                    originalError: n,
                    type: null === e || void 0 === e ? void 0 : e.name,
                    message: c,
                    stack: l,
                    causes: d,
                    fingerprint: Ut(n)
                }
            }

            function Ut(t) {
                return t instanceof Error && "dd_fingerprint" in t ? String(t.dd_fingerprint) : void 0
            }

            function Dt(t, e) {
                for (var n = t, r = [];
                    (null === n || void 0 === n ? void 0 : n.cause) instanceof Error && r.length < 10;) {
                    var i = bt(n.cause);
                    r.push({
                        message: n.cause.message,
                        source: e,
                        type: null === i || void 0 === i ? void 0 : i.name,
                        stack: i && Nt(i)
                    }), n = n.cause
                }
                return r.length ? r : void 0
            }

            function Pt(t, e) {
                var n = window.__ddBrowserSdkExtensionCallback;
                n && n({
                    type: t,
                    payload: e
                })
            }

            function zt() {
                var t, e = window.navigator;
                return {
                    status: e.onLine ? "connected" : "not_connected",
                    interfaces: e.connection && e.connection.type ? [e.connection.type] : void 0,
                    effective_type: null === (t = e.connection) || void 0 === t ? void 0 : t.effectiveType
                }
            }

            function Bt(t, e) {
                var n = t.indexOf(e);
                n >= 0 && t.splice(n, 1)
            }
            var Ft, Vt = function() {
                    function t() {
                        this.buffer = []
                    }
                    return t.prototype.add = function(t) {
                        this.buffer.push(t) > 500 && this.buffer.splice(0, 1)
                    }, t.prototype.remove = function(t) {
                        Bt(this.buffer, t)
                    }, t.prototype.drain = function(t) {
                        this.buffer.forEach((function(e) {
                            return e(t)
                        })), this.buffer.length = 0
                    }, t
                }(),
                Ht = "log",
                qt = "configuration",
                Gt = "usage",
                jt = ["https://www.datadoghq-browser-agent.com", "https://www.datad0g-browser-agent.com", "https://d3uc069fcn7uxw.cloudfront.net", "https://d20xtzwzcl0ceb.cloudfront.net", "http://localhost", "<anonymous>"],
                Zt = ["ddog-gov.com"],
                Kt = new Vt,
                Wt = function(t) {
                    Kt.add((function() {
                        return Wt(t)
                    }))
                };

            function Yt(t, e) {
                var n, r, i = new W,
                    o = new Set,
                    a = !g(Zt, e.site) && nt(e.telemetrySampleRate),
                    s = ((n = {})[Ht] = a, n[qt] = a && nt(e.telemetryConfigurationSampleRate), n[Gt] = a && nt(e.telemetryUsageSampleRate), n),
                    u = {
                        is_local_file: "file:" === window.location.protocol,
                        is_worker: "WorkerGlobalScope" in self
                    };
                return Wt = function(n) {
                    var a = M(n);
                    if (s[n.type] && o.size < e.maxTelemetryEventsPerPage && !o.has(a)) {
                        var c = function(t, e, n) {
                            return G({
                                type: "telemetry",
                                date: ft(),
                                service: t,
                                version: "5.21.0",
                                source: "browser",
                                _dd: {
                                    format_version: 2
                                },
                                telemetry: G(e, {
                                    runtime_env: n,
                                    connectivity: zt()
                                }),
                                experimental_features: _(Q)
                            }, void 0 !== r ? r() : {})
                        }(t, n, u);
                        i.notify(c), Pt("telemetry", c), o.add(a)
                    }
                }, l = Jt, {
                    setContextProvider: function(t) {
                        r = t
                    },
                    observable: i,
                    enabled: a
                }
            }

            function Xt(t, e) {
                m(r.debug, t, e), Wt(E({
                    type: Ht,
                    message: t,
                    status: "debug"
                }, e))
            }

            function Jt(t, e) {
                Wt(E({
                    type: Ht,
                    status: "error"
                }, function(t) {
                    if (t instanceof Error) {
                        var e = bt(t);
                        return {
                            error: {
                                kind: e.name,
                                stack: Nt(Qt(e))
                            },
                            message: e.message
                        }
                    }
                    return {
                        error: {
                            stack: "No stack, consider using an instance of Error"
                        },
                        message: "".concat("Uncaught", " ").concat(M(t))
                    }
                }(t), e))
            }

            function $t(t) {
                Wt({
                    type: Gt,
                    usage: t
                })
            }

            function Qt(t) {
                return t.stack = t.stack.filter((function(t) {
                    return !t.url || jt.some((function(e) {
                        return S(t.url, e)
                    }))
                })), t
            }

            function te(t, e, n, r, i) {
                return ee(t, e, [n], r, i)
            }

            function ee(t, e, n, r, i) {
                var o = void 0 === i ? {} : i,
                    a = o.once,
                    s = o.capture,
                    u = o.passive,
                    c = v((function(e) {
                        (e.isTrusted || e.__ddIsTrusted || t.allowUntrustedEvents) && (a && f(), r(e))
                    })),
                    l = u ? {
                        capture: s,
                        passive: u
                    } : s,
                    d = x(e, "addEventListener");

                function f() {
                    var t = x(e, "removeEventListener");
                    n.forEach((function(n) {
                        return t.call(e, n, c, l)
                    }))
                }
                return n.forEach((function(t) {
                    return d.call(e, t, c, l)
                })), {
                    stop: f
                }
            }! function(t) {
                t.BEFORE_UNLOAD = "beforeunload", t.CLICK = "click", t.DBL_CLICK = "dblclick", t.KEY_DOWN = "keydown", t.LOAD = "load", t.POP_STATE = "popstate", t.SCROLL = "scroll", t.TOUCH_START = "touchstart", t.TOUCH_END = "touchend", t.TOUCH_MOVE = "touchmove", t.VISIBILITY_CHANGE = "visibilitychange", t.PAGE_SHOW = "pageshow", t.FREEZE = "freeze", t.RESUME = "resume", t.DOM_CONTENT_LOADED = "DOMContentLoaded", t.POINTER_DOWN = "pointerdown", t.POINTER_UP = "pointerup", t.POINTER_CANCEL = "pointercancel", t.HASH_CHANGE = "hashchange", t.PAGE_HIDE = "pagehide", t.MOUSE_DOWN = "mousedown", t.MOUSE_UP = "mouseup", t.MOUSE_MOVE = "mousemove", t.FOCUS = "focus", t.BLUR = "blur", t.CONTEXT_MENU = "contextmenu", t.RESIZE = "resize", t.CHANGE = "change", t.INPUT = "input", t.PLAY = "play", t.PAUSE = "pause", t.SECURITY_POLICY_VIOLATION = "securitypolicyviolation", t.SELECTION_CHANGE = "selectionchange", t.STORAGE = "storage"
            }(Ft || (Ft = {}));
            var ne = [];

            function re(t, e, n, r) {
                var i = function(t, e) {
                    return "".concat("_dd_c", "_").concat(t, "_").concat(e)
                }(n, r);

                function o() {
                    var t = localStorage.getItem(i);
                    return null !== t ? JSON.parse(t) : {}
                }
                ne.push(te(t, window, Ft.STORAGE, (function(t) {
                    var n = t.key;
                    i === n && e.setContext(o())
                }))), e.changeObservable.subscribe((function() {
                    localStorage.setItem(i, JSON.stringify(e.getContext()))
                })), e.setContext(G(o(), e.getContext()))
            }

            function ie() {
                var t = "",
                    e = 0;
                return {
                    isAsync: !1,
                    get isEmpty() {
                        return !t
                    },
                    write: function(n, r) {
                        var i = k(n);
                        e += i, t += n, r && r(i)
                    },
                    finish: function(t) {
                        t(this.finishSync())
                    },
                    finishSync: function() {
                        var n = {
                            output: t,
                            outputBytesCount: e,
                            rawBytesCount: e,
                            pendingData: ""
                        };
                        return t = "", e = 0, n
                    },
                    estimateEncodedBytesCount: function(t) {
                        return t.length
                    }
                }
            }

            function oe(t) {
                var e = E({}, t);
                return ["id", "name", "email"].forEach((function(t) {
                    t in e && (e[t] = String(e[t]))
                })), e
            }

            function ae(t, e) {
                e.silentMultipleInit || s.error("".concat(t, " is already initialized."))
            }

            function se() {
                var t = C().DatadogEventBridge;
                if (t) return {
                    getCapabilities: function() {
                        var e;
                        return JSON.parse((null === (e = t.getCapabilities) || void 0 === e ? void 0 : e.call(t)) || "[]")
                    },
                    getPrivacyLevel: function() {
                        var e;
                        return null === (e = t.getPrivacyLevel) || void 0 === e ? void 0 : e.call(t)
                    },
                    getAllowedWebViewHosts: function() {
                        return JSON.parse(t.getAllowedWebViewHosts())
                    },
                    send: function(e, n, r) {
                        var i = r ? {
                            id: r
                        } : void 0;
                        t.send(JSON.stringify({
                            eventType: e,
                            event: n,
                            view: i
                        }))
                    }
                }
            }

            function ue(t) {
                var e = se();
                return !!e && g(e.getCapabilities(), t)
            }

            function ce(t) {
                var e;
                void 0 === t && (t = null === (e = C().location) || void 0 === e ? void 0 : e.hostname);
                var n = se();
                return !!n && n.getAllowedWebViewHosts().some((function(e) {
                    return t === e || (n = t, r = ".".concat(e), n.slice(-r.length) === r);
                    var n, r
                }))
            }

            function le(t) {
                return t ? (parseInt(t, 10) ^ 16 * Math.random() >> parseInt(t, 10) / 4).toString(16) : "".concat(1e7, "-").concat(1e3, "-").concat(4e3, "-").concat(8e3, "-").concat(1e11).replace(/[018]/g, le)
            }
            var de, fe, pe = /([\w-]+)\s*=\s*([^;]+)/g;

            function ve(t, e) {
                for (pe.lastIndex = 0;;) {
                    var n = pe.exec(t);
                    if (!n) break;
                    if (n[1] === e) return n[2]
                }
            }

            function he(t, e, n) {
                void 0 === n && (n = "");
                var r = t.charCodeAt(e - 1),
                    i = r >= 55296 && r <= 56319 ? e + 1 : e;
                return t.length <= i ? t : "".concat(t.slice(0, i)).concat(n)
            }

            function me(t, e, n, r) {
                var i = new Date;
                i.setTime(i.getTime() + n);
                var o = "expires=".concat(i.toUTCString()),
                    a = r && r.crossSite ? "none" : "strict",
                    s = r && r.domain ? ";domain=".concat(r.domain) : "",
                    u = r && r.secure ? ";secure" : "",
                    c = r && r.partitioned ? ";partitioned" : "";
                document.cookie = "".concat(t, "=").concat(e, ";").concat(o, ";path=/;samesite=").concat(a).concat(s).concat(u).concat(c)
            }

            function ge(t) {
                return ve(document.cookie, t)
            }

            function _e(t) {
                return de || (de = function(t) {
                    var e = new Map;
                    for (pe.lastIndex = 0;;) {
                        var n = pe.exec(t);
                        if (!n) break;
                        e.set(n[1], n[2])
                    }
                    return e
                }(document.cookie)), de.get(t)
            }

            function ye(t, e) {
                me(t, "", 0, e)
            }
            var be;

            function we() {
                return Boolean(window._DATADOG_SYNTHETICS_INJECTS_RUM || _e("datadog-synthetics-injects-rum"))
            }

            function Se() {
                return null !== be && void 0 !== be ? be : be = function(t) {
                    var e;
                    void 0 === t && (t = window);
                    var n = t.navigator.userAgent;
                    if (t.chrome || /HeadlessChrome/.test(n)) return 1;
                    if (0 === (null === (e = t.navigator.vendor) || void 0 === e ? void 0 : e.indexOf("Apple")) || /safari/i.test(n) && !/chrome|android/i.test(n)) return 2;
                    if (t.document.documentMode) return 0;
                    return 3
                }()
            }
            var Ee = /^([a-zA-Z]+)=([a-z0-9-]+)$/;

            function Ce(t) {
                return z(t)
            }

            function Te(t) {
                return !Ce(t)
            }

            function ke(t) {
                return void 0 !== t.isExpired || !((void 0 === (e = t).created || dt() - Number(e.created) < 144e5) && (void 0 === e.expire || dt() < Number(e.expire)));
                var e
            }

            function xe(t) {
                t.expire = String(dt() + 9e5)
            }

            function Ae(t) {
                return w(t).map((function(t) {
                    var e = t[0],
                        n = t[1];
                    return "".concat(e, "=").concat(n)
                })).join("&")
            }

            function Re(t) {
                var e = {};
                return function(t) {
                    return !!t && (-1 !== t.indexOf("&") || Ee.test(t))
                }(t) && t.split("&").forEach((function(t) {
                    var n = Ee.exec(t);
                    if (null !== n) {
                        var r = n[1],
                            i = n[2];
                        e[r] = i
                    }
                })), e
            }

            function Ie(t) {
                var e = function(t) {
                    var e = {};
                    e.secure = !!t.useSecureSessionCookie || !!t.usePartitionedCrossSiteSessionCookie || !!t.useCrossSiteSessionCookie, e.crossSite = !!t.usePartitionedCrossSiteSessionCookie || !!t.useCrossSiteSessionCookie, e.partitioned = !!t.usePartitionedCrossSiteSessionCookie, t.trackSessionAcrossSubdomains && (e.domain = function() {
                        if (void 0 === fe) {
                            for (var t = "dd_site_test_".concat(le()), e = window.location.hostname.split("."), n = e.pop(); e.length && !ge(t);) me(t, "test", 1e3, {
                                domain: n = "".concat(e.pop(), ".").concat(n)
                            });
                            ye(t, {
                                domain: n
                            }), fe = n
                        }
                        return fe
                    }());
                    return e
                }(t);
                return function(t) {
                    if (void 0 === document.cookie || null === document.cookie) return !1;
                    try {
                        var e = "dd_cookie_test_".concat(le());
                        me(e, "test", 6e4, t);
                        var n = "test" === ge(e);
                        return ye(e, t), n
                    } catch (t) {
                        return s.error(t), !1
                    }
                }(e) ? {
                    type: "Cookie",
                    cookieOptions: e
                } : void 0
            }

            function Oe(t) {
                var e, n = {
                    isLockEnabled: 1 === Se(),
                    persistSession: (e = t, function(t) {
                        me("_dd_s", Ae(t), 9e5, e)
                    }),
                    retrieveSession: Ne,
                    expireSession: function() {
                        return function(t) {
                            me("_dd_s", Ae({
                                isExpired: "1"
                            }), 144e5, t)
                        }(t)
                    }
                };
                return function(t) {
                    if (!_e("_dd_s")) {
                        var e = _e("_dd"),
                            n = _e("_dd_r"),
                            r = _e("_dd_l"),
                            i = {};
                        e && (i.id = e), r && /^[01]$/.test(r) && (i.logs = r), n && /^[012]$/.test(n) && (i.rum = n), Te(i) && (xe(i), t.persistSession(i))
                    }
                }(n), n
            }

            function Ne() {
                return Re(ge("_dd_s"))
            }

            function Le(t) {
                localStorage.setItem("_dd_s", Ae(t))
            }

            function Me() {
                return Re(localStorage.getItem("_dd_s"))
            }

            function Ue() {
                Le({
                    isExpired: "1"
                })
            }
            var De, Pe = [];

            function ze(t, e, n) {
                var r;
                void 0 === n && (n = 0);
                var i = e.isLockEnabled,
                    o = e.persistSession,
                    a = e.expireSession,
                    s = function(t) {
                        return o(E({}, t, {
                            lock: c
                        }))
                    },
                    u = function() {
                        var t = e.retrieveSession(),
                            n = t.lock;
                        return t.lock && delete t.lock, {
                            session: t,
                            lock: n
                        }
                    };
                if (De || (De = t), t === De)
                    if (i && n >= 100) Fe(e);
                    else {
                        var c, l = u();
                        if (i) {
                            if (l.lock) return void Be(t, e, n);
                            if (c = le(), s(l.session), (l = u()).lock !== c) return void Be(t, e, n)
                        }
                        var d = t.process(l.session);
                        if (i && (l = u()).lock !== c) Be(t, e, n);
                        else {
                            if (d && (ke(d) ? a() : (xe(d), i ? s(d) : o(d))), i && (!d || !ke(d))) {
                                if ((l = u()).lock !== c) return void Be(t, e, n);
                                o(l.session), d = l.session
                            }
                            null === (r = t.after) || void 0 === r || r.call(t, d || l.session), Fe(e)
                        }
                    }
                else Pe.push(t)
            }

            function Be(t, e, n) {
                A((function() {
                    ze(t, e, n + 1)
                }), 10)
            }

            function Fe(t) {
                De = void 0;
                var e = Pe.shift();
                e && ze(e, t)
            }

            function Ve(t) {
                var e = Ie(t);
                return !e && t.allowFallbackToLocalStorage && (e = function() {
                    try {
                        var t = le(),
                            e = "".concat("_dd_test_").concat(t);
                        localStorage.setItem(e, t);
                        var n = localStorage.getItem(e);
                        return localStorage.removeItem(e), t === n ? {
                            type: "LocalStorage"
                        } : void 0
                    } catch (t) {
                        return
                    }
                }()), e
            }

            function He(t, e, n) {
                var r, i = new W,
                    o = new W,
                    a = new W,
                    s = "Cookie" === t.type ? Oe(t.cookieOptions) : {
                        isLockEnabled: !1,
                        persistSession: Le,
                        retrieveSession: Me,
                        expireSession: Ue
                    },
                    u = s.expireSession,
                    c = I((function() {
                        ze({
                            process: function(t) {
                                return ke(t) ? {
                                    isExpired: "1"
                                } : void 0
                            },
                            after: p
                        }, s)
                    }), 1e3);
                v();
                var l = N((function() {
                        ze({
                            process: function(t) {
                                if (!Ce(t)) {
                                    var r = p(t);
                                    return function(t) {
                                        if (Ce(t)) return !1;
                                        var r = n(t[e]),
                                            i = r.trackingType,
                                            o = r.isTracked;
                                        t[e] = i, delete t.isExpired, o && !t.id && (t.id = le(), t.created = String(dt()))
                                    }(r), r
                                }
                            },
                            after: function(t) {
                                Te(t) && !h() && function(t) {
                                    r = t, i.notify()
                                }(t), r = t
                            }
                        }, s)
                    }), 1e3),
                    d = l.throttled,
                    f = l.cancel;

                function p(t) {
                    return ke(t) && (t = {
                        isExpired: "1"
                    }), h() && (! function(t) {
                        return r.id !== t.id || r[e] !== t[e]
                    }(t) ? (a.notify({
                        previousState: r,
                        newState: t
                    }), r = t) : (r = {
                        isExpired: "1"
                    }, o.notify())), t
                }

                function v() {
                    ze({
                        process: function(t) {
                            if (Ce(t)) return {
                                isExpired: "1"
                            }
                        },
                        after: function(t) {
                            r = t
                        }
                    }, s)
                }

                function h() {
                    return void 0 !== r[e]
                }
                return {
                    expandOrRenewSession: d,
                    expandSession: function() {
                        ze({
                            process: function(t) {
                                return h() ? p(t) : void 0
                            }
                        }, s)
                    },
                    getSession: function() {
                        return r
                    },
                    renewObservable: i,
                    expireObservable: o,
                    sessionStateUpdateObservable: a,
                    restartSession: v,
                    expire: function() {
                        f(), u(), p({
                            isExpired: "1"
                        })
                    },
                    stop: function() {
                        O(c)
                    },
                    updateSessionState: function(t) {
                        ze({
                            process: function(e) {
                                return E({}, e, t)
                            },
                            after: p
                        }, s)
                    }
                }
            }

            function qe(t) {
                return Ge(t, location.href).href
            }

            function Ge(t, e) {
                var n = function() {
                    if (void 0 === je) try {
                        var t = new Ze("http://test/path");
                        je = "http://test/path" === t.href
                    } catch (t) {
                        je = !1
                    }
                    return je ? Ze : void 0
                }();
                if (n) try {
                    return void 0 !== e ? new n(t, e) : new n(t)
                } catch (n) {
                    throw new Error("Failed to construct URL: ".concat(String(n), " ").concat(M({
                        url: t,
                        base: e
                    })))
                }
                if (void 0 === e && !/:/.test(t)) throw new Error("Invalid URL: '".concat(t, "'"));
                var r = document,
                    i = r.createElement("a");
                if (void 0 !== e) {
                    var o = (r = document.implementation.createHTMLDocument("")).createElement("base");
                    o.href = e, r.head.appendChild(o), r.body.appendChild(i)
                }
                return i.href = t, i
            }
            var je, Ze = URL;

            function Ke(t, e, n) {
                var r = function(t, e) {
                    var n = "/api/v2/".concat(e),
                        r = t.proxy;
                    if ("string" === typeof r) {
                        var i = qe(r);
                        return function(t) {
                            return "".concat(i, "?ddforward=").concat(encodeURIComponent("".concat(n, "?").concat(t)))
                        }
                    }
                    if ("function" === typeof r) return function(t) {
                        return r({
                            path: n,
                            parameters: t
                        })
                    };
                    var o = function(t, e) {
                        var n = e.site,
                            r = void 0 === n ? "datadoghq.com" : n,
                            i = e.internalAnalyticsSubdomain;
                        if ("logs" === t && e.usePciIntake && "datadoghq.com" === r) return "pci.browser-intake-datadoghq.com";
                        if (i && "datadoghq.com" === r) return "".concat(i, ".").concat("datadoghq.com");
                        if ("dd0g-gov.com" === r) return "http-intake.logs.".concat(r);
                        var o = r.split("."),
                            a = o.pop();
                        return "browser-intake-".concat(o.join("-"), ".").concat(a)
                    }(e, t);
                    return function(t) {
                        return "https://".concat(o).concat(n, "?").concat(t)
                    }
                }(t, e);
                return {
                    build: function(i, o) {
                        var a = function(t, e, n, r, i) {
                            var o = t.clientToken,
                                a = t.internalAnalyticsSubdomain,
                                s = i.retry,
                                u = i.encoding,
                                c = ["sdk_version:".concat("5.21.0"), "api:".concat(r)].concat(n);
                            s && c.push("retry_count:".concat(s.count), "retry_after:".concat(s.lastFailureStatus));
                            var l = ["ddsource=browser", "ddtags=".concat(encodeURIComponent(c.join(","))), "dd-api-key=".concat(o), "dd-evp-origin-version=".concat(encodeURIComponent("5.21.0")), "dd-evp-origin=browser", "dd-request-id=".concat(le())];
                            u && l.push("dd-evp-encoding=".concat(u));
                            "rum" === e && l.push("batch_time=".concat(ft()));
                            a && l.reverse();
                            return l.join("&")
                        }(t, e, n, i, o);
                        return r(a)
                    },
                    urlPrefix: r(""),
                    trackType: e
                }
            }
            var We = /[^a-z0-9_:./-]/;

            function Ye(t, e) {
                var n = 200 - t.length - 1;
                (e.length > n || We.test(e)) && s.warn("".concat(t, " value doesn't meet tag requirements and will be sanitized. More details: ").concat(u, "/getting_started/tagging/#defining-tags"));
                var r = e.replace(/,/g, "_");
                return "".concat(t, ":").concat(r)
            }

            function Xe(t) {
                var e = t.site || "datadoghq.com",
                    n = function(t) {
                        var e = t.env,
                            n = t.service,
                            r = t.version,
                            i = t.datacenter,
                            o = [];
                        return e && o.push(Ye("env", e)), n && o.push(Ye("service", n)), r && o.push(Ye("version", r)), i && o.push(Ye("datacenter", i)), o
                    }(t),
                    r = function(t, e) {
                        return {
                            logsEndpointBuilder: Ke(t, "logs", e),
                            rumEndpointBuilder: Ke(t, "rum", e),
                            sessionReplayEndpointBuilder: Ke(t, "replay", e)
                        }
                    }(t, n),
                    i = function(t, e) {
                        var n = b(t).map((function(t) {
                            return t.urlPrefix
                        }));
                        "datadoghq.com" === e && n.push("https://".concat("pci.browser-intake-datadoghq.com", "/"));
                        return n
                    }(r, e),
                    o = function(t, e, n) {
                        if (!t.replica) return;
                        var r = E({}, t, {
                                site: "datadoghq.com",
                                clientToken: t.replica.clientToken
                            }),
                            i = {
                                logsEndpointBuilder: Ke(r, "logs", n),
                                rumEndpointBuilder: Ke(r, "rum", n)
                            };
                        return e.push.apply(e, b(i).map((function(t) {
                            return t.urlPrefix
                        }))), E({
                            applicationId: t.replica.applicationId
                        }, i)
                    }(t, i, n);
                return E({
                    isIntakeUrl: function(t) {
                        return i.some((function(e) {
                            return 0 === t.indexOf(e)
                        }))
                    },
                    replica: o,
                    site: e
                }, r)
            }
            var Je = {
                    ALLOW: "allow",
                    MASK: "mask",
                    MASK_USER_INPUT: "mask-user-input"
                },
                $e = {
                    ALL: "all",
                    SAMPLED: "sampled"
                };

            function Qe(t, e) {
                return void 0 === t || null === t || "string" === typeof t || (s.error("".concat(e, " must be defined as a string")), !1)
            }

            function tn(t) {
                var e = V(t);
                return "string" === e || "function" === e || t instanceof RegExp
            }

            function en(t, e, n) {
                return void 0 === n && (n = !1), t.some((function(t) {
                    try {
                        if ("function" === typeof t) return t(e);
                        if (t instanceof RegExp) return t.test(e);
                        if ("string" === typeof t) return n ? S(e, t) : t === e
                    } catch (t) {
                        s.error(t)
                    }
                    return !1
                }))
            }

            function nn(t) {
                0 !== t.status || t.isAborted || (t.traceId = void 0, t.spanId = void 0, t.traceSampled = void 0)
            }

            function rn(t, e, n, r) {
                if (void 0 !== on() && n.findTrackedSession()) {
                    var i = y(t.allowedTracingUrls, (function(t) {
                        return en([t.match], e.url, !0)
                    }));
                    i && (e.traceSampled = !ot(t.traceSampleRate) || nt(t.traceSampleRate), (e.traceSampled || t.traceContextInjection === $e.ALL) && (e.traceId = new an, e.spanId = new an, r(function(t, e, n, r) {
                        var i = {};
                        return r.forEach((function(r) {
                            switch (r) {
                                case "datadog":
                                    E(i, {
                                        "x-datadog-origin": "rum",
                                        "x-datadog-parent-id": e.toDecimalString(),
                                        "x-datadog-sampling-priority": n ? "1" : "0",
                                        "x-datadog-trace-id": t.toDecimalString()
                                    });
                                    break;
                                case "tracecontext":
                                    E(i, {
                                        traceparent: "00-0000000000000000".concat(t.toPaddedHexadecimalString(), "-").concat(e.toPaddedHexadecimalString(), "-0").concat(n ? "1" : "0")
                                    });
                                    break;
                                case "b3":
                                    E(i, {
                                        b3: "".concat(t.toPaddedHexadecimalString(), "-").concat(e.toPaddedHexadecimalString(), "-").concat(n ? "1" : "0")
                                    });
                                    break;
                                case "b3multi":
                                    E(i, {
                                        "X-B3-TraceId": t.toPaddedHexadecimalString(),
                                        "X-B3-SpanId": e.toPaddedHexadecimalString(),
                                        "X-B3-Sampled": n ? "1" : "0"
                                    })
                            }
                        })), i
                    }(e.traceId, e.spanId, e.traceSampled, i.propagatorTypes))))
                }
            }

            function on() {
                return window.crypto || window.msCrypto
            }
            var an = function() {
                    function t() {
                        this.buffer = new Uint8Array(8), on().getRandomValues(this.buffer), this.buffer[0] = 127 & this.buffer[0]
                    }
                    return t.prototype.toString = function(t) {
                        var e = this.readInt32(0),
                            n = this.readInt32(4),
                            r = "";
                        do {
                            var i = e % t * 4294967296 + n;
                            e = Math.floor(e / t), n = Math.floor(i / t), r = (i % t).toString(t) + r
                        } while (e || n);
                        return r
                    }, t.prototype.toDecimalString = function() {
                        return this.toString(10)
                    }, t.prototype.toPaddedHexadecimalString = function() {
                        var t = this.toString(16);
                        return Array(17 - t.length).join("0") + t
                    }, t.prototype.readInt32 = function(t) {
                        return 16777216 * this.buffer[t] + (this.buffer[t + 1] << 16) + (this.buffer[t + 2] << 8) + this.buffer[t + 3]
                    }, t
                }(),
                sn = ["tracecontext", "datadog"];

            function un(t) {
                var e, n;
                if (t.applicationId)
                    if (void 0 === t.sessionReplaySampleRate || it(t.sessionReplaySampleRate))
                        if (void 0 === t.traceSampleRate || it(t.traceSampleRate))
                            if (void 0 === t.excludedActivityUrls || Array.isArray(t.excludedActivityUrls)) {
                                var r = function(t) {
                                    if (void 0 !== t.allowedTracingUrls) {
                                        if (!Array.isArray(t.allowedTracingUrls)) return void s.error("Allowed Tracing URLs should be an array");
                                        if (0 !== t.allowedTracingUrls.length && void 0 === t.service) return void s.error("Service needs to be configured when tracing is enabled");
                                        var e = [];
                                        return t.allowedTracingUrls.forEach((function(t) {
                                            tn(t) ? e.push({
                                                match: t,
                                                propagatorTypes: sn
                                            }) : ! function(t) {
                                                var e = t;
                                                return "object" === V(e) && tn(e.match) && Array.isArray(e.propagatorTypes)
                                            }(t) ? s.warn("Allowed Tracing Urls parameters should be a string, RegExp, function, or an object. Ignoring parameter", t) : e.push(t)
                                        })), e
                                    }
                                    return []
                                }(t);
                                if (r) {
                                    var i = function(t) {
                                        var e, n, r, i, o;
                                        if (t && t.clientToken)
                                            if (void 0 === t.sessionSampleRate || it(t.sessionSampleRate))
                                                if (void 0 === t.telemetrySampleRate || it(t.telemetrySampleRate))
                                                    if (void 0 === t.telemetryConfigurationSampleRate || it(t.telemetryConfigurationSampleRate))
                                                        if (void 0 === t.telemetryUsageSampleRate || it(t.telemetryUsageSampleRate)) {
                                                            if (Qe(t.version, "Version") && Qe(t.env, "Env") && Qe(t.service, "Service"))
                                                                if (void 0 === t.trackingConsent || P($, t.trackingConsent)) {
                                                                    var a;
                                                                    if (!t.site || (a = t.site, /(datadog|ddog|datad0g|dd0g)/.test(a))) return E({
                                                                        beforeSend: t.beforeSend && c(t.beforeSend, "beforeSend threw an error:"),
                                                                        sessionStoreStrategyType: Ve(t),
                                                                        sessionSampleRate: null !== (e = t.sessionSampleRate) && void 0 !== e ? e : 100,
                                                                        telemetrySampleRate: null !== (n = t.telemetrySampleRate) && void 0 !== n ? n : 20,
                                                                        telemetryConfigurationSampleRate: null !== (r = t.telemetryConfigurationSampleRate) && void 0 !== r ? r : 5,
                                                                        telemetryUsageSampleRate: null !== (i = t.telemetryUsageSampleRate) && void 0 !== i ? i : 5,
                                                                        service: t.service || void 0,
                                                                        silentMultipleInit: !!t.silentMultipleInit,
                                                                        allowUntrustedEvents: !!t.allowUntrustedEvents,
                                                                        trackingConsent: null !== (o = t.trackingConsent) && void 0 !== o ? o : $.GRANTED,
                                                                        storeContextsAcrossPages: !!t.storeContextsAcrossPages,
                                                                        batchBytesLimit: 16384,
                                                                        eventRateLimiterThreshold: 3e3,
                                                                        maxTelemetryEventsPerPage: 15,
                                                                        flushTimeout: 3e4,
                                                                        batchMessagesLimit: 50,
                                                                        messageBytesLimit: 262144
                                                                    }, Xe(t));
                                                                    s.error("Site should be a valid Datadog site. Learn more here: ".concat(u, "/getting_started/site/."))
                                                                } else s.error('Tracking Consent should be either "granted" or "not-granted"')
                                                        } else s.error("Telemetry Usage Sample Rate should be a number between 0 and 100");
                                        else s.error("Telemetry Configuration Sample Rate should be a number between 0 and 100");
                                        else s.error("Telemetry Sample Rate should be a number between 0 and 100");
                                        else s.error("Session Sample Rate should be a number between 0 and 100");
                                        else s.error("Client Token is not configured, we will not send any data.")
                                    }(t);
                                    if (i) return E({
                                        applicationId: t.applicationId,
                                        version: t.version || void 0,
                                        actionNameAttribute: t.actionNameAttribute,
                                        sessionReplaySampleRate: null !== (e = t.sessionReplaySampleRate) && void 0 !== e ? e : 0,
                                        startSessionReplayRecordingManually: !!t.startSessionReplayRecordingManually,
                                        traceSampleRate: t.traceSampleRate,
                                        allowedTracingUrls: r,
                                        excludedActivityUrls: null !== (n = t.excludedActivityUrls) && void 0 !== n ? n : [],
                                        workerUrl: t.workerUrl,
                                        compressIntakeRequests: !!t.compressIntakeRequests,
                                        trackUserInteractions: !!t.trackUserInteractions,
                                        trackViewsManually: !!t.trackViewsManually,
                                        trackResources: !!t.trackResources,
                                        trackLongTasks: !!t.trackLongTasks,
                                        subdomain: t.subdomain,
                                        defaultPrivacyLevel: P(Je, t.defaultPrivacyLevel) ? t.defaultPrivacyLevel : Je.MASK,
                                        customerDataTelemetrySampleRate: 1,
                                        traceContextInjection: P($e, t.traceContextInjection) ? t.traceContextInjection : $e.ALL
                                    }, i)
                                }
                            } else s.error("Excluded Activity Urls should be an array");
                else s.error("Trace Sample Rate should be a number between 0 and 100");
                else s.error("Session Replay Sample Rate should be a number between 0 and 100");
                else s.error("Application ID is not configured, no RUM data will be collected.")
            }

            function cn(t) {
                var e = new Set;
                return Array.isArray(t.allowedTracingUrls) && t.allowedTracingUrls.length > 0 && t.allowedTracingUrls.forEach((function(t) {
                    tn(t) ? sn.forEach((function(t) {
                        return e.add(t)
                    })) : "object" === V(t) && Array.isArray(t.propagatorTypes) && t.propagatorTypes.forEach((function(t) {
                        return e.add(t)
                    }))
                })), _(e)
            }

            function ln(t, e, n, r) {
                var i, o, a, u, c = t.ignoreInitIfSyntheticsWillInjectRum,
                    l = t.startDeflateWorker,
                    d = new Vt,
                    f = n.observable.subscribe(p);

                function p() {
                    if (a && u && n.isGranted()) {
                        var t;
                        if (f.unsubscribe(), u.trackViewsManually) {
                            if (!i) return;
                            d.remove(i.callback), t = i.options
                        }
                        var e = r(u, o, t);
                        d.drain(e)
                    }
                }
                return {
                    init: function(t) {
                        if (t) {
                            tt(t.enableExperimentalFeatures);
                            var e = ce();
                            if (e && (t = function(t) {
                                    var e, n;
                                    return E({}, t, {
                                        applicationId: "00000000-aaaa-0000-aaaa-000000000000",
                                        clientToken: "empty",
                                        sessionSampleRate: 100,
                                        defaultPrivacyLevel: null !== (e = t.defaultPrivacyLevel) && void 0 !== e ? e : null === (n = se()) || void 0 === n ? void 0 : n.getPrivacyLevel()
                                    })
                                }(t)), a = t, function(t) {
                                    Wt({
                                        type: qt,
                                        configuration: t
                                    })
                                }(function(t) {
                                    var e, n = {
                                        session_sample_rate: (e = t).sessionSampleRate,
                                        telemetry_sample_rate: e.telemetrySampleRate,
                                        telemetry_configuration_sample_rate: e.telemetryConfigurationSampleRate,
                                        telemetry_usage_sample_rate: e.telemetryUsageSampleRate,
                                        use_before_send: !!e.beforeSend,
                                        use_cross_site_session_cookie: e.useCrossSiteSessionCookie,
                                        use_partitioned_cross_site_session_cookie: e.usePartitionedCrossSiteSessionCookie,
                                        use_secure_session_cookie: e.useSecureSessionCookie,
                                        use_proxy: !!e.proxy,
                                        silent_multiple_init: e.silentMultipleInit,
                                        track_session_across_subdomains: e.trackSessionAcrossSubdomains,
                                        allow_fallback_to_local_storage: !!e.allowFallbackToLocalStorage,
                                        store_contexts_across_pages: !!e.storeContextsAcrossPages,
                                        allow_untrusted_events: !!e.allowUntrustedEvents,
                                        tracking_consent: e.trackingConsent
                                    };
                                    return E({
                                        session_replay_sample_rate: t.sessionReplaySampleRate,
                                        start_session_replay_recording_manually: t.startSessionReplayRecordingManually,
                                        trace_sample_rate: t.traceSampleRate,
                                        trace_context_injection: t.traceContextInjection,
                                        action_name_attribute: t.actionNameAttribute,
                                        use_allowed_tracing_urls: Array.isArray(t.allowedTracingUrls) && t.allowedTracingUrls.length > 0,
                                        selected_tracing_propagators: cn(t),
                                        default_privacy_level: t.defaultPrivacyLevel,
                                        use_excluded_activity_urls: Array.isArray(t.excludedActivityUrls) && t.excludedActivityUrls.length > 0,
                                        use_worker_url: !!t.workerUrl,
                                        compress_intake_requests: t.compressIntakeRequests,
                                        track_views_manually: t.trackViewsManually,
                                        track_user_interactions: t.trackUserInteractions,
                                        track_resources: t.trackResources,
                                        track_long_task: t.trackLongTasks
                                    }, n)
                                }(t)), u) ae("DD_RUM", t);
                            else if (!c || !we()) {
                                var r = un(t);
                                r && (e || r.sessionStoreStrategyType ? r.compressIntakeRequests && !e && l && !(o = l(r, "Datadog RUM", L)) || (u = r, n.tryToInit(r.trackingConsent), p()) : s.warn("No storage available for session. We will not send any data."))
                            }
                        } else s.error("Missing configuration")
                    },
                    get initConfiguration() {
                        return a
                    },
                    getInternalContext: L,
                    stopSession: L,
                    addTiming: function(t, e) {
                        void 0 === e && (e = ft()), d.add((function(n) {
                            return n.addTiming(t, e)
                        }))
                    },
                    startView: function(t, e) {
                        void 0 === e && (e = vt());
                        var n = function(n) {
                            n.startView(t, e)
                        };
                        d.add(n), i || (i = {
                            options: t,
                            callback: n
                        }, p())
                    },
                    addAction: function(t, n) {
                        void 0 === n && (n = e()), d.add((function(e) {
                            return e.addAction(t, n)
                        }))
                    },
                    addError: function(t, n) {
                        void 0 === n && (n = e()), d.add((function(e) {
                            return e.addError(t, n)
                        }))
                    },
                    addFeatureFlagEvaluation: function(t, e) {
                        d.add((function(n) {
                            return n.addFeatureFlagEvaluation(t, e)
                        }))
                    },
                    startDurationVital: function(t) {
                        d.add((function(e) {
                            return e.startDurationVital(t)
                        }))
                    },
                    stopDurationVital: function(t) {
                        d.add((function(e) {
                            return e.stopDurationVital(t)
                        }))
                    }
                }
            }
            var dn = {
                HIDDEN: "visibility_hidden",
                UNLOADING: "before_unload",
                PAGEHIDE: "page_hide",
                FROZEN: "page_frozen"
            };

            function fn(t) {
                return g(b(dn), t)
            }

            function pn() {
                var t, e = window;
                if (e.Zone && (t = x(e, "MutationObserver"), e.MutationObserver && t === e.MutationObserver)) {
                    var n = x(new e.MutationObserver(L), "originalInstance");
                    t = n && n.constructor
                }
                return t || (t = e.MutationObserver), t
            }

            function vn(t, e, n) {
                if (document.readyState === e || "complete" === document.readyState) n();
                else {
                    var r = "complete" === e ? Ft.LOAD : Ft.DOM_CONTENT_LOADED;
                    te(t, window, r, n, {
                        once: !0
                    })
                }
            }
            var hn = [
                ["document", function(t) {
                    return "initial_document" === t
                }],
                ["xhr", function(t) {
                    return "xmlhttprequest" === t
                }],
                ["fetch", function(t) {
                    return "fetch" === t
                }],
                ["beacon", function(t) {
                    return "beacon" === t
                }],
                ["css", function(t, e) {
                    return /\.css$/i.test(e)
                }],
                ["js", function(t, e) {
                    return /\.js$/i.test(e)
                }],
                ["image", function(t, e) {
                    return g(["image", "img", "icon"], t) || null !== /\.(gif|jpg|jpeg|tiff|png|svg|ico)$/i.exec(e)
                }],
                ["font", function(t, e) {
                    return null !== /\.(woff|eot|woff2|ttf)$/i.exec(e)
                }],
                ["media", function(t, e) {
                    return g(["audio", "video"], t) || null !== /\.(mp3|mp4)$/i.exec(e)
                }]
            ];

            function mn(t) {
                var e = t.name;
                if (! function(t) {
                        try {
                            return !!Ge(t)
                        } catch (t) {
                            return !1
                        }
                    }(e)) return Xt('Failed to construct URL for "'.concat(t.name, '"')), "other";
                for (var n = function(t) {
                        var e = Ge(t).pathname;
                        return "/" === e[0] ? e : "/".concat(e)
                    }(e), r = 0, i = hn; r < i.length; r++) {
                    var o = i[r],
                        a = o[0];
                    if ((0, o[1])(t.initiatorType, n)) return a
                }
                return "other"
            }

            function gn() {
                for (var t = [], e = 0; e < arguments.length; e++) t[e] = arguments[e];
                for (var n = 1; n < t.length; n += 1)
                    if (t[n - 1] > t[n]) return !1;
                return !0
            }

            function _n(t) {
                var e = t.duration,
                    n = t.startTime,
                    r = t.responseEnd;
                return lt(0 === e && n < r ? mt(n, r) : e)
            }

            function yn(t) {
                if (bn(t)) {
                    var e = t.startTime,
                        n = t.fetchStart,
                        r = t.redirectStart,
                        i = t.redirectEnd,
                        o = t.domainLookupStart,
                        a = t.domainLookupEnd,
                        s = t.connectStart,
                        u = t.secureConnectionStart,
                        c = t.connectEnd,
                        l = t.requestStart,
                        d = t.responseStart,
                        f = {
                            download: wn(e, d, t.responseEnd),
                            first_byte: wn(e, l, d)
                        };
                    return n < c && (f.connect = wn(e, s, c), s <= u && u <= c && (f.ssl = wn(e, u, c))), n < a && (f.dns = wn(e, o, a)), e < i && (f.redirect = wn(e, r, i)), f
                }
            }

            function bn(t) {
                if (et(J.TOLERANT_RESOURCE_TIMINGS)) return !0;
                var e = gn(t.startTime, t.fetchStart, t.domainLookupStart, t.domainLookupEnd, t.connectStart, t.connectEnd, t.requestStart, t.responseStart, t.responseEnd),
                    n = ! function(t) {
                        return t.redirectEnd > t.startTime
                    }(t) || gn(t.startTime, t.redirectStart, t.redirectEnd, t.fetchStart);
                return e && n
            }

            function wn(t, e, n) {
                if (t <= e && e <= n) return {
                    duration: lt(mt(e, n)),
                    start: lt(mt(t, e))
                }
            }

            function Sn(t) {
                if (t.startTime < t.responseStart) {
                    var e = t.encodedBodySize,
                        n = t.decodedBodySize;
                    return {
                        size: n,
                        encoded_body_size: e,
                        decoded_body_size: n,
                        transfer_size: t.transferSize
                    }
                }
                return {
                    size: void 0,
                    encoded_body_size: void 0,
                    decoded_body_size: void 0,
                    transfer_size: void 0
                }
            }

            function En(t, e) {
                return e && !t.isIntakeUrl(e)
            }
            var Cn = /data:(.+)?(;base64)?,/g;

            function Tn(t) {
                return !(t.length <= 24e3) && ("data:" === t.substring(0, 5) && (t = t.substring(0, 24e3), !0))
            }

            function kn(t) {
                return "".concat(t.match(Cn)[0], "[...]")
            }

            function xn(t) {
                return t.nodeType === Node.TEXT_NODE
            }

            function An(t) {
                return t.nodeType === Node.ELEMENT_NODE
            }

            function Rn(t) {
                return An(t) && Boolean(t.shadowRoot)
            }

            function In(t) {
                var e = t;
                return !!e.host && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE && An(e.host)
            }

            function On(t, e) {
                for (var n = t.firstChild; n;) e(n), n = n.nextSibling;
                Rn(t) && e(t.shadowRoot)
            }

            function Nn(t) {
                return In(t) ? t.host : t.parentNode
            }
            var Ln;

            function Mn(t) {
                var e = function(t) {
                    var e = t.querySelector("meta[name=dd-trace-id]"),
                        n = t.querySelector("meta[name=dd-trace-time]");
                    return Un(e && e.content, n && n.content)
                }(t) || function(t) {
                    var e = function(t) {
                        for (var e = 0; e < t.childNodes.length; e += 1) {
                            if (n = Dn(t.childNodes[e])) return n
                        }
                        if (t.body)
                            for (e = t.body.childNodes.length - 1; e >= 0; e -= 1) {
                                var n, r = t.body.childNodes[e];
                                if (n = Dn(r)) return n;
                                if (!xn(r)) break
                            }
                    }(t);
                    if (!e) return;
                    return Un(ve(e, "trace-id"), ve(e, "trace-time"))
                }(t);
                if (e && !(e.traceTime <= dt() - 12e4)) return e.traceId
            }

            function Un(t, e) {
                var n = e && Number(e);
                if (t && n) return {
                    traceId: t,
                    traceTime: n
                }
            }

            function Dn(t) {
                if (t && function(t) {
                        return t.nodeType === Node.COMMENT_NODE
                    }(t)) {
                    var e = /^\s*DATADOG;(.*?)\s*$/.exec(t.data);
                    if (e) return e[1]
                }
            }

            function Pn() {
                return void 0 !== window.performance && "getEntries" in performance
            }

            function zn(t) {
                return window.PerformanceObserver && void 0 !== PerformanceObserver.supportedEntryTypes && PerformanceObserver.supportedEntryTypes.includes(t)
            }

            function Bn(t, e) {
                var n = [];
                if (function(t, e) {
                        vn(t, "interactive", (function() {
                            var t, n = {
                                entryType: Ln.RESOURCE,
                                initiatorType: "initial_document",
                                traceId: Mn(document),
                                toJSON: function() {
                                    return E({}, t, {
                                        toJSON: void 0
                                    })
                                }
                            };
                            if (zn(Ln.NAVIGATION) && performance.getEntriesByType(Ln.NAVIGATION).length > 0) {
                                var r = performance.getEntriesByType(Ln.NAVIGATION)[0];
                                t = E(r.toJSON(), n)
                            } else {
                                var i = Fn();
                                t = E(i, {
                                    decodedBodySize: 0,
                                    encodedBodySize: 0,
                                    transferSize: 0,
                                    renderBlockingStatus: "non-blocking",
                                    duration: i.responseEnd,
                                    name: window.location.href,
                                    startTime: 0
                                }, n)
                            }
                            e(t)
                        }))
                    }(e, (function(n) {
                        Vn(t, e, [n])
                    })), Pn()) {
                    var r = performance.getEntries();
                    A((function() {
                        return Vn(t, e, r)
                    }))
                }
                if (window.PerformanceObserver) {
                    var i = v((function(n) {
                            return Vn(t, e, n.getEntries())
                        })),
                        o = [Ln.RESOURCE, Ln.NAVIGATION, Ln.LONG_TASK, Ln.PAINT],
                        a = [Ln.LARGEST_CONTENTFUL_PAINT, Ln.FIRST_INPUT, Ln.LAYOUT_SHIFT, Ln.EVENT];
                    try {
                        a.forEach((function(t) {
                            var e = new window.PerformanceObserver(i);
                            e.observe({
                                type: t,
                                buffered: !0,
                                durationThreshold: 40
                            }), n.push((function() {
                                return e.disconnect()
                            }))
                        }))
                    } catch (t) {
                        o.push.apply(o, a)
                    }
                    var s = new PerformanceObserver(i);
                    if (s.observe({
                            entryTypes: o
                        }), n.push((function() {
                            return s.disconnect()
                        })), Pn() && "addEventListener" in performance) {
                        var u = te(e, performance, "resourcetimingbufferfull", (function() {
                            performance.clearResourceTimings()
                        })).stop;
                        n.push(u)
                    }
                }
                if (zn(Ln.NAVIGATION) || function(t, e) {
                        function n() {
                            e(E(Fn(), {
                                entryType: Ln.NAVIGATION
                            }))
                        }
                        vn(t, "complete", (function() {
                            A(n)
                        }))
                    }(e, (function(n) {
                        Vn(t, e, [n])
                    })), !zn(Ln.FIRST_INPUT)) {
                    var c = function(t, e) {
                        var n = dt(),
                            r = !1,
                            i = ee(t, window, [Ft.CLICK, Ft.MOUSE_DOWN, Ft.KEY_DOWN, Ft.TOUCH_START, Ft.POINTER_DOWN], (function(e) {
                                if (e.cancelable) {
                                    var n = {
                                        entryType: Ln.FIRST_INPUT,
                                        processingStart: pt(),
                                        processingEnd: pt(),
                                        startTime: e.timeStamp,
                                        duration: 0,
                                        name: ""
                                    };
                                    e.type === Ft.POINTER_DOWN ? function(t, e) {
                                        ee(t, window, [Ft.POINTER_UP, Ft.POINTER_CANCEL], (function(t) {
                                            t.type === Ft.POINTER_UP && o(e)
                                        }), {
                                            once: !0
                                        })
                                    }(t, n) : o(n)
                                }
                            }), {
                                passive: !0,
                                capture: !0
                            }).stop;
                        return {
                            stop: i
                        };

                        function o(t) {
                            if (!r) {
                                r = !0, i();
                                var o = t.processingStart - t.startTime;
                                o >= 0 && o < dt() - n && e(t)
                            }
                        }
                    }(e, (function(n) {
                        Vn(t, e, [n])
                    })).stop;
                    n.push(c)
                }
                return {
                    stop: function() {
                        n.forEach((function(t) {
                            return t()
                        }))
                    }
                }
            }

            function Fn() {
                var t = {},
                    e = performance.timing;
                for (var n in e)
                    if (ot(e[n])) {
                        var r = n,
                            i = e[r];
                        t[r] = 0 === i ? 0 : _t(i)
                    }
                return t
            }

            function Vn(t, e, n) {
                var r = n.filter((function(t) {
                    return P(Ln, t.entryType)
                })).filter((function(t) {
                    return ! function(t) {
                        return t.entryType === Ln.NAVIGATION && t.loadEventEnd <= 0
                    }(t) && ! function(t, e) {
                        return e.entryType === Ln.RESOURCE && !En(t, e.name)
                    }(e, t)
                }));
                r.length && t.notify(0, r)
            }! function(t) {
                t.EVENT = "event", t.FIRST_INPUT = "first-input", t.LARGEST_CONTENTFUL_PAINT = "largest-contentful-paint", t.LAYOUT_SHIFT = "layout-shift", t.LONG_TASK = "longtask", t.NAVIGATION = "navigation", t.PAINT = "paint", t.RESOURCE = "resource"
            }(Ln || (Ln = {}));
            var Hn = "agent",
                qn = "console",
                Gn = "custom",
                jn = "source",
                Zn = "report";

            function Kn(t, e, n) {
                var r = 0,
                    i = !1;
                return {
                    isLimitReached: function() {
                        if (0 === r && A((function() {
                                r = 0
                            }), 6e4), (r += 1) <= e || i) return i = !1, !1;
                        if (r === e + 1) {
                            i = !0;
                            try {
                                n({
                                    message: "Reached max number of ".concat(t, "s by minute: ").concat(e),
                                    source: Hn,
                                    startClocks: vt()
                                })
                            } finally {
                                i = !1
                            }
                        }
                        return !0
                    }
                }
            }

            function Wn() {
                var t = function() {
                        var t = window._DATADOG_SYNTHETICS_PUBLIC_ID || _e("datadog-synthetics-public-id");
                        return "string" === typeof t ? t : void 0
                    }(),
                    e = function() {
                        var t = window._DATADOG_SYNTHETICS_RESULT_ID || _e("datadog-synthetics-result-id");
                        return "string" === typeof t ? t : void 0
                    }();
                if (t && e) return {
                    test_id: t,
                    result_id: e,
                    injected: we()
                }
            }

            function Yn(t, e, n) {
                var r = q(t),
                    i = n(r);
                return w(e).forEach((function(e) {
                    var n = e[0],
                        i = e[1],
                        o = function(t, e) {
                            for (var n = t, r = 0, i = e.split("."); r < i.length; r++) {
                                var o = i[r];
                                if (!$n(n, o)) return;
                                n = n[o]
                            }
                            return n
                        }(r, n),
                        a = V(o);
                    a === i ? Xn(t, n, j(o)) : "object" !== i || "undefined" !== a && "null" !== a || Xn(t, n, {})
                })), i
            }

            function Xn(t, e, n) {
                for (var r = t, i = e.split("."), o = 0; o < i.length; o += 1) {
                    var a = i[o];
                    if (!Jn(r)) return;
                    o !== i.length - 1 ? r = r[a] : r[a] = n
                }
            }

            function Jn(t) {
                return "object" === V(t)
            }

            function $n(t, e) {
                return Jn(t) && Object.prototype.hasOwnProperty.call(t, e)
            }
            var Qn, tr = {
                    "view.name": "string",
                    "view.url": "string",
                    "view.referrer": "string"
                },
                er = {
                    context: "object"
                };

            function nr(t, e, n, r, i, o, a, u, c, l) {
                var d, f, p = et(J.MICRO_FRONTEND) ? {
                    service: "string",
                    version: "string"
                } : {};
                (d = {}).view = tr, d.error = E({
                    "error.message": "string",
                    "error.stack": "string",
                    "error.resource.url": "string",
                    "error.fingerprint": "string"
                }, er, tr, p), d.resource = E({
                    "resource.url": "string"
                }, et(J.WRITABLE_RESOURCE_GRAPHQL) ? {
                    "resource.graphql": "object"
                } : {}, er, tr, p), d.action = E({
                    "action.target.name": "string"
                }, er, tr, p), d.long_task = E({}, er, tr), d.vital = E({}, er, tr), Qn = d;
                var v = ((f = {}).error = Kn("error", t.eventRateLimiterThreshold, l), f.action = Kn("action", t.eventRateLimiterThreshold, l), f.vital = Kn("vital", t.eventRateLimiterThreshold, l), f),
                    h = Wn();
                e.subscribe(12, (function(l) {
                    var d, f = l.startTime,
                        p = l.rawRumEvent,
                        m = l.domainContext,
                        g = l.savedCommonContext,
                        _ = l.customerContext,
                        y = r.findView(f),
                        b = i.findUrl(f),
                        w = n.findTrackedSession(f);
                    if (w && y && b) {
                        var S = g || c(),
                            E = o.findActionId(f),
                            C = G({
                                _dd: {
                                    format_version: 2,
                                    drift: Math.round(dt() - gt(yt(), performance.now())),
                                    configuration: {
                                        session_sample_rate: rt(t.sessionSampleRate, 3),
                                        session_replay_sample_rate: rt(t.sessionReplaySampleRate, 3)
                                    },
                                    browser_sdk_version: ce() ? "5.21.0" : void 0
                                },
                                application: {
                                    id: t.applicationId
                                },
                                date: ft(),
                                service: y.service || t.service,
                                version: y.version || t.version,
                                source: "browser",
                                session: {
                                    id: w.id,
                                    type: h ? "synthetics" : u.get() ? "ci_test" : "user"
                                },
                                view: {
                                    id: y.id,
                                    name: y.name,
                                    url: b.url,
                                    referrer: b.referrer
                                },
                                action: (d = p, -1 !== ["error", "resource", "long_task"].indexOf(d.type) && E ? {
                                    id: E
                                } : void 0),
                                synthetics: h,
                                ci_test: u.get(),
                                display: a.get(),
                                connectivity: zt()
                            }, p);
                        C.context = G(S.context, _), "has_replay" in C.session || (C.session.has_replay = S.hasReplay), "view" === C.type && (C.session.sampled_for_replay = 1 === w.sessionReplay), z(S.user) || (C.usr = S.user),
                            function(t, e, n, r) {
                                var i;
                                if (e) {
                                    var o = Yn(t, Qn[t.type], (function(t) {
                                        return e(t, n)
                                    }));
                                    if (!1 === o && "view" !== t.type) return !1;
                                    !1 === o && s.warn("Can't dismiss view events using beforeSend!")
                                }
                                return !(null === (i = r[t.type]) || void 0 === i ? void 0 : i.isLimitReached())
                            }(C, t.beforeSend, m, v) && (z(C.context) && delete C.context, e.notify(13, C))
                    }
                }))
            }
            var rr, ir = function() {
                    function t() {
                        this.callbacks = {}
                    }
                    return t.prototype.notify = function(t, e) {
                        var n = this.callbacks[t];
                        n && n.forEach((function(t) {
                            return t(e)
                        }))
                    }, t.prototype.subscribe = function(t, e) {
                        var n = this;
                        return this.callbacks[t] || (this.callbacks[t] = []), this.callbacks[t].push(e), {
                            unsubscribe: function() {
                                n.callbacks[t] = n.callbacks[t].filter((function(t) {
                                    return e !== t
                                }))
                            }
                        }
                    }, t
                }(),
                or = function() {
                    function t(t, e) {
                        var n = this;
                        this.expireDelay = t, this.maxEntries = e, this.entries = [], this.clearOldValuesInterval = I((function() {
                            return n.clearOldValues()
                        }), 6e4)
                    }
                    return t.prototype.add = function(t, e) {
                        var n = this,
                            r = {
                                value: t,
                                startTime: e,
                                endTime: 1 / 0,
                                remove: function() {
                                    Bt(n.entries, r)
                                },
                                close: function(t) {
                                    r.endTime = t
                                }
                            };
                        return this.maxEntries && this.entries.length >= this.maxEntries && this.entries.pop(), this.entries.unshift(r), r
                    }, t.prototype.find = function(t, e) {
                        void 0 === t && (t = 1 / 0), void 0 === e && (e = {
                            returnInactive: !1
                        });
                        for (var n = 0, r = this.entries; n < r.length; n++) {
                            var i = r[n];
                            if (i.startTime <= t) {
                                if (e.returnInactive || t <= i.endTime) return i.value;
                                break
                            }
                        }
                    }, t.prototype.closeActive = function(t) {
                        var e = this.entries[0];
                        e && e.endTime === 1 / 0 && e.close(t)
                    }, t.prototype.findAll = function(t, e) {
                        void 0 === t && (t = 1 / 0), void 0 === e && (e = 0);
                        var n = gt(t, e);
                        return this.entries.filter((function(e) {
                            return e.startTime <= n && t <= e.endTime
                        })).map((function(t) {
                            return t.value
                        }))
                    }, t.prototype.reset = function() {
                        this.entries = []
                    }, t.prototype.stop = function() {
                        O(this.clearOldValuesInterval)
                    }, t.prototype.clearOldValues = function() {
                        for (var t = pt() - this.expireDelay; this.entries.length > 0 && this.entries[this.entries.length - 1].endTime < t;) this.entries.pop()
                    }, t
                }();

            function ar(t, e, n, r) {
                var i = (void 0 === r ? {} : r).computeHandlingStack,
                    o = t[e];
                if ("function" !== typeof o) {
                    if (!S(e, "on")) return {
                        stop: L
                    };
                    o = L
                }
                var a = !1,
                    s = function() {
                        if (a) return o.apply(this, arguments);
                        var t, e = _(arguments);
                        h(n, null, [{
                            target: this,
                            parameters: e,
                            onPostCall: function(e) {
                                t = e
                            },
                            handlingStack: i ? Ot() : void 0
                        }]);
                        var r = o.apply(this, e);
                        return t && h(t, null, [r]), r
                    };
                return t[e] = s, {
                    stop: function() {
                        a = !0, t[e] === s && (t[e] = o)
                    }
                }
            }

            function sr(t, e, n) {
                var r = Object.getOwnPropertyDescriptor(t, e);
                if (!r || !r.set || !r.configurable) return {
                    stop: L
                };
                var i = L,
                    o = function(t, e) {
                        A((function() {
                            o !== i && n(t, e)
                        }), 0)
                    },
                    a = function(t) {
                        r.set.call(this, t), o(this, t)
                    };
                return Object.defineProperty(t, e, {
                    set: a
                }), {
                    stop: function() {
                        var n;
                        (null === (n = Object.getOwnPropertyDescriptor(t, e)) || void 0 === n ? void 0 : n.set) === a && Object.defineProperty(t, e, r), o = i
                    }
                }
            }
            var ur, cr = new WeakMap;

            function lr(t) {
                return rr || (rr = function(t) {
                    return new W((function(e) {
                        var n = ar(XMLHttpRequest.prototype, "open", dr).stop,
                            r = ar(XMLHttpRequest.prototype, "send", (function(n) {
                                ! function(t, e, n) {
                                    var r = t.target,
                                        i = t.handlingStack,
                                        o = cr.get(r);
                                    if (!o) return;
                                    var a = o;
                                    a.state = "start", a.startClocks = vt(), a.isAborted = !1, a.xhr = r, a.handlingStack = i;
                                    var s = !1,
                                        u = ar(r, "onreadystatechange", (function() {
                                            r.readyState === XMLHttpRequest.DONE && c()
                                        })).stop,
                                        c = function() {
                                            if (l(), u(), !s) {
                                                s = !0;
                                                var t = o;
                                                t.state = "complete", t.duration = mt(a.startClocks.timeStamp, ft()), t.status = r.status, n.notify(D(t))
                                            }
                                        },
                                        l = te(e, r, "loadend", c).stop;
                                    n.notify(a)
                                }(n, t, e)
                            }), {
                                computeHandlingStack: !0
                            }).stop,
                            i = ar(XMLHttpRequest.prototype, "abort", fr).stop;
                        return function() {
                            n(), r(), i()
                        }
                    }))
                }(t)), rr
            }

            function dr(t) {
                var e = t.target,
                    n = t.parameters,
                    r = n[0],
                    i = n[1];
                cr.set(e, {
                    state: "open",
                    method: String(r).toUpperCase(),
                    url: qe(String(i))
                })
            }

            function fr(t) {
                var e = t.target,
                    n = cr.get(e);
                n && (n.isAborted = !0)
            }

            function pr() {
                return ur || (ur = new W((function(t) {
                    if (window.fetch) return ar(window, "fetch", (function(e) {
                        return function(t, e) {
                            var n = t.parameters,
                                r = t.onPostCall,
                                i = t.handlingStack,
                                o = n[0],
                                a = n[1],
                                s = a && a.method;
                            void 0 === s && o instanceof Request && (s = o.method);
                            var u = void 0 !== s ? String(s).toUpperCase() : "GET",
                                c = o instanceof Request ? o.url : qe(String(o)),
                                l = vt(),
                                d = {
                                    state: "start",
                                    init: a,
                                    input: o,
                                    method: u,
                                    startClocks: l,
                                    url: c,
                                    handlingStack: i
                                };
                            e.notify(d), n[0] = d.input, n[1] = d.init, r((function(t) {
                                return function(t, e, n) {
                                    var r = function(e) {
                                        var r = n;
                                        r.state = "resolve", "stack" in e || e instanceof Error ? (r.status = 0, r.isAborted = e instanceof DOMException && e.code === DOMException.ABORT_ERR, r.error = e) : "status" in e && (r.response = e, r.responseType = e.type, r.status = e.status, r.isAborted = !1), t.notify(r)
                                    };
                                    e.then(v(r), v(r))
                                }(e, t, d)
                            }))
                        }(e, t)
                    }), {
                        computeHandlingStack: !0
                    }).stop
                }))), ur
            }
            var vr = 1;

            function hr(t, e, n) {
                var r = function(t, e) {
                    return {
                        clearTracingIfNeeded: nn,
                        traceFetch: function(n) {
                            return rn(t, n, e, (function(t) {
                                var e;
                                if (n.input instanceof Request && !(null === (e = n.init) || void 0 === e ? void 0 : e.headers)) n.input = new Request(n.input), Object.keys(t).forEach((function(e) {
                                    n.input.headers.append(e, t[e])
                                }));
                                else {
                                    n.init = D(n.init);
                                    var r = [];
                                    n.init.headers instanceof Headers ? n.init.headers.forEach((function(t, e) {
                                        r.push([e, t])
                                    })) : Array.isArray(n.init.headers) ? n.init.headers.forEach((function(t) {
                                        r.push(t)
                                    })) : n.init.headers && Object.keys(n.init.headers).forEach((function(t) {
                                        r.push([t, n.init.headers[t]])
                                    })), n.init.headers = r.concat(w(t))
                                }
                            }))
                        },
                        traceXhr: function(n, r) {
                            return rn(t, n, e, (function(t) {
                                Object.keys(t).forEach((function(e) {
                                    r.setRequestHeader(e, t[e])
                                }))
                            }))
                        }
                    }
                }(e, n);
                ! function(t, e, n) {
                    var r = lr(e).subscribe((function(r) {
                        var i = r;
                        if (En(e, i.url)) switch (i.state) {
                            case "start":
                                n.traceXhr(i, i.xhr), i.requestIndex = mr(), t.notify(7, {
                                    requestIndex: i.requestIndex,
                                    url: i.url
                                });
                                break;
                            case "complete":
                                n.clearTracingIfNeeded(i), t.notify(8, {
                                    duration: i.duration,
                                    method: i.method,
                                    requestIndex: i.requestIndex,
                                    spanId: i.spanId,
                                    startClocks: i.startClocks,
                                    status: i.status,
                                    traceId: i.traceId,
                                    traceSampled: i.traceSampled,
                                    type: "xhr",
                                    url: i.url,
                                    xhr: i.xhr,
                                    isAborted: i.isAborted,
                                    handlingStack: i.handlingStack
                                })
                        }
                    }))
                }(t, e, r),
                function(t, e, n) {
                    var r = pr().subscribe((function(r) {
                        var i = r;
                        if (En(e, i.url)) switch (i.state) {
                            case "start":
                                n.traceFetch(i), i.requestIndex = mr(), t.notify(7, {
                                    requestIndex: i.requestIndex,
                                    url: i.url
                                });
                                break;
                            case "resolve":
                                ! function(t, e) {
                                    var n = t.response && function(t) {
                                        try {
                                            return t.clone()
                                        } catch (t) {
                                            return
                                        }
                                    }(t.response);
                                    n && n.body ? function(t, e, n) {
                                        var r = t.getReader(),
                                            i = [],
                                            o = 0;

                                        function a() {
                                            var t, a;
                                            if (r.cancel().catch(L), n.collectStreamBody) {
                                                var s;
                                                if (1 === i.length) s = i[0];
                                                else {
                                                    s = new Uint8Array(o);
                                                    var u = 0;
                                                    i.forEach((function(t) {
                                                        s.set(t, u), u += t.length
                                                    }))
                                                }
                                                t = s.slice(0, n.bytesLimit), a = s.length > n.bytesLimit
                                            }
                                            e(void 0, t, a)
                                        }! function t() {
                                            r.read().then(v((function(e) {
                                                e.done ? a() : (n.collectStreamBody && i.push(e.value), (o += e.value.length) > n.bytesLimit ? a() : t())
                                            })), v((function(t) {
                                                return e(t)
                                            })))
                                        }()
                                    }(n.body, (function() {
                                        e(mt(t.startClocks.timeStamp, ft()))
                                    }), {
                                        bytesLimit: Number.POSITIVE_INFINITY,
                                        collectStreamBody: !1
                                    }) : e(mt(t.startClocks.timeStamp, ft()))
                                }(i, (function(e) {
                                    n.clearTracingIfNeeded(i), t.notify(8, {
                                        duration: e,
                                        method: i.method,
                                        requestIndex: i.requestIndex,
                                        responseType: i.responseType,
                                        spanId: i.spanId,
                                        startClocks: i.startClocks,
                                        status: i.status,
                                        traceId: i.traceId,
                                        traceSampled: i.traceSampled,
                                        type: "fetch",
                                        url: i.url,
                                        response: i.response,
                                        init: i.init,
                                        input: i.input,
                                        isAborted: i.isAborted,
                                        handlingStack: i.handlingStack
                                    })
                                }))
                        }
                    }))
                }(t, e, r)
            }

            function mr() {
                var t = vr;
                return vr += 1, t
            }

            function gr(t) {
                return ot(t) && t < 0 ? void 0 : t
            }

            function _r(t) {
                var e = t.lifeCycle,
                    n = t.isChildEvent,
                    r = t.onChange,
                    i = void 0 === r ? L : r,
                    o = {
                        errorCount: 0,
                        longTaskCount: 0,
                        resourceCount: 0,
                        actionCount: 0,
                        frustrationCount: 0
                    },
                    a = e.subscribe(13, (function(t) {
                        var e;
                        if ("view" !== t.type && "vital" !== t.type && n(t)) switch (t.type) {
                            case "error":
                                o.errorCount += 1, i();
                                break;
                            case "action":
                                o.actionCount += 1, t.action.frustration && (o.frustrationCount += t.action.frustration.type.length), i();
                                break;
                            case "long_task":
                                o.longTaskCount += 1, i();
                                break;
                            case "resource":
                                (null === (e = t._dd) || void 0 === e ? void 0 : e.discarded) || (o.resourceCount += 1, i())
                        }
                    }));
                return {
                    stop: function() {
                        a.unsubscribe()
                    },
                    eventCounts: o
                }
            }

            function yr(t, e, n, r, i) {
                return function(t, e, n) {
                    var r, i = !1,
                        o = A(v((function() {
                            return c({
                                hadActivity: !1
                            })
                        })), 100),
                        a = void 0 !== n ? A(v((function() {
                            return c({
                                hadActivity: !0,
                                end: ft()
                            })
                        })), n) : void 0,
                        s = t.subscribe((function(t) {
                            var e = t.isBusy;
                            R(o), R(r);
                            var n = ft();
                            e || (r = A(v((function() {
                                return c({
                                    hadActivity: !0,
                                    end: n
                                })
                            })), 100))
                        })),
                        u = function() {
                            i = !0, R(o), R(r), R(a), s.unsubscribe()
                        };

                    function c(t) {
                        i || (u(), e(t))
                    }
                    return {
                        stop: u
                    }
                }(function(t, e, n) {
                    return new W((function(r) {
                        var i, o = [],
                            a = 0;
                        o.push(e.subscribe(c), t.subscribe(0, (function(t) {
                            t.some((function(t) {
                                return t.entryType === Ln.RESOURCE && !br(n, t.name)
                            })) && c()
                        })), t.subscribe(7, (function(t) {
                            br(n, t.url) || (void 0 === i && (i = t.requestIndex), a += 1, c())
                        })), t.subscribe(8, (function(t) {
                            br(n, t.url) || void 0 === i || t.requestIndex < i || (a -= 1, c())
                        })));
                        var s, u = (s = c, ar(window, "open", s)).stop;
                        return function() {
                            u(), o.forEach((function(t) {
                                return t.unsubscribe()
                            }))
                        };

                        function c() {
                            r.notify({
                                isBusy: a > 0
                            })
                        }
                    }))
                }(t, e, n), r, i)
            }

            function br(t, e) {
                return en(t.excludedActivityUrls, e)
            }

            function wr(t) {
                return window.CSS && window.CSS.escape ? window.CSS.escape(t) : t.replace(/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g, (function(t, e) {
                    return e ? "\0" === t ? "�" : "".concat(t.slice(0, -1), "\\").concat(t.charCodeAt(t.length - 1).toString(16), " ") : "\\".concat(t)
                }))
            }

            function Sr(t, e) {
                return t.matches ? t.matches(e) : !!t.msMatchesSelector && t.msMatchesSelector(e)
            }

            function Er(t) {
                if (t.parentElement) return t.parentElement;
                for (; t.parentNode;) {
                    if (t.parentNode.nodeType === Node.ELEMENT_NODE) return t.parentNode;
                    t = t.parentNode
                }
                return null
            }
            var Cr = function() {
                function t(t) {
                    var e = this;
                    this.map = new WeakMap, t && t.forEach((function(t) {
                        return e.map.set(t, 1)
                    }))
                }
                return t.prototype.add = function(t) {
                    return this.map.set(t, 1), this
                }, t.prototype.delete = function(t) {
                    return this.map.delete(t)
                }, t.prototype.has = function(t) {
                    return this.map.has(t)
                }, t
            }();

            function Tr(t, e) {
                var n;
                if (function() {
                        void 0 === xr && (xr = "closest" in HTMLElement.prototype);
                        return xr
                    }()) n = t.closest("[".concat(e, "]"));
                else
                    for (var r = t; r;) {
                        if (r.hasAttribute(e)) {
                            n = r;
                            break
                        }
                        r = Er(r)
                    }
                if (n) return Nr(Or(n.getAttribute(e).trim()))
            }
            var kr, xr, Ar = [function(t, e) {
                    if (function() {
                            void 0 === kr && (kr = "labels" in HTMLInputElement.prototype);
                            return kr
                        }()) {
                        if ("labels" in t && t.labels && t.labels.length > 0) return Lr(t.labels[0], e)
                    } else if (t.id) {
                        var n = t.ownerDocument && y(t.ownerDocument.querySelectorAll("label"), (function(e) {
                            return e.htmlFor === t.id
                        }));
                        return n && Lr(n, e)
                    }
                }, function(t) {
                    if ("INPUT" === t.nodeName) {
                        var e = t,
                            n = e.getAttribute("type");
                        if ("button" === n || "submit" === n || "reset" === n) return e.value
                    }
                }, function(t, e) {
                    if ("BUTTON" === t.nodeName || "LABEL" === t.nodeName || "button" === t.getAttribute("role")) return Lr(t, e)
                }, function(t) {
                    return t.getAttribute("aria-label")
                }, function(t, e) {
                    var n = t.getAttribute("aria-labelledby");
                    if (n) return n.split(/\s+/).map((function(e) {
                        return function(t, e) {
                            return t.ownerDocument ? t.ownerDocument.getElementById(e) : null
                        }(t, e)
                    })).filter((function(t) {
                        return Boolean(t)
                    })).map((function(t) {
                        return Lr(t, e)
                    })).join(" ")
                }, function(t) {
                    return t.getAttribute("alt")
                }, function(t) {
                    return t.getAttribute("name")
                }, function(t) {
                    return t.getAttribute("title")
                }, function(t) {
                    return t.getAttribute("placeholder")
                }, function(t, e) {
                    if ("options" in t && t.options.length > 0) return Lr(t.options[0], e)
                }],
                Rr = [function(t, e) {
                    return Lr(t, e)
                }];

            function Ir(t, e, n) {
                for (var r = t, i = 0; i <= 10 && r && "BODY" !== r.nodeName && "HTML" !== r.nodeName && "HEAD" !== r.nodeName;) {
                    for (var o = 0, a = n; o < a.length; o++) {
                        var s = (0, a[o])(r, e);
                        if ("string" === typeof s) {
                            var u = s.trim();
                            if (u) return Nr(Or(u))
                        }
                    }
                    if ("FORM" === r.nodeName) break;
                    r = Er(r), i += 1
                }
            }

            function Or(t) {
                return t.replace(/\s+/g, " ")
            }

            function Nr(t) {
                return t.length > 100 ? "".concat(he(t, 100), " [...]") : t
            }

            function Lr(t, e) {
                if (!t.isContentEditable) {
                    if ("innerText" in t) {
                        var n = t.innerText,
                            r = function(e) {
                                for (var r = t.querySelectorAll(e), i = 0; i < r.length; i += 1) {
                                    var o = r[i];
                                    if ("innerText" in o) {
                                        var a = o.innerText;
                                        a && a.trim().length > 0 && (n = n.replace(a, ""))
                                    }
                                }
                            };
                        return 0 === Se() && r("script, style"), r("[".concat("data-dd-action-name", "]")), e && r("[".concat(e, "]")), n
                    }
                    return t.textContent
                }
            }
            var Mr, Ur = ["data-dd-action-name", "data-testid", "data-test", "data-qa", "data-cy", "data-test-id", "data-qa-id", "data-testing", "data-component", "data-element", "data-source-file"],
                Dr = [Fr, function(t) {
                    if (t.id && !Br(t.id)) return "#".concat(wr(t.id))
                }],
                Pr = [Fr, function(t) {
                    if ("BODY" === t.tagName) return;
                    for (var e = function(t) {
                            var e;
                            if (t.classList) return t.classList;
                            var n = null === (e = t.getAttribute("class")) || void 0 === e ? void 0 : e.trim();
                            return n ? n.split(/\s+/) : []
                        }(t), n = 0; n < e.length; n += 1) {
                        var r = e[n];
                        if (!Br(r)) return "".concat(wr(t.tagName), ".").concat(wr(r))
                    }
                }, function(t) {
                    return wr(t.tagName)
                }];

            function zr(t, e) {
                if (function(t) {
                        if ("isConnected" in t) return t.isConnected;
                        return t.ownerDocument.documentElement.contains(t)
                    }(t)) {
                    for (var n, r = t; r && "HTML" !== r.nodeName;) {
                        var i = Hr(r, Dr, qr, e, n);
                        if (i) return i;
                        n = Hr(r, Pr, Gr, e, n) || jr(Vr(r), n), r = Er(r)
                    }
                    return n
                }
            }

            function Br(t) {
                return /[0-9]/.test(t)
            }

            function Fr(t, e) {
                if (e && (i = o(e))) return i;
                for (var n = 0, r = Ur; n < r.length; n++) {
                    var i;
                    if (i = o(r[n])) return i
                }

                function o(e) {
                    if (t.hasAttribute(e)) return "".concat(wr(t.tagName), "[").concat(e, '="').concat(wr(t.getAttribute(e)), '"]')
                }
            }

            function Vr(t) {
                for (var e = Er(t).firstElementChild, n = 1; e && e !== t;) e.tagName === t.tagName && (n += 1), e = e.nextElementSibling;
                return "".concat(wr(t.tagName), ":nth-of-type(").concat(n, ")")
            }

            function Hr(t, e, n, r, i) {
                for (var o = 0, a = e; o < a.length; o++) {
                    var s = (0, a[o])(t, r);
                    if (s && n(t, s, i)) return jr(s, i)
                }
            }

            function qr(t, e, n) {
                return 1 === t.ownerDocument.querySelectorAll(jr(e, n)).length
            }

            function Gr(t, e, n) {
                var r;
                if (void 0 === n) r = function(t) {
                    return Sr(t, e)
                };
                else {
                    var i = function() {
                        if (void 0 === Mr) try {
                            document.querySelector(":scope"), Mr = !0
                        } catch (t) {
                            Mr = !1
                        }
                        return Mr
                    }() ? jr("".concat(e, ":scope"), n) : jr(e, n);
                    r = function(t) {
                        return null !== t.querySelector(i)
                    }
                }
                for (var o = Er(t).firstElementChild; o;) {
                    if (o !== t && r(o)) return !1;
                    o = o.nextElementSibling
                }
                return !0
            }

            function jr(t, e) {
                return e ? "".concat(t, ">").concat(e) : t
            }

            function Zr(t, e) {
                var n, r = [],
                    i = 0;

                function o(t) {
                    t.stopObservable.subscribe(a), r.push(t), R(n), n = A(s, 1e3)
                }

                function a() {
                    1 === i && r.every((function(t) {
                        return t.isStopped()
                    })) && (i = 2, e(r))
                }

                function s() {
                    R(n), 0 === i && (i = 1, a())
                }
                return o(t), {
                    tryAppend: function(t) {
                        return 0 === i && (r.length > 0 && ! function(t, e) {
                            return t.target === e.target && (n = t, r = e, Math.sqrt(Math.pow(n.clientX - r.clientX, 2) + Math.pow(n.clientY - r.clientY, 2)) <= 100) && t.timeStamp - e.timeStamp <= 1e3;
                            var n, r
                        }(r[r.length - 1].event, t.event) ? (s(), !1) : (o(t), !0))
                    },
                    stop: function() {
                        s()
                    }
                }
            }

            function Kr() {
                var t = window.getSelection();
                return !t || t.isCollapsed
            }

            function Wr(t) {
                return t.target instanceof Element && !1 !== t.isPrimary
            }

            function Yr(t, e) {
                if (function(t) {
                        if (t.some((function(t) {
                                return t.getUserActivity().selection || t.getUserActivity().scroll
                            }))) return !1;
                        for (var e = 0; e < t.length - 2; e += 1)
                            if (t[e + 3 - 1].event.timeStamp - t[e].event.timeStamp <= 1e3) return !0;
                        return !1
                    }(t)) return e.addFrustration("rage_click"), t.some(Xr) && e.addFrustration("dead_click"), e.hasError && e.addFrustration("error_click"), {
                    isRage: !0
                };
                var n = t.some((function(t) {
                    return t.getUserActivity().selection
                }));
                return t.forEach((function(t) {
                    t.hasError && t.addFrustration("error_click"), Xr(t) && !n && t.addFrustration("dead_click")
                })), {
                    isRage: !1
                }
            }

            function Xr(t) {
                return !(t.hasPageActivity || t.getUserActivity().input || t.getUserActivity().scroll) && !Sr(t.event.target, 'input:not([type="checkbox"]):not([type="radio"]):not([type="button"]):not([type="submit"]):not([type="reset"]):not([type="range"]),textarea,select,[contenteditable],[contenteditable] *,canvas,a[href],a[href] *')
            }

            function Jr(t, e, n) {
                var r, i = new or(3e5),
                    o = new W;
                t.subscribe(10, (function() {
                    i.reset()
                })), t.subscribe(5, u);
                var a = function(t, e) {
                    var n, r, i = e.onPointerDown,
                        o = e.onPointerUp,
                        a = {
                            selection: !1,
                            input: !1,
                            scroll: !1
                        },
                        s = [te(t, window, Ft.POINTER_DOWN, (function(t) {
                            Wr(t) && (n = Kr(), a = {
                                selection: !1,
                                input: !1,
                                scroll: !1
                            }, r = i(t))
                        }), {
                            capture: !0
                        }), te(t, window, Ft.SELECTION_CHANGE, (function() {
                            n && Kr() || (a.selection = !0)
                        }), {
                            capture: !0
                        }), te(t, window, Ft.SCROLL, (function() {
                            a.scroll = !0
                        }), {
                            capture: !0,
                            passive: !0
                        }), te(t, window, Ft.POINTER_UP, (function(t) {
                            if (Wr(t) && r) {
                                var e = a;
                                o(r, t, (function() {
                                    return e
                                })), r = void 0
                            }
                        }), {
                            capture: !0
                        }), te(t, window, Ft.INPUT, (function() {
                            a.input = !0
                        }), {
                            capture: !0
                        })];
                    return {
                        stop: function() {
                            s.forEach((function(t) {
                                return t.stop()
                            }))
                        }
                    }
                }(n, {
                    onPointerDown: function(r) {
                        return function(t, e, n, r) {
                            var i = function(t, e) {
                                    var n = t.target.getBoundingClientRect();
                                    return {
                                        type: "click",
                                        target: {
                                            width: Math.round(n.width),
                                            height: Math.round(n.height),
                                            selector: zr(t.target, e)
                                        },
                                        position: {
                                            x: Math.round(t.clientX - n.left),
                                            y: Math.round(t.clientY - n.top)
                                        },
                                        name: (r = t.target, i = e, Tr(r, "data-dd-action-name") || i && Tr(r, i) || Ir(r, i, Ar) || Ir(r, i, Rr) || "")
                                    };
                                    var r, i
                                }(r, t.actionNameAttribute),
                                o = !1;
                            return yr(e, n, t, (function(t) {
                                o = t.hadActivity
                            }), 100), {
                                clickActionBase: i,
                                hadActivityOnPointerDown: function() {
                                    return o
                                }
                            }
                        }(n, t, e, r)
                    },
                    onPointerUp: function(r, a, u) {
                        var c = r.clickActionBase,
                            l = r.hadActivityOnPointerDown;
                        return function(t, e, n, r, i, o, a, s, u, c) {
                            var l = function t(e, n, r, i, o) {
                                var a, s = le(),
                                    u = vt(),
                                    c = n.add(s, u.relative),
                                    l = _r({
                                        lifeCycle: e,
                                        isChildEvent: function(t) {
                                            return void 0 !== t.action && (Array.isArray(t.action.id) ? g(t.action.id, s) : t.action.id === s)
                                        }
                                    }),
                                    d = 0,
                                    f = [],
                                    p = new W;

                                function v(t) {
                                    0 === d && (d = 1, (a = t) ? c.close(_t(a)) : c.remove(), l.stop(), p.notify())
                                }
                                return {
                                    event: o,
                                    stop: v,
                                    stopObservable: p,
                                    get hasError() {
                                        return l.eventCounts.errorCount > 0
                                    },
                                    get hasPageActivity() {
                                        return void 0 !== a
                                    },
                                    getUserActivity: r,
                                    addFrustration: function(t) {
                                        f.push(t)
                                    },
                                    startClocks: u,
                                    isStopped: function() {
                                        return 1 === d || 2 === d
                                    },
                                    clone: function() {
                                        return t(e, n, r, i, o)
                                    },
                                    validate: function(t) {
                                        if (v(), 1 === d) {
                                            var n = l.eventCounts,
                                                r = n.resourceCount,
                                                c = n.errorCount,
                                                p = n.longTaskCount,
                                                h = E({
                                                    type: "click",
                                                    duration: a && mt(u.timeStamp, a),
                                                    startClocks: u,
                                                    id: s,
                                                    frustrationTypes: f,
                                                    counts: {
                                                        resourceCount: r,
                                                        errorCount: c,
                                                        longTaskCount: p
                                                    },
                                                    events: null !== t && void 0 !== t ? t : [o],
                                                    event: o
                                                }, i);
                                            e.notify(1, h), d = 2
                                        }
                                    },
                                    discard: function() {
                                        v(), d = 2
                                    }
                                }
                            }(e, r, u, a, s);
                            o(l);
                            var d = yr(e, n, t, (function(t) {
                                    t.hadActivity && t.end < l.startClocks.timeStamp ? l.discard() : t.hadActivity ? l.stop(t.end) : c() ? l.stop(l.startClocks.timeStamp) : l.stop()
                                }), 1e4).stop,
                                f = e.subscribe(5, (function(t) {
                                    var e = t.endClocks;
                                    l.stop(e.timeStamp)
                                })),
                                p = i.subscribe((function() {
                                    l.stop()
                                }));
                            l.stopObservable.subscribe((function() {
                                f.unsubscribe(), d(), p.unsubscribe()
                            }))
                        }(n, t, e, i, o, s, c, a, u, l)
                    }
                }).stop;
                return {
                    stop: function() {
                        u(), o.notify(), a()
                    },
                    actionContexts: {
                        findActionId: function(t) {
                            return i.findAll(t)
                        }
                    }
                };

                function s(t) {
                    if (!r || !r.tryAppend(t)) {
                        var e = t.clone();
                        r = Zr(t, (function(t) {
                            ! function(t, e) {
                                Yr(t, e).isRage ? (t.forEach((function(t) {
                                    return t.discard()
                                })), e.stop(ft()), e.validate(t.map((function(t) {
                                    return t.event
                                })))) : (e.discard(), t.forEach((function(t) {
                                    return t.validate()
                                })))
                            }(t, e)
                        }))
                    }
                }

                function u() {
                    r && r.stop()
                }
            }

            function $r(t, e) {
                var n = Qr(t) ? {
                        action: {
                            id: t.id,
                            loading_time: gr(lt(t.duration)),
                            frustration: {
                                type: t.frustrationTypes
                            },
                            error: {
                                count: t.counts.errorCount
                            },
                            long_task: {
                                count: t.counts.longTaskCount
                            },
                            resource: {
                                count: t.counts.resourceCount
                            }
                        },
                        _dd: {
                            action: {
                                target: t.target,
                                position: t.position
                            }
                        }
                    } : void 0,
                    r = Qr(t) ? void 0 : t.context,
                    i = G({
                        action: {
                            id: le(),
                            target: {
                                name: t.name
                            },
                            type: t.type
                        },
                        date: t.startClocks.timeStamp,
                        type: "action",
                        view: {
                            in_foreground: e.wasInPageStateAt("active", t.startClocks.relative)
                        }
                    }, n),
                    o = Qr(t) ? {
                        events: t.events
                    } : {};
                return !Qr(t) && t.handlingStack && et(J.MICRO_FRONTEND) && (o.handlingStack = t.handlingStack), {
                    customerContext: r,
                    rawRumEvent: i,
                    startTime: t.startClocks.relative,
                    domainContext: o
                }
            }

            function Qr(t) {
                return "custom" !== t.type
            }

            function ti(t) {
                var e, n = function(e, n) {
                        var r = Mt({
                            stackTrace: e,
                            originalError: n,
                            startClocks: vt(),
                            nonErrorPrefix: "Uncaught",
                            source: jn,
                            handling: "unhandled"
                        });
                        t.notify(r)
                    },
                    r = (e = n, ar(window, "onerror", (function(t) {
                        var n, r = t.parameters,
                            i = r[0],
                            o = r[1],
                            a = r[2],
                            s = r[3],
                            u = r[4];
                        n = u instanceof Error ? bt(u) : Rt(i, o, a, s), e(n, null !== u && void 0 !== u ? u : i)
                    }))).stop,
                    i = function(t) {
                        return ar(window, "onunhandledrejection", (function(e) {
                            var n = e.parameters[0].reason || "Empty reason",
                                r = bt(n);
                            t(r, n)
                        }))
                    }(n).stop;
                return {
                    stop: function() {
                        r(), i()
                    }
                }
            }
            var ei = {};

            function ni(t) {
                var e = t.map((function(t) {
                    return ei[t] || (ei[t] = function(t) {
                        return new W((function(e) {
                            var n = i[t];
                            return i[t] = function() {
                                    for (var r = [], i = 0; i < arguments.length; i++) r[i] = arguments[i];
                                    n.apply(console, r);
                                    var o = Ot();
                                    h((function() {
                                        e.notify(ri(r, t, o))
                                    }))
                                },
                                function() {
                                    i[t] = n
                                }
                        }))
                    }(t)), ei[t]
                }));
                return Y.apply(void 0, e)
            }

            function ri(t, e, n) {
                var i, o, a, s = t.map((function(t) {
                    return function(t) {
                        if ("string" === typeof t) return j(t);
                        if (t instanceof Error) return Lt(bt(t));
                        return M(j(t), void 0, 2)
                    }(t)
                })).join(" ");
                if (e === r.error) {
                    var u = y(t, (function(t) {
                        return t instanceof Error
                    }));
                    i = u ? Nt(bt(u)) : void 0, o = Ut(u), a = u ? Dt(u, "console") : void 0
                }
                return {
                    api: e,
                    message: s,
                    stack: i,
                    handlingStack: n,
                    fingerprint: o,
                    causes: a
                }
            }
            var ii = "intervention",
                oi = "csp_violation";

            function ai(t, e) {
                var n = [];
                g(e, oi) && n.push(function(t) {
                    return new W((function(e) {
                        return te(t, document, Ft.SECURITY_POLICY_VIOLATION, (function(t) {
                            e.notify(function(t) {
                                var e = oi,
                                    n = "'".concat(t.blockedURI, "' blocked by '").concat(t.effectiveDirective, "' directive");
                                return {
                                    type: oi,
                                    subtype: t.effectiveDirective,
                                    message: "".concat(e, ": ").concat(n),
                                    stack: si(t.effectiveDirective, t.originalPolicy ? "".concat(n, ' of the policy "').concat(he(t.originalPolicy, 100, "..."), '"') : "no policy", t.sourceFile, t.lineNumber, t.columnNumber),
                                    originalReport: t
                                }
                            }(t))
                        })).stop
                    }))
                }(t));
                var r = e.filter((function(t) {
                    return t !== oi
                }));
                return r.length && n.push(function(t) {
                    return new W((function(e) {
                        if (window.ReportingObserver) {
                            var n = v((function(t, n) {
                                    return t.forEach((function(t) {
                                        e.notify(function(t) {
                                            var e = t.type,
                                                n = t.body;
                                            return {
                                                type: e,
                                                subtype: n.id,
                                                message: "".concat(e, ": ").concat(n.message),
                                                originalReport: t,
                                                stack: si(n.id, n.message, n.sourceFile, n.lineNumber, n.columnNumber)
                                            }
                                        }(t))
                                    }))
                                })),
                                r = new window.ReportingObserver(n, {
                                    types: t,
                                    buffered: !0
                                });
                            return r.observe(),
                                function() {
                                    r.disconnect()
                                }
                        }
                    }))
                }(r)), Y.apply(void 0, n)
            }

            function si(t, e, n, r, i) {
                return n ? Nt({
                    name: t,
                    message: e,
                    stack: [{
                        func: "?",
                        url: n,
                        line: null !== r && void 0 !== r ? r : void 0,
                        column: null !== i && void 0 !== i ? i : void 0
                    }]
                }) : void 0
            }

            function ui(t, e, n, i) {
                var o = new W;
                return function(t) {
                        var e = ni([r.error]).subscribe((function(e) {
                            return t.notify({
                                startClocks: vt(),
                                message: e.message,
                                stack: e.stack,
                                fingerprint: e.fingerprint,
                                source: qn,
                                handling: "handled",
                                handlingStack: e.handlingStack
                            })
                        }))
                    }(o), ti(o),
                    function(t, e) {
                        var n = ai(t, [oi, ii]).subscribe((function(t) {
                            var n = {
                                startClocks: vt(),
                                message: t.message,
                                stack: t.stack,
                                type: t.subtype,
                                source: Zn,
                                handling: "unhandled",
                                originalError: t.originalReport
                            };
                            return "securitypolicyviolation" === t.originalReport.type && (n.csp = {
                                disposition: t.originalReport.disposition
                            }), e.notify(n)
                        }))
                    }(e, o), o.subscribe((function(e) {
                        return t.notify(14, {
                            error: e
                        })
                    })),
                    function(t, e, n) {
                        return t.subscribe(14, (function(r) {
                            var i = r.error,
                                o = r.customerContext,
                                a = r.savedCommonContext;
                            t.notify(12, E({
                                customerContext: o,
                                savedCommonContext: a
                            }, function(t, e, n) {
                                var r = {
                                        date: t.startClocks.timeStamp,
                                        error: {
                                            id: le(),
                                            message: t.message,
                                            source: t.source,
                                            stack: t.stack,
                                            handling_stack: t.handlingStack,
                                            type: t.type,
                                            handling: t.handling,
                                            causes: t.causes,
                                            source_type: "browser",
                                            fingerprint: t.fingerprint,
                                            csp: t.csp
                                        },
                                        type: "error",
                                        view: {
                                            in_foreground: e.wasInPageStateAt("active", t.startClocks.relative)
                                        }
                                    },
                                    i = n.findFeatureFlagEvaluations(t.startClocks.relative);
                                i && !z(i) && (r.feature_flags = i);
                                var o = {
                                    error: t.originalError
                                };
                                et(J.MICRO_FRONTEND) && (o.handlingStack = t.handlingStack);
                                return {
                                    rawRumEvent: r,
                                    startTime: t.startClocks.relative,
                                    domainContext: o
                                }
                            }(i, e, n)))
                        })), {
                            addError: function(e, n) {
                                var r = e.error,
                                    i = e.handlingStack,
                                    o = e.startClocks,
                                    a = e.context,
                                    s = Mt({
                                        stackTrace: r instanceof Error ? bt(r) : void 0,
                                        originalError: r,
                                        handlingStack: i,
                                        startClocks: o,
                                        nonErrorPrefix: "Provided",
                                        source: Gn,
                                        handling: "handled"
                                    });
                                t.notify(14, {
                                    customerContext: a,
                                    savedCommonContext: n,
                                    error: s
                                })
                            }
                        }
                    }(t, n, i)
            }
            var ci = new Cr;

            function li(t) {
                if (performance && "getEntriesByName" in performance) {
                    var e = performance.getEntriesByName(t.url, "resource");
                    if (e.length && "toJSON" in e[0]) {
                        var n = e.filter((function(t) {
                            return !ci.has(t)
                        })).filter((function(t) {
                            return bn(t)
                        })).filter((function(e) {
                            return n = e, r = t.startClocks.relative, i = di({
                                startTime: t.startClocks.relative,
                                duration: t.duration
                            }), n.startTime >= r - 1 && di(n) <= gt(i, 1);
                            var n, r, i
                        }));
                        return 1 === n.length ? (ci.add(n[0]), n[0].toJSON()) : void 0
                    }
                }
            }

            function di(t) {
                return gt(t.startTime, t.duration)
            }

            function fi(t, e, n) {
                t.subscribe(8, (function(r) {
                    var i = function(t, e, n) {
                        var r = li(t),
                            i = r ? st(r.startTime) : t.startClocks,
                            o = function(t, e) {
                                if (!(t.traceSampled && t.traceId && t.spanId)) return;
                                return {
                                    _dd: {
                                        span_id: t.spanId.toDecimalString(),
                                        trace_id: t.traceId.toDecimalString(),
                                        rule_psr: hi(e)
                                    }
                                }
                            }(t, e);
                        if (!e.trackResources && !o) return;
                        var a = "xhr" === t.type ? "xhr" : "fetch",
                            s = r ? vi(r) : void 0,
                            u = function(t, e, n) {
                                return t.wasInPageStateDuringPeriod("frozen", e.relative, n) ? void 0 : lt(n)
                            }(n, i, t.duration),
                            c = G({
                                date: i.timeStamp,
                                resource: {
                                    id: le(),
                                    type: a,
                                    duration: u,
                                    method: t.method,
                                    status_code: t.status,
                                    url: Tn(t.url) ? kn(t.url) : t.url
                                },
                                type: "resource",
                                _dd: {
                                    discarded: !e.trackResources
                                }
                            }, o, s),
                            l = {
                                startTime: i.relative,
                                rawRumEvent: c,
                                domainContext: {
                                    performanceEntry: r,
                                    xhr: t.xhr,
                                    response: t.response,
                                    requestInput: t.input,
                                    requestInit: t.init,
                                    error: t.error,
                                    isAborted: t.isAborted
                                }
                            };
                        et(J.MICRO_FRONTEND) && (l.domainContext.handlingStack = t.handlingStack);
                        return l
                    }(r, e, n);
                    i && t.notify(12, i)
                })), t.subscribe(0, (function(n) {
                    for (var r = 0, i = n; r < i.length; r++) {
                        var o = i[r];
                        if (o.entryType === Ln.RESOURCE && ("xmlhttprequest" !== (s = o).initiatorType && "fetch" !== s.initiatorType)) {
                            var a = pi(o, e);
                            a && t.notify(12, a)
                        }
                    }
                    var s
                }))
            }

            function pi(t, e) {
                var n = st(t.startTime),
                    r = function(t, e) {
                        if (!t.traceId) return;
                        return {
                            _dd: {
                                trace_id: t.traceId,
                                rule_psr: hi(e)
                            }
                        }
                    }(t, e);
                if (e.trackResources || r) {
                    var i, o = mn(t),
                        a = vi(t),
                        s = G({
                            date: n.timeStamp,
                            resource: {
                                id: le(),
                                type: o,
                                url: t.name,
                                status_code: (i = t.responseStatus, 0 === i ? void 0 : i)
                            },
                            type: "resource",
                            _dd: {
                                discarded: !e.trackResources
                            }
                        }, r, a);
                    return {
                        startTime: n.relative,
                        rawRumEvent: s,
                        domainContext: {
                            performanceEntry: t
                        }
                    }
                }
            }

            function vi(t) {
                var e = t.renderBlockingStatus;
                return {
                    resource: E({
                        duration: _n(t),
                        render_blocking_status: e
                    }, Sn(t), yn(t))
                }
            }

            function hi(t) {
                return ot(t.traceSampleRate) ? t.traceSampleRate / 100 : void 0
            }

            function mi(t, e, n, r) {
                var i = {},
                    o = function(t, e) {
                        return {
                            stop: t.subscribe(0, (function(t) {
                                for (var n = 0, r = t; n < r.length; n++) {
                                    var i = r[n];
                                    i.entryType === Ln.NAVIGATION && e({
                                        domComplete: i.domComplete,
                                        domContentLoaded: i.domContentLoadedEventEnd,
                                        domInteractive: i.domInteractive,
                                        loadEvent: i.loadEventEnd,
                                        firstByte: i.responseStart >= 0 && i.responseStart <= pt() ? i.responseStart : void 0
                                    })
                                }
                            })).unsubscribe
                        }
                    }(t, (function(t) {
                        n(t.loadEvent), i.navigationTimings = t, r()
                    })).stop,
                    a = function(t, e) {
                        var n, r;
                        return void 0 === e && (e = window), "hidden" === document.visibilityState ? n = 0 : (n = 1 / 0, r = ee(t, e, [Ft.PAGE_HIDE, Ft.VISIBILITY_CHANGE], (function(t) {
                            t.type !== Ft.PAGE_HIDE && "hidden" !== document.visibilityState || (n = t.timeStamp, r())
                        }), {
                            capture: !0
                        }).stop), {
                            get timeStamp() {
                                return n
                            },
                            stop: function() {
                                null === r || void 0 === r || r()
                            }
                        }
                    }(e),
                    s = function(t, e, n) {
                        return {
                            stop: t.subscribe(0, (function(t) {
                                var r = y(t, (function(t) {
                                    return t.entryType === Ln.PAINT && "first-contentful-paint" === t.name && t.startTime < e.timeStamp && t.startTime < 6e5
                                }));
                                r && n(r.startTime)
                            })).unsubscribe
                        }
                    }(t, a, (function(t) {
                        i.firstContentfulPaint = t, r()
                    })).stop,
                    u = function(t, e, n, r, i) {
                        var o = 1 / 0,
                            a = ee(e, r, [Ft.POINTER_DOWN, Ft.KEY_DOWN], (function(t) {
                                o = t.timeStamp
                            }), {
                                capture: !0,
                                once: !0
                            }).stop,
                            s = 0,
                            u = t.subscribe(0, (function(t) {
                                var r = function(t, e) {
                                    for (var n = t.length - 1; n >= 0; n -= 1) {
                                        var r = t[n];
                                        if (e(r, n, t)) return r
                                    }
                                }(t, (function(t) {
                                    return t.entryType === Ln.LARGEST_CONTENTFUL_PAINT && t.startTime < o && t.startTime < n.timeStamp && t.startTime < 6e5 && t.size > s
                                }));
                                if (r) {
                                    var a = void 0;
                                    r.element && (a = zr(r.element, e.actionNameAttribute)), i({
                                        value: r.startTime,
                                        targetSelector: a
                                    }), s = r.size
                                }
                            })).unsubscribe;
                        return {
                            stop: function() {
                                a(), u()
                            }
                        }
                    }(t, e, a, window, (function(t) {
                        i.largestContentfulPaint = t, r()
                    })).stop,
                    c = function(t, e, n, r) {
                        return {
                            stop: t.subscribe(0, (function(t) {
                                var i = y(t, (function(t) {
                                    return t.entryType === Ln.FIRST_INPUT && t.startTime < n.timeStamp
                                }));
                                if (i) {
                                    var o = mt(i.startTime, i.processingStart),
                                        a = void 0;
                                    i.target && An(i.target) && (a = zr(i.target, e.actionNameAttribute)), r({
                                        delay: o >= 0 ? o : 0,
                                        time: i.startTime,
                                        targetSelector: a
                                    })
                                }
                            })).unsubscribe
                        }
                    }(t, e, a, (function(t) {
                        i.firstInput = t, r()
                    })).stop;
                return {
                    stop: function() {
                        o(), s(), u(), c(), a.stop()
                    },
                    initialViewMetrics: i
                }
            }

            function gi(t, e, n, r) {
                if (!zn(Ln.LAYOUT_SHIFT)) return {
                    stop: L
                };
                var i, o, a = 0;
                r({
                    value: 0
                });
                var s = function() {
                    var t, e, n = 0,
                        r = 0;
                    return {
                        update: function(i) {
                            var o;
                            return void 0 === t || i.startTime - e >= 1e3 || i.startTime - t >= 5e3 ? (t = e = i.startTime, r = n = i.value, o = !0) : (n += i.value, e = i.startTime, (o = i.value > r) && (r = i.value)), {
                                cumulatedValue: n,
                                isMaxValue: o
                            }
                        }
                    }
                }();
                return {
                    stop: e.subscribe(0, (function(e) {
                        for (var u = 0, c = e; u < c.length; u++) {
                            var l = c[u];
                            if (l.entryType === Ln.LAYOUT_SHIFT && !l.hadRecentInput) {
                                var d = s.update(l),
                                    f = d.cumulatedValue;
                                if (d.isMaxValue) {
                                    var p = _i(l.sources);
                                    i = p ? new WeakRef(p) : void 0, o = mt(n, l.startTime)
                                }
                                if (f > a) {
                                    a = f;
                                    p = null === i || void 0 === i ? void 0 : i.deref();
                                    r({
                                        value: rt(a, 4),
                                        targetSelector: p && zr(p, t.actionNameAttribute),
                                        time: o
                                    })
                                }
                            }
                        }
                    })).unsubscribe
                }
            }

            function _i(t) {
                var e;
                if (t) return null === (e = y(t, (function(t) {
                    return !!t.node && An(t.node)
                }))) || void 0 === e ? void 0 : e.node
            }
            var yi;
            var bi = 0,
                wi = 1 / 0,
                Si = 0;
            var Ei, Ci = function() {
                return yi ? bi : window.performance.interactionCount || 0
            };

            function Ti(t, e, n, r) {
                if (!(zn(Ln.EVENT) && window.PerformanceEventTiming && "interactionId" in PerformanceEventTiming.prototype)) return {
                    getInteractionToNextPaint: function() {},
                    setViewEnd: L,
                    stop: L
                };
                var i, o, a = function(t) {
                        "interactionCount" in performance || yi || (yi = new window.PerformanceObserver(v((function(t) {
                            t.getEntries().forEach((function(t) {
                                var e = t;
                                e.interactionId && (wi = Math.min(wi, e.interactionId), Si = Math.max(Si, e.interactionId), bi = (Si - wi) / 7 + 1)
                            }))
                        })))).observe({
                            type: "event",
                            buffered: !0,
                            durationThreshold: 0
                        });
                        var e = "initial_load" === t ? 0 : Ci(),
                            n = {
                                stopped: !1
                            };

                        function r() {
                            return Ci() - e
                        }
                        return {
                            getViewInteractionCount: function() {
                                return n.stopped ? n.interactionCount : r()
                            },
                            stopViewInteractionCount: function() {
                                n = {
                                    stopped: !0,
                                    interactionCount: r()
                                }
                            }
                        }
                    }(n),
                    s = a.getViewInteractionCount,
                    u = a.stopViewInteractionCount,
                    c = 1 / 0,
                    l = function(t) {
                        var e = [];

                        function n() {
                            e.sort((function(t, e) {
                                return e.duration - t.duration
                            })).splice(10)
                        }
                        return {
                            process: function(t) {
                                var r = e.findIndex((function(e) {
                                        return t.interactionId === e.interactionId
                                    })),
                                    i = e[e.length - 1]; - 1 !== r ? t.duration > e[r].duration && (e[r] = t, n()) : (e.length < 10 || t.duration > i.duration) && (e.push(t), n())
                            },
                            estimateP98Interaction: function() {
                                var n = Math.min(e.length - 1, Math.floor(t() / 50));
                                return e[n]
                            }
                        }
                    }(s),
                    d = -1;
                return {
                    getInteractionToNextPaint: function() {
                        return d >= 0 ? {
                            value: Math.min(d, 6e4),
                            targetSelector: i,
                            time: o
                        } : s() ? {
                            value: 0
                        } : void 0
                    },
                    setViewEnd: function(t) {
                        c = t, u()
                    },
                    stop: r.subscribe(0, (function(n) {
                        for (var r = 0, a = n; r < a.length; r++) {
                            var s = a[r];
                            (s.entryType === Ln.EVENT || s.entryType === Ln.FIRST_INPUT) && s.interactionId && s.startTime >= e && s.startTime <= c && l.process(s)
                        }
                        var u = l.estimateP98Interaction();
                        u && u.duration !== d && (d = u.duration, o = mt(e, u.startTime), i = u.target && An(u.target) ? zr(u.target, t.actionNameAttribute) : void 0)
                    })).unsubscribe
                }
            }

            function ki() {
                var t, e = window.visualViewport;
                return t = e ? e.pageLeft - e.offsetLeft : void 0 !== window.scrollX ? window.scrollX : window.pageXOffset || 0, Math.round(t)
            }

            function xi() {
                var t, e = window.visualViewport;
                return t = e ? e.pageTop - e.offsetTop : void 0 !== window.scrollY ? window.scrollY : window.pageYOffset || 0, Math.round(t)
            }

            function Ai(t) {
                return Ei || (Ei = function(t) {
                    return new W((function(e) {
                        var n = N((function() {
                            e.notify(Ri())
                        }), 200).throttled;
                        return te(t, window, Ft.RESIZE, n, {
                            capture: !0,
                            passive: !0
                        }).stop
                    }))
                }(t)), Ei
            }

            function Ri() {
                var t = window.visualViewport;
                return t ? {
                    width: Number(t.width * t.scale),
                    height: Number(t.height * t.scale)
                } : {
                    width: Number(window.innerWidth || 0),
                    height: Number(window.innerHeight || 0)
                }
            }

            function Ii(t, e, n, r) {
                void 0 === r && (r = function(t, e) {
                    void 0 === e && (e = 1e3);
                    return new W((function(n) {
                        if (window.ResizeObserver) {
                            var r = N((function() {
                                    n.notify(function() {
                                        var t = xi(),
                                            e = Ri().height,
                                            n = Math.round((document.scrollingElement || document.documentElement).scrollHeight),
                                            r = Math.round(e + t);
                                        return {
                                            scrollHeight: n,
                                            scrollDepth: r,
                                            scrollTop: t
                                        }
                                    }())
                                }), e, {
                                    leading: !1,
                                    trailing: !0
                                }),
                                i = document.scrollingElement || document.documentElement,
                                o = new ResizeObserver(v(r.throttled));
                            o.observe(i);
                            var a = te(t, window, Ft.SCROLL, r.throttled, {
                                passive: !0
                            });
                            return function() {
                                r.cancel(), o.unobserve(i), a.stop()
                            }
                        }
                    }))
                }(t));
                var i = 0,
                    o = 0,
                    a = 0,
                    s = r.subscribe((function(t) {
                        var r = t.scrollDepth,
                            s = t.scrollTop,
                            u = t.scrollHeight,
                            c = !1;
                        if (r > i && (i = r, c = !0), u > o) {
                            o = u;
                            var l = pt();
                            a = mt(e.relative, l), c = !0
                        }
                        c && n({
                            maxDepth: Math.min(i, o),
                            maxDepthScrollTop: s,
                            maxScrollHeight: o,
                            maxScrollHeightTime: a
                        })
                    }));
                return {
                    stop: function() {
                        return s.unsubscribe()
                    }
                }
            }

            function Oi(t, e, n, r, i, o) {
                var a = {},
                    s = function(t, e, n, r, i, o) {
                        var a = "initial_load" === r,
                            s = !0,
                            u = [];

                        function c() {
                            !s && !a && u.length > 0 && o(Math.max.apply(Math, u))
                        }
                        return {
                            stop: yr(t, e, n, (function(t) {
                                s && (s = !1, t.hadActivity && u.push(mt(i.timeStamp, t.end)), c())
                            })).stop,
                            setLoadEvent: function(t) {
                                a && (a = !1, u.push(t), c())
                            }
                        }
                    }(t, e, n, i, o, (function(t) {
                        a.loadingTime = t, r()
                    })),
                    u = s.stop,
                    c = s.setLoadEvent,
                    l = Ii(n, o, (function(t) {
                        a.scroll = t
                    })).stop,
                    d = gi(n, t, o.relative, (function(t) {
                        a.cumulativeLayoutShift = t, r()
                    })).stop,
                    f = Ti(n, o.relative, i, t),
                    p = f.stop,
                    v = f.getInteractionToNextPaint;
                return {
                    stop: function() {
                        u(), d(), l()
                    },
                    stopINPTracking: p,
                    setLoadEvent: c,
                    setViewEnd: f.setViewEnd,
                    getCommonViewMetrics: function() {
                        return a.interactionToNextPaint = v(), a
                    }
                }
            }

            function Ni(t, e, n, r, i, o, a) {
                var u, c = new Set,
                    l = d("initial_load", ht(), a);

                function d(i, o, a) {
                    var u = function(t, e, n, r, i, o, a) {
                        void 0 === o && (o = vt());
                        var u, c, l, d, f = le(),
                            p = new W,
                            v = {},
                            h = 0,
                            m = D(r),
                            g = !0;
                        a && (c = a.name, l = a.service, d = a.version);
                        var _ = {
                            id: f,
                            name: c,
                            startClocks: o,
                            service: l,
                            version: d
                        };
                        t.notify(2, _), t.notify(3, _);
                        var y = N(V, 3e3, {
                                leading: !1
                            }),
                            b = y.throttled,
                            w = y.cancel,
                            S = Oi(t, e, n, b, i, o),
                            E = S.setLoadEvent,
                            C = S.setViewEnd,
                            T = S.stop,
                            k = S.stopINPTracking,
                            x = S.getCommonViewMetrics,
                            R = "initial_load" === i ? mi(t, n, E, b) : {
                                stop: L,
                                initialViewMetrics: {}
                            },
                            M = R.stop,
                            U = R.initialViewMetrics,
                            P = function(t, e, n) {
                                var r = _r({
                                    lifeCycle: t,
                                    isChildEvent: function(t) {
                                        return t.view.id === e
                                    },
                                    onChange: n
                                });
                                return {
                                    stop: r.stop,
                                    eventCounts: r.eventCounts
                                }
                            }(t, f, b),
                            z = P.stop,
                            B = P.eventCounts,
                            F = I(V, 3e5);

                        function V() {
                            w(), h += 1;
                            var e = void 0 === u ? ft() : u.timeStamp;
                            t.notify(4, {
                                customTimings: v,
                                documentVersion: h,
                                id: f,
                                name: c,
                                service: l,
                                version: d,
                                loadingType: i,
                                location: m,
                                startClocks: o,
                                commonViewMetrics: x(),
                                initialViewMetrics: U,
                                duration: mt(o.timeStamp, e),
                                isActive: void 0 === u,
                                sessionIsActive: g,
                                eventCounts: B
                            })
                        }
                        return V(), {
                            name: c,
                            service: l,
                            version: d,
                            stopObservable: p,
                            end: function(e) {
                                var n, r, i = this;
                                void 0 === e && (e = {}), u || (u = null !== (n = e.endClocks) && void 0 !== n ? n : vt(), g = null === (r = e.sessionIsActive) || void 0 === r || r, t.notify(5, {
                                    endClocks: u
                                }), t.notify(6, {
                                    endClocks: u
                                }), O(F), C(u.relative), T(), V(), A((function() {
                                    i.stop()
                                }), 3e5))
                            },
                            stop: function() {
                                M(), z(), k(), p.notify()
                            },
                            addTiming: function(t, e) {
                                if (!u) {
                                    var n = function(t) {
                                        return t < 31536e6
                                    }(e) ? e : mt(o.timeStamp, e);
                                    v[function(t) {
                                        var e = t.replace(/[^a-zA-Z0-9-_.@$]/g, "_");
                                        e !== t && s.warn("Invalid timing name: ".concat(t, ", sanitized to: ").concat(e));
                                        return e
                                    }(t)] = n, b()
                                }
                            }
                        }
                    }(e, n, r, t, i, o, a);
                    return c.add(u), u.stopObservable.subscribe((function() {
                        c.delete(u)
                    })), u
                }
                return e.subscribe(10, (function() {
                    l = d("route_change", void 0, {
                        name: l.name,
                        service: l.service,
                        version: l.version
                    })
                })), e.subscribe(9, (function() {
                    l.end({
                        sessionIsActive: !1
                    })
                })), e.subscribe(11, (function(t) {
                    t.reason === dn.UNLOADING && l.end()
                })), o && (u = function(t) {
                    return t.subscribe((function(t) {
                        var e, n, r = t.oldLocation,
                            i = t.newLocation;
                        n = i, ((e = r).pathname !== n.pathname || ! function(t) {
                            var e = t.substring(1);
                            return "" !== e && !!document.getElementById(e)
                        }(n.hash) && Li(n.hash) !== Li(e.hash)) && (l.end(), l = d("route_change"))
                    }))
                }(i)), {
                    addTiming: function(t, e) {
                        void 0 === e && (e = ft()), l.addTiming(t, e)
                    },
                    startView: function(t, e) {
                        l.end({
                            endClocks: e
                        }), l = d("route_change", e, t)
                    },
                    stop: function() {
                        null === u || void 0 === u || u.unsubscribe(), l.end(), c.forEach((function(t) {
                            return t.stop()
                        }))
                    }
                }
            }

            function Li(t) {
                var e = t.indexOf("?");
                return e < 0 ? t : t.slice(0, e)
            }

            function Mi(t, e, n, r, i, o, a, s, u) {
                return t.subscribe(4, (function(n) {
                    return t.notify(12, function(t, e, n, r, i) {
                        var o, a, s, u, c, l, d, f, p, v, h, m, g, _, y, b, w = r.getReplayStats(t.id),
                            S = n.findFeatureFlagEvaluations(t.startClocks.relative),
                            E = i.findAll(t.startClocks.relative, t.duration),
                            C = {
                                _dd: {
                                    document_version: t.documentVersion,
                                    replay_stats: w,
                                    page_states: E,
                                    configuration: {
                                        start_session_replay_recording_manually: e.startSessionReplayRecordingManually
                                    }
                                },
                                date: t.startClocks.timeStamp,
                                type: "view",
                                view: {
                                    action: {
                                        count: t.eventCounts.actionCount
                                    },
                                    frustration: {
                                        count: t.eventCounts.frustrationCount
                                    },
                                    cumulative_layout_shift: null === (o = t.commonViewMetrics.cumulativeLayoutShift) || void 0 === o ? void 0 : o.value,
                                    cumulative_layout_shift_time: lt(null === (a = t.commonViewMetrics.cumulativeLayoutShift) || void 0 === a ? void 0 : a.time),
                                    cumulative_layout_shift_target_selector: null === (s = t.commonViewMetrics.cumulativeLayoutShift) || void 0 === s ? void 0 : s.targetSelector,
                                    first_byte: lt(null === (u = t.initialViewMetrics.navigationTimings) || void 0 === u ? void 0 : u.firstByte),
                                    dom_complete: lt(null === (c = t.initialViewMetrics.navigationTimings) || void 0 === c ? void 0 : c.domComplete),
                                    dom_content_loaded: lt(null === (l = t.initialViewMetrics.navigationTimings) || void 0 === l ? void 0 : l.domContentLoaded),
                                    dom_interactive: lt(null === (d = t.initialViewMetrics.navigationTimings) || void 0 === d ? void 0 : d.domInteractive),
                                    error: {
                                        count: t.eventCounts.errorCount
                                    },
                                    first_contentful_paint: lt(t.initialViewMetrics.firstContentfulPaint),
                                    first_input_delay: lt(null === (f = t.initialViewMetrics.firstInput) || void 0 === f ? void 0 : f.delay),
                                    first_input_time: lt(null === (p = t.initialViewMetrics.firstInput) || void 0 === p ? void 0 : p.time),
                                    first_input_target_selector: null === (v = t.initialViewMetrics.firstInput) || void 0 === v ? void 0 : v.targetSelector,
                                    interaction_to_next_paint: lt(null === (h = t.commonViewMetrics.interactionToNextPaint) || void 0 === h ? void 0 : h.value),
                                    interaction_to_next_paint_time: lt(null === (m = t.commonViewMetrics.interactionToNextPaint) || void 0 === m ? void 0 : m.time),
                                    interaction_to_next_paint_target_selector: null === (g = t.commonViewMetrics.interactionToNextPaint) || void 0 === g ? void 0 : g.targetSelector,
                                    is_active: t.isActive,
                                    name: t.name,
                                    largest_contentful_paint: lt(null === (_ = t.initialViewMetrics.largestContentfulPaint) || void 0 === _ ? void 0 : _.value),
                                    largest_contentful_paint_target_selector: null === (y = t.initialViewMetrics.largestContentfulPaint) || void 0 === y ? void 0 : y.targetSelector,
                                    load_event: lt(null === (b = t.initialViewMetrics.navigationTimings) || void 0 === b ? void 0 : b.loadEvent),
                                    loading_time: gr(lt(t.commonViewMetrics.loadingTime)),
                                    loading_type: t.loadingType,
                                    long_task: {
                                        count: t.eventCounts.longTaskCount
                                    },
                                    resource: {
                                        count: t.eventCounts.resourceCount
                                    },
                                    time_spent: lt(t.duration)
                                },
                                feature_flags: S && !z(S) ? S : void 0,
                                display: t.commonViewMetrics.scroll ? {
                                    scroll: {
                                        max_depth: t.commonViewMetrics.scroll.maxDepth,
                                        max_depth_scroll_top: t.commonViewMetrics.scroll.maxDepthScrollTop,
                                        max_scroll_height: t.commonViewMetrics.scroll.maxScrollHeight,
                                        max_scroll_height_time: lt(t.commonViewMetrics.scroll.maxScrollHeightTime)
                                    }
                                } : void 0,
                                session: {
                                    has_replay: !!w || void 0,
                                    is_active: !!t.sessionIsActive && void 0
                                },
                                privacy: {
                                    replay_level: e.defaultPrivacyLevel
                                }
                            };
                        z(t.customTimings) || (C.view.custom_timings = function(t, e) {
                            for (var n = {}, r = 0, i = Object.keys(t); r < i.length; r++) {
                                var o = i[r];
                                n[o] = e(t[o])
                            }
                            return n
                        }(t.customTimings, lt));
                        return {
                            rawRumEvent: C,
                            startTime: t.startClocks.relative,
                            domainContext: {
                                location: t.location
                            }
                        }
                    }(n, e, o, s, a))
                })), Ni(n, t, r, e, i, !e.trackViewsManually, u)
            }
            var Ui = [];

            function Di(t, e, n, r) {
                var i = new W,
                    o = new W,
                    a = He(t.sessionStoreStrategyType, e, n);
                Ui.push((function() {
                    return a.stop()
                }));
                var s = new or(144e5);

                function u() {
                    return {
                        id: a.getSession().id,
                        trackingType: a.getSession()[e],
                        isReplayForced: !!a.getSession().forcedReplay
                    }
                }
                return Ui.push((function() {
                        return s.stop()
                    })), a.renewObservable.subscribe((function() {
                        s.add(u(), pt()), i.notify()
                    })), a.expireObservable.subscribe((function() {
                        o.notify(), s.closeActive(pt())
                    })), a.expandOrRenewSession(), s.add(u(), ht().relative), r.observable.subscribe((function() {
                        r.isGranted() ? a.expandOrRenewSession() : a.expire()
                    })),
                    function(t, e) {
                        var n = ee(t, window, [Ft.CLICK, Ft.TOUCH_START, Ft.KEY_DOWN, Ft.SCROLL], e, {
                            capture: !0,
                            passive: !0
                        }).stop;
                        Ui.push(n)
                    }(t, (function() {
                        r.isGranted() && a.expandOrRenewSession()
                    })),
                    function(t, e) {
                        var n = function() {
                                "visible" === document.visibilityState && e()
                            },
                            r = te(t, document, Ft.VISIBILITY_CHANGE, n).stop;
                        Ui.push(r);
                        var i = I(n, 6e4);
                        Ui.push((function() {
                            O(i)
                        }))
                    }(t, (function() {
                        return a.expandSession()
                    })),
                    function(t, e) {
                        var n = te(t, window, Ft.RESUME, e, {
                            capture: !0
                        }).stop;
                        Ui.push(n)
                    }(t, (function() {
                        return a.restartSession()
                    })), {
                        findSession: function(t, e) {
                            return s.find(t, e)
                        },
                        renewObservable: i,
                        expireObservable: o,
                        sessionStateUpdateObservable: a.sessionStateUpdateObservable,
                        expire: a.expire,
                        updateSessionState: a.updateSessionState
                    }
            }

            function Pi(t, e, n) {
                var r = Di(t, "rum", (function(e) {
                    return function(t, e) {
                        var n;
                        n = function(t) {
                            return "0" === t || "1" === t || "2" === t
                        }(e) ? e : nt(t.sessionSampleRate) ? nt(t.sessionReplaySampleRate) ? "1" : "2" : "0";
                        return {
                            trackingType: n,
                            isTracked: zi(n)
                        }
                    }(t, e)
                }), n);
                return r.expireObservable.subscribe((function() {
                    e.notify(9)
                })), r.renewObservable.subscribe((function() {
                    e.notify(10)
                })), r.sessionStateUpdateObservable.subscribe((function(t) {
                    var e = t.previousState,
                        n = t.newState;
                    if (!e.forcedReplay && n.forcedReplay) {
                        var i = r.findSession();
                        i && (i.isReplayForced = !0)
                    }
                })), {
                    findTrackedSession: function(t) {
                        var e = r.findSession(t);
                        if (e && zi(e.trackingType)) return {
                            id: e.id,
                            sessionReplay: "1" === e.trackingType ? 1 : e.isReplayForced ? 2 : 0
                        }
                    },
                    expire: r.expire,
                    expireObservable: r.expireObservable,
                    setForcedReplay: function() {
                        return r.updateSessionState({
                            forcedReplay: "1"
                        })
                    }
                }
            }

            function zi(t) {
                return "2" === t || "1" === t
            }
            var Bi = function() {
                function t(t, e, n, r) {
                    var i = this;
                    this.encoder = t, this.request = e, this.flushController = n, this.messageBytesLimit = r, this.upsertBuffer = {}, this.flushSubscription = this.flushController.flushObservable.subscribe((function(t) {
                        return i.flush(t)
                    }))
                }
                return t.prototype.add = function(t) {
                    this.addOrUpdate(t)
                }, t.prototype.upsert = function(t, e) {
                    this.addOrUpdate(t, e)
                }, t.prototype.stop = function() {
                    this.flushSubscription.unsubscribe()
                }, t.prototype.flush = function(t) {
                    var e = b(this.upsertBuffer).join("\n");
                    this.upsertBuffer = {};
                    var n = fn(t.reason),
                        r = n ? this.request.sendOnExit : this.request.send;
                    if (n && this.encoder.isAsync) {
                        var i = this.encoder.finishSync();
                        i.outputBytesCount && r(Fi(i));
                        var o = [i.pendingData, e].filter(Boolean).join("\n");
                        o && r({
                            data: o,
                            bytesCount: k(o)
                        })
                    } else e && this.encoder.write(this.encoder.isEmpty ? e : "\n".concat(e)), this.encoder.finish((function(t) {
                        r(Fi(t))
                    }))
                }, t.prototype.addOrUpdate = function(t, e) {
                    var n = M(t),
                        r = this.encoder.estimateEncodedBytesCount(n);
                    r >= this.messageBytesLimit ? s.warn("Discarded a message whose size was bigger than the maximum allowed size ".concat(this.messageBytesLimit, "KB. More details: ").concat(u, "/real_user_monitoring/browser/troubleshooting/#technical-limitations")) : (this.hasMessageFor(e) && this.remove(e), this.push(n, r, e))
                }, t.prototype.push = function(t, e, n) {
                    var r = this;
                    this.flushController.notifyBeforeAddMessage(e), void 0 !== n ? (this.upsertBuffer[n] = t, this.flushController.notifyAfterAddMessage()) : this.encoder.write(this.encoder.isEmpty ? t : "\n".concat(t), (function(t) {
                        r.flushController.notifyAfterAddMessage(t - e)
                    }))
                }, t.prototype.remove = function(t) {
                    var e = this.upsertBuffer[t];
                    delete this.upsertBuffer[t];
                    var n = this.encoder.estimateEncodedBytesCount(e);
                    this.flushController.notifyAfterRemoveMessage(n)
                }, t.prototype.hasMessageFor = function(t) {
                    return void 0 !== t && void 0 !== this.upsertBuffer[t]
                }, t
            }();

            function Fi(t) {
                return {
                    data: "string" === typeof t.output ? t.output : new Blob([t.output], {
                        type: "text/plain"
                    }),
                    bytesCount: t.outputBytesCount,
                    encoding: t.encoding
                }
            }

            function Vi(t, e, n, r, i) {
                0 === e.transportStatus && 0 === e.queuedPayloads.size() && e.bandwidthMonitor.canHandle(t) ? Hi(t, e, n, {
                    onSuccess: function() {
                        return qi(0, e, n, r, i)
                    },
                    onFailure: function() {
                        e.queuedPayloads.enqueue(t),
                            function t(e, n, r, i) {
                                if (2 !== e.transportStatus) return;
                                A((function() {
                                    Hi(e.queuedPayloads.first(), e, n, {
                                        onSuccess: function() {
                                            e.queuedPayloads.dequeue(), e.currentBackoffTime = 1e3, qi(1, e, n, r, i)
                                        },
                                        onFailure: function() {
                                            e.currentBackoffTime = Math.min(6e4, 2 * e.currentBackoffTime), t(e, n, r, i)
                                        }
                                    })
                                }), e.currentBackoffTime)
                            }(e, n, r, i)
                    }
                }) : e.queuedPayloads.enqueue(t)
            }

            function Hi(t, e, n, r) {
                var i = r.onSuccess,
                    o = r.onFailure;
                e.bandwidthMonitor.add(t), n(t, (function(n) {
                    e.bandwidthMonitor.remove(t), ! function(t) {
                        return "opaque" !== t.type && (0 === t.status && !navigator.onLine || 408 === t.status || 429 === t.status || (e = t.status, e >= 500));
                        var e
                    }(n) ? (e.transportStatus = 0, i()) : (e.transportStatus = e.bandwidthMonitor.ongoingRequestCount > 0 ? 1 : 2, t.retry = {
                        count: t.retry ? t.retry.count + 1 : 1,
                        lastFailureStatus: n.status
                    }, o())
                }))
            }

            function qi(t, e, n, r, i) {
                0 === t && e.queuedPayloads.isFull() && !e.queueFullReported && (i({
                    message: "Reached max ".concat(r, " events size queued for upload: ").concat(3, "MiB"),
                    source: Hn,
                    startClocks: vt()
                }), e.queueFullReported = !0);
                var o = e.queuedPayloads;
                for (e.queuedPayloads = Gi(); o.size() > 0;) Vi(o.dequeue(), e, n, r, i)
            }

            function Gi() {
                var t = [];
                return {
                    bytesCount: 0,
                    enqueue: function(e) {
                        this.isFull() || (t.push(e), this.bytesCount += e.bytesCount)
                    },
                    first: function() {
                        return t[0]
                    },
                    dequeue: function() {
                        var e = t.shift();
                        return e && (this.bytesCount -= e.bytesCount), e
                    },
                    size: function() {
                        return t.length
                    },
                    isFull: function() {
                        return this.bytesCount >= 3145728
                    }
                }
            }

            function ji(t, e, n, r) {
                var i = {
                        transportStatus: 0,
                        currentBackoffTime: 1e3,
                        bandwidthMonitor: {
                            ongoingRequestCount: 0,
                            ongoingByteCount: 0,
                            canHandle: function(t) {
                                return 0 === this.ongoingRequestCount || this.ongoingByteCount + t.bytesCount <= 81920 && this.ongoingRequestCount < 32
                            },
                            add: function(t) {
                                this.ongoingRequestCount += 1, this.ongoingByteCount += t.bytesCount
                            },
                            remove: function(t) {
                                this.ongoingRequestCount -= 1, this.ongoingByteCount -= t.bytesCount
                            }
                        },
                        queuedPayloads: Gi(),
                        queueFullReported: !1
                    },
                    o = function(r, i) {
                        return function(t, e, n, r, i) {
                            if (function() {
                                    try {
                                        return window.Request && "keepalive" in new Request("http://a")
                                    } catch (t) {
                                        return !1
                                    }
                                }() && r.bytesCount < n) {
                                var o = e.build("fetch", r);
                                fetch(o, {
                                    method: "POST",
                                    body: r.data,
                                    keepalive: !0,
                                    mode: "cors"
                                }).then(v((function(t) {
                                    return null === i || void 0 === i ? void 0 : i({
                                        status: t.status,
                                        type: t.type
                                    })
                                })), v((function() {
                                    var n = e.build("xhr", r);
                                    Ki(t, n, r.data, i)
                                })))
                            } else {
                                var a = e.build("xhr", r);
                                Ki(t, a, r.data, i)
                            }
                        }(t, e, n, r, i)
                    };
                return {
                    send: function(t) {
                        Vi(t, i, o, e.trackType, r)
                    },
                    sendOnExit: function(r) {
                        ! function(t, e, n, r) {
                            if (navigator.sendBeacon && r.bytesCount < n) try {
                                var i = e.build("beacon", r);
                                if (navigator.sendBeacon(i, r.data)) return
                            } catch (t) {
                                ! function(t) {
                                    Zi || (Zi = !0, Jt(t))
                                }(t)
                            }
                            var o = e.build("xhr", r);
                            Ki(t, o, r.data)
                        }(t, e, n, r)
                    }
                }
            }
            var Zi = !1;

            function Ki(t, e, n, r) {
                var i = new XMLHttpRequest;
                i.open("POST", e, !0), n instanceof Blob && i.setRequestHeader("Content-Type", n.type), te(t, i, "loadend", (function() {
                    null === r || void 0 === r || r({
                        status: i.status
                    })
                }), {
                    once: !0
                }), i.send(n)
            }

            function Wi(t) {
                var e, n = t.messagesLimit,
                    r = t.bytesLimit,
                    i = t.durationLimit,
                    o = t.pageExitObservable,
                    a = t.sessionExpireObservable,
                    s = o.subscribe((function(t) {
                        return f(t.reason)
                    })),
                    u = a.subscribe((function() {
                        return f("session_expire")
                    })),
                    c = new W((function() {
                        return function() {
                            s.unsubscribe(), u.unsubscribe()
                        }
                    })),
                    l = 0,
                    d = 0;

                function f(t) {
                    if (0 !== d) {
                        var e = d,
                            n = l;
                        d = 0, l = 0, p(), c.notify({
                            reason: t,
                            messagesCount: e,
                            bytesCount: n
                        })
                    }
                }

                function p() {
                    R(e), e = void 0
                }
                return {
                    flushObservable: c,
                    get messagesCount() {
                        return d
                    },
                    notifyBeforeAddMessage: function(t) {
                        l + t >= r && f("bytes_limit"), d += 1, l += t, void 0 === e && (e = A((function() {
                            f("duration_limit")
                        }), i))
                    },
                    notifyAfterAddMessage: function(t) {
                        void 0 === t && (t = 0), l += t, d >= n ? f("messages_limit") : l >= r && f("bytes_limit")
                    },
                    notifyAfterRemoveMessage: function(t) {
                        l -= t, 0 === (d -= 1) && p()
                    }
                }
            }

            function Yi(t, e, n, r, i, o, a) {
                var s = t.replica,
                    u = function(t, e, n, r, i, o) {
                        var a = u(t, e),
                            s = n && u(t, n);

                        function u(t, e) {
                            var n = e.endpoint,
                                a = e.encoder;
                            return new Bi(a, ji(t, n, t.batchBytesLimit, r), Wi({
                                messagesLimit: t.batchMessagesLimit,
                                bytesLimit: t.batchBytesLimit,
                                durationLimit: t.flushTimeout,
                                pageExitObservable: i,
                                sessionExpireObservable: o
                            }), t.messageBytesLimit)
                        }
                        return {
                            flushObservable: a.flushController.flushObservable,
                            add: function(t, e) {
                                void 0 === e && (e = !0), a.add(t), s && e && s.add(n.transformMessage ? n.transformMessage(t) : t)
                            },
                            upsert: function(t, e) {
                                a.upsert(t, e), s && s.upsert(n.transformMessage ? n.transformMessage(t) : t, e)
                            },
                            stop: function() {
                                a.stop(), null === s || void 0 === s || s.stop()
                            }
                        }
                    }(t, {
                        endpoint: t.rumEndpointBuilder,
                        encoder: a(2)
                    }, s && {
                        endpoint: s.rumEndpointBuilder,
                        transformMessage: function(t) {
                            return G(t, {
                                application: {
                                    id: s.applicationId
                                }
                            })
                        },
                        encoder: a(3)
                    }, r, i, o);
                return e.subscribe(13, (function(t) {
                    "view" === t.type ? u.upsert(t, t.view.id) : u.add(t)
                })), n.subscribe((function(e) {
                    return u.add(e, function(t) {
                        return "datad0g.com" === t.site
                    }(t))
                })), u
            }

            function Xi(t, e) {
                var n = D(e);
                return new W((function(r) {
                    var i = function(t, e) {
                            var n = ar(history, "pushState", (function(t) {
                                    (0, t.onPostCall)(e)
                                })).stop,
                                r = ar(history, "replaceState", (function(t) {
                                    (0, t.onPostCall)(e)
                                })).stop,
                                i = te(t, window, Ft.POP_STATE, e).stop;
                            return {
                                stop: function() {
                                    n(), r(), i()
                                }
                            }
                        }(t, a).stop,
                        o = function(t, e) {
                            return te(t, window, Ft.HASH_CHANGE, e)
                        }(t, a).stop;

                    function a() {
                        if (n.href !== e.href) {
                            var t = D(e);
                            r.notify({
                                newLocation: t,
                                oldLocation: n
                            }), n = t
                        }
                    }
                    return function() {
                        i(), o()
                    }
                }))
            }
            var Ji, $i, Qi;

            function to() {
                0 !== Ji.batchCount && (Xt("Customer data measures", Ji), ro())
            }

            function eo(t, e) {
                t.sum += e, t.min = Math.min(t.min, e), t.max = Math.max(t.max, e)
            }

            function no(t, e) {
                t.sum += e.sum, t.min = Math.min(t.min, e.min), t.max = Math.max(t.max, e.max)
            }

            function ro() {
                Ji = {
                    batchCount: 0,
                    batchBytesCount: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    },
                    batchMessagesCount: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    },
                    globalContextBytes: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    },
                    userContextBytes: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    },
                    featureFlagBytes: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    }
                }
            }

            function io() {
                Qi = !1, $i = {
                    globalContextBytes: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    },
                    userContextBytes: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    },
                    featureFlagBytes: {
                        min: 1 / 0,
                        max: 0,
                        sum: 0
                    }
                }
            }

            function oo(t, e) {
                void 0 === e && (e = 500);
                var n, r = new or(144e5, 4e3);
                o(ao(), pt());
                var i = ee(t, window, [Ft.PAGE_SHOW, Ft.FOCUS, Ft.BLUR, Ft.VISIBILITY_CHANGE, Ft.RESUME, Ft.FREEZE, Ft.PAGE_HIDE], (function(t) {
                    o(function(t) {
                        if (t.type === Ft.FREEZE) return "frozen";
                        if (t.type === Ft.PAGE_HIDE) return t.persisted ? "frozen" : "terminated";
                        return ao()
                    }(t), t.timeStamp)
                }), {
                    capture: !0
                }).stop;

                function o(t, e) {
                    void 0 === e && (e = pt()), t !== n && (n = t, r.closeActive(e), r.add({
                        state: n,
                        startTime: e
                    }, e))
                }
                var a = {
                    findAll: function(t, n) {
                        var i = r.findAll(t, n);
                        if (0 !== i.length) {
                            for (var o = [], a = Math.max(0, i.length - e), s = i.length - 1; s >= a; s--) {
                                var u = i[s],
                                    c = mt(t, u.startTime);
                                o.push({
                                    state: u.state,
                                    start: lt(c)
                                })
                            }
                            return o
                        }
                    },
                    wasInPageStateAt: function(t, e) {
                        return a.wasInPageStateDuringPeriod(t, e, 0)
                    },
                    wasInPageStateDuringPeriod: function(t, e, n) {
                        return r.findAll(e, n).some((function(e) {
                            return e.state === t
                        }))
                    },
                    addPageState: o,
                    stop: function() {
                        i(), r.stop()
                    }
                };
                return a
            }

            function ao() {
                return "hidden" === document.visibilityState ? "hidden" : document.hasFocus() ? "active" : "passive"
            }

            function so(t, e) {
                var n = new Map;
                return t.subscribe(10, (function() {
                    n.clear()
                })), {
                    startDurationVital: function(t) {
                        n.set(t.name, t)
                    },
                    stopDurationVital: function(r) {
                        var i = n.get(r.name);
                        if (i) {
                            var o = function(t, e) {
                                return {
                                    name: t.name,
                                    type: "duration",
                                    startClocks: t.startClocks,
                                    value: mt(t.startClocks.timeStamp, e.stopClocks.timeStamp),
                                    context: G(t.context, e.context)
                                }
                            }(i, r);
                            n.delete(o.name),
                                function(t) {
                                    return !e.wasInPageStateDuringPeriod("frozen", t.startClocks.relative, t.value)
                                }(o) && t.notify(12, function(t, e) {
                                    var n, r = {
                                        date: t.startClocks.timeStamp,
                                        vital: {
                                            id: le(),
                                            type: t.type,
                                            name: t.name,
                                            custom: (n = {}, n[t.name] = t.value, n)
                                        },
                                        type: "vital"
                                    };
                                    e && (r._dd = {
                                        vital: {
                                            computed_value: !0
                                        }
                                    });
                                    return {
                                        rawRumEvent: r,
                                        startTime: t.startClocks.relative,
                                        customerContext: t.context,
                                        domainContext: {}
                                    }
                                }(o, !0))
                        }
                    }
                }
            }

            function uo(t, e) {
                var n = window.cookieStore ? function(t) {
                    return function(e, n) {
                        return te(t, window.cookieStore, Ft.CHANGE, (function(t) {
                            var r = y(t.changed, (function(t) {
                                return t.name === e
                            })) || y(t.deleted, (function(t) {
                                return t.name === e
                            }));
                            r && n(r.value)
                        })).stop
                    }
                }(t) : co;
                return new W((function(t) {
                    return n(e, (function(e) {
                        return t.notify(e)
                    }))
                }))
            }

            function co(t, e) {
                var n = ve(document.cookie, t),
                    r = I((function() {
                        var r = ve(document.cookie, t);
                        r !== n && e(r)
                    }), 1e3);
                return function() {
                    O(r)
                }
            }

            function lo(t, e) {
                var n = e.session,
                    r = e.viewContext,
                    i = e.errorType,
                    o = n ? n.id : "no-session-id",
                    a = [];
                void 0 !== i && a.push("error-type=".concat(i)), r && (a.push("seed=".concat(r.id)), a.push("from=".concat(r.startClocks.timeStamp)));
                var s = function(t) {
                        var e = t.site,
                            n = t.subdomain || function(t) {
                                switch (t.site) {
                                    case "datadoghq.com":
                                    case "datadoghq.eu":
                                        return "app";
                                    case "datad0g.com":
                                        return "dd";
                                    default:
                                        return
                                }
                            }(t);
                        return "https://".concat(n ? "".concat(n, ".") : "").concat(e)
                    }(t),
                    u = "/rum/replay/sessions/".concat(o);
                return "".concat(s).concat(u, "?").concat(a.join("&"))
            }
            var fo;

            function po(t) {
                return vo(t).segments_count
            }

            function vo(t) {
                var e;
                return fo || (fo = new Map), fo.has(t) ? e = fo.get(t) : (e = {
                    records_count: 0,
                    segments_count: 0,
                    segments_total_raw_size: 0
                }, fo.set(t, e), fo.size > 10 && function() {
                    if (!fo) return;
                    if (fo.keys) fo.delete(fo.keys().next().value);
                    else {
                        var t = !0;
                        fo.forEach((function(e, n) {
                            t && (fo.delete(n), t = !1)
                        }))
                    }
                }()), e
            }
            var ho = {
                    IGNORE: "ignore",
                    HIDDEN: "hidden",
                    ALLOW: Je.ALLOW,
                    MASK: Je.MASK,
                    MASK_USER_INPUT: Je.MASK_USER_INPUT
                },
                mo = "data:image/gif;base64,R0lGODlhAQABAIAAAMLCwgAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw==",
                go = {
                    INPUT: !0,
                    OUTPUT: !0,
                    TEXTAREA: !0,
                    SELECT: !0,
                    OPTION: !0,
                    DATALIST: !0,
                    OPTGROUP: !0
                };

            function _o(t, e, n) {
                if (n && n.has(t)) return n.get(t);
                var r = Nn(t),
                    i = r ? _o(r, e, n) : e,
                    o = yo(bo(t), i);
                return n && n.set(t, o), o
            }

            function yo(t, e) {
                switch (e) {
                    case ho.HIDDEN:
                    case ho.IGNORE:
                        return e
                }
                switch (t) {
                    case ho.ALLOW:
                    case ho.MASK:
                    case ho.MASK_USER_INPUT:
                    case ho.HIDDEN:
                    case ho.IGNORE:
                        return t;
                    default:
                        return e
                }
            }

            function bo(t) {
                if (An(t)) {
                    var e = t.getAttribute("data-dd-privacy");
                    if ("BASE" === t.tagName) return ho.ALLOW;
                    if ("INPUT" === t.tagName) {
                        var n = t;
                        if ("password" === n.type || "email" === n.type || "tel" === n.type) return ho.MASK;
                        if ("hidden" === n.type) return ho.MASK;
                        var r = n.getAttribute("autocomplete");
                        if (r && 0 === r.indexOf("cc-")) return ho.MASK
                    }
                    return "hidden" === e || t.classList.contains("dd-privacy-hidden") ? ho.HIDDEN : "mask" === e || t.classList.contains("dd-privacy-mask") ? ho.MASK : "mask-user-input" === e || t.classList.contains("dd-privacy-mask-user-input") ? ho.MASK_USER_INPUT : "allow" === e || t.classList.contains("dd-privacy-allow") ? ho.ALLOW : function(t) {
                        if ("SCRIPT" === t.nodeName) return !0;
                        if ("LINK" === t.nodeName) {
                            var e = i("rel");
                            return /preload|prefetch/i.test(e) && "script" === i("as") || "shortcut icon" === e || "icon" === e
                        }
                        if ("META" === t.nodeName) {
                            var n = i("name"),
                                r = (e = i("rel"), i("property"));
                            return /^msapplication-tile(image|color)$/.test(n) || "application-name" === n || "icon" === e || "apple-touch-icon" === e || "shortcut icon" === e || "keywords" === n || "description" === n || /^(og|twitter|fb):/.test(r) || /^(og|twitter):/.test(n) || "pinterest" === n || "robots" === n || "googlebot" === n || "bingbot" === n || t.hasAttribute("http-equiv") || "author" === n || "generator" === n || "framework" === n || "publisher" === n || "progid" === n || /^article:/.test(r) || /^product:/.test(r) || "google-site-verification" === n || "yandex-verification" === n || "csrf-token" === n || "p:domain_verify" === n || "verify-v1" === n || "verification" === n || "shopify-checkout-api-token" === n
                        }

                        function i(e) {
                            return (t.getAttribute(e) || "").toLowerCase()
                        }
                        return !1
                    }(t) ? ho.IGNORE : void 0
                }
            }

            function wo(t, e) {
                switch (e) {
                    case ho.MASK:
                    case ho.HIDDEN:
                    case ho.IGNORE:
                        return !0;
                    case ho.MASK_USER_INPUT:
                        return xn(t) ? So(t.parentNode) : So(t);
                    default:
                        return !1
                }
            }

            function So(t) {
                if (!t || t.nodeType !== t.ELEMENT_NODE) return !1;
                var e = t;
                if ("INPUT" === e.tagName) switch (e.type) {
                    case "button":
                    case "color":
                    case "reset":
                    case "submit":
                        return !1
                }
                return !!go[e.tagName]
            }

            function Eo(t, e, n) {
                var r, i = null === (r = t.parentElement) || void 0 === r ? void 0 : r.tagName,
                    o = t.textContent || "";
                if (!e || o.trim()) {
                    if ("SCRIPT" === i) o = "***";
                    else if (n === ho.HIDDEN) o = "***";
                    else if (wo(t, n))
                        if ("DATALIST" === i || "SELECT" === i || "OPTGROUP" === i) {
                            if (!o.trim()) return
                        } else o = "OPTION" === i ? "***" : o.replace(/\S/g, "x");
                    return o
                }
            }
            var Co = new WeakMap;

            function To(t) {
                return Co.has(t)
            }

            function ko(t) {
                return Co.get(t)
            }

            function xo(t, e) {
                var n = t.tagName,
                    r = t.value;
                if (wo(t, e)) {
                    var i = t.type;
                    if ("INPUT" === n && ("button" === i || "submit" === i || "reset" === i)) return r;
                    if (!r || "OPTION" === n) return;
                    return "***"
                }
                return "OPTION" === n || "SELECT" === n ? t.value : "INPUT" === n || "TEXTAREA" === n ? r : void 0
            }
            var Ao = /url\((?:(')([^']*)'|(")([^"]*)"|([^)]*))\)/gm,
                Ro = /^[A-Za-z]+:|^\/\//,
                Io = /^data:.*,/i;

            function Oo(t, e) {
                return t.replace(Ao, (function(t, n, r, i, o, a) {
                    var s = r || o || a;
                    if (!e || !s || Ro.test(s) || Io.test(s)) return t;
                    var u = n || i || "";
                    return "url(".concat(u).concat(function(t, e) {
                        try {
                            return Ge(t, e).href
                        } catch (e) {
                            return t
                        }
                    }(s, e)).concat(u, ")")
                }))
            }
            var No = /[^a-z1-6-_]/;

            function Lo(t) {
                var e = t.toLowerCase().trim();
                return No.test(e) ? "div" : e
            }

            function Mo(t, e) {
                return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='".concat(t, "' height='").concat(e, "' style='background-color:silver'%3E%3C/svg%3E")
            }
            var Uo = 2,
                Do = 3,
                Po = 4,
                zo = 6,
                Bo = 7,
                Fo = 8,
                Vo = 9,
                Ho = 0,
                qo = 1,
                Go = 2,
                jo = 3,
                Zo = 4,
                Ko = 11,
                Wo = 0,
                Yo = 1,
                Xo = 2,
                Jo = 3,
                $o = 4,
                Qo = 5,
                ta = 6,
                ea = 7,
                na = 8,
                ra = 0,
                ia = 1,
                oa = 2,
                aa = 3,
                sa = 4,
                ua = 5,
                ca = 6,
                la = 7,
                da = 9,
                fa = 0,
                pa = 1;

            function va(t) {
                if (void 0 !== t && 0 !== t.length) return t.map((function(t) {
                    var e = t.cssRules || t.rules;
                    return {
                        cssRules: Array.from(e, (function(t) {
                            return t.cssText
                        })),
                        disabled: t.disabled || void 0,
                        media: t.media.length > 0 ? Array.from(t.media) : void 0
                    }
                }))
            }

            function ha(t, e, n, r) {
                if (e === ho.HIDDEN) return null;
                var i = t.getAttribute(n);
                if (e === ho.MASK && "data-dd-privacy" !== n && !Ur.includes(n) && n !== r.actionNameAttribute) {
                    var o = t.tagName;
                    switch (n) {
                        case "title":
                        case "alt":
                        case "placeholder":
                            return "***"
                    }
                    if ("IMG" === o && ("src" === n || "srcset" === n)) {
                        var a = t;
                        if (a.naturalWidth > 0) return Mo(a.naturalWidth, a.naturalHeight);
                        var s = t.getBoundingClientRect(),
                            u = s.width,
                            c = s.height;
                        return u > 0 || c > 0 ? Mo(u, c) : mo
                    }
                    if ("SOURCE" === o && ("src" === n || "srcset" === n)) return mo;
                    if ("A" === o && "href" === n) return "***";
                    if (i && S(n, "data-")) return "***";
                    if ("IFRAME" === o && "srcdoc" === n) return "***"
                }
                return i && "string" === typeof i && Tn(i) ? kn(i) : i
            }

            function ma(t) {
                if (!t) return null;
                var e;
                try {
                    e = t.rules || t.cssRules
                } catch (t) {}
                return e ? Oo(Array.from(e, 2 === Se() ? ga : _a).join(""), t.href) : null
            }

            function ga(t) {
                if (function(t) {
                        return "selectorText" in t
                    }(t) && t.selectorText.includes(":")) {
                    return t.cssText.replace(/(\[[\w-]+[^\\])(:[^\]]+\])/g, "$1\\$2")
                }
                return _a(t)
            }

            function _a(t) {
                return function(t) {
                    return "styleSheet" in t
                }(t) && ma(t.styleSheet) || t.cssText
            }

            function ya(t, e) {
                var n = function(t, e) {
                    switch (t.nodeType) {
                        case t.DOCUMENT_NODE:
                            return function(t, e) {
                                return {
                                    type: Ho,
                                    childNodes: wa(t, e),
                                    adoptedStyleSheets: va(t.adoptedStyleSheets)
                                }
                            }(t, e);
                        case t.DOCUMENT_FRAGMENT_NODE:
                            return function(t, e) {
                                var n = In(t);
                                n && e.serializationContext.shadowRootsController.addShadowRoot(t);
                                return {
                                    type: Ko,
                                    childNodes: wa(t, e),
                                    isShadowRoot: n,
                                    adoptedStyleSheets: n ? va(t.adoptedStyleSheets) : void 0
                                }
                            }(t, e);
                        case t.DOCUMENT_TYPE_NODE:
                            return {
                                type: qo,
                                name: (n = t).name,
                                publicId: n.publicId,
                                systemId: n.systemId
                            };
                        case t.ELEMENT_NODE:
                            return function(t, e) {
                                var n, r = Lo(t.tagName),
                                    i = (a = t, "svg" === a.tagName || a instanceof SVGElement || void 0),
                                    o = yo(bo(t), e.parentNodePrivacyLevel);
                                var a;
                                if (o === ho.HIDDEN) {
                                    var s = t.getBoundingClientRect(),
                                        u = s.width,
                                        c = s.height;
                                    return {
                                        type: Go,
                                        tagName: r,
                                        attributes: (n = {
                                            rr_width: "".concat(u, "px"),
                                            rr_height: "".concat(c, "px")
                                        }, n["data-dd-privacy"] = "hidden", n),
                                        childNodes: [],
                                        isSVG: i
                                    }
                                }
                                if (o === ho.IGNORE) return;
                                var l = function(t, e, n) {
                                        var r;
                                        if (e === ho.HIDDEN) return {};
                                        for (var i = {}, o = Lo(t.tagName), a = t.ownerDocument, s = 0; s < t.attributes.length; s += 1) {
                                            var u = t.attributes.item(s).name,
                                                c = ha(t, e, u, n.configuration);
                                            null !== c && (i[u] = c)
                                        }
                                        if (t.value && ("textarea" === o || "select" === o || "option" === o || "input" === o)) {
                                            var l = xo(t, e);
                                            void 0 !== l && (i.value = l)
                                        }
                                        if ("option" === o && e === ho.ALLOW) {
                                            var d = t;
                                            d.selected && (i.selected = d.selected)
                                        }
                                        if ("link" === o) {
                                            var f, p = Array.from(a.styleSheets).find((function(e) {
                                                return e.href === t.href
                                            }));
                                            (f = ma(p)) && p && (i._cssText = f)
                                        }
                                        "style" === o && t.sheet && (f = ma(t.sheet)) && (i._cssText = f);
                                        var v, h, m = t;
                                        if ("input" !== o || "radio" !== m.type && "checkbox" !== m.type || (e === ho.ALLOW ? i.checked = !!m.checked : wo(m, e) && delete i.checked), "audio" === o || "video" === o) {
                                            var g = t;
                                            i.rr_mediaState = g.paused ? "paused" : "played"
                                        }
                                        var _ = n.serializationContext;
                                        switch (_.status) {
                                            case 0:
                                                v = Math.round(t.scrollTop), h = Math.round(t.scrollLeft), (v || h) && _.elementsScrollPositions.set(t, {
                                                    scrollTop: v,
                                                    scrollLeft: h
                                                });
                                                break;
                                            case 1:
                                                _.elementsScrollPositions.has(t) && (v = (r = _.elementsScrollPositions.get(t)).scrollTop, h = r.scrollLeft)
                                        }
                                        return h && (i.rr_scrollLeft = h), v && (i.rr_scrollTop = v), i
                                    }(t, o, e),
                                    d = [];
                                if (function(t) {
                                        return t.childNodes.length > 0 || Rn(t)
                                    }(t) && "style" !== r) {
                                    var f = void 0;
                                    f = e.parentNodePrivacyLevel === o && e.ignoreWhiteSpace === ("head" === r) ? e : E({}, e, {
                                        parentNodePrivacyLevel: o,
                                        ignoreWhiteSpace: "head" === r
                                    }), d = wa(t, f)
                                }
                                return {
                                    type: Go,
                                    tagName: r,
                                    attributes: l,
                                    childNodes: d,
                                    isSVG: i
                                }
                            }(t, e);
                        case t.TEXT_NODE:
                            return function(t, e) {
                                var n = Eo(t, e.ignoreWhiteSpace || !1, e.parentNodePrivacyLevel);
                                if (void 0 === n) return;
                                return {
                                    type: jo,
                                    textContent: n
                                }
                            }(t, e);
                        case t.CDATA_SECTION_NODE:
                            return {
                                type: Zo,
                                textContent: ""
                            }
                    }
                    var n
                }(t, e);
                if (!n) return null;
                var r = ko(t) || ba++,
                    i = n;
                return i.id = r,
                    function(t, e) {
                        Co.set(t, e)
                    }(t, r), e.serializedNodeIds && e.serializedNodeIds.add(r), i
            }
            var ba = 1;

            function wa(t, e) {
                var n = [];
                return On(t, (function(t) {
                    var r = ya(t, e);
                    r && n.push(r)
                })), n
            }

            function Sa(t, e, n) {
                return ya(t, {
                    serializationContext: n,
                    parentNodePrivacyLevel: e.defaultPrivacyLevel,
                    configuration: e
                })
            }

            function Ea(t) {
                return Boolean(t.changedTouches)
            }

            function Ca(t) {
                return !0 === t.composed && Rn(t.target) ? t.composedPath()[0] : t.target
            }
            var Ta = function(t, e) {
                    var n = window.visualViewport,
                        r = {
                            layoutViewportX: t,
                            layoutViewportY: e,
                            visualViewportX: t,
                            visualViewportY: e
                        };
                    return n ? (! function(t) {
                        return Math.abs(t.pageTop - t.offsetTop - window.scrollY) > 25 || Math.abs(t.pageLeft - t.offsetLeft - window.scrollX) > 25
                    }(n) ? (r.visualViewportX = Math.round(t - n.offsetLeft), r.visualViewportY = Math.round(e - n.offsetTop)) : (r.layoutViewportX = Math.round(t + n.offsetLeft), r.layoutViewportY = Math.round(e + n.offsetTop)), r) : r
                },
                ka = function(t) {
                    return {
                        scale: t.scale,
                        offsetLeft: t.offsetLeft,
                        offsetTop: t.offsetTop,
                        pageLeft: t.pageLeft,
                        pageTop: t.pageTop,
                        height: t.height,
                        width: t.width
                    }
                };

            function xa(t, e) {
                return {
                    data: E({
                        source: t
                    }, e),
                    type: Do,
                    timestamp: ft()
                }
            }
            var Aa;

            function Ra(t, e) {
                var n = N((function(t) {
                        var n = Ca(t);
                        if (To(n)) {
                            var r = Ia(t);
                            if (!r) return;
                            var i = {
                                id: ko(n),
                                timeOffset: 0,
                                x: r.x,
                                y: r.y
                            };
                            e(xa(Ea(t) ? ta : Yo, {
                                positions: [i]
                            }))
                        }
                    }), 50, {
                        trailing: !1
                    }),
                    r = n.throttled,
                    i = n.cancel,
                    o = ee(t, document, [Ft.MOUSE_MOVE, Ft.TOUCH_MOVE], r, {
                        capture: !0,
                        passive: !0
                    }).stop;
                return {
                    stop: function() {
                        o(), i()
                    }
                }
            }

            function Ia(t) {
                var e = Ea(t) ? t.changedTouches[0] : t,
                    n = e.clientX,
                    r = e.clientY;
                if (window.visualViewport) {
                    var i = Ta(n, r);
                    n = i.visualViewportX, r = i.visualViewportY
                }
                if (Number.isFinite(n) && Number.isFinite(r)) return {
                    x: n,
                    y: r
                };
                t.isTrusted && Xt("mouse/touch event without x/y")
            }
            var Oa = ((Aa = {})[Ft.POINTER_UP] = ra, Aa[Ft.MOUSE_DOWN] = ia, Aa[Ft.CLICK] = oa, Aa[Ft.CONTEXT_MENU] = aa, Aa[Ft.DBL_CLICK] = sa, Aa[Ft.FOCUS] = ua, Aa[Ft.BLUR] = ca, Aa[Ft.TOUCH_START] = la, Aa[Ft.TOUCH_END] = da, Aa);

            function Na(t, e, n) {
                return ee(t, document, Object.keys(Oa), (function(r) {
                    var i = Ca(r);
                    if (_o(i, t.defaultPrivacyLevel) !== ho.HIDDEN && To(i)) {
                        var o, a = ko(i),
                            s = Oa[r.type];
                        if (s !== ca && s !== ua) {
                            var u = Ia(r);
                            if (!u) return;
                            o = {
                                id: a,
                                type: s,
                                x: u.x,
                                y: u.y
                            }
                        } else o = {
                            id: a,
                            type: s
                        };
                        var c = E({
                            id: n.getIdForEvent(r)
                        }, xa(Xo, o));
                        e(c)
                    }
                }), {
                    capture: !0,
                    passive: !0
                })
            }

            function La(t, e, n, r) {
                void 0 === r && (r = document);
                var i = N((function(r) {
                        var i = Ca(r);
                        if (i && _o(i, t.defaultPrivacyLevel) !== ho.HIDDEN && To(i)) {
                            var o = ko(i),
                                a = i === document ? {
                                    scrollTop: xi(),
                                    scrollLeft: ki()
                                } : {
                                    scrollTop: Math.round(i.scrollTop),
                                    scrollLeft: Math.round(i.scrollLeft)
                                };
                            n.set(i, a), e(xa(Jo, {
                                id: o,
                                x: a.scrollLeft,
                                y: a.scrollTop
                            }))
                        }
                    }), 100),
                    o = i.throttled,
                    a = i.cancel,
                    s = te(t, r, Ft.SCROLL, o, {
                        capture: !0,
                        passive: !0
                    }).stop;
                return {
                    stop: function() {
                        s(), a()
                    }
                }
            }

            function Ma(t, e) {
                var n = Ai(t).subscribe((function(t) {
                    e(xa($o, t))
                }));
                return {
                    stop: function() {
                        n.unsubscribe()
                    }
                }
            }

            function Ua(t, e) {
                return ee(t, document, [Ft.PLAY, Ft.PAUSE], (function(n) {
                    var r = Ca(n);
                    r && _o(r, t.defaultPrivacyLevel) !== ho.HIDDEN && To(r) && e(xa(ea, {
                        id: ko(r),
                        type: n.type === Ft.PLAY ? fa : pa
                    }))
                }), {
                    capture: !0,
                    passive: !0
                })
            }

            function Da(t) {
                function e(t, e) {
                    t && To(t.ownerNode) && e(ko(t.ownerNode))
                }
                var n = [ar(CSSStyleSheet.prototype, "insertRule", (function(n) {
                    var r = n.target,
                        i = n.parameters,
                        o = i[0],
                        a = i[1];
                    e(r, (function(e) {
                        return t(xa(na, {
                            id: e,
                            adds: [{
                                rule: o,
                                index: a
                            }]
                        }))
                    }))
                })), ar(CSSStyleSheet.prototype, "deleteRule", (function(n) {
                    var r = n.target,
                        i = n.parameters[0];
                    e(r, (function(e) {
                        return t(xa(na, {
                            id: e,
                            removes: [{
                                index: i
                            }]
                        }))
                    }))
                }))];

                function r(r) {
                    n.push(ar(r.prototype, "insertRule", (function(n) {
                        var r = n.target,
                            i = n.parameters,
                            o = i[0],
                            a = i[1];
                        e(r.parentStyleSheet, (function(e) {
                            var n = Pa(r);
                            n && (n.push(a || 0), t(xa(na, {
                                id: e,
                                adds: [{
                                    rule: o,
                                    index: n
                                }]
                            })))
                        }))
                    })), ar(r.prototype, "deleteRule", (function(n) {
                        var r = n.target,
                            i = n.parameters[0];
                        e(r.parentStyleSheet, (function(e) {
                            var n = Pa(r);
                            n && (n.push(i), t(xa(na, {
                                id: e,
                                removes: [{
                                    index: n
                                }]
                            })))
                        }))
                    })))
                }
                return "undefined" !== typeof CSSGroupingRule ? r(CSSGroupingRule) : (r(CSSMediaRule), r(CSSSupportsRule)), {
                    stop: function() {
                        n.forEach((function(t) {
                            return t.stop()
                        }))
                    }
                }
            }

            function Pa(t) {
                for (var e = [], n = t; n.parentRule;) {
                    var r = Array.from(n.parentRule.cssRules).indexOf(n);
                    e.unshift(r), n = n.parentRule
                }
                if (n.parentStyleSheet) {
                    var i = Array.from(n.parentStyleSheet.cssRules).indexOf(n);
                    return e.unshift(i), e
                }
            }

            function za(t, e) {
                return ee(t, window, [Ft.FOCUS, Ft.BLUR], (function() {
                    e({
                        data: {
                            has_focus: document.hasFocus()
                        },
                        type: zo,
                        timestamp: ft()
                    })
                }))
            }

            function Ba(t, e, n) {
                var r = t.subscribe(12, (function(t) {
                    var r, i, o;
                    "action" === t.rawRumEvent.type && "click" === t.rawRumEvent.action.type && (null === (i = null === (r = t.rawRumEvent.action.frustration) || void 0 === r ? void 0 : r.type) || void 0 === i ? void 0 : i.length) && "events" in t.domainContext && (null === (o = t.domainContext.events) || void 0 === o ? void 0 : o.length) && e({
                        timestamp: t.rawRumEvent.date,
                        type: Vo,
                        data: {
                            frustrationTypes: t.rawRumEvent.action.frustration.type,
                            recordIds: t.domainContext.events.map((function(t) {
                                return n.getIdForEvent(t)
                            }))
                        }
                    })
                }));
                return {
                    stop: function() {
                        r.unsubscribe()
                    }
                }
            }

            function Fa(t, e) {
                var n = t.subscribe(5, (function() {
                    e({
                        timestamp: ft(),
                        type: Bo
                    })
                }));
                return {
                    stop: function() {
                        n.unsubscribe()
                    }
                }
            }

            function Va(t, e, n) {
                void 0 === n && (n = document);
                var r, i = t.defaultPrivacyLevel,
                    o = new WeakMap,
                    a = n !== document,
                    s = ee(t, n, a ? [Ft.CHANGE] : [Ft.INPUT, Ft.CHANGE], (function(t) {
                        var e = Ca(t);
                        (e instanceof HTMLInputElement || e instanceof HTMLTextAreaElement || e instanceof HTMLSelectElement) && c(e)
                    }), {
                        capture: !0,
                        passive: !0
                    }).stop;
                if (a) r = L;
                else {
                    var u = [sr(HTMLInputElement.prototype, "value", c), sr(HTMLInputElement.prototype, "checked", c), sr(HTMLSelectElement.prototype, "value", c), sr(HTMLTextAreaElement.prototype, "value", c), sr(HTMLSelectElement.prototype, "selectedIndex", c)];
                    r = function() {
                        u.forEach((function(t) {
                            return t.stop()
                        }))
                    }
                }
                return {
                    stop: function() {
                        r(), s()
                    }
                };

                function c(t) {
                    var e = _o(t, i);
                    if (e !== ho.HIDDEN) {
                        var n, r = t.type;
                        if ("radio" === r || "checkbox" === r) {
                            if (wo(t, e)) return;
                            n = {
                                isChecked: t.checked
                            }
                        } else {
                            var o = xo(t, e);
                            if (void 0 === o) return;
                            n = {
                                text: o
                            }
                        }
                        l(t, n);
                        var a, s, u = t.name;
                        "radio" === r && u && t.checked && (a = document.querySelectorAll('input[type="radio"][name="'.concat(wr(u), '"]')), s = function(e) {
                            e !== t && l(e, {
                                isChecked: !1
                            })
                        }, Array.prototype.forEach.call(a, s))
                    }
                }

                function l(t, n) {
                    if (To(t)) {
                        var r = o.get(t);
                        r && r.text === n.text && r.isChecked === n.isChecked || (o.set(t, n), e(xa(Qo, E({
                            id: ko(t)
                        }, n))))
                    }
                }
            }

            function Ha(t) {
                var e = L,
                    n = [];

                function r() {
                    e(), t(n), n = []
                }
                return {
                    addMutations: function(t) {
                        0 === n.length && (e = function(t, e) {
                            if (window.requestIdleCallback && window.cancelIdleCallback) {
                                var n = window.requestIdleCallback(v(t), e);
                                return function() {
                                    return window.cancelIdleCallback(n)
                                }
                            }
                            var r = window.requestAnimationFrame(v(t));
                            return function() {
                                return window.cancelAnimationFrame(r)
                            }
                        }(r, {
                            timeout: 100
                        })), n.push.apply(n, t)
                    },
                    flush: r,
                    stop: function() {
                        e()
                    }
                }
            }

            function qa(t, e, n, r) {
                var i = pn();
                if (!i) return {
                    stop: L,
                    flush: L
                };
                var o = Ha((function(r) {
                        ! function(t, e, n, r) {
                            var i = new Map;
                            t.filter((function(t) {
                                return "childList" === t.type
                            })).forEach((function(t) {
                                t.removedNodes.forEach((function(t) {
                                    ! function t(e, n) {
                                        Rn(e) && n(e.shadowRoot);
                                        On(e, (function(e) {
                                            return t(e, n)
                                        }))
                                    }(t, r.removeShadowRoot)
                                }))
                            }));
                            var o = t.filter((function(t) {
                                    return t.target.isConnected && function(t) {
                                        for (var e = t; e;) {
                                            if (!To(e) && !In(e)) return !1;
                                            e = Nn(e)
                                        }
                                        return !0
                                    }(t.target) && _o(t.target, n.defaultPrivacyLevel, i) !== ho.HIDDEN
                                })),
                                a = function(t, e, n, r) {
                                    for (var i = new Set, o = new Map, a = function(t) {
                                            t.addedNodes.forEach((function(t) {
                                                i.add(t)
                                            })), t.removedNodes.forEach((function(e) {
                                                i.has(e) || o.set(e, t.target), i.delete(e)
                                            }))
                                        }, s = 0, u = t; s < u.length; s++) {
                                        var c = u[s];
                                        a(c)
                                    }
                                    var l = Array.from(i);
                                    d = l, d.sort((function(t, e) {
                                        var n = t.compareDocumentPosition(e);
                                        return n & Node.DOCUMENT_POSITION_CONTAINED_BY ? -1 : n & Node.DOCUMENT_POSITION_CONTAINS || n & Node.DOCUMENT_POSITION_FOLLOWING ? 1 : n & Node.DOCUMENT_POSITION_PRECEDING ? -1 : 0
                                    }));
                                    var d;
                                    for (var f = new Set, p = [], v = 0, h = l; v < h.length; v++) {
                                        var m = h[v];
                                        if (!w(m)) {
                                            var g = _o(m.parentNode, e.defaultPrivacyLevel, r);
                                            if (g !== ho.HIDDEN && g !== ho.IGNORE) {
                                                var _ = ya(m, {
                                                    serializedNodeIds: f,
                                                    parentNodePrivacyLevel: g,
                                                    serializationContext: {
                                                        status: 2,
                                                        shadowRootsController: n
                                                    },
                                                    configuration: e
                                                });
                                                if (_) {
                                                    var y = Nn(m);
                                                    p.push({
                                                        nextId: S(m),
                                                        parentId: ko(y),
                                                        node: _
                                                    })
                                                }
                                            }
                                        }
                                    }
                                    var b = [];
                                    return o.forEach((function(t, e) {
                                        To(e) && b.push({
                                            parentId: ko(t),
                                            id: ko(e)
                                        })
                                    })), {
                                        adds: p,
                                        removes: b,
                                        hasBeenSerialized: w
                                    };

                                    function w(t) {
                                        return To(t) && f.has(ko(t))
                                    }

                                    function S(t) {
                                        for (var e = t.nextSibling; e;) {
                                            if (To(e)) return ko(e);
                                            e = e.nextSibling
                                        }
                                        return null
                                    }
                                }(o.filter((function(t) {
                                    return "childList" === t.type
                                })), n, r, i),
                                s = a.adds,
                                u = a.removes,
                                c = a.hasBeenSerialized,
                                l = function(t, e, n) {
                                    for (var r, i = [], o = new Set, a = t.filter((function(t) {
                                            return !o.has(t.target) && (o.add(t.target), !0)
                                        })), s = 0, u = a; s < u.length; s++) {
                                        var c = u[s];
                                        if (c.target.textContent !== c.oldValue) {
                                            var l = _o(Nn(c.target), e.defaultPrivacyLevel, n);
                                            l !== ho.HIDDEN && l !== ho.IGNORE && i.push({
                                                id: ko(c.target),
                                                value: null !== (r = Eo(c.target, !1, l)) && void 0 !== r ? r : null
                                            })
                                        }
                                    }
                                    return i
                                }(o.filter((function(t) {
                                    return "characterData" === t.type && !c(t.target)
                                })), n, i),
                                d = function(t, e, n) {
                                    for (var r = [], i = new Map, o = t.filter((function(t) {
                                            var e = i.get(t.target);
                                            return !(null === e || void 0 === e ? void 0 : e.has(t.attributeName)) && (e ? e.add(t.attributeName) : i.set(t.target, new Set([t.attributeName])), !0)
                                        })), a = new Map, s = 0, u = o; s < u.length; s++) {
                                        var c = u[s];
                                        if (c.target.getAttribute(c.attributeName) !== c.oldValue) {
                                            var l = _o(c.target, e.defaultPrivacyLevel, n),
                                                d = ha(c.target, l, c.attributeName, e),
                                                f = void 0;
                                            if ("value" === c.attributeName) {
                                                var p = xo(c.target, l);
                                                if (void 0 === p) continue;
                                                f = p
                                            } else f = "string" === typeof d ? d : null;
                                            var v = a.get(c.target);
                                            v || (v = {
                                                id: ko(c.target),
                                                attributes: {}
                                            }, r.push(v), a.set(c.target, v)), v.attributes[c.attributeName] = f
                                        }
                                    }
                                    return r
                                }(o.filter((function(t) {
                                    return "attributes" === t.type && !c(t.target)
                                })), n, i);
                            if (!l.length && !d.length && !u.length && !s.length) return;
                            e(xa(Wo, {
                                adds: s,
                                removes: u,
                                texts: l,
                                attributes: d
                            }))
                        }(r.concat(a.takeRecords()), t, e, n)
                    })),
                    a = new i(v(o.addMutations));
                return a.observe(r, {
                    attributeOldValue: !0,
                    attributes: !0,
                    characterData: !0,
                    characterDataOldValue: !0,
                    childList: !0,
                    subtree: !0
                }), {
                    stop: function() {
                        a.disconnect(), o.stop()
                    },
                    flush: function() {
                        o.flush()
                    }
                }
            }

            function Ga(t) {
                var e = t.emit,
                    n = t.configuration,
                    r = t.lifeCycle;
                if (!e) throw new Error("emit function is required");
                var i = function(n) {
                        e(n), Pt("record", {
                            record: n
                        });
                        var r = t.viewContexts.findView();
                        vo(r.id).records_count += 1
                    },
                    o = function() {
                        var t = new WeakMap;
                        return {
                            set: function(e, n) {
                                (e !== document || document.scrollingElement) && t.set(e === document ? document.scrollingElement : e, n)
                            },
                            get: function(e) {
                                return t.get(e)
                            },
                            has: function(e) {
                                return t.has(e)
                            }
                        }
                    }(),
                    a = function(t, e, n) {
                        var r = new Map,
                            i = {
                                addShadowRoot: function(o) {
                                    if (!r.has(o)) {
                                        var a = qa(e, t, i, o),
                                            s = Va(t, e, o),
                                            u = La(t, e, n, o);
                                        r.set(o, {
                                            flush: function() {
                                                return a.flush()
                                            },
                                            stop: function() {
                                                a.stop(), s.stop(), u.stop()
                                            }
                                        })
                                    }
                                },
                                removeShadowRoot: function(t) {
                                    var e = r.get(t);
                                    e && (e.stop(), r.delete(t))
                                },
                                stop: function() {
                                    r.forEach((function(t) {
                                        return (0, t.stop)()
                                    }))
                                },
                                flush: function() {
                                    r.forEach((function(t) {
                                        return (0, t.flush)()
                                    }))
                                }
                            };
                        return i
                    }(n, i, o),
                    s = function(t, e, n, r, i, o) {
                        var a = function(n, i) {
                            void 0 === n && (n = ft()), void 0 === i && (i = {
                                status: 0,
                                elementsScrollPositions: t,
                                shadowRootsController: e
                            });
                            var o = Ri(),
                                a = o.width,
                                s = [{
                                    data: {
                                        height: o.height,
                                        href: window.location.href,
                                        width: a
                                    },
                                    type: Po,
                                    timestamp: n
                                }, {
                                    data: {
                                        has_focus: document.hasFocus()
                                    },
                                    type: zo,
                                    timestamp: n
                                }, {
                                    data: {
                                        node: Sa(document, r, i),
                                        initialOffset: {
                                            left: ki(),
                                            top: xi()
                                        }
                                    },
                                    type: Uo,
                                    timestamp: n
                                }];
                            return window.visualViewport && s.push({
                                data: ka(window.visualViewport),
                                type: Fo,
                                timestamp: n
                            }), s
                        };
                        return o(a()), {
                            stop: n.subscribe(3, (function(n) {
                                i(), o(a(n.startClocks.timeStamp, {
                                    shadowRootsController: e,
                                    status: 1,
                                    elementsScrollPositions: t
                                }))
                            })).unsubscribe
                        }
                    }(o, a, r, n, u, (function(t) {
                        return t.forEach((function(t) {
                            return i(t)
                        }))
                    })).stop;

                function u() {
                    a.flush(), l.flush()
                }
                var c = function() {
                        var t = new WeakMap,
                            e = 1;
                        return {
                            getIdForEvent: function(n) {
                                return t.has(n) || t.set(n, e++), t.get(n)
                            }
                        }
                    }(),
                    l = qa(i, n, a, document),
                    d = [l, Ra(n, i), Na(n, i, c), La(n, i, o, document), Ma(n, i), Va(n, i), Ua(n, i), Da(i), za(n, i), Ma(n, i), Ba(r, i, c), Fa(r, (function(t) {
                        u(), i(t)
                    }))];
                return {
                    stop: function() {
                        a.stop(), d.forEach((function(t) {
                            return t.stop()
                        })), s()
                    },
                    flushMutations: u,
                    shadowRootsController: a
                }
            }
            var ja = function() {
                    function t(t, e, n) {
                        this.encoder = t, this.encodedBytesCount = 0;
                        var r = e.view.id;
                        this.metadata = E({
                                start: 1 / 0,
                                end: -1 / 0,
                                creation_reason: n,
                                records_count: 0,
                                has_full_snapshot: !1,
                                index_in_view: po(r),
                                source: "browser"
                            }, e),
                            function(t) {
                                vo(t).segments_count += 1
                            }(r)
                    }
                    return t.prototype.addRecord = function(t, e) {
                        var n, r = this;
                        this.metadata.start = Math.min(this.metadata.start, t.timestamp), this.metadata.end = Math.max(this.metadata.end, t.timestamp), this.metadata.records_count += 1, (n = this.metadata).has_full_snapshot || (n.has_full_snapshot = t.type === Uo);
                        var i = this.encoder.isEmpty ? '{"records":[' : ",";
                        this.encoder.write(i + JSON.stringify(t), (function(t) {
                            r.encodedBytesCount += t, e(r.encodedBytesCount)
                        }))
                    }, t.prototype.flush = function(t) {
                        var e = this;
                        if (this.encoder.isEmpty) throw new Error("Empty segment flushed");
                        this.encoder.write("],".concat(JSON.stringify(this.metadata).slice(1), "\n")), this.encoder.finish((function(n) {
                            var r, i;
                            r = e.metadata.view.id, i = n.rawBytesCount, vo(r).segments_total_raw_size += i, t(e.metadata, n)
                        }))
                    }, t
                }(),
                Za = 6e4;

            function Ka(t, e, n, r, i, o) {
                return function(t, e, n, r) {
                    var i = {
                            status: 0,
                            nextSegmentCreationReason: "init"
                        },
                        o = t.subscribe(3, (function() {
                            s("view_change")
                        })).unsubscribe,
                        a = t.subscribe(11, (function(t) {
                            s(t.reason)
                        })).unsubscribe;

                    function s(t) {
                        1 === i.status && (i.segment.flush((function(e, r) {
                            var i = function(t, e, n) {
                                var r = new FormData;
                                r.append("segment", new Blob([t], {
                                    type: "application/octet-stream"
                                }), "".concat(e.session.id, "-").concat(e.start));
                                var i = E({
                                        raw_segment_size: n,
                                        compressed_segment_size: t.byteLength
                                    }, e),
                                    o = JSON.stringify(i);
                                return r.append("event", new Blob([o], {
                                    type: "application/json"
                                })), {
                                    data: r,
                                    bytesCount: t.byteLength
                                }
                            }(r.output, e, r.rawBytesCount);
                            fn(t) ? n.sendOnExit(i) : n.send(i)
                        })), R(i.expirationTimeoutId)), i = "stop" !== t ? {
                            status: 0,
                            nextSegmentCreationReason: t
                        } : {
                            status: 2
                        }
                    }
                    return {
                        addRecord: function(t) {
                            if (2 !== i.status) {
                                if (0 === i.status) {
                                    var n = e();
                                    if (!n) return;
                                    i = {
                                        status: 1,
                                        segment: new ja(r, n, i.nextSegmentCreationReason),
                                        expirationTimeoutId: A((function() {
                                            s("segment_duration_limit")
                                        }), 3e4)
                                    }
                                }
                                i.segment.addRecord(t, (function(t) {
                                    t > Za && s("segment_bytes_limit")
                                }))
                            }
                        },
                        stop: function() {
                            s("stop"), o(), a()
                        }
                    }
                }(t, (function() {
                    return function(t, e, n) {
                        var r = e.findTrackedSession(),
                            i = n.findView();
                        if (!r || !i) return;
                        return {
                            application: {
                                id: t
                            },
                            session: {
                                id: r.id
                            },
                            view: {
                                id: i.id
                            }
                        }
                    }(e.applicationId, n, r)
                }), i, o)
            }

            function Wa() {
                return "function" === typeof Array.from && "function" === typeof CSSSupportsRule && "function" === typeof URL.createObjectURL && "forEach" in NodeList.prototype
            }

            function Ya(t, e, n, r) {
                var i = e.findTrackedSession(),
                    o = function(t, e) {
                        if (!Wa()) return "browser-not-supported";
                        if (!t) return "rum-not-tracked";
                        if (0 === t.sessionReplay) return "incorrect-session-plan";
                        if (!e) return "replay-not-started"
                    }(i, r);
                return lo(t, {
                    viewContext: n.findView(),
                    errorType: o,
                    session: i
                })
            }

            function Xa(t, e, n) {
                var r, i = 0,
                    o = [],
                    a = 0,
                    s = [],
                    u = te(t, e, "message", (function(t) {
                        var e = t.data;
                        if ("wrote" === e.type && e.streamId === n) {
                            i += e.additionalBytesCount, o.push(e.result), r = e.trailer;
                            var a = s.shift();
                            a && a.id === e.id ? a.writeCallback ? a.writeCallback(e.result.byteLength) : a.finishCallback && a.finishCallback() : (u(), Xt("Worker responses received out of order."))
                        }
                    })).stop;

                function c() {
                    var t = 0 === o.length ? new Uint8Array(0) : function(t) {
                            for (var e = t.reduce((function(t, e) {
                                    return t + e.length
                                }), 0), n = new Uint8Array(e), r = 0, i = 0, o = t; i < o.length; i++) {
                                var a = o[i];
                                n.set(a, r), r += a.length
                            }
                            return n
                        }(o.concat(r)),
                        e = {
                            rawBytesCount: i,
                            output: t,
                            outputBytesCount: t.byteLength,
                            encoding: "deflate"
                        };
                    return i = 0, o = [], e
                }

                function l() {
                    a > 0 && (e.postMessage({
                        action: "reset",
                        streamId: n
                    }), a = 0)
                }
                return {
                    isAsync: !0,
                    get isEmpty() {
                        return 0 === a
                    },
                    write: function(t, r) {
                        e.postMessage({
                            action: "write",
                            id: a,
                            data: t,
                            streamId: n
                        }), s.push({
                            id: a,
                            writeCallback: r,
                            data: t
                        }), a += 1
                    },
                    finish: function(t) {
                        l(), s.length ? (s.forEach((function(t) {
                            delete t.writeCallback
                        })), s[s.length - 1].finishCallback = function() {
                            return t(c())
                        }) : t(c())
                    },
                    finishSync: function() {
                        l();
                        var t = s.map((function(t) {
                            return delete t.writeCallback, delete t.finishCallback, t.data
                        })).join("");
                        return E(c(), {
                            pendingData: t
                        })
                    },
                    estimateEncodedBytesCount: function(t) {
                        return t.length / 8
                    },
                    stop: function() {
                        u()
                    }
                }
            }

            function Ja(t) {
                return new Worker(t.workerUrl || URL.createObjectURL(new Blob(['!function(){"use strict";function t(t){for(var e=t.reduce((function(t,e){return t+e.length}),0),a=new Uint8Array(e),n=0,r=0,i=t;r<i.length;r++){var s=i[r];a.set(s,n),n+=s.length}return a}function e(t){for(var e=t.length;--e>=0;)t[e]=0}var a=256,n=286,r=30,i=15,s=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),_=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),h=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),l=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),o=new Array(576);e(o);var d=new Array(60);e(d);var u=new Array(512);e(u);var f=new Array(256);e(f);var c=new Array(29);e(c);var p,g,w,v=new Array(r);function b(t,e,a,n,r){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=n,this.max_length=r,this.has_stree=t&&t.length}function m(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}e(v);var y=function(t){return t<256?u[t]:u[256+(t>>>7)]},k=function(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},z=function(t,e,a){t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,k(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)},x=function(t,e,a){z(t,a[2*e],a[2*e+1])},A=function(t,e){var a=0;do{a|=1&t,t>>>=1,a<<=1}while(--e>0);return a>>>1},E=function(t,e,a){var n,r,s=new Array(16),_=0;for(n=1;n<=i;n++)s[n]=_=_+a[n-1]<<1;for(r=0;r<=e;r++){var h=t[2*r+1];0!==h&&(t[2*r]=A(s[h]++,h))}},Z=function(t){var e;for(e=0;e<n;e++)t.dyn_ltree[2*e]=0;for(e=0;e<r;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0},U=function(t){t.bi_valid>8?k(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},S=function(t,e,a,n){var r=2*e,i=2*a;return t[r]<t[i]||t[r]===t[i]&&n[e]<=n[a]},R=function(t,e,a){for(var n=t.heap[a],r=a<<1;r<=t.heap_len&&(r<t.heap_len&&S(e,t.heap[r+1],t.heap[r],t.depth)&&r++,!S(e,n,t.heap[r],t.depth));)t.heap[a]=t.heap[r],a=r,r<<=1;t.heap[a]=n},L=function(t,e,n){var r,i,h,l,o=0;if(0!==t.last_lit)do{r=t.pending_buf[t.d_buf+2*o]<<8|t.pending_buf[t.d_buf+2*o+1],i=t.pending_buf[t.l_buf+o],o++,0===r?x(t,i,e):(h=f[i],x(t,h+a+1,e),0!==(l=s[h])&&(i-=c[h],z(t,i,l)),r--,h=y(r),x(t,h,n),0!==(l=_[h])&&(r-=v[h],z(t,r,l)))}while(o<t.last_lit);x(t,256,e)},F=function(t,e){var a,n,r,s=e.dyn_tree,_=e.stat_desc.static_tree,h=e.stat_desc.has_stree,l=e.stat_desc.elems,o=-1;for(t.heap_len=0,t.heap_max=573,a=0;a<l;a++)0!==s[2*a]?(t.heap[++t.heap_len]=o=a,t.depth[a]=0):s[2*a+1]=0;for(;t.heap_len<2;)s[2*(r=t.heap[++t.heap_len]=o<2?++o:0)]=1,t.depth[r]=0,t.opt_len--,h&&(t.static_len-=_[2*r+1]);for(e.max_code=o,a=t.heap_len>>1;a>=1;a--)R(t,s,a);r=l;do{a=t.heap[1],t.heap[1]=t.heap[t.heap_len--],R(t,s,1),n=t.heap[1],t.heap[--t.heap_max]=a,t.heap[--t.heap_max]=n,s[2*r]=s[2*a]+s[2*n],t.depth[r]=(t.depth[a]>=t.depth[n]?t.depth[a]:t.depth[n])+1,s[2*a+1]=s[2*n+1]=r,t.heap[1]=r++,R(t,s,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var a,n,r,s,_,h,l=e.dyn_tree,o=e.max_code,d=e.stat_desc.static_tree,u=e.stat_desc.has_stree,f=e.stat_desc.extra_bits,c=e.stat_desc.extra_base,p=e.stat_desc.max_length,g=0;for(s=0;s<=i;s++)t.bl_count[s]=0;for(l[2*t.heap[t.heap_max]+1]=0,a=t.heap_max+1;a<573;a++)(s=l[2*l[2*(n=t.heap[a])+1]+1]+1)>p&&(s=p,g++),l[2*n+1]=s,n>o||(t.bl_count[s]++,_=0,n>=c&&(_=f[n-c]),h=l[2*n],t.opt_len+=h*(s+_),u&&(t.static_len+=h*(d[2*n+1]+_)));if(0!==g){do{for(s=p-1;0===t.bl_count[s];)s--;t.bl_count[s]--,t.bl_count[s+1]+=2,t.bl_count[p]--,g-=2}while(g>0);for(s=p;0!==s;s--)for(n=t.bl_count[s];0!==n;)(r=t.heap[--a])>o||(l[2*r+1]!==s&&(t.opt_len+=(s-l[2*r+1])*l[2*r],l[2*r+1]=s),n--)}}(t,e),E(s,o,t.bl_count)},T=function(t,e,a){var n,r,i=-1,s=e[1],_=0,h=7,l=4;for(0===s&&(h=138,l=3),e[2*(a+1)+1]=65535,n=0;n<=a;n++)r=s,s=e[2*(n+1)+1],++_<h&&r===s||(_<l?t.bl_tree[2*r]+=_:0!==r?(r!==i&&t.bl_tree[2*r]++,t.bl_tree[32]++):_<=10?t.bl_tree[34]++:t.bl_tree[36]++,_=0,i=r,0===s?(h=138,l=3):r===s?(h=6,l=3):(h=7,l=4))},I=function(t,e,a){var n,r,i=-1,s=e[1],_=0,h=7,l=4;for(0===s&&(h=138,l=3),n=0;n<=a;n++)if(r=s,s=e[2*(n+1)+1],!(++_<h&&r===s)){if(_<l)do{x(t,r,t.bl_tree)}while(0!=--_);else 0!==r?(r!==i&&(x(t,r,t.bl_tree),_--),x(t,16,t.bl_tree),z(t,_-3,2)):_<=10?(x(t,17,t.bl_tree),z(t,_-3,3)):(x(t,18,t.bl_tree),z(t,_-11,7));_=0,i=r,0===s?(h=138,l=3):r===s?(h=6,l=3):(h=7,l=4)}},N=!1,O=function(t,e,a,n){z(t,0+(n?1:0),3),function(t,e,a,n){U(t),n&&(k(t,a),k(t,~a)),t.pending_buf.set(t.window.subarray(e,e+a),t.pending),t.pending+=a}(t,e,a,!0)},D=function(t,e,n,r){var i,s,_=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,n=4093624447;for(e=0;e<=31;e++,n>>>=1)if(1&n&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<a;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),F(t,t.l_desc),F(t,t.d_desc),_=function(t){var e;for(T(t,t.dyn_ltree,t.l_desc.max_code),T(t,t.dyn_dtree,t.d_desc.max_code),F(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*l[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=i&&(i=s)):i=s=n+5,n+4<=i&&-1!==e?O(t,e,n,r):4===t.strategy||s===i?(z(t,2+(r?1:0),3),L(t,o,d)):(z(t,4+(r?1:0),3),function(t,e,a,n){var r;for(z(t,e-257,5),z(t,a-1,5),z(t,n-4,4),r=0;r<n;r++)z(t,t.bl_tree[2*l[r]+1],3);I(t,t.dyn_ltree,e-1),I(t,t.dyn_dtree,a-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,_+1),L(t,t.dyn_ltree,t.dyn_dtree)),Z(t),r&&U(t)},B={_tr_init:function(t){N||(!function(){var t,e,a,l,m,y=new Array(16);for(a=0,l=0;l<28;l++)for(c[l]=a,t=0;t<1<<s[l];t++)f[a++]=l;for(f[a-1]=l,m=0,l=0;l<16;l++)for(v[l]=m,t=0;t<1<<_[l];t++)u[m++]=l;for(m>>=7;l<r;l++)for(v[l]=m<<7,t=0;t<1<<_[l]-7;t++)u[256+m++]=l;for(e=0;e<=i;e++)y[e]=0;for(t=0;t<=143;)o[2*t+1]=8,t++,y[8]++;for(;t<=255;)o[2*t+1]=9,t++,y[9]++;for(;t<=279;)o[2*t+1]=7,t++,y[7]++;for(;t<=287;)o[2*t+1]=8,t++,y[8]++;for(E(o,287,y),t=0;t<r;t++)d[2*t+1]=5,d[2*t]=A(t,5);p=new b(o,s,257,n,i),g=new b(d,_,0,r,i),w=new b(new Array(0),h,0,19,7)}(),N=!0),t.l_desc=new m(t.dyn_ltree,p),t.d_desc=new m(t.dyn_dtree,g),t.bl_desc=new m(t.bl_tree,w),t.bi_buf=0,t.bi_valid=0,Z(t)},_tr_stored_block:O,_tr_flush_block:D,_tr_tally:function(t,e,n){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&n,t.last_lit++,0===e?t.dyn_ltree[2*n]++:(t.matches++,e--,t.dyn_ltree[2*(f[n]+a+1)]++,t.dyn_dtree[2*y(e)]++),t.last_lit===t.lit_bufsize-1},_tr_align:function(t){z(t,2,3),x(t,256,o),function(t){16===t.bi_valid?(k(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)}},C=function(t,e,a,n){for(var r=65535&t|0,i=t>>>16&65535|0,s=0;0!==a;){a-=s=a>2e3?2e3:a;do{i=i+(r=r+e[n++]|0)|0}while(--s);r%=65521,i%=65521}return r|i<<16|0},H=new Uint32Array(function(){for(var t,e=[],a=0;a<256;a++){t=a;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e}()),M=function(t,e,a,n){var r=H,i=n+a;t^=-1;for(var s=n;s<i;s++)t=t>>>8^r[255&(t^e[s])];return-1^t},Y={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},K={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},P=B._tr_init,j=B._tr_stored_block,G=B._tr_flush_block,X=B._tr_tally,W=B._tr_align,q=K.Z_NO_FLUSH,J=K.Z_PARTIAL_FLUSH,Q=K.Z_FULL_FLUSH,V=K.Z_FINISH,$=K.Z_BLOCK,tt=K.Z_OK,et=K.Z_STREAM_END,at=K.Z_STREAM_ERROR,nt=K.Z_DATA_ERROR,rt=K.Z_BUF_ERROR,it=K.Z_DEFAULT_COMPRESSION,st=K.Z_FILTERED,_t=K.Z_HUFFMAN_ONLY,ht=K.Z_RLE,lt=K.Z_FIXED,ot=K.Z_DEFAULT_STRATEGY,dt=K.Z_UNKNOWN,ut=K.Z_DEFLATED,ft=258,ct=262,pt=103,gt=113,wt=666,vt=function(t,e){return t.msg=Y[e],e},bt=function(t){return(t<<1)-(t>4?9:0)},mt=function(t){for(var e=t.length;--e>=0;)t[e]=0},yt=function(t,e,a){return(e<<t.hash_shift^a)&t.hash_mask},kt=function(t){var e=t.state,a=e.pending;a>t.avail_out&&(a=t.avail_out),0!==a&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+a),t.next_out),t.next_out+=a,e.pending_out+=a,t.total_out+=a,t.avail_out-=a,e.pending-=a,0===e.pending&&(e.pending_out=0))},zt=function(t,e){G(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,kt(t.strm)},xt=function(t,e){t.pending_buf[t.pending++]=e},At=function(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},Et=function(t,e){var a,n,r=t.max_chain_length,i=t.strstart,s=t.prev_length,_=t.nice_match,h=t.strstart>t.w_size-ct?t.strstart-(t.w_size-ct):0,l=t.window,o=t.w_mask,d=t.prev,u=t.strstart+ft,f=l[i+s-1],c=l[i+s];t.prev_length>=t.good_match&&(r>>=2),_>t.lookahead&&(_=t.lookahead);do{if(l[(a=e)+s]===c&&l[a+s-1]===f&&l[a]===l[i]&&l[++a]===l[i+1]){i+=2,a++;do{}while(l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&l[++i]===l[++a]&&i<u);if(n=ft-(u-i),i=u-ft,n>s){if(t.match_start=e,s=n,n>=_)break;f=l[i+s-1],c=l[i+s]}}}while((e=d[e&o])>h&&0!=--r);return s<=t.lookahead?s:t.lookahead},Zt=function(t){var e,a,n,r,i,s,_,h,l,o,d=t.w_size;do{if(r=t.window_size-t.lookahead-t.strstart,t.strstart>=d+(d-ct)){t.window.set(t.window.subarray(d,d+d),0),t.match_start-=d,t.strstart-=d,t.block_start-=d,e=a=t.hash_size;do{n=t.head[--e],t.head[e]=n>=d?n-d:0}while(--a);e=a=d;do{n=t.prev[--e],t.prev[e]=n>=d?n-d:0}while(--a);r+=d}if(0===t.strm.avail_in)break;if(s=t.strm,_=t.window,h=t.strstart+t.lookahead,l=r,o=void 0,(o=s.avail_in)>l&&(o=l),a=0===o?0:(s.avail_in-=o,_.set(s.input.subarray(s.next_in,s.next_in+o),h),1===s.state.wrap?s.adler=C(s.adler,_,o,h):2===s.state.wrap&&(s.adler=M(s.adler,_,o,h)),s.next_in+=o,s.total_in+=o,o),t.lookahead+=a,t.lookahead+t.insert>=3)for(i=t.strstart-t.insert,t.ins_h=t.window[i],t.ins_h=yt(t,t.ins_h,t.window[i+1]);t.insert&&(t.ins_h=yt(t,t.ins_h,t.window[i+3-1]),t.prev[i&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=i,i++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<ct&&0!==t.strm.avail_in)},Ut=function(t,e){for(var a,n;;){if(t.lookahead<ct){if(Zt(t),t.lookahead<ct&&e===q)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=yt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==a&&t.strstart-a<=t.w_size-ct&&(t.match_length=Et(t,a)),t.match_length>=3)if(n=X(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=yt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=yt(t,t.ins_h,t.window[t.strstart+1]);else n=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(zt(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,e===V?(zt(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(zt(t,!1),0===t.strm.avail_out)?1:2},St=function(t,e){for(var a,n,r;;){if(t.lookahead<ct){if(Zt(t),t.lookahead<ct&&e===q)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=yt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==a&&t.prev_length<t.max_lazy_match&&t.strstart-a<=t.w_size-ct&&(t.match_length=Et(t,a),t.match_length<=5&&(t.strategy===st||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){r=t.strstart+t.lookahead-3,n=X(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=r&&(t.ins_h=yt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,n&&(zt(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((n=X(t,0,t.window[t.strstart-1]))&&zt(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=X(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,e===V?(zt(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(zt(t,!1),0===t.strm.avail_out)?1:2};function Rt(t,e,a,n,r){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=n,this.func=r}var Lt=[new Rt(0,0,0,0,(function(t,e){var a=65535;for(a>t.pending_buf_size-5&&(a=t.pending_buf_size-5);;){if(t.lookahead<=1){if(Zt(t),0===t.lookahead&&e===q)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+a;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,zt(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-ct&&(zt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(zt(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(zt(t,!1),t.strm.avail_out),1)})),new Rt(4,4,8,4,Ut),new Rt(4,5,16,8,Ut),new Rt(4,6,32,32,Ut),new Rt(4,4,16,16,St),new Rt(8,16,32,32,St),new Rt(8,16,128,128,St),new Rt(8,32,128,256,St),new Rt(32,128,258,1024,St),new Rt(32,258,258,4096,St)];function Ft(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=ut,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),mt(this.dyn_ltree),mt(this.dyn_dtree),mt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),mt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),mt(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var Tt=function(t){if(!t||!t.state)return vt(t,at);t.total_in=t.total_out=0,t.data_type=dt;var e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:gt,t.adler=2===e.wrap?0:1,e.last_flush=q,P(e),tt},It=function(t){var e,a=Tt(t);return a===tt&&((e=t.state).window_size=2*e.w_size,mt(e.head),e.max_lazy_match=Lt[e.level].max_lazy,e.good_match=Lt[e.level].good_length,e.nice_match=Lt[e.level].nice_length,e.max_chain_length=Lt[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),a},Nt=function(t,e,a,n,r,i){if(!t)return at;var s=1;if(e===it&&(e=6),n<0?(s=0,n=-n):n>15&&(s=2,n-=16),r<1||r>9||a!==ut||n<8||n>15||e<0||e>9||i<0||i>lt)return vt(t,at);8===n&&(n=9);var _=new Ft;return t.state=_,_.strm=t,_.wrap=s,_.gzhead=null,_.w_bits=n,_.w_size=1<<_.w_bits,_.w_mask=_.w_size-1,_.hash_bits=r+7,_.hash_size=1<<_.hash_bits,_.hash_mask=_.hash_size-1,_.hash_shift=~~((_.hash_bits+3-1)/3),_.window=new Uint8Array(2*_.w_size),_.head=new Uint16Array(_.hash_size),_.prev=new Uint16Array(_.w_size),_.lit_bufsize=1<<r+6,_.pending_buf_size=4*_.lit_bufsize,_.pending_buf=new Uint8Array(_.pending_buf_size),_.d_buf=1*_.lit_bufsize,_.l_buf=3*_.lit_bufsize,_.level=e,_.strategy=i,_.method=a,It(t)},Ot={deflateInit:function(t,e){return Nt(t,e,ut,15,8,ot)},deflateInit2:Nt,deflateReset:It,deflateResetKeep:Tt,deflateSetHeader:function(t,e){return t&&t.state?2!==t.state.wrap?at:(t.state.gzhead=e,tt):at},deflate:function(t,e){var a,n;if(!t||!t.state||e>$||e<0)return t?vt(t,at):at;var r=t.state;if(!t.output||!t.input&&0!==t.avail_in||r.status===wt&&e!==V)return vt(t,0===t.avail_out?rt:at);r.strm=t;var i=r.last_flush;if(r.last_flush=e,42===r.status)if(2===r.wrap)t.adler=0,xt(r,31),xt(r,139),xt(r,8),r.gzhead?(xt(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),xt(r,255&r.gzhead.time),xt(r,r.gzhead.time>>8&255),xt(r,r.gzhead.time>>16&255),xt(r,r.gzhead.time>>24&255),xt(r,9===r.level?2:r.strategy>=_t||r.level<2?4:0),xt(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(xt(r,255&r.gzhead.extra.length),xt(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(t.adler=M(t.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(xt(r,0),xt(r,0),xt(r,0),xt(r,0),xt(r,0),xt(r,9===r.level?2:r.strategy>=_t||r.level<2?4:0),xt(r,3),r.status=gt);else{var s=ut+(r.w_bits-8<<4)<<8;s|=(r.strategy>=_t||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(s|=32),s+=31-s%31,r.status=gt,At(r,s),0!==r.strstart&&(At(r,t.adler>>>16),At(r,65535&t.adler)),t.adler=1}if(69===r.status)if(r.gzhead.extra){for(a=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>a&&(t.adler=M(t.adler,r.pending_buf,r.pending-a,a)),kt(t),a=r.pending,r.pending!==r.pending_buf_size));)xt(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>a&&(t.adler=M(t.adler,r.pending_buf,r.pending-a,a)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(t.adler=M(t.adler,r.pending_buf,r.pending-a,a)),kt(t),a=r.pending,r.pending===r.pending_buf_size)){n=1;break}n=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,xt(r,n)}while(0!==n);r.gzhead.hcrc&&r.pending>a&&(t.adler=M(t.adler,r.pending_buf,r.pending-a,a)),0===n&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(t.adler=M(t.adler,r.pending_buf,r.pending-a,a)),kt(t),a=r.pending,r.pending===r.pending_buf_size)){n=1;break}n=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,xt(r,n)}while(0!==n);r.gzhead.hcrc&&r.pending>a&&(t.adler=M(t.adler,r.pending_buf,r.pending-a,a)),0===n&&(r.status=pt)}else r.status=pt;if(r.status===pt&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&kt(t),r.pending+2<=r.pending_buf_size&&(xt(r,255&t.adler),xt(r,t.adler>>8&255),t.adler=0,r.status=gt)):r.status=gt),0!==r.pending){if(kt(t),0===t.avail_out)return r.last_flush=-1,tt}else if(0===t.avail_in&&bt(e)<=bt(i)&&e!==V)return vt(t,rt);if(r.status===wt&&0!==t.avail_in)return vt(t,rt);if(0!==t.avail_in||0!==r.lookahead||e!==q&&r.status!==wt){var _=r.strategy===_t?function(t,e){for(var a;;){if(0===t.lookahead&&(Zt(t),0===t.lookahead)){if(e===q)return 1;break}if(t.match_length=0,a=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,a&&(zt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(zt(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(zt(t,!1),0===t.strm.avail_out)?1:2}(r,e):r.strategy===ht?function(t,e){for(var a,n,r,i,s=t.window;;){if(t.lookahead<=ft){if(Zt(t),t.lookahead<=ft&&e===q)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(n=s[r=t.strstart-1])===s[++r]&&n===s[++r]&&n===s[++r]){i=t.strstart+ft;do{}while(n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&r<i);t.match_length=ft-(i-r),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(a=X(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(a=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),a&&(zt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(zt(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(zt(t,!1),0===t.strm.avail_out)?1:2}(r,e):Lt[r.level].func(r,e);if(3!==_&&4!==_||(r.status=wt),1===_||3===_)return 0===t.avail_out&&(r.last_flush=-1),tt;if(2===_&&(e===J?W(r):e!==$&&(j(r,0,0,!1),e===Q&&(mt(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),kt(t),0===t.avail_out))return r.last_flush=-1,tt}return e!==V?tt:r.wrap<=0?et:(2===r.wrap?(xt(r,255&t.adler),xt(r,t.adler>>8&255),xt(r,t.adler>>16&255),xt(r,t.adler>>24&255),xt(r,255&t.total_in),xt(r,t.total_in>>8&255),xt(r,t.total_in>>16&255),xt(r,t.total_in>>24&255)):(At(r,t.adler>>>16),At(r,65535&t.adler)),kt(t),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?tt:et)},deflateEnd:function(t){if(!t||!t.state)return at;var e=t.state.status;return 42!==e&&69!==e&&73!==e&&91!==e&&e!==pt&&e!==gt&&e!==wt?vt(t,at):(t.state=null,e===gt?vt(t,nt):tt)},deflateSetDictionary:function(t,e){var a=e.length;if(!t||!t.state)return at;var n=t.state,r=n.wrap;if(2===r||1===r&&42!==n.status||n.lookahead)return at;if(1===r&&(t.adler=C(t.adler,e,a,0)),n.wrap=0,a>=n.w_size){0===r&&(mt(n.head),n.strstart=0,n.block_start=0,n.insert=0);var i=new Uint8Array(n.w_size);i.set(e.subarray(a-n.w_size,a),0),e=i,a=n.w_size}var s=t.avail_in,_=t.next_in,h=t.input;for(t.avail_in=a,t.next_in=0,t.input=e,Zt(n);n.lookahead>=3;){var l=n.strstart,o=n.lookahead-2;do{n.ins_h=yt(n,n.ins_h,n.window[l+3-1]),n.prev[l&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=l,l++}while(--o);n.strstart=l,n.lookahead=2,Zt(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,t.next_in=_,t.input=h,t.avail_in=s,n.wrap=r,tt},deflateInfo:"pako deflate (from Nodeca project)"};for(var Dt=new Uint8Array(256),Bt=0;Bt<256;Bt++)Dt[Bt]=Bt>=252?6:Bt>=248?5:Bt>=240?4:Bt>=224?3:Bt>=192?2:1;Dt[254]=Dt[254]=1;var Ct=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Ht=Object.prototype.toString,Mt=K.Z_NO_FLUSH,Yt=K.Z_SYNC_FLUSH,Kt=K.Z_FULL_FLUSH,Pt=K.Z_FINISH,jt=K.Z_OK,Gt=K.Z_STREAM_END,Xt=K.Z_DEFAULT_COMPRESSION,Wt=K.Z_DEFAULT_STRATEGY,qt=K.Z_DEFLATED;function Jt(){this.options={level:Xt,method:qt,chunkSize:16384,windowBits:15,memLevel:8,strategy:Wt};var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Ct,this.strm.avail_out=0;var e=Ot.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(e!==jt)throw new Error(Y[e]);if(t.header&&Ot.deflateSetHeader(this.strm,t.header),t.dictionary){var a;if(a="[object ArrayBuffer]"===Ht.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(e=Ot.deflateSetDictionary(this.strm,a))!==jt)throw new Error(Y[e]);this._dict_set=!0}}function Qt(t,e,a){try{t.postMessage({type:"errored",error:e,streamId:a})}catch(n){t.postMessage({type:"errored",error:String(e),streamId:a})}}function Vt(t){var e=t.strm.adler;return new Uint8Array([3,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e])}Jt.prototype.push=function(t,e){var a,n,r=this.strm,i=this.options.chunkSize;if(this.ended)return!1;for(n=e===~~e?e:!0===e?Pt:Mt,"[object ArrayBuffer]"===Ht.call(t)?r.input=new Uint8Array(t):r.input=t,r.next_in=0,r.avail_in=r.input.length;;)if(0===r.avail_out&&(r.output=new Uint8Array(i),r.next_out=0,r.avail_out=i),(n===Yt||n===Kt)&&r.avail_out<=6)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else{if((a=Ot.deflate(r,n))===Gt)return r.next_out>0&&this.onData(r.output.subarray(0,r.next_out)),a=Ot.deflateEnd(this.strm),this.onEnd(a),this.ended=!0,a===jt;if(0!==r.avail_out){if(n>0&&r.next_out>0)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else if(0===r.avail_in)break}else this.onData(r.output)}return!0},Jt.prototype.onData=function(t){this.chunks.push(t)},Jt.prototype.onEnd=function(t){t===jt&&(this.result=function(t){for(var e=0,a=0,n=t.length;a<n;a++)e+=t[a].length;for(var r=new Uint8Array(e),i=0,s=0,_=t.length;i<_;i++){var h=t[i];r.set(h,s),s+=h.length}return r}(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},function(e){void 0===e&&(e=self);try{var a=new Map;e.addEventListener("message",(function(n){try{var r=function(e,a){switch(a.action){case"init":return{type:"initialized",version:"5.21.0"};case"write":var n=e.get(a.streamId);n||(n=new Jt,e.set(a.streamId,n));var r=n.chunks.length,i=function(t){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);var e,a,n,r,i,s=t.length,_=0;for(r=0;r<s;r++)55296==(64512&(a=t.charCodeAt(r)))&&r+1<s&&56320==(64512&(n=t.charCodeAt(r+1)))&&(a=65536+(a-55296<<10)+(n-56320),r++),_+=a<128?1:a<2048?2:a<65536?3:4;for(e=new Uint8Array(_),i=0,r=0;i<_;r++)55296==(64512&(a=t.charCodeAt(r)))&&r+1<s&&56320==(64512&(n=t.charCodeAt(r+1)))&&(a=65536+(a-55296<<10)+(n-56320),r++),a<128?e[i++]=a:a<2048?(e[i++]=192|a>>>6,e[i++]=128|63&a):a<65536?(e[i++]=224|a>>>12,e[i++]=128|a>>>6&63,e[i++]=128|63&a):(e[i++]=240|a>>>18,e[i++]=128|a>>>12&63,e[i++]=128|a>>>6&63,e[i++]=128|63&a);return e}(a.data);return n.push(i,K.Z_SYNC_FLUSH),{type:"wrote",id:a.id,streamId:a.streamId,result:t(n.chunks.slice(r)),trailer:Vt(n),additionalBytesCount:i.length};case"reset":e.delete(a.streamId)}}(a,n.data);r&&e.postMessage(r)}catch(t){Qt(e,t,n.data&&"streamId"in n.data?n.data.streamId:void 0)}}))}catch(t){Qt(e,t)}}()}();'])))
            }
            var $a = {
                status: 0
            };

            function Qa(t, e, n, r) {
                switch (void 0 === r && (r = Ja), 0 === $a.status && function(t, e, n) {
                    void 0 === n && (n = Ja);
                    try {
                        var r = n(t),
                            i = te(t, r, "error", (function(n) {
                                es(t, e, n)
                            })).stop,
                            o = te(t, r, "message", (function(n) {
                                var r, i = n.data;
                                "errored" === i.type ? es(t, e, i.error, i.streamId) : "initialized" === i.type && (r = i.version, 1 === $a.status && ($a = {
                                    status: 3,
                                    worker: $a.worker,
                                    stop: $a.stop,
                                    version: r
                                }))
                            })).stop;
                        r.postMessage({
                            action: "init"
                        }), A((function() {
                            return function(t) {
                                1 === $a.status && (s.error("".concat(t, " failed to start: a timeout occurred while initializing the Worker")), $a.initializationFailureCallbacks.forEach((function(t) {
                                    return t()
                                })), $a = {
                                    status: 2
                                })
                            }(e)
                        }), 1e4);
                        $a = {
                            status: 1,
                            worker: r,
                            stop: function() {
                                i(), o()
                            },
                            initializationFailureCallbacks: []
                        }
                    } catch (n) {
                        es(t, e, n)
                    }
                }(t, e, r), $a.status) {
                    case 1:
                        return $a.initializationFailureCallbacks.push(n), $a.worker;
                    case 3:
                        return $a.worker
                }
            }

            function ts() {
                return $a.status
            }

            function es(t, e, n, r) {
                if (1 === $a.status || 0 === $a.status) {
                    if (s.error("".concat(e, " failed to start: an error occurred while creating the Worker:"), n), n instanceof Event || n instanceof Error && (g(o = n.message, "Content Security Policy") || g(o, "requires 'TrustedScriptURL'"))) {
                        var i = void 0;
                        i = t.workerUrl ? "Please make sure the Worker URL ".concat(t.workerUrl, " is correct and CSP is correctly configured.") : "Please make sure CSP is correctly configured.", s.error("".concat(i, " See documentation at ").concat(u, "/integrations/content_security_policy_logs/#use-csp-with-real-user-monitoring-and-session-replay"))
                    } else Jt(n);
                    1 === $a.status && $a.initializationFailureCallbacks.forEach((function(t) {
                        return t()
                    })), $a = {
                        status: 2
                    }
                } else Jt(n, {
                    worker_version: 3 === $a.status && $a.version,
                    stream_id: r
                });
                var o
            }
            n.d(e, "datadogRum", (function() {
                return ns
            })), n.d(e, "DefaultPrivacyLevel", (function() {
                return Je
            }));
            var ns = function(t, e, n) {
                void 0 === n && (n = {});
                var r = B(0),
                    i = X(r.getOrCreateTracker(2)),
                    o = X(r.getOrCreateTracker(1)),
                    a = function(t) {
                        var e = new W;
                        return {
                            tryToInit: function(e) {
                                t || (t = e)
                            },
                            update: function(n) {
                                t = n, e.notify()
                            },
                            isGranted: function() {
                                return t === $.GRANTED
                            },
                            observable: e
                        }
                    }();

                function u() {
                    return function(t, e, n) {
                        return {
                            context: t.getContext(),
                            user: e.getContext(),
                            hasReplay: !!n.isRecording() || void 0
                        }
                    }(i, o, e)
                }
                var c = ln(n, u, a, (function(s, l, f) {
                        et(J.CUSTOM_VITALS) && (d.startDurationVital = v((function(t, e) {
                            c.startDurationVital({
                                name: j(t),
                                startClocks: (null === e || void 0 === e ? void 0 : e.startTime) ? ut(e.startTime) : vt(),
                                context: j(null === e || void 0 === e ? void 0 : e.context)
                            }), $t({
                                feature: "start-duration-vital"
                            })
                        })), d.stopDurationVital = v((function(t, e) {
                            c.stopDurationVital({
                                name: j(t),
                                stopClocks: (null === e || void 0 === e ? void 0 : e.stopTime) ? ut(e.stopTime) : vt(),
                                context: j(null === e || void 0 === e ? void 0 : e.context)
                            })
                        }))), s.storeContextsAcrossPages && (re(s, i, "rum", 2), re(s, o, "rum", 1)), r.setCompressionStatus(l ? 1 : 2);
                        var p = t(s, e, r, u, f, l && n.createDeflateEncoder ? function(t) {
                            return n.createDeflateEncoder(s, l, t)
                        } : ie, a);
                        return e.onRumStart(p.lifeCycle, s, p.session, p.viewContexts, l), c = function(t, e) {
                            return E({
                                init: function(t) {
                                    ae("DD_RUM", t)
                                },
                                initConfiguration: t.initConfiguration
                            }, e)
                        }(c, p), p
                    })),
                    l = v((function(t) {
                        var e = "object" === typeof t ? t : {
                            name: t
                        };
                        c.startView(e), $t({
                            feature: "start-view"
                        })
                    })),
                    d = function(t) {
                        var e = E({
                            version: "5.21.0",
                            onReady: function(t) {
                                t()
                            }
                        }, t);
                        return Object.defineProperty(e, "_setDebug", {
                            get: function() {
                                return p
                            },
                            enumerable: !1
                        }), e
                    }({
                        init: v((function(t) {
                            return c.init(t)
                        })),
                        setTrackingConsent: v((function(t) {
                            a.update(t), $t({
                                feature: "set-tracking-consent",
                                tracking_consent: t
                            })
                        })),
                        setGlobalContext: v((function(t) {
                            i.setContext(t), $t({
                                feature: "set-global-context"
                            })
                        })),
                        getGlobalContext: v((function() {
                            return i.getContext()
                        })),
                        setGlobalContextProperty: v((function(t, e) {
                            i.setContextProperty(t, e), $t({
                                feature: "set-global-context"
                            })
                        })),
                        removeGlobalContextProperty: v((function(t) {
                            return i.removeContextProperty(t)
                        })),
                        clearGlobalContext: v((function() {
                            return i.clearContext()
                        })),
                        getInternalContext: v((function(t) {
                            return c.getInternalContext(t)
                        })),
                        getInitConfiguration: v((function() {
                            return q(c.initConfiguration)
                        })),
                        addAction: function(t, e) {
                            var n = Ot();
                            h((function() {
                                c.addAction({
                                    name: j(t),
                                    context: j(e),
                                    startClocks: vt(),
                                    type: "custom",
                                    handlingStack: n
                                }), $t({
                                    feature: "add-action"
                                })
                            }))
                        },
                        addError: function(t, e) {
                            var n = Ot();
                            h((function() {
                                c.addError({
                                    error: t,
                                    handlingStack: n,
                                    context: j(e),
                                    startClocks: vt()
                                }), $t({
                                    feature: "add-error"
                                })
                            }))
                        },
                        addTiming: v((function(t, e) {
                            c.addTiming(j(t), e)
                        })),
                        setUser: v((function(t) {
                            (function(t) {
                                var e = "object" === V(t);
                                return e || s.error("Unsupported user:", t), e
                            })(t) && o.setContext(oe(t)), $t({
                                feature: "set-user"
                            })
                        })),
                        getUser: v((function() {
                            return o.getContext()
                        })),
                        setUserProperty: v((function(t, e) {
                            var n, r = oe((n = {}, n[t] = e, n))[t];
                            o.setContextProperty(t, r), $t({
                                feature: "set-user"
                            })
                        })),
                        removeUserProperty: v((function(t) {
                            return o.removeContextProperty(t)
                        })),
                        clearUser: v((function() {
                            return o.clearContext()
                        })),
                        startView: l,
                        stopSession: v((function() {
                            c.stopSession(), $t({
                                feature: "stop-session"
                            })
                        })),
                        addFeatureFlagEvaluation: v((function(t, e) {
                            c.addFeatureFlagEvaluation(j(t), j(e)), $t({
                                feature: "add-feature-flag-evaluation"
                            })
                        })),
                        getSessionReplayLink: v((function() {
                            return e.getSessionReplayLink()
                        })),
                        startSessionReplayRecording: v((function(t) {
                            e.start(t), $t({
                                feature: "start-session-replay-recording",
                                force: null === t || void 0 === t ? void 0 : t.force
                            })
                        })),
                        stopSessionReplayRecording: v((function() {
                            return e.stop()
                        }))
                    });
                return d
            }((function(t, e, n, r, i, o, a) {
                var s = [],
                    u = new ir;
                u.subscribe(13, (function(t) {
                    return Pt("rum", t)
                }));
                var c = function(t) {
                    var e = Yt("browser-rum-sdk", t);
                    if (ce()) {
                        var n = se();
                        e.observable.subscribe((function(t) {
                            return n.send("internal_telemetry", t)
                        }))
                    }
                    return e
                }(t);
                c.setContextProvider((function() {
                    var e, n;
                    return {
                        application: {
                            id: t.applicationId
                        },
                        session: {
                            id: null === (e = h.findTrackedSession()) || void 0 === e ? void 0 : e.id
                        },
                        view: {
                            id: null === (n = w.findView()) || void 0 === n ? void 0 : n.id
                        },
                        action: {
                            id: T.findActionId()
                        }
                    }
                }));
                var l = function(t) {
                        u.notify(14, {
                            error: t
                        }), Xt("Error reported to customer", {
                            "error.message": t.message
                        })
                    },
                    d = function(t, e) {
                        var n = new or(144e5);
                        return t.subscribe(2, (function(t) {
                            var r = t.startClocks;
                            n.add({}, r.relative), e.resetCustomerData()
                        })), t.subscribe(6, (function(t) {
                            var e = t.endClocks;
                            n.closeActive(e.relative)
                        })), {
                            findFeatureFlagEvaluations: function(t) {
                                return n.find(t)
                            },
                            addFeatureFlagEvaluation: function(t, r) {
                                var i = n.find();
                                i && (i[t] = r, e.updateCustomerData(i))
                            },
                            stop: function() {
                                return e.stop()
                            }
                        }
                    }(u, n.getOrCreateTracker(0)),
                    f = function(t) {
                        return new W((function(e) {
                            var n = ee(t, window, [Ft.VISIBILITY_CHANGE, Ft.FREEZE], (function(t) {
                                    t.type === Ft.VISIBILITY_CHANGE && "hidden" === document.visibilityState ? e.notify({
                                        reason: dn.HIDDEN
                                    }) : t.type === Ft.FREEZE && e.notify({
                                        reason: dn.FROZEN
                                    })
                                }), {
                                    capture: !0
                                }).stop,
                                r = te(t, window, Ft.BEFORE_UNLOAD, (function() {
                                    e.notify({
                                        reason: dn.UNLOADING
                                    })
                                })).stop;
                            return function() {
                                n(), r()
                            }
                        }))
                    }(t),
                    p = f.subscribe((function(t) {
                        u.notify(11, t)
                    }));
                s.push((function() {
                    return p.unsubscribe()
                }));
                var h = ce() ? function() {
                    var t = {
                        id: "00000000-aaaa-0000-aaaa-000000000000",
                        sessionReplay: ue("records") ? 1 : 0
                    };
                    return {
                        findTrackedSession: function() {
                            return t
                        },
                        expire: L,
                        expireObservable: new W,
                        setForcedReplay: L
                    }
                }() : Pi(t, u, a);
                if (ce()) ! function(t) {
                    var e = se();
                    t.subscribe(13, (function(t) {
                        e.send("rum", t)
                    }))
                }(u);
                else {
                    var m = Yi(t, u, c.observable, l, f, h.expireObservable, o);
                    s.push((function() {
                            return m.stop()
                        })),
                        function(t, e, n, r, i) {
                            e.enabled && nt(t.customerDataTelemetrySampleRate) && (ro(), io(), n.subscribe(13, (function(t) {
                                Qi = !0, eo($i.globalContextBytes, r.getOrCreateTracker(2).getBytesCount()), eo($i.userContextBytes, r.getOrCreateTracker(1).getBytesCount()), eo($i.featureFlagBytes, g(["view", "error"], t.type) ? r.getOrCreateTracker(0).getBytesCount() : 0)
                            })), i.subscribe((function(t) {
                                var e = t.bytesCount,
                                    n = t.messagesCount;
                                Qi && (Ji.batchCount += 1, eo(Ji.batchBytesCount, e), eo(Ji.batchMessagesCount, n), no(Ji.globalContextBytes, $i.globalContextBytes), no(Ji.userContextBytes, $i.userContextBytes), no(Ji.featureFlagBytes, $i.featureFlagBytes), io())
                            })), I(to, 1e4))
                        }(t, c, u, n, m.flushObservable)
                }
                var _ = function() {
                        var t = pn();
                        return new W((function(e) {
                            if (t) {
                                var n = new t(v((function() {
                                    return e.notify()
                                })));
                                return n.observe(document, {
                                        attributes: !0,
                                        characterData: !0,
                                        childList: !0,
                                        subtree: !0
                                    }),
                                    function() {
                                        return n.disconnect()
                                    }
                            }
                        }))
                    }(),
                    y = Xi(t, location),
                    b = function(t, e, n, r, i, o, a, s) {
                        var u = function(t) {
                                var e = new or(144e5);
                                return t.subscribe(2, (function(t) {
                                    e.add(function(t) {
                                        return {
                                            service: t.service,
                                            version: t.version,
                                            id: t.id,
                                            name: t.name,
                                            startClocks: t.startClocks
                                        }
                                    }(t), t.startClocks.relative)
                                })), t.subscribe(6, (function(t) {
                                    var n = t.endClocks;
                                    e.closeActive(n.relative)
                                })), t.subscribe(10, (function() {
                                    e.reset()
                                })), {
                                    findView: function(t) {
                                        return e.find(t)
                                    },
                                    stop: function() {
                                        e.stop()
                                    }
                                }
                            }(t),
                            c = function(t, e, n) {
                                var r, i = new or(144e5);
                                t.subscribe(2, (function(t) {
                                    var e = t.startClocks,
                                        o = n.href;
                                    i.add(a({
                                        url: o,
                                        referrer: r || document.referrer
                                    }), e.relative), r = o
                                })), t.subscribe(6, (function(t) {
                                    var e = t.endClocks;
                                    i.closeActive(e.relative)
                                }));
                                var o = e.subscribe((function(t) {
                                    var e = t.newLocation,
                                        n = i.find();
                                    if (n) {
                                        var r = pt();
                                        i.closeActive(r), i.add(a({
                                            url: e.href,
                                            referrer: n.referrer
                                        }), r)
                                    }
                                }));

                                function a(t) {
                                    return {
                                        url: t.url,
                                        referrer: t.referrer
                                    }
                                }
                                return {
                                    findUrl: function(t) {
                                        return i.find(t)
                                    },
                                    stop: function() {
                                        o.unsubscribe(), i.stop()
                                    }
                                }
                            }(t, i, n),
                            l = oo(e),
                            d = function(t, e, n, r) {
                                t.subscribe(1, (function(e) {
                                    return t.notify(12, $r(e, r))
                                }));
                                var i = {
                                    findActionId: L
                                };
                                return n.trackUserInteractions && (i = Jr(t, e, n).actionContexts), {
                                    addAction: function(e, n) {
                                        t.notify(12, E({
                                            savedCommonContext: n
                                        }, $r(e, r)))
                                    },
                                    actionContexts: i
                                }
                            }(t, o, e, l),
                            f = d.addAction,
                            p = d.actionContexts,
                            v = function(t) {
                                var e = Ri();
                                return {
                                    get: function() {
                                        return {
                                            viewport: e
                                        }
                                    },
                                    stop: Ai(t).subscribe((function(t) {
                                        e = t
                                    })).unsubscribe
                                }
                            }(e),
                            h = function(t, e) {
                                var n;
                                void 0 === e && (e = uo(t, "datadog-ci-visibility-test-execution-id"));
                                var r = _e("datadog-ci-visibility-test-execution-id") || (null === (n = window.Cypress) || void 0 === n ? void 0 : n.env("traceId")),
                                    i = e.subscribe((function(t) {
                                        r = t
                                    }));
                                return {
                                    get: function() {
                                        if ("string" === typeof r) return {
                                            test_execution_id: r
                                        }
                                    },
                                    stop: function() {
                                        return i.unsubscribe()
                                    }
                                }
                            }(e);
                        return nr(e, t, r, u, c, p, v, h, a, s), {
                            viewContexts: u,
                            pageStateHistory: l,
                            urlContexts: c,
                            addAction: f,
                            actionContexts: p,
                            stop: function() {
                                h.stop(), v.stop(), l.stop(), c.stop(), u.stop(), l.stop()
                            }
                        }
                    }(u, t, location, h, y, _, r, l),
                    w = b.viewContexts,
                    S = b.pageStateHistory,
                    C = b.urlContexts,
                    T = b.actionContexts,
                    k = b.addAction,
                    x = b.stop;
                s.push(x), Kt.drain(),
                    function(t, e) {
                        t.subscribe(0, (function(n) {
                            for (var r = 0, i = n; r < i.length; r++) {
                                var o = i[r];
                                if (o.entryType !== Ln.LONG_TASK) break;
                                if (!e.trackLongTasks) break;
                                var a = st(o.startTime),
                                    s = {
                                        date: a.timeStamp,
                                        long_task: {
                                            id: le(),
                                            duration: lt(o.duration)
                                        },
                                        type: "long_task",
                                        _dd: {
                                            discarded: !1
                                        }
                                    };
                                t.notify(12, {
                                    rawRumEvent: s,
                                    startTime: a.relative,
                                    domainContext: {
                                        performanceEntry: o
                                    }
                                })
                            }
                        }))
                    }(u, t), fi(u, t, S);
                var A = Mi(u, t, location, _, y, d, S, e, i),
                    R = A.addTiming,
                    O = A.startView,
                    N = A.stop;
                s.push(N);
                var M = ui(u, t, S, d).addError;
                hr(u, t, h);
                var U = Bn(u, t).stop;
                s.push(U);
                var D = so(u, S),
                    P = function(t, e, n, r, i) {
                        return {
                            get: function(o) {
                                var a = n.findView(o),
                                    s = i.findUrl(o),
                                    u = e.findTrackedSession(o);
                                if (u && a && s) {
                                    var c = r.findActionId(o);
                                    return {
                                        application_id: t,
                                        session_id: u.id,
                                        user_action: c ? {
                                            id: c
                                        } : void 0,
                                        view: {
                                            id: a.id,
                                            name: a.name,
                                            referrer: s.referrer,
                                            url: s.url
                                        }
                                    }
                                }
                            }
                        }
                    }(t.applicationId, h, w, T, C);
                return {
                    addAction: k,
                    addError: M,
                    addTiming: R,
                    addFeatureFlagEvaluation: d.addFeatureFlagEvaluation,
                    startView: O,
                    lifeCycle: u,
                    viewContexts: w,
                    session: h,
                    stopSession: function() {
                        return h.expire()
                    },
                    getInternalContext: P.get,
                    startDurationVital: D.startDurationVital,
                    stopDurationVital: D.stopDurationVital,
                    stop: function() {
                        s.forEach((function(t) {
                            return t()
                        }))
                    }
                }
            }), function(t, e) {
                if (ce() && !ue("records") || !Wa()) return {
                    start: L,
                    stop: L,
                    getReplayStats: function() {},
                    onRumStart: L,
                    isRecording: function() {
                        return !1
                    },
                    getSessionReplayLink: function() {}
                };
                var n = {
                        status: 1
                    },
                    r = function() {
                        n = {
                            status: 1
                        }
                    },
                    i = function() {
                        n = {
                            status: 0
                        }
                    },
                    o = L;
                return {
                    start: function(t) {
                        return r(t)
                    },
                    stop: function() {
                        return i()
                    },
                    getSessionReplayLink: function() {
                        return o()
                    },
                    onRumStart: function(a, s, u, c, l) {
                        var d;
                        s.startSessionReplayRecordingManually && (n = {
                            status: 0
                        }), a.subscribe(9, (function() {
                            2 !== n.status && 3 !== n.status || (i(), n = {
                                status: 1
                            })
                        })), a.subscribe(11, (function(t) {
                            t.reason === dn.UNLOADING && i()
                        })), a.subscribe(10, (function() {
                            1 === n.status && r()
                        })), r = function(r) {
                            var o = u.findTrackedSession();
                            o && (0 !== o.sessionReplay || (null === r || void 0 === r ? void 0 : r.force)) ? 2 !== n.status && 3 !== n.status && (n = {
                                status: 2
                            }, vn(s, "interactive", (function() {
                                if (2 === n.status) {
                                    var r = (d || (l || (l = Qa(s, "Datadog Session Replay", (function() {
                                        i()
                                    }), e)), l && (d = Xa(s, l, 1))), d);
                                    if (r) {
                                        var o = t(a, s, u, c, r).stop;
                                        n = {
                                            status: 3,
                                            stopRecording: o
                                        }
                                    } else n = {
                                        status: 0
                                    }
                                }
                            })), (null === r || void 0 === r ? void 0 : r.force) && 0 === o.sessionReplay && u.setForcedReplay()) : n = {
                                status: 1
                            }
                        }, i = function() {
                            0 !== n.status && (3 === n.status && n.stopRecording(), n = {
                                status: 0
                            })
                        }, o = function() {
                            return Ya(s, u, c, 0 !== n.status)
                        }, 1 === n.status && r()
                    },
                    isRecording: function() {
                        return 3 === ts() && 3 === n.status
                    },
                    getReplayStats: function(t) {
                        return 3 === ts() ? function(t) {
                            return null === fo || void 0 === fo ? void 0 : fo.get(t)
                        }(t) : void 0
                    }
                }
            }((function(t, e, n, r, i, o) {
                var a, s = [],
                    u = o || ji(e, e.sessionReplayEndpointBuilder, Za, (function(e) {
                        t.notify(14, {
                            error: e
                        }), Xt("Error reported to customer", {
                            "error.message": e.message
                        })
                    }));
                if (ce()) a = function(t) {
                    var e = se();
                    return {
                        addRecord: function(n) {
                            var r = t.findView();
                            e.send("record", n, r.id)
                        }
                    }
                }(r).addRecord;
                else {
                    var c = Ka(t, e, n, r, u, i);
                    a = c.addRecord, s.push(c.stop)
                }
                var l = Ga({
                    emit: a,
                    configuration: e,
                    lifeCycle: t,
                    viewContexts: r
                }).stop;
                return s.push(l), {
                    stop: function() {
                        s.forEach((function(t) {
                            return t()
                        }))
                    }
                }
            })), {
                startDeflateWorker: Qa,
                createDeflateEncoder: Xa
            });
            ! function(t, e, n) {
                var r = t[e];
                r && !r.q && r.version && s.warn("SDK is loaded more than once. This is unsupported and might have unexpected behavior."), t[e] = n, r && r.q && r.q.forEach((function(t) {
                    return c(t, "onReady callback threw an error:")()
                }))
            }(C(), "DD_RUM", ns)
        }
    }
]);
//# sourceMappingURL=278.9be73138.chunk.js.map