import Vue from 'vue';
import Vuetify from 'vuetify/lib';
import { UserVuetifyPreset } from 'vuetify';
import colors from 'vuetify/lib/util/colors';

Vue.use(Vuetify);

import ko from '../languages/ko/';

export default new Vuetify({
	lang: {
		locales: { ko },
		current: 'ko',
		// 누락된 번역을 처리하기 위한 fallback
		fallback: 'ko',
	},
	theme: {
		themes: {
			light: {
				primary: colors.purple,
			},
		},
	},
} as UserVuetifyPreset);
