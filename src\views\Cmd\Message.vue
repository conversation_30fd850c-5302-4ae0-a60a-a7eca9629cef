<template>
  <v-card flat class="mt-4">
    <v-card-text>
      <div class="d-flex align-center mb-4">
        <v-text-field
          v-model="searchQuery"
          outlined
          dense
          hide-details
          clearable
          prepend-inner-icon="mdi-magnify"
          placeholder="명령어 또는 응답 검색"
          class="rounded-lg mr-3"
          @input="filterMessages">
        </v-text-field>
        <v-btn 
          color="purple" 
          class="white--text rounded-lg ml-auto" 
          elevation="2"
          @click="addMessageEvent">
          <v-icon left>mdi-message-plus</v-icon>
          {{ $t('add') }}
        </v-btn>
      </div>
      
      <v-card outlined class="command-container">
        <v-slide-y-transition group>
          <div
            v-for="(item, index) of filteredMessages" 
            :key="'message_' + index"
            class="command-item pa-3"
            :class="{'grey lighten-5': index % 2 === 0}">
            <v-row>
              <v-col cols="12" sm="4" class="pb-0">
                <v-text-field
                  :placeholder="$t('cmd.command')"
                  outlined
                  dense
                  hide-details
                  class="rounded-lg"
                  background-color="transparent"
                  v-model="item.command"/>
              </v-col>
              <v-col cols="12" sm="6" class="pb-0">
                <v-textarea
                  :placeholder="$t('cmd.reply')"
                  outlined
                  auto-grow
                  :rows="expandedIndex === index ? 5 : 1"
                  hide-details
                  class="rounded-lg"
                  background-color="transparent"
                  dense
                  v-model="item.message"
                  @focus="expandTextarea(index)"
                  @blur="collapseTextarea(index)"/>
              </v-col>
              <v-col cols="12" sm="2" class="d-flex align-center justify-space-between pb-0">
                <v-select
                  :items="permitList"
                  outlined
                  dense
                  hide-details
                  class="rounded-lg mr-2"
                  background-color="transparent"
                  v-model="item.permit"
                  item-text="text"
                  item-value="value">
                  <template v-slot:selection="{ item }">
                    {{ $t('cmd.permit.' + item.value) }}
                  </template>
                </v-select>
                <v-btn 
                  icon 
                  @click="delMessageEvent(getOriginalIndex(index))">
                  <v-icon color="red darken-3">mdi-close-circle</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-divider v-if="index < filteredMessages.length - 1" class="mt-3"></v-divider>
          </div>
        </v-slide-y-transition>
        
        <div 
          class="text-center pa-6 grey--text" 
          v-if="filteredMessages.length === 0 && !searchQuery">
          <v-icon large class="mb-2">mdi-message-text-outline</v-icon>
          <div>{{ $t('cmd.no-messages') }}</div>
        </div>
        
        <div 
          class="text-center pa-6 grey--text" 
          v-else-if="filteredMessages.length === 0 && searchQuery">
          <v-icon large class="mb-2">mdi-file-search-outline</v-icon>
          <div>'{{ searchQuery }}' 검색 결과가 없습니다</div>
        </div>
      </v-card>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import CfgLite from '@/plugins/cfg-lite-ipc';

export interface MessageStruct {
	command: string;
	message: string;
	permit: string;
}

interface PermitItem {
	text: string;
	value: string;
}

@Component({
  components: {
  },
})
export default class CmdMessage extends Mixins(GlobalMixins) {
	public liveMessage: MessageStruct[] = [];
  public filteredMessages: MessageStruct[] = [];
  public searchQuery: string = '';
  public expandedIndex: number = -1;
  public messageIndexMap: number[] = [];

	public cfgPath: string = this.$path('userData', 'cmd.cfg');
	public cfg: CfgLite = new CfgLite(this.cfgPath);

	public readonly permitList: PermitItem[] = [
		{
			text: this.$t('cmd.permit.all'),
			value: 'all',
		},
		{
			text: this.$t('cmd.permit.manager'),
			value: 'manager',
		},
	];

	public mounted() {
		this.liveMessage = this.cfg.get('live_message') || [];
    this.filteredMessages = [...this.liveMessage];
    this.updateIndexMap();
    
		this.$evt.$on('cmd:save', () => {
			this.cfg.set('live_message', this.liveMessage);
			this.cfg.save();
		});
	}

	public addMessageEvent() {
		this.liveMessage.unshift({
			command: '',
			message: '',
			permit: 'all',
		});
    this.filterMessages();
	}

	public delMessageEvent(idx: number) {
		this.liveMessage.splice(idx, 1);
    this.filterMessages();
	}
  
  public filterMessages() {
    if (!this.searchQuery) {
      this.filteredMessages = [...this.liveMessage];
    } else {
      const query = this.searchQuery.toLowerCase();
      this.filteredMessages = this.liveMessage.filter(item => 
        item.command.toLowerCase().includes(query) || 
        item.message.toLowerCase().includes(query)
      );
    }
    this.updateIndexMap();
  }
  
  public updateIndexMap() {
    this.messageIndexMap = [];
    this.filteredMessages.forEach(filtered => {
      const index = this.liveMessage.findIndex(original => 
        original.command === filtered.command && 
        original.message === filtered.message && 
        original.permit === filtered.permit
      );
      this.messageIndexMap.push(index);
    });
  }
  
  public getOriginalIndex(filteredIndex: number): number {
    return this.messageIndexMap[filteredIndex];
  }
  
  public expandTextarea(index: number) {
    if (this.filteredMessages[index].message.length > 50) {
      this.expandedIndex = index;
    }
  }
  
  public collapseTextarea(index: number) {
    this.expandedIndex = -1;
  }

	public beforeUnmount() {
		this.$evt.$off('cmd:save');
	}
}
</script>

<style scoped>
.command-container {
  border-radius: 8px;
  overflow: hidden;
}

.command-item {
  position: relative;
  transition: background-color 0.2s ease;
}

.command-item:hover {
  background-color: #f9f4fa !important;
}
</style>
