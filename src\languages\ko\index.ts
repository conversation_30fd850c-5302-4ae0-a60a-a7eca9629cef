/*
 * index.ts
 * Created on Sat Jul 18 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
 */
import app from './app/';
import home from './home/';
import lives from './lives/';
import users from './users/';
import cmd from './cmd/';
import bundle from './bundle/';
import page from './page';
import tutorial from './tutorial';

export default {
	app,
	home,
	lives,
	users,
	cmd,
	bundle,
	page,
	tutorial,
	// common
	'msg': {
		alert: '알림',
	},
	'send': 'Send',
	'spoon': '스푼',
	'confirm': '확인',
	'cancel': '취소',
	'enable': '사용',
	'apply': '적용',
	'add': '추가',
	'save-success': '저장에 성공했습니다.',
	'login': '로그인',
	'error': '오류',
	'errors': {
		'file-not-found': '$0 파일을 찾을 수 없습니다.',
	},
	'close': '닫기',
	'install': '설치',
	'success': '성공',
	'uninstall': '제거',
	'yes': '예',
	'no': '아니오',
	'spoon-logout': '스푼 로그아웃',
	'show-release-note': '릴리즈 노트 전부 보기',
	'update': '업데이트',
	'donation': '후원하기',
	'click-picture': '클릭하여 사진 크게 보기',
	'refresh': '새로고침',
	'carousel': {
		'prev': '이전',
		'next': '다음'
	},
	// Vuetify 관련 번역
	'loading': '로딩 중...',
	'input': {
		'prependAction': '앞에 추가',
		'appendAction': '뒤에 추가'
	},
	'no-results': '결과 없음',
	'noDataText': '데이터가 없습니다',
	'dataTable': {
		'noDataText': '데이터가 없습니다',
		'noResultsText': '검색 결과가 없습니다',
		'sortBy': '정렬 기준',
		'itemsPerPageText': '페이지 당 항목 수',
		'ariaLabel': {
			'sortNone': '정렬되지 않음',
			'sortAscending': '오름차순 정렬',
			'sortDescending': '내림차순 정렬',
			'activateAscending': '오름차순으로 정렬하려면 활성화하세요',
			'activateDescending': '내림차순으로 정렬하려면 활성화하세요',
			'activateNone': '정렬을 제거하려면 활성화하세요'
		}
	},
	'dataFooter': {
		'itemsPerPageText': '페이지 당 항목 수:',
		'itemsPerPageAll': '전체',
		'prevPage': '이전 페이지',
		'nextPage': '다음 페이지',
		'firstPage': '첫 페이지',
		'lastPage': '마지막 페이지',
		'pageText': '{0}-{1} / {2}'
	},
	'more-results': '더 보기',
};
