import React, { useState } from 'react';

interface TokenSelectModalProps {
  isOpen: boolean;
  spoonTokens: any[];
  onClose: () => void;
  onConfirm: (tokenId: string) => void;
}

export const TokenSelectModal: React.FC<TokenSelectModalProps> = ({
  isOpen,
  spoonTokens,
  onClose,
  onConfirm
}) => {
  const [selectedTokenId, setSelectedTokenId] = useState<string>('');

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '24px',
        maxWidth: '500px',
        width: '90%',
        maxHeight: '70vh',
        overflow: 'auto'
      }}>
        <h3 style={{ marginBottom: '16px' }}>관리자 토큰 선택</h3>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          관리자가 사용할 스푼 토큰을 선택하세요
        </p>
        
        <div style={{ marginBottom: '20px' }}>
          {spoonTokens.map((token) => (
            <div
              key={token.id}
              style={{
                padding: '16px',
                border: selectedTokenId === token.id ? '2px solid #7c3aed' : '1px solid #e2e8f0',
                borderRadius: '12px',
                marginBottom: '12px',
                cursor: 'pointer',
                backgroundColor: selectedTokenId === token.id ? '#f3f4f6' : 'white',
                display: 'flex',
                alignItems: 'center',
                gap: '16px'
              }}
              onClick={() => setSelectedTokenId(token.id)}
            >
              <div style={{ position: 'relative' }}>
                <img
                  src={token.user?.profile_url || '/default-profile.png'}
                  alt="프로필"
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '50%',
                    objectFit: 'cover'
                  }}
                />
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ 
                  fontWeight: '600', 
                  marginBottom: '4px',
                  fontSize: '16px'
                }}>
                  {token.user?.nickname || '닉네임 없음'}
                </div>
                <div style={{ 
                  color: '#666', 
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span>#{token.user?.tag}</span>
                  <span>•</span>
                  <span>ID: {token.user?.id}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 24px',
              border: '1px solid #e2e8f0',
              borderRadius: '8px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            취소
          </button>
          <button
            onClick={() => selectedTokenId && onConfirm(selectedTokenId)}
            disabled={!selectedTokenId}
            style={{
              padding: '12px 24px',
              border: 'none',
              borderRadius: '8px',
              background: selectedTokenId ? '#7c3aed' : '#ccc',
              color: 'white',
              cursor: selectedTokenId ? 'pointer' : 'not-allowed',
              fontSize: '14px'
            }}
          >
            선택
          </button>
        </div>
      </div>
    </div>
  );
}; 