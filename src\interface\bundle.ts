export interface BundlePackage {
	name: string;
	version: string;
	description: string;
	page: string;
	pageRoot: string;
	pageType: string;
	'page-version': number;
	icon: string;
	idx: number;
	owner: number;
	owner_name: string;
	is_official: boolean;
	debug: boolean;
	dependencies: Record<string, string>;
	'release-note'?: Record<string, string>;
	'name:ko'?: string;
	'description:ko'?: string;
	main?: string;
	stp?: {
		domain?: string;
		file?: string;
	};
}

// 타입 중복 방지를 위한 추가 타입 정의
export type BundleInfo = BundlePackage;

// 샘플 번들 패키지 객체 (값으로 사용 가능)
export const EmptyBundlePackage: BundlePackage = {
	name: '',
	version: '',
	description: '',
	page: '',
	pageRoot: '',
	pageType: '',
	'page-version': 0,
	icon: '',
	idx: 0,
	owner: 0,
	owner_name: '',
	is_official: false,
	debug: false,
	dependencies: {},
};

// 기본 내보내기 - 인터페이스를 타입으로 내보냄
export default EmptyBundlePackage;
