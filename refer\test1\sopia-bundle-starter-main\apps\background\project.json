{"name": "background", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/background/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/background", "main": "apps/background/src/index.ts", "tsConfig": "apps/background/tsconfig.lib.json", "assets": ["apps/background/*.md"]}}}}