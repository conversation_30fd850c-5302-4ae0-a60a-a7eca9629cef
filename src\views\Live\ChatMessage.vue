<!--
 * ChatMessage.vue
 * Created on Mon Oct 12 2020
 *
 * Copyright (c) TreeSome. Licensed under the MIT License.
-->
<template>
	<div class="chat-message">
		<!-- 메시지와 스티커 이벤트 -->
		<div v-if="evt.event === LiveEvent.LIVE_MESSAGE || evt.event === LiveEvent.LIVE_PRESENT" 
			:class="['chat-row', {'continued-message': isContinuedMessage}]">
			<!-- 프로필 아바타 - 연속 메시지가 아닐 때만 표시 -->
			<div v-if="!isContinuedMessage" class="chat-avatar" @click="$assign('/user/' + evt.data.user.id)">
				<img :src="profileURL" alt="Profile" class="avatar-img">
			</div>
			<div v-else class="avatar-spacer"></div>
			
			<!-- 유저 정보 및 메시지 -->
			<div class="chat-content-wrapper">
				<!-- 유저 이름 - 연속 메시지가 아닐 때만 표시 -->
				<div v-if="!isContinuedMessage" class="user-name-container">
					<div class="badge-and-name">
						<span v-if="isDJ" class="dj-tag">DJ</span>
						<span v-if="isManager && !isDJ" class="manager-tag">매니저</span>
						<span class="name-text">{{ author.nickname }}</span>
					</div>
				</div>
				
				<!-- 메시지 버블 -->
				<div 
					:class="['message-bubble', {
						'dj-message': isDJ, 
						'manager-message': isManager && !isDJ
					}]"
					@click="showBlockConfirm(evt.data.user.id)">
					<div v-if="evt.event === LiveEvent.LIVE_MESSAGE" class="message-text">
						<pre v-html="message"></pre>
					</div>
					<div v-else-if="evt.event === LiveEvent.LIVE_PRESENT" class="sticker-content">
						<img :src="stickerImg" class="sticker-img">
						<div class="sticker-amount">
							{{ evt.data.amount }}{{ $t('spoon') }}
							<span v-if="evt.data.combo > 1" class="combo-text">X {{ evt.data.combo }}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- 알림 메시지들 (기존 코드와 동일하게 유지) -->
		<p
			class="indigo--text text--lighten-4 font-weight-bold mt-4 notification-message"
			style="overflow-wrap: anywhere; padding: 8px 12px; background: rgba(63, 81, 181, 0.05); border-radius: 8px; display: inline-block;"
			v-else-if="evt.event === LiveEvent.LIVE_JOIN">
			{{ author.nickname }}{{ $t('lives.notice.join') }}
		</p>
		<p
			class="indigo--text text--lighten-4 font-weight-bold mt-4 notification-message no-animation"
			style="overflow-wrap: anywhere; padding: 8px 12px; background: rgba(63, 81, 181, 0.05); border-radius: 8px; display: inline-block; text-align: center; width: 100%;"
			v-else-if="evt.event === LiveEvent.LIVE_LIKE">
			<v-icon small color="purple darken-1" class="mr-1">mdi-heart</v-icon>
			{{ author.nickname }}{{ $t('lives.notice.like') }}
			<v-icon small color="purple darken-1" class="ml-1">mdi-heart</v-icon>
		</p>
		<p
			class="red--text text--lighten-2 font-weight-bold mt-4 notification-message"
			style="overflow-wrap: anywhere; padding: 8px 12px; background: rgba(244, 67, 54, 0.05); border-radius: 8px; display: inline-block;"
			v-else-if="evt.event === LiveEvent.LIVE_BLOCK">
			{{ author.nickname }}{{ $t('lives.notice.block') }}
		</p>
		<p
			class="red--text text--lighten-2 font-weight-bold mt-4 notification-message"
			style="overflow-wrap: anywhere; padding: 8px 12px; background: rgba(244, 67, 54, 0.05); border-radius: 8px; display: inline-block;"
			v-else-if="evt.event === LiveEvent.LIVE_COMMAND && evt.detail.command === 'chat'">
			{{ evt.detail.user.nickname }}{{ $t('lives.notice.chatban-' + evt.detail.state) }}
		</p>
	</div>
</template>
<script lang="ts">
import { Component, Prop, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { LiveEvent as EventList, User } from '@sopia-bot/core';

const URL_REGIX = /(https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*))/gi;

@Component
export default class ChatMessage extends Mixins(GlobalMixins) {
	@Prop(Object) public evt: any;
	@Prop({ type: Number, default: 0 }) public index!: number;
	@Prop({ type: Array, default: () => [] }) public messages!: any[];
	@Prop({ type: Array, default: () => [] }) public managerIds!: number[];
	public LiveEvent = EventList;
	public defaultProfileUrl = require('assets/default-profile.png');
	private lastClickTime: number = 0;

	get author(): User {
		return this.evt.data.user || this.evt.data.author;
	}

	get profileURL() {
		return this.evt.data.user?.profile_url || this.defaultProfileUrl;
	}

	// DJ 여부 확인
	get isDJ() {
		// 라이브 방송의 DJ가 메시지를 보냈는지 확인
		return this.evt.data.user && this.evt.data.live && 
			this.evt.data.user.id === this.evt.data.live.author?.id;
	}
	
	// 매니저 여부 확인
	get isManager() {
		if (!this.evt.data.user) return false;
		
		// 사용자 ID 가져오기
		const userId = this.evt.data.user.id;
		
		// 1. props로 전달받은 managerIds 확인 (Player에서 전달)
		if (Array.isArray(this.managerIds) && this.managerIds.length > 0) {
			return this.managerIds.includes(userId);
		}
		
		// 2. 이벤트 데이터에서 직접 확인
		if (this.evt.data.live && Array.isArray(this.evt.data.live.manager_ids)) {
			return this.evt.data.live.manager_ids.includes(userId);
		}
		
		return false;
	}
	
	// Tamm 앱 사용자 여부 확인
	get isTammUser() {
		if (!this.evt.data.user) return false;
		
		// 앱에서 보낸 메시지인지 확인
		// 사용자 태그에 'tamm' 포함 또는 메시지 메타데이터에 source: 'tamm' 포함 여부 확인
		const hasTag = this.evt.data.user.tag && this.evt.data.user.tag.toLowerCase().includes('tamm');
		const hasSource = this.evt.data.source === 'tamm' || 
						(this.evt.update_component && this.evt.update_component.source === 'tamm');
						
		// 로그인한 사용자가 자신의 메시지인지도 확인
		const isSelfMessage = this.evt.data.user.id === this.$store.getters.user?.id;
		
		return hasTag || hasSource || isSelfMessage;
	}
	
	// 이전 메시지와 동일한 사용자인지 확인
	get isContinuedMessage() {
		// 첫 번째 메시지는 항상 새로운 사용자 (이전 메시지가 없음)
		if (this.index === 0) {
			return false;
		}
		
		// 배열 범위 체크
		if (!this.messages || this.index >= this.messages.length || !this.evt) {
			return false;
		}
		
		const prevEvt = this.messages[this.index - 1];
		if (!prevEvt || !prevEvt.data || !this.evt.data) {
			return false;
		}
		
		// 이전 메시지가 일반 메시지나 스티커가 아니면 연속된 메시지가 아님
		if (prevEvt.event !== this.LiveEvent.LIVE_MESSAGE && 
			prevEvt.event !== this.LiveEvent.LIVE_PRESENT) {
			return false;
		}
		
		// 현재 메시지 및 이전 메시지 사용자 ID
		const prevAuthorId = (prevEvt.data.user && prevEvt.data.user.id) || 
							(prevEvt.data.author && prevEvt.data.author.id) || 0;
		
		const currentAuthorId = (this.evt.data.user && this.evt.data.user.id) || 
							  (this.evt.data.author && this.evt.data.author.id) || 0;
		
		// 메시지 사이 타임스탬프 확인 (가능한 경우)
		let isWithinTimeLimit = true;
		if (prevEvt.timestamp && this.evt.timestamp) {
			// 5분(300초) 이내의 메시지만 연속으로 처리
			const timeDiff = this.evt.timestamp - prevEvt.timestamp;
			isWithinTimeLimit = timeDiff < 300000; // 5분 = 300000ms
		}
		
		// ID가 유효하고 동일한지 확인
		const isSameAuthor = prevAuthorId > 0 && currentAuthorId > 0 && prevAuthorId === currentAuthorId;
		
		return isSameAuthor && isWithinTimeLimit;
	}

	public blockUser(id: number) {
		this.$evt.$emit('live-block', id);
	}

	public showBlockConfirm(id: number) {
		// 클릭 지점 기록 (연속 클릭 방지용)
		const now = Date.now();
		if (now - this.lastClickTime < 300) {
			return; // 더블 클릭 방지
		}
		this.lastClickTime = now;
		
		// 차단 확인 다이얼로그 표시
		this.$swal({
			title: `<span class="block-title">${this.$t('lives.block')}</span>`,
			html: `
				<div class="block-confirm-content">
					<div class="block-avatar-wrapper">
						<img src="${this.profileURL}" class="block-avatar-img">
					</div>
					<p class="block-username">${this.author.nickname}</p>
					<p class="block-message">${this.$t('lives.block-user')}</p>
				</div>
			`,
			showCancelButton: true,
			confirmButtonText: this.$t('confirm'),
			cancelButtonText: this.$t('cancel'),
			confirmButtonColor: '#F44336',
			cancelButtonColor: '#757575',
			buttonsStyling: true,
			customClass: {
				popup: 'block-popup',
				title: 'block-popup-title',
				htmlContainer: 'block-popup-content',
				confirmButton: 'block-confirm-btn',
				cancelButton: 'block-cancel-btn'
			},
			showCloseButton: false,  // 닫기 버튼 제거
			focusConfirm: false,
			backdrop: true,
			allowOutsideClick: true,
			iconHtml: '',  // 아이콘 제거
			showClass: {
				popup: 'animated fadeIn faster'
			},
			hideClass: {
				popup: 'animated fadeOut faster'
			}
		}).then((result) => {
			if (result.isConfirmed) {
				this.blockUser(id);
			}
		});
	}

	public escapeHtml(unsafe) {
		return unsafe
			.replace(/&/g, "&amp;")
			.replace(/</g, "&lt;")
			.replace(/>/g, "&gt;")
			.replace(/"/g, "&quot;")
			.replace(/'/g, "&#039;");
	}

	get stickerImg() {
		return this.$sopia.sticker.findSticker(this.evt.data.sticker)?.image_thumbnail;
	}

	get message(): string {
		let msg = this.escapeHtml(this.evt.update_component.message.value as string);

		const m = msg.match(URL_REGIX);
		if ( m ) {
		for ( const url of m ) {
			msg = msg.replace(url, `<a href="${url}" target="_blank" class="indigo--text text--lighten-2">${url}</a>`);
		}
		}
		return msg;
	}
}
</script>
<style>
.chat-message {
	font-family: JoyPixels, GangwonEdu_OTFBoldA, sans-serif !important;
	font-size: 0.9rem;
	margin: 0;
	padding: 0;
	width: 100%;
	box-sizing: border-box;
	max-width: 450px; /* 전체 채팅 너비 제한 늘림 */
}

.chat-row {
	display: flex;
	margin-bottom: 8px;
	padding: 0 6px;
	width: 100%;
	box-sizing: border-box;
	max-width: none; /* 제한 제거 */
}

.chat-avatar {
	width: 36px;
	height: 36px;
	border-radius: 50%;
	overflow: hidden;
	flex-shrink: 0;
	cursor: pointer;
	border: 2px solid rgba(255, 255, 255, 0.2);
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
	margin-right: 8px;
}

.avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 연속 메시지를 위한 스타일 */
.continued-message {
	margin-top: -2px;
	margin-bottom: 2px;
}

.avatar-spacer {
	width: 36px;
	flex-shrink: 0;
	margin-right: 8px;
}

.chat-content-wrapper {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-width: 0;
	width: auto; /* 자동 너비 */
	max-width: none; /* 제한 제거 */
}

/* 유저 이름 컨테이너 */
.user-name-container {
	margin-bottom: 2px;
	padding-left: 4px;
	width: 100%;
	max-width: none; /* 제한 제거 */
}

/* 뱃지와 이름을 함께 표시하는 컨테이너 */
.badge-and-name {
	display: flex;
	align-items: flex-start; /* 상단 정렬 변경 */
	flex-wrap: wrap;
	width: auto; /* 자동 너비 */
	max-width: none; /* 제한 제거 */
}

/* 태그 스타일 */
.dj-tag, .manager-tag {
	margin-right: 5px;
	flex-shrink: 0;
	align-self: flex-start; /* 상단 정렬 */
}

/* 이름 텍스트 */
.name-text {
	color: white;
	font-weight: 500;
	font-size: 0.85rem;
	word-break: break-word;
	flex: 1; /* 공간 차지 */
	min-width: 0; /* flex 아이템 오버플로우 방지 */
	width: auto; /* 자동 너비 */
	max-width: none; /* 제한 제거 */
}

.message-bubble {
	display: inline-block;
	background: rgba(30, 30, 30, 0.35);
	border-radius: 18px;
	padding: 6px 12px;
	margin-top: 2px;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
	border: thin solid rgba(255, 255, 255, 0.08);
	cursor: pointer;
	align-self: flex-start;
	transition: background 0.2s ease;
	max-width: 100%;
	will-change: transform, box-shadow, border;
	transform: translateZ(0);
	-webkit-font-smoothing: subpixel-antialiased;
	backface-visibility: hidden;
}

.message-bubble:hover {
	background: rgba(40, 40, 40, 0.45);
}

.message-text {
	color: white;
	white-space: pre-wrap;
	word-break: break-word;
	line-height: 1.2;
}

.message-text pre {
	margin: 0;
	font-family: inherit;
	font-size: inherit;
	white-space: pre-wrap;
}

.dj-tag {
	background-color: #ff5722;
	color: white;
	font-size: 0.7rem;
	font-weight: 700;
	padding: 3px 8px;
	border-radius: 12px;
	display: inline-block;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
	text-transform: uppercase;
	letter-spacing: 0.5px;
	animation: pulse 2s infinite;
	white-space: nowrap;
}

.dj-message {
	border: 2px solid rgba(255, 87, 34, 0.6) !important;
	box-shadow: 0 2px 12px rgba(255, 87, 34, 0.15) !important;
	/* 렌더링 안정화를 위한 속성 추가 */
	position: relative;
	z-index: 1;
}

.sticker-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.sticker-img {
	max-width: 80px;
	max-height: 80px;
	margin-bottom: 4px;
}

.sticker-amount {
	font-size: 0.8rem;
	font-weight: 500;
	color: white;
}

.combo-text {
	font-weight: bold;
	color: #9fa8da;
}

.notification-message {
	transition: all 0.2s ease;
	animation: fadeIn 0.3s ease-in;
}

@keyframes pulse {
	0% { opacity: 1; }
	50% { opacity: 0.8; }
	100% { opacity: 1; }
}

@keyframes fadeIn {
	from { opacity: 0; transform: translateY(5px); }
	to { opacity: 1; transform: translateY(0); }
}

/* 강제퇴장 알림창 스타일은 유지 */
.block-popup {
	background-color: #1e1e1e !important;
	border-radius: 12px !important;
	box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.08) !important;
	backdrop-filter: blur(10px) !important;
}

.block-popup-title {
	color: #ffffff !important;
	font-size: 1.3rem !important;
	font-weight: 600 !important;
	padding-top: 1.5rem !important;
}

.block-title {
	color: #F44336;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.block-confirm-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: 20px 0;
	padding: 0 10px;
}

.block-avatar-wrapper {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	overflow: hidden;
	border: 3px solid #F44336;
	box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
	margin-bottom: 15px;
}

.block-avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.block-username {
	font-size: 1.2rem;
	font-weight: 600;
	color: #ffffff;
	margin: 5px 0;
}

.block-message {
	font-size: 0.95rem;
	color: rgba(255, 255, 255, 0.8);
	margin: 5px 0 15px;
	text-align: center;
}

.block-confirm-btn {
	border-radius: 8px !important;
	font-weight: 600 !important;
	padding: 10px 24px !important;
	letter-spacing: 0.5px !important;
	margin-bottom: 10px !important;
}

.block-cancel-btn {
	border-radius: 8px !important;
	font-weight: 500 !important;
	padding: 10px 24px !important;
	letter-spacing: 0.5px !important;
	margin-bottom: 10px !important;
}

/* Tamm 태그 스타일 */
.tamm-tag {
	background-color: #6200ea;
	color: white;
	font-size: 0.7rem;
	font-weight: 700;
	padding: 3px 8px;
	border-radius: 12px;
	margin-right: 5px;
	display: inline-block;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

/* Tamm 메시지 스타일 - 보라색 테두리 */
.tamm-message {
	border: 2px solid rgba(98, 0, 234, 0.6) !important;
	box-shadow: 0 2px 12px rgba(98, 0, 234, 0.15) !important;
}

/* 알림 메시지에서 애니메이션을 제거한 버전 */
.no-animation {
	transition: none !important;
	animation: none !important;
}

/* 또는 더 부드러운 애니메이션으로 대체 가능 */
@keyframes smoothFadeIn {
	from { opacity: 0.8; }
	to { opacity: 1; }
}

/* 매니저 태그 스타일 */
.manager-tag {
	background-color: #1565c0;
	color: white;
	font-size: 0.7rem;
	font-weight: 700;
	padding: 3px 8px;
	border-radius: 12px;
	display: inline-block;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
	text-transform: uppercase;
	letter-spacing: 0.5px;
	white-space: nowrap;
}

/* 매니저 메시지 스타일 - 파란색 테두리 */
.manager-message {
	border: 2px solid rgba(21, 101, 192, 0.6) !important;
	box-shadow: 0 2px 12px rgba(21, 101, 192, 0.15) !important;
	/* 렌더링 안정화를 위한 속성 추가 */
	position: relative;
	z-index: 1;
}

/* 스크롤바 숨기기 */
::-webkit-scrollbar {
	width: 0px;
	background: transparent;
}

/* Firefox */
* {
	scrollbar-width: none;
}

/* IE 및 Edge */
* {
	-ms-overflow-style: none;
}
</style>