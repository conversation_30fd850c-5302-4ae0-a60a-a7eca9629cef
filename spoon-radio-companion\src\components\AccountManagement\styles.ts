import styled from 'styled-components';

// 메인 컨테이너
export const Container = styled.div`
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
  background: #fafafa;
  min-height: 100vh;
`;

export const Header = styled.div`
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
`;

export const HeaderLeft = styled.div``;

export const Title = styled.h1`
  color: #1a1a1a;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

export const Subtitle = styled.p`
  color: #64748b;
  font-size: 16px;
  font-weight: 400;
`;

// 검색 및 필터
export const SearchContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
`;

export const SearchInput = styled.input`
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  width: 280px;
  transition: all 0.3s ease;
  background: white;

  &:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
  }

  &::placeholder {
    color: #94a3b8;
  }
`;

export const FilterSelect = styled.select`
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
  }
`;

// 통계 섹션
export const TabContainer = styled.div`
  margin-bottom: 32px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
`;

export const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 24px;
`;

export const StatCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
`;

export const StatNumber = styled.h3`
  font-size: 24px;
  font-weight: 600;
  color: #7c3aed;
  margin-bottom: 8px;
`;

export const StatLabel = styled.p`
  color: #666;
  font-size: 14px;
  margin: 0;
`;

// 카드 레이아웃
export const CardsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
`;

export const UserCard = styled.div`
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #7c3aed, #a855f7);
  }
`;

export const CardHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
`;

export const ProfileImage = styled.img`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #f1f5f9;
  background: #f8fafc;
`;

export const DefaultProfileIcon = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
`;

export const UserMainInfo = styled.div`
  flex: 1;
`;

export const SpoonNickname = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const SpoonTag = styled.span`
  font-size: 14px;
  color: #7c3aed;
  font-weight: 500;
  background: rgba(124, 58, 237, 0.1);
  padding: 2px 8px;
  border-radius: 6px;
`;

export const CardBody = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
`;

export const InfoSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const InfoLabel = styled.span`
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const InfoValue = styled.span`
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
`;

// 배지 컴포넌트
export const StatusBadge = styled.span<{ status: string }>`
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  ${props => {
    switch (props.status) {
      case 'active':
        return 'background: #dcfce7; color: #166534; border: 1px solid #bbf7d0;';
      case 'inactive':
        return 'background: #fef3c7; color: #92400e; border: 1px solid #fde68a;';
      case 'suspended':
        return 'background: #fecaca; color: #991b1b; border: 1px solid #fca5a5;';
      case 'linked':
        return 'background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd;';
      case 'unlinked':
        return 'background: #f1f5f9; color: #475569; border: 1px solid #cbd5e1;';
      default:
        return 'background: #f1f5f9; color: #475569; border: 1px solid #cbd5e1;';
    }
  }}

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
  }
`;

export const RoleBadge = styled.span<{ role: string }>`
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  ${props => props.role === 'admin' 
    ? 'background: #ede9fe; color: #6b21a8; border: 1px solid #c4b5fd;'
    : 'background: #f1f5f9; color: #475569; border: 1px solid #cbd5e1;'
  }

  &::before {
    content: '${props => props.role === 'admin' ? '👑' : '👤'}';
    font-size: 10px;
  }
`;

export const ExpiryBadge = styled.span<{ isExpired: boolean }>`
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  ${props => props.isExpired 
    ? 'background: #fecaca; color: #991b1b; border: 1px solid #fca5a5;'
    : 'background: #dcfce7; color: #166534; border: 1px solid #bbf7d0;'
  }

  &::before {
    content: '${props => props.isExpired ? '⚠️' : '✅'}';
    font-size: 10px;
  }
`;

// 액션 버튼
export const CardActions = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
`;

export const ActionButton = styled.button<{ variant: 'view' | 'delete' | 'unlink' | 'link' | 'token' }>`
  padding: 8px 16px;
  border: none;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  ${props => {
    switch (props.variant) {
      case 'view':
        return `
          background: #ede9fe;
          color: #6b21a8;
          &:hover { background: #ddd6fe; transform: translateY(-1px); }
        `;
      case 'token':
        return `
          background: #dbeafe;
          color: #1e40af;
          &:hover { background: #bfdbfe; transform: translateY(-1px); }
        `;
      case 'delete':
        return `
          background: #fecaca;
          color: #991b1b;
          &:hover { background: #fca5a5; transform: translateY(-1px); }
        `;
      case 'unlink':
        return `
          background: #fef3c7;
          color: #92400e;
          &:hover { background: #fde68a; transform: translateY(-1px); }
        `;
      case 'link':
        return `
          background: #dcfce7;
          color: #166534;
          &:hover { background: #bbf7d0; transform: translateY(-1px); }
        `;
      default:
        return `
          background: #f1f5f9;
          color: #475569;
          &:hover { background: #e2e8f0; transform: translateY(-1px); }
        `;
    }
  }}
`;

// 빈 상태
export const EmptyState = styled.div`
  text-align: center;
  padding: 80px 20px;
  color: #64748b;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
`;

export const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
`;

export const EmptyText = styled.p`
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: 500;
`;

// 모달
export const Modal = styled.div<{ isOpen: boolean }>`
  display: ${props => props.isOpen ? 'flex' : 'none'};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: ${props => props.isOpen ? 'fadeIn 0.3s ease' : 'none'};

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
`;

export const ModalContent = styled.div`
  background: white;
  border-radius: 24px;
  padding: 0;
  max-width: 700px;
  width: 90%;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: slideUp 0.3s ease;

  @keyframes slideUp {
    from { 
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to { 
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
`;

export const ModalHeader = styled.div`
  padding: 32px 32px 0;
  border-bottom: 1px solid #f1f5f9;
`;

export const ModalTitle = styled.h2`
  color: #1a1a1a;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

export const ModalBody = styled.div`
  padding: 32px;
`;

export const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;

  &:last-child {
    border-bottom: none;
  }
`;

export const DetailLabel = styled.span`
  font-weight: 600;
  color: #64748b;
  min-width: 140px;
  font-size: 14px;
`;

export const DetailValue = styled.span`
  color: #1a1a1a;
  flex: 1;
  text-align: right;
  word-break: break-all;
  font-size: 14px;
  font-weight: 500;
`;

export const ModalActions = styled.div`
  padding: 20px 32px 32px;
  border-top: 1px solid #f1f5f9;
  display: flex;
  gap: 16px;
  justify-content: flex-end;
`;

export const ModalButton = styled.button<{ variant: 'primary' | 'secondary' | 'danger' }>`
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #7c3aed, #a855f7);
          color: white;
          &:hover { 
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(124, 58, 237, 0.25);
          }
        `;
      case 'secondary':
        return `
          background: #f1f5f9;
          color: #475569;
          &:hover { 
            background: #e2e8f0;
            transform: translateY(-2px);
          }
        `;
      case 'danger':
        return `
          background: #ef4444;
          color: white;
          &:hover { 
            background: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.25);
          }
        `;
      default:
        return `
          background: #f1f5f9;
          color: #475569;
          &:hover { 
            background: #e2e8f0;
            transform: translateY(-2px);
          }
        `;
    }
  }}
`;

export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px;
  color: #64748b;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
`;

// 토큰 관련 스타일
export const TokenSection = styled.div`
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  background: white;
`;

export const TokenSectionHeader = styled.div`
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 16px 20px;
  font-weight: 600;
  color: #1a1a1a;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
`;

export const TokenSectionContent = styled.div`
  padding: 20px;
`;

export const JWTField = styled.div`
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

export const JWTLabel = styled.div`
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const JWTValue = styled.div`
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  word-break: break-all;
  white-space: pre-wrap;
`;

export const ErrorMessage = styled.div`
  background: #fecaca;
  color: #991b1b;
  padding: 16px;
  border-radius: 12px;
  font-size: 14px;
  margin-bottom: 16px;
  border: 1px solid #fca5a5;
`;

// 테이블 형식 스타일
export const TableContainer = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 24px;
  overflow-x: auto;
  min-width: 100%;
`;

export const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 1000px;
`;

export const TableHeader = styled.thead`
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
`;

export const TableHeaderRow = styled.tr``;

export const TableHeaderCell = styled.th`
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
  min-width: fit-content;
`;

export const TableBody = styled.tbody``;

export const TableRow = styled.tr<{ isLinked?: boolean }>`
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
  background: ${props => props.isLinked ? '#f0f9ff' : 'white'};
  
  &:hover {
    background: ${props => props.isLinked ? '#e0f2fe' : '#f8fafc'};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

export const TableCell = styled.td`
  padding: 12px;
  font-size: 14px;
  color: #374151;
  vertical-align: middle;
  border-bottom: 1px solid #f1f5f9;
  white-space: nowrap;
  
  &:nth-child(2) {
    min-width: 80px;
    max-width: 100px;
    white-space: normal;
  }
  
  &:nth-child(5) {
    min-width: 180px;
    white-space: normal;
  }
  
  &:nth-child(6) {
    min-width: 120px;
  }
  
  &:nth-child(7) {
    min-width: 200px;
  }
`;

export const TypeBadge = styled.span<{ type: 'user' | 'token' }>`
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => props.type === 'user' ? '#dbeafe' : '#fef3c7'};
  color: ${props => props.type === 'user' ? '#1e40af' : '#92400e'};
`;

export const ProfileCell = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const ProfileImageSmall = styled.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
`;

export const DefaultProfileIconSmall = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border: 2px solid #e2e8f0;
`;

export const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

export const UserName = styled.div`
  font-weight: 500;
  color: #111827;
`;

export const UserEmail = styled.div`
  font-size: 12px;
  color: #6b7280;
`;

export const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

export const ActionButtonSmall = styled.button<{ variant: 'view' | 'token' | 'unlink' | 'delete' }>`
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  
  ${props => {
    switch (props.variant) {
      case 'view':
        return `
          background: #3b82f6;
          color: white;
          &:hover { background: #2563eb; }
        `;
      case 'token':
        return `
          background: #8b5cf6;
          color: white;
          &:hover { background: #7c3aed; }
        `;
      case 'unlink':
        return `
          background: #f59e0b;
          color: white;
          &:hover { background: #d97706; }
        `;
      case 'delete':
        return `
          background: #ef4444;
          color: white;
          &:hover { background: #dc2626; }
        `;
      default:
        return `
          background: #6b7280;
          color: white;
          &:hover { background: #4b5563; }
        `;
    }
  }}
`;

export const EmptyRow = styled.tr`
  td {
    padding: 48px;
    text-align: center;
    color: #6b7280;
    font-style: italic;
  }
`; 