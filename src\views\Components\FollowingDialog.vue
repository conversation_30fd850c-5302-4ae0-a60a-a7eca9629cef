<!--
 * FollowingDialog.vue
 * 사용자의 팔로잉 목록을 보여주는 다이얼로그 컴포넌트
-->
<template>
  <div>
    <v-dialog
      v-model="isOpen"
      max-width="450px"
      scrollable
      content-class="following-dialog"
    >
      <v-card class="elevation-2 rounded-lg">
        <v-card-title class="headline pa-4 purple lighten-5 d-flex align-center">
          <div class="d-flex align-center">
            <v-icon size="20" color="purple darken-1" class="mr-2">mdi-account-multiple</v-icon>
            <span class="text-subtitle-1 font-weight-medium">내 팔로잉 {{ followingList.length }}명</span>
          </div>
          <v-spacer></v-spacer>
          <v-btn icon x-small @click="close">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider></v-divider>
        
        <v-card-text class="pa-0 custom-scrollbar" @scroll="onScroll">
          <v-list class="py-0">
            <div v-if="isLoading" class="d-flex flex-column align-center py-5">
              <v-progress-circular indeterminate color="purple"></v-progress-circular>
              <div class="mt-3 grey--text">팔로잉 목록을 불러오는 중...</div>
            </div>
            
            <div v-else-if="followingList.length === 0" class="d-flex flex-column align-center py-5">
              <v-icon size="64" color="grey lighten-2">mdi-account-multiple-outline</v-icon>
              <div class="mt-3 grey--text text--darken-1">팔로잉 중인 사용자가 없습니다.</div>
            </div>
            
            <template v-else>
              <div v-for="(user, idx) in followingList" :key="user.id" class="user-item">
                <div @click="goToUserProfile(user.id)" class="d-flex align-center px-3 py-2 user-row">
                  <div class="user-avatar-container position-relative mr-3">
                    <v-avatar size="40" class="live-avatar-container" :class="{'live-profile-border': user.current_live}">
                      <v-img :src="user.profile_url" alt="프로필 이미지"></v-img>
                    </v-avatar>
                    <div v-if="user.current_live" class="live-badge-circle">
                      <span class="live-text">LIVE</span>
                    </div>
                  </div>
                  
                  <div class="user-info">
                    <div class="d-flex align-center">
                      <div class="user-name font-weight-medium">{{ user.nickname }}</div>
                    </div>
                    <div class="user-tag grey--text text--darken-1 caption">@{{ user.tag }}</div>
                  </div>
                  
                  <div class="time-info caption font-weight-bold ml-2">
                    <v-chip v-if="user.current_live" x-small color="red" text-color="white" class="live-chip">
                      Live
                    </v-chip>
                    <span v-else>{{ getTimeAgo(user.last_live_created || user.date_joined) }}</span>
                  </div>
                  
                  <v-spacer></v-spacer>
                  
                  <div class="follower-stats d-flex align-center">
                    <div class="follower-count text-center mr-3">
                      <div class="font-weight-bold counts-text">{{ user.follower_count || 0 }}</div>
                      <div class="caption grey--text">팬</div>
                    </div>
                    <div class="following-count text-center" @click.stop="openUserFollowingList(user)" style="cursor: pointer;">
                      <div class="font-weight-bold counts-text">{{ user.following_count || 0 }}</div>
                      <div class="caption grey--text">팔로잉</div>
                    </div>
                  </div>

                  <div class="ml-2">
                    <v-btn
                      small
                      rounded
                      :color="isFollowing(user) ? 'purple' : 'purple darken-1'"
                      :outlined="isFollowing(user)"
                      :dark="!isFollowing(user)"
                      class="fan-btn"
                      @click.stop="toggleFollow(user)"
                      :loading="user.isLoading"
                    >
                      <v-icon left size="16">{{ isFollowing(user) ? 'mdi-account-check' : 'mdi-account-plus' }}</v-icon>
                      <span>팬</span>
                    </v-btn>
                  </div>
                </div>
                <v-divider v-if="idx < followingList.length - 1" class="mx-3"></v-divider>
              </div>
            </template>
          </v-list>
        </v-card-text>
        
        <v-divider v-if="followingList.length > 0 && isLoadingMore"></v-divider>
        
        <v-card-actions v-if="followingList.length > 0 && isLoadingMore" class="pa-2 d-flex justify-center">
          <v-progress-circular
            indeterminate
            color="purple"
            size="24"
          ></v-progress-circular>
          <span class="ml-2 grey--text">로딩 중...</span>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- 사용자의 팔로잉 목록 다이얼로그 -->
    <v-dialog
      v-model="userFollowingDialog.isOpen"
      max-width="450px"
      scrollable
      content-class="following-dialog"
    >
      <v-card class="elevation-2 rounded-lg">
        <v-card-title class="headline pa-4 purple lighten-5 d-flex align-center">
          <div class="d-flex align-center">
            <v-icon size="20" color="purple darken-1" class="mr-2">mdi-account-multiple</v-icon>
            <span class="text-subtitle-1 font-weight-medium">{{ userFollowingDialog.nickname }} 팔로잉 {{ userFollowingList.length }}명</span>
          </div>
          <v-spacer></v-spacer>
          <v-btn icon x-small @click="closeUserFollowingDialog">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider></v-divider>
        
        <v-card-text class="pa-0 custom-scrollbar" @scroll="onUserFollowingScroll">
          <v-list class="py-0">
            <div v-if="userFollowingDialog.isLoading" class="d-flex flex-column align-center py-5">
              <v-progress-circular indeterminate color="purple"></v-progress-circular>
              <div class="mt-3 grey--text">팔로잉 목록을 불러오는 중...</div>
            </div>
            
            <div v-else-if="userFollowingList.length === 0" class="d-flex flex-column align-center py-5">
              <v-icon size="64" color="grey lighten-2">mdi-account-multiple-outline</v-icon>
              <div class="mt-3 grey--text text--darken-1">팔로잉 중인 사용자가 없습니다.</div>
            </div>
            
            <template v-else>
              <div v-for="(user, idx) in userFollowingList" :key="user.id" class="user-item">
                <div @click="goToUserProfile(user.id)" class="d-flex align-center px-3 py-2 user-row">
                  <div class="user-avatar-container position-relative mr-3">
                    <v-avatar size="40" class="live-avatar-container" :class="{'live-profile-border': user.current_live}">
                      <v-img :src="user.profile_url" alt="프로필 이미지"></v-img>
                    </v-avatar>
                    <div v-if="user.current_live" class="live-badge-circle">
                      <span class="live-text">LIVE</span>
                    </div>
                  </div>
                  
                  <div class="user-info">
                    <div class="d-flex align-center">
                      <div class="user-name font-weight-medium">{{ user.nickname }}</div>
                    </div>
                    <div class="user-tag grey--text text--darken-1 caption">@{{ user.tag }}</div>
                  </div>
                  
                  <div class="time-info caption font-weight-bold ml-2">
                    <v-chip v-if="user.current_live" x-small color="red" text-color="white" class="live-chip">
                      Live
                    </v-chip>
                    <span v-else>{{ getTimeAgo(user.last_live_created || user.date_joined) }}</span>
                  </div>
                  
                  <v-spacer></v-spacer>
                  
                  <div class="follower-stats d-flex align-center">
                    <div class="follower-count text-center mr-3">
                      <div class="font-weight-bold counts-text">{{ user.follower_count || 0 }}</div>
                      <div class="caption grey--text">팬</div>
                    </div>
                    <div class="following-count text-center" @click.stop="openUserFollowingList(user)" style="cursor: pointer;">
                      <div class="font-weight-bold counts-text">{{ user.following_count || 0 }}</div>
                      <div class="caption grey--text">팔로잉</div>
                    </div>
                  </div>

                  <div class="ml-2">
                    <v-btn
                      small
                      rounded
                      :color="isFollowing(user) ? 'purple' : 'purple darken-1'"
                      :outlined="isFollowing(user)"
                      :dark="!isFollowing(user)"
                      class="fan-btn"
                      @click.stop="toggleFollow(user)"
                      :loading="user.isLoading"
                    >
                      <v-icon left size="16">{{ isFollowing(user) ? 'mdi-account-check' : 'mdi-account-plus' }}</v-icon>
                      <span>팬</span>
                    </v-btn>
                  </div>
                </div>
                <v-divider v-if="idx < userFollowingList.length - 1" class="mx-3"></v-divider>
              </div>
            </template>
          </v-list>
        </v-card-text>
        
        <v-divider v-if="userFollowingList.length > 0 && userFollowingDialog.isLoadingMore"></v-divider>
        
        <v-card-actions v-if="userFollowingList.length > 0 && userFollowingDialog.isLoadingMore" class="pa-2 d-flex justify-center">
          <v-progress-circular
            indeterminate
            color="purple"
            size="24"
          ></v-progress-circular>
          <span class="ml-2 grey--text">로딩 중...</span>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Prop, Watch } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { User } from '@sopia-bot/core';

// User 타입을 확장한 인터페이스 정의
interface FollowingUser {
  id: number;
  nickname: string;
  tag: string;
  profile_url: string;
  last_live_created?: string;
  date_joined?: string;
  current_live?: {
    id: number;
  } | null;
  follower_count?: number;
  following_count?: number;
  gender?: number;
  description?: string;
  isLoading?: boolean;
  follow_status?: number;
}

@Component
export default class FollowingDialog extends Mixins(GlobalMixins) {
  public isLoading: boolean = true;
  public isLoadingMore: boolean = false;
  public followingList: FollowingUser[] = [];
  public hasMoreFollowing: boolean = false;
  public followingManager: any = null;
  
  // 사용자 팔로잉 관련 속성 추가
  public userFollowingList: FollowingUser[] = [];
  public userFollowingDialog = {
    isOpen: false,
    isLoading: false,
    isLoadingMore: false,
    hasMore: false,
    userId: 0,
    nickname: '',
    followingManager: null as any
  };

  public get isOpen(): boolean {
    return this.$store.state.followingDialogOpen;
  }

  public set isOpen(value: boolean) {
    if (!value) {
      this.close();
    }
  }

  @Watch('isOpen')
  onDialogOpen(newVal: boolean) {
    if (newVal) {
      this.loadFollowing();
    }
  }

  // 프로필 페이지에서 호출된 사용자별 팔로잉 다이얼로그 감시
  @Watch('userFollowingDialogOpen')
  onUserFollowingDialogOpen(newVal: boolean) {
    console.log('userFollowingDialogOpen 변경됨:', newVal);
    console.log('사용자 정보:', this.userFollowingInfo);
    
    if (newVal) {
      this.openProfileUserFollowingList();
    }
  }

  // 프로필 페이지에서 호출된 사용자 팔로잉 정보 가져오기
  public get userFollowingDialogOpen(): boolean {
    return this.$store.state.userFollowingDialogOpen;
  }

  public get userFollowingInfo(): { userId: number; nickname: string } {
    return this.$store.state.userFollowingInfo;
  }

  public async loadFollowing() {
    this.isLoading = true;
    this.followingList = [];
    this.followingManager = null;
    
    try {
      // 스푼라디오 API를 통해 팔로잉 목록 가져오기
      const userId = this.$store.getters.user.id;
      if (!userId) {
        console.error('사용자 ID를 가져올 수 없습니다.');
        return;
      }

      // 팔로잉 API 요청
      this.followingManager = await this.$sopia.api.request({
        url: `/users/${userId}/followings/`,
        method: 'GET',
      });
      
      if (this.followingManager && this.followingManager.res && this.followingManager.res.results) {
        // 각 사용자에게 isLoading 속성 추가
        this.followingList = this.followingManager.res.results.map((user: FollowingUser) => ({
          ...user,
          isLoading: false
        }));
        this.hasMoreFollowing = !!this.followingManager.res.next;
      }
    } catch (error) {
      console.error('팔로잉 목록을 가져오는 중 오류 발생:', error);
    } finally {
      this.isLoading = false;
    }
  }

  public async loadMoreFollowing() {
    if (!this.followingManager || this.isLoadingMore) return;
    
    this.isLoadingMore = true;
    
    try {
      const res = await this.followingManager.next();
      if (res && res.results) {
        // 각 사용자에게 isLoading 속성 추가
        const newUsers = res.results.map((user: FollowingUser) => ({
          ...user,
          isLoading: false
        }));
        this.followingList = [...this.followingList, ...newUsers];
        this.hasMoreFollowing = !!res.next;
      }
    } catch (error) {
      console.error('추가 팔로잉 목록을 가져오는 중 오류 발생:', error);
    } finally {
      this.isLoadingMore = false;
    }
  }

  public getTimeAgo(dateStr: string | undefined): string {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    const diffYear = Math.floor(diffDay / 365);
    
    if (diffYear > 0) {
      return `${diffYear}년 전`;
    } else if (diffDay > 0) {
      if (diffDay > 14) {
        return this.formatDate(dateStr);
      }
      return `${diffDay}일 전`;
    } else if (diffHour > 0) {
      return `${diffHour}시간 전`;
    } else if (diffMin > 0) {
      return `${diffMin}분 전`;
    } else {
      return '방금';
    }
  }
  
  public formatDate(dateStr: string | undefined): string {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  }

  public goToUserProfile(userId: number) {
    this.close();
    this.$assign(`/user/${userId}`);
  }

  public goToUserLive(user: FollowingUser) {
    if (user && user.current_live && user.current_live.id) {
      this.close();
      this.$evt.$emit('live-join', user.current_live.id);
    }
  }

  public close() {
    this.$store.commit('setFollowingDialogOpen', false);
  }

  public isFollowing(user: FollowingUser): boolean {
    // follow_status가 1 또는 3이면 팔로우 중인 상태
    return user.follow_status === 1 || user.follow_status === 3;
  }

  public async toggleFollow(user: FollowingUser) {
    // 이미 로딩 중이면 중복 요청 방지
    if (user.isLoading) return;
    
    // 해당 사용자의 로딩 상태를 true로 설정
    const userIndex = this.followingList.findIndex(u => u.id === user.id);
    if (userIndex === -1) return;
    
    // 로딩 상태 변경
    this.$set(this.followingList[userIndex], 'isLoading', true);
    
    try {
      const isCurrentlyFollowing = this.isFollowing(user);
      const action = isCurrentlyFollowing ? 'unfollow' : 'follow';
      const url = `/users/${user.id}/${action}/`;
      
      const response = await this.$sopia.api.request({
        url,
        method: 'POST',
      });
      
      console.log(`${action} 응답:`, response);
      
      if (response && response.res && response.res.status_code === 200) {
        // 팔로우 상태 변경 성공
        if (isCurrentlyFollowing) {
          // 언팔로우 성공 - follow_status를 0으로 변경
          this.$set(this.followingList[userIndex], 'follow_status', 0);
        } else {
          // 팔로우 성공 - follow_status를 1로 변경
          this.$set(this.followingList[userIndex], 'follow_status', 1);
        }
      }
      
      // 로딩 상태 해제
      this.$set(this.followingList[userIndex], 'isLoading', false);
      
    } catch (error) {
      console.error(`팔로우 상태 변경 중 오류 발생:`, error);
      // 오류 발생 시 로딩 상태 해제
      this.$set(this.followingList[userIndex], 'isLoading', false);
    }
  }

  // 사용자의 팔로잉 목록 다이얼로그 열기
  public async openUserFollowingList(user: FollowingUser) {
    // 기존 다이얼로그가 열려있는지 확인하고 닫기
    if (this.isOpen) {
      this.close();
    }
    
    // 기존 사용자 팔로잉 다이얼로그가 열려있다면 닫기
    if (this.userFollowingDialog.isOpen) {
      this.userFollowingDialog.isOpen = false;
      this.userFollowingList = [];
    }
    
    // 약간의 딜레이 후 새로운 다이얼로그 열기 (애니메이션 충돌 방지)
    setTimeout(() => {
      this.userFollowingDialog.isOpen = true;
      this.userFollowingDialog.userId = user.id;
      this.userFollowingDialog.nickname = user.nickname;
      this.userFollowingDialog.isLoading = true;
      this.userFollowingList = [];
      
      this.loadUserFollowing();
    }, 100);
  }
  
  // 사용자의 팔로잉 목록을 불러오는 로직을 별도 메서드로 분리
  private async loadUserFollowing() {
    try {
      // 사용자의 팔로잉 목록 가져오기
      this.userFollowingDialog.followingManager = await this.$sopia.api.request({
        url: `/users/${this.userFollowingDialog.userId}/followings/`,
        method: 'GET',
      });
      
      if (this.userFollowingDialog.followingManager && 
          this.userFollowingDialog.followingManager.res && 
          this.userFollowingDialog.followingManager.res.results) {
        // 각 사용자에게 isLoading 속성 추가
        this.userFollowingList = this.userFollowingDialog.followingManager.res.results.map((user: FollowingUser) => ({
          ...user,
          isLoading: false
        }));
        this.userFollowingDialog.hasMore = !!this.userFollowingDialog.followingManager.res.next;
      }
    } catch (error) {
      console.error('사용자 팔로잉 목록을 가져오는 중 오류 발생:', error);
    } finally {
      this.userFollowingDialog.isLoading = false;
    }
  }
  
  // 사용자 팔로잉 더 불러오기
  public async loadMoreUserFollowing() {
    if (!this.userFollowingDialog.followingManager || this.userFollowingDialog.isLoadingMore) return;
    
    this.userFollowingDialog.isLoadingMore = true;
    
    try {
      const res = await this.userFollowingDialog.followingManager.next();
      if (res && res.results) {
        // 각 사용자에게 isLoading 속성 추가
        const newUsers = res.results.map((user: FollowingUser) => ({
          ...user,
          isLoading: false
        }));
        this.userFollowingList = [...this.userFollowingList, ...newUsers];
        this.userFollowingDialog.hasMore = !!res.next;
      }
    } catch (error) {
      console.error('추가 사용자 팔로잉 목록을 가져오는 중 오류 발생:', error);
    } finally {
      this.userFollowingDialog.isLoadingMore = false;
    }
  }
  
  // 사용자 팔로잉 다이얼로그 닫기
  public closeUserFollowingDialog() {
    this.userFollowingDialog.isOpen = false;
    this.userFollowingList = [];
    // Vuex 상태 업데이트 (열려있는 경우에만)
    if (this.$store.state.userFollowingDialogOpen) {
      this.$store.commit('setUserFollowingDialogOpen', false);
    }
  }
  
  // 스크롤 이벤트 핸들러 - 내 팔로잉 리스트
  public onScroll(event: Event) {
    const target = event.target as HTMLElement;
    // 스크롤이 하단에 가까워지면 (남은 스크롤 높이가 50px 이하일 때) 추가 데이터 로드
    if (target.scrollHeight - target.scrollTop - target.clientHeight < 50) {
      if (this.hasMoreFollowing && !this.isLoadingMore) {
        this.loadMoreFollowing();
      }
    }
  }
  
  // 스크롤 이벤트 핸들러 - 사용자 팔로잉 리스트
  public onUserFollowingScroll(event: Event) {
    const target = event.target as HTMLElement;
    // 스크롤이 하단에 가까워지면 (남은 스크롤 높이가 50px 이하일 때) 추가 데이터 로드
    if (target.scrollHeight - target.scrollTop - target.clientHeight < 50) {
      if (this.userFollowingDialog.hasMore && !this.userFollowingDialog.isLoadingMore) {
        this.loadMoreUserFollowing();
      }
    }
  }

  // 프로필 페이지에서 요청한 사용자의 팔로잉 목록 표시
  public async openProfileUserFollowingList() {
    console.log('openProfileUserFollowingList 호출됨');
    console.log('사용자 ID:', this.userFollowingInfo.userId);
    console.log('사용자 닉네임:', this.userFollowingInfo.nickname);
    
    this.userFollowingDialog.isOpen = true;
    this.userFollowingDialog.userId = this.userFollowingInfo.userId;
    this.userFollowingDialog.nickname = this.userFollowingInfo.nickname;
    this.userFollowingDialog.isLoading = true;
    this.userFollowingList = [];
    
    // 사용자 팔로잉 로드
    this.loadUserFollowing();
    
    // Vuex 상태 업데이트 (다이얼로그 닫기)
    this.$store.commit('setUserFollowingDialogOpen', false);
  }
}
</script>

<style>
.following-dialog {
  border-radius: 12px;
}

.user-row {
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 60px;
}

.user-row:hover {
  background-color: #f5f5f5;
}

.user-avatar-container {
  position: relative;
}

.live-avatar-container {
  object-fit: cover;
  overflow: hidden;
}

.live-badge-circle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  background-color: #ff4c4c;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.live-text {
  color: white;
  font-size: 6px;
  font-weight: bold;
  text-align: center;
  letter-spacing: 0.5px;
  line-height: 1;
}

.time-info {
  font-size: 11px;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.follower-stats {
  font-size: 11px;
}

.user-name {
  max-width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-tag {
  font-size: 11px;
}

.user-info {
  min-width: 120px;
}

.follower-count,
.following-count {
  min-width: 35px;
}

.counts-text {
  font-size: 14px;
}

.live-profile-border {
  border: 2px solid #ff4c4c !important;
}

.live-avatar-container {
  object-fit: cover;
  overflow: hidden;
}

.live-chip {
  height: 20px !important;
  font-size: 10px !important;
  font-weight: bold;
}

.fan-btn {
  min-width: 40px !important;
  font-size: 12px !important;
  height: 28px !important;
}

/* 사용자 정의 스크롤바 스타일 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 39, 176, 0.5) transparent;
}

/* Chrome, Edge, Safari 브라우저용 스크롤바 스타일 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 39, 176, 0.5);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 39, 176, 0.8);
}
</style> 