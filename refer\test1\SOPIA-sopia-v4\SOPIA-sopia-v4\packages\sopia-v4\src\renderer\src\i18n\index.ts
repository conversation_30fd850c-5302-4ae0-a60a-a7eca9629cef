import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

const resources = {
  ko: {
    translation: {
      'app.title': 'SOPIA',
      'app.description': '스푼 라이브 스트리밍을 더욱 편리하게 관리하세요.',
      'features.chat': '실시간 채팅 관리',
      'features.chat.description': '채팅을 효율적으로 모니터링하고 관리할 수 있습니다.',
      'features.customize': '다양한 커스터마이징',
      'features.customize.description': '나만의 방송 환경을 자유롭게 설정하세요.',
      'login.title': '소피아 로그인',
      'login.description': '소피아 계정과 스푼 게정은 별도로 관리됩니다.',
      'auth.getStarted': '시작하기',
      'auth.createAccount': '지금 바로 계정을 생성하세요',
      'auth.id': '아이디',
      'auth.password': '비밀번호',
      'auth.login': '로그인',
      'auth.noAccount': '계정이 없으신가요?',
      'auth.signup': '회원가입',
      'auth.loginWithSpoon': '스푼에서 로그인',
      'auth.haveAccount': '이미 계정이 있으신가요?',
      signup: {
        title: '회원가입',
        description: 'SOPIA의 새로운 멤버가 되어주세요',
        idPlaceholder: '아이디를 입력하세요',
        passwordPlaceholder: '비밀번호를 입력하세요',
        passwordConfirm: '비밀번호 확인',
        passwordConfirmPlaceholder: '비밀번호를 다시 입력하세요',
        agreeToTerms: '이용약관에 동의합니다',
        agreeToPrivacy: '개인정보 처리방침에 동의합니다',
        viewTerms: '이용약관 보기',
        viewPrivacy: '개인정보 처리방침 보기',
        submit: '가입하기',
        errors: {
          idTooShort: '아이디는 4자 이상이어야 합니다',
          passwordTooShort: '비밀번호는 8자 이상이어야 합니다',
          passwordMismatch: '비밀번호가 일치하지 않습니다'
        },
        termsTitle: '소피아 서비스 이용약관',
        privacyTitle: '개인정보 처리방침',
        privacyContent: '개인정보 처리방침 내용을 입력해주세요',
        terms: {
          title: '소피아 서비스 이용 약관',
          purpose: {
            title: '1. 목적',
            content:
              '본 약관은 서비스 이용자가 소피아에서 제공하는 서비스에 사용자로 가입하고, 이를 이용함에 있어 서비스 제공자와 사용자 (본 약관에 동의하고 회원가입 완료한 이용자. 이하 `사용자`)의 책임사항을 규정함을 목적으로 합니다.'
          },
          effect: {
            title: '2. 효력 및 변경',
            items: [
              '이 약관은 서비스 화면이나 기타의 방법으로 이용고객에게 공지함으로써 효력을 발생합니다.',
              '서비스 제공자는 이 약관의 내용을 변경할 수 있으며, 변경된 약관은 1번과 같은 방법으로 공지 또는 통지함으로써 효력을 발생합니다.'
            ]
          },
          agreement: {
            title: '3. 이용계약의 성립',
            content: '해당 이용 약관 동의를 확인하고 회원가입시 이용계약은 성립합니다.'
          },
          provider: {
            title: '4. 서비스 제공자의 의무',
            items: [
              '서비스 기능의 전체를 차별없이 이용자에게 제공합니다.',
              '서비스 제공과 관련하여 취득한 사용자의 개인정보를 동의없이 타인에게 누설, 공개 또는 배포할 수 없으며 서비스 관련 이외의 목적으로 사용할 수 없습니다.'
            ]
          },
          user: {
            title: '5. 사용자의 의무',
            hacking: '프로그램을 해킹하려는 행위는 하면 안 됩니다. 어차피',
            opensource: '오픈소스',
            traffic:
              '제공된 서비스를 이용하여 다른 사용자 또는 (주)스푼라디오의 서비스에 지장이 가도록 해서는 안 됩니다. (예. 과도한 트래픽 발생 또는 채팅 전송의 행위)',
            disclaimer:
              '제공된 서비스를 이용하면서 생긴 모든 불이익은 서비스 제공자가 책임지지 않습니다.'
          },
          limitation: {
            title: '6. 서비스 제한 및 정지',
            items: [
              '서비스 제공자의 부재시 서비스 제공이 중단될 수 있습니다.',
              '(주)스푼라디오의 정식 중지 요청이 들어오면 중단될 수 있습니다.',
              '서비스 제공자가 서비스 제공을 중단할 때, 사용자에게 통보해야할 의무는 없습니다.'
            ]
          },
          effectiveDate: '이 약관은 2022년 3월 4일부터 시행합니다.'
        },
        privacy: {
          intro:
            '소피아는 최소의 개인정보를 필요한 시점에 수집하며, 수집한 개인정보는 이용자에게 고지하고 동의 받은 범위내에서만 이용합니다. 사전에 동의 받은 내용의 범위를 초과해서 이용하거나 외부에 공개하지 않습니다.',
          collection: {
            title: '1. 개인정보 수집 항목',
            items: [
              '(주)스푼라디오 서비스의 계정 아이디',
              '(주)스푼라디오 서비스에 등록된 성별',
              '소피아를 사용하는 활동 기록'
            ]
          },
          purpose: {
            title: '2. 개인정보 처리 목적',
            items: [
              '서비스 가입, 서비스 이용, 부정이용 확인 및 방지를 위하여 개인정보를 처리합니다.',
              '서비스 통계 및 개선을 위하여 개인정보를 처리합니다.'
            ]
          },
          retention: {
            title: '3. 개인정보 처리 및 보유 기간',
            items: [
              '소피아는 개인정보 수집시 동의 받은 기간동안 개인정보를 처리 및 보유합니다.',
              '수집된 정보는 서비스 중단시 파기됩니다.'
            ]
          },
          notice: {
            title: '4. 고지의 의무',
            content:
              '이 개인정보처리방침은 시행일로부터 적용되며, 내용의 추가, 삭제 및 정정이 있는 경우에는 공지사항 등을 통하여 알려드립니다.',
            activity: '소피아 활동 기록의 경우, 소피아 사이트 또는 서비스 등에 공개될 수 있습니다.'
          },
          effectiveDate: '해당 방침은 2022년 3월 4일부터 시행합니다.'
        }
      },
      common: {
        back: '뒤로가기',
        close: '닫기'
      }
    }
  },
  en: {
    translation: {
      'auth.loginWithSpoon': 'Login with Spoon',
      'auth.haveAccount': 'Already have an account?',
      signup: {
        title: 'Sign Up',
        description: 'Become a new member of SOPIA',
        idPlaceholder: 'Enter your ID',
        passwordPlaceholder: 'Enter your password',
        passwordConfirm: 'Confirm Password',
        passwordConfirmPlaceholder: 'Enter your password again',
        agreeToTerms: 'I agree to the Terms of Service',
        agreeToPrivacy: 'I agree to the Privacy Policy',
        viewTerms: 'View Terms',
        viewPrivacy: 'View Privacy Policy',
        submit: 'Sign Up',
        errors: {
          idTooShort: 'ID must be at least 4 characters',
          passwordTooShort: 'Password must be at least 8 characters',
          passwordMismatch: 'Passwords do not match'
        },
        privacyTitle: 'Privacy Policy',
        privacyContent: 'Please enter the privacy policy content',
        terms: {
          title: 'Terms of Service for SOPIA',
          purpose: {
            title: '1. Purpose',
            content:
              'These terms and conditions govern the responsibilities of the service provider and users (hereinafter referred to as "Users") who agree to these terms and complete registration when users join and use the services provided by SOPIA.'
          },
          effect: {
            title: '2. Effect and Changes',
            items: [
              'These terms take effect when notified to users through the service screen or other methods.',
              'The service provider may change these terms, and the changed terms take effect when notified or communicated in the same way as in item 1.'
            ]
          },
          agreement: {
            title: '3. Formation of Service Agreement',
            content:
              'The service agreement is formed when the agreement to these terms is confirmed and registration is completed.'
          },
          provider: {
            title: "4. Service Provider's Obligations",
            items: [
              'Provide all service functions without discrimination to users.',
              "The service provider shall not disclose, publish, or distribute users' personal information obtained in connection with service provision to third parties without consent, and shall not use it for purposes other than service-related purposes."
            ]
          },
          user: {
            title: "5. User's Obligations",
            hacking: "Users shall not attempt to hack the program. After all, it's",
            opensource: 'open source',
            traffic:
              "Users shall not use the provided service to cause inconvenience to other users or (주)스푼라디오's service. (e.g., excessive traffic generation or chat sending)",
            disclaimer:
              'The service provider is not responsible for any disadvantages arising from using the provided service.'
          },
          limitation: {
            title: '6. Service Restrictions and Suspension',
            items: [
              'Service provision may be suspended in the absence of the service provider.',
              'Service may be suspended upon formal request from (주)스푼라디오.',
              'The service provider has no obligation to notify users when suspending service provision.'
            ]
          },
          effectiveDate: 'These terms take effect from March 4, 2022.'
        },
        privacy: {
          intro:
            'SOPIA collects minimal personal information at necessary times and uses the collected personal information only within the scope agreed upon by users after notification. We do not use or disclose information beyond the scope of pre-agreed content.',
          collection: {
            title: '1. Personal Information Collection Items',
            items: [
              'Account ID of (주)스푼라디오 service',
              'Gender registered in (주)스푼라디오 service',
              'Activity records of using SOPIA'
            ]
          },
          purpose: {
            title: '2. Purpose of Personal Information Processing',
            items: [
              'Process personal information for service registration, use, and prevention of unauthorized use.',
              'Process personal information for service statistics and improvement.'
            ]
          },
          retention: {
            title: '3. Personal Information Processing and Retention Period',
            items: [
              'SOPIA processes and retains personal information for the period agreed upon at the time of collection.',
              'Collected information is destroyed when the service is discontinued.'
            ]
          },
          notice: {
            title: '4. Duty to Notify',
            content:
              'This privacy policy applies from the effective date, and we will notify you through notices if there are any additions, deletions, or corrections to the content.',
            activity: 'SOPIA activity records may be disclosed on the SOPIA website or service.'
          },
          effectiveDate: 'This policy takes effect from March 4, 2022.'
        }
      },
      common: {
        back: 'Back',
        close: 'Close'
      }
    }
  }
}

i18n.use(initReactI18next).init({
  resources,
  lng: 'ko',
  fallbackLng: 'ko',
  interpolation: {
    escapeValue: false
  }
})

export default i18n
