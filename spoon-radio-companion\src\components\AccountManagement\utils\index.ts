import { JWTDecoded } from '../types';

// 상태 텍스트 변환
export const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '활성';
    case 'inactive': return '비활성';
    case 'suspended': return '정지';
    case 'linked': return '연결됨';
    case 'unlinked': return '연결안됨';
    default: return status;
  }
};

// 역할 텍스트 변환
export const getRoleText = (role: string) => {
  return role === 'admin' ? '관리자' : '사용자';
};

// 날짜 포맷팅
export const formatDate = (timestamp: number | string | Date) => {
  try {
    let date: Date;
    
    if (typeof timestamp === 'number') {
      date = new Date(timestamp * 1000);
    } else if (typeof timestamp === 'string') {
      if (timestamp === '방금 전' || timestamp === '로그인 기록 없음') {
        return timestamp;
      }
      date = new Date(timestamp);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else {
      return '알 수 없음';
    }

    if (isNaN(date.getTime())) {
      return '잘못된 날짜';
    }

    return date.toLocaleDateString('ko-KR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('날짜 포맷팅 오류:', error);
    return '포맷 오류';
  }
};

// 타임스탬프 포맷팅
export const formatTimestamp = (timestamp: number) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// JWT 토큰 디코딩
export const decodeJWT = (token: string): JWTDecoded => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return {
        header: null,
        payload: null,
        signature: '',
        isValid: false,
        isExpired: true,
        error: 'JWT 형식이 올바르지 않습니다'
      };
    }

    const [headerPart, payloadPart, signaturePart] = parts;

    const decodeBase64Url = (str: string) => {
      let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
      
      const padding = base64.length % 4;
      if (padding) {
        base64 += '='.repeat(4 - padding);
      }
      
      try {
        const decoded = atob(base64);
        return JSON.parse(decoded);
      } catch (e) {
        throw new Error('Base64 디코딩 실패');
      }
    };

    const header = decodeBase64Url(headerPart);
    const payload = decodeBase64Url(payloadPart);

    const now = Math.floor(Date.now() / 1000);
    const isExpired = payload.exp ? payload.exp < now : false;

    return {
      header,
      payload,
      signature: signaturePart,
      isValid: true,
      isExpired,
      error: undefined
    };
  } catch (error) {
    return {
      header: null,
      payload: null,
      signature: '',
      isValid: false,
      isExpired: true,
      error: error instanceof Error ? error.message : 'JWT 디코딩 실패'
    };
  }
};

// 필터 옵션 생성
export const getFilterOptions = () => {
  return [
    { value: 'all', label: '전체' },
    { value: 'linked', label: '연결된 계정' },
    { value: 'unlinked', label: '연결되지 않은 계정' },
    { value: 'admin', label: '관리자' },
    { value: 'user', label: '일반 사용자' }
  ];
};

// 검색 플레이스홀더 텍스트
export const getSearchPlaceholder = () => {
  return '이름, 이메일, 스푼 닉네임으로 검색...';
}; 