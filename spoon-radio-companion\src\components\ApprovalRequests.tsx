import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { db } from '../firebaseConfig';
import { collection, getDocs, query, where, doc, updateDoc, deleteDoc } from 'firebase/firestore';
import { useAuthContext } from '../contexts/AuthContext';

interface PendingUser {
  id: string;
  email: string;
  displayName: string;
  requestDate: string;
  reason?: string;
  name?: string;
  createdAt?: any;
}

const Container = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
`;

const Title = styled.h1`
  color: #333;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
`;

const Subtitle = styled.p`
  color: #666;
  font-size: 14px;
`;

const RequestsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const RequestCard = styled.div`
  background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
  border: 1px solid #e1e8f0;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8b5cf6, #a855f7, #c084fc);
  }
  
  &:hover {
    box-shadow: 0 12px 40px rgba(139, 92, 246, 0.15);
    transform: translateY(-4px);
    border-color: #c084fc;
  }
`;

const RequestHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
`;

const UserInfo = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
`;

const UserAvatar = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 700;
  text-transform: uppercase;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
`;

const UserDetails = styled.div`
  flex: 1;
`;

const UserName = styled.h3`
  color: #1a202c;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
  line-height: 1.2;
`;

const UserEmail = styled.p`
  color: #718096;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
`;

const RequestDate = styled.div`
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  color: #4a5568;
  font-size: 12px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  white-space: nowrap;
`;

const Reason = styled.div`
  margin-bottom: 24px;
`;

const ReasonLabel = styled.p`
  color: #2d3748;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &::before {
    content: '💬';
    font-size: 16px;
  }
`;

const ReasonText = styled.p`
  color: #4a5568;
  font-size: 15px;
  line-height: 1.6;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  font-style: italic;
  position: relative;
  
  &::before {
    content: '"';
    position: absolute;
    top: 8px;
    left: 8px;
    font-size: 24px;
    color: #cbd5e0;
    font-family: serif;
  }
  
  &::after {
    content: '"';
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 24px;
    color: #cbd5e0;
    font-family: serif;
  }
`;

const Actions = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
`;

const Button = styled.button<{ variant: 'approve' | 'reject' }>`
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
  
  ${props => props.variant === 'approve' ? `
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(72, 187, 120, 0.3);
    
    &:hover {
      background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
    }
    
    &::before {
      content: '✓';
      font-size: 16px;
    }
  ` : `
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(245, 101, 101, 0.3);
    
    &:hover {
      background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(245, 101, 101, 0.4);
    }
    
    &::before {
      content: '✕';
      font-size: 16px;
    }
  `}
  
  &:disabled {
    background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  font-size: 16px;
  margin-bottom: 8px;
`;

const EmptySubtext = styled.p`
  font-size: 14px;
  color: #999;
`;

const ApprovalRequests: React.FC = () => {
  const [pendingUsers, setPendingUsers] = useState<PendingUser[]>([]);
  const [loading, setLoading] = useState(true);
  const { user: authUser } = useAuthContext();

  // 관리자 권한 확인
  const isAdmin = authUser?.email === '<EMAIL>' || (authUser as any)?.isAdmin;

  useEffect(() => {
    if (isAdmin) {
      loadPendingUsers();
    } else {
      setLoading(false);
    }
  }, [isAdmin]);

  const loadPendingUsers = async () => {
    setLoading(true);
    try {
      console.log('🔍 승인 대기 사용자 로드 시작...');

      // 디버깅: 전체 users 컬렉션 확인
      const allUsersCollection = collection(db, 'users');
      const allUsersSnapshot = await getDocs(allUsersCollection);
      console.log('📊 전체 users 컬렉션 문서 수:', allUsersSnapshot.size);
      
      if (allUsersSnapshot.size === 0) {
        console.log('⚠️ users 컬렉션이 비어있습니다. 사용자 데이터가 Firestore에 저장되지 않았습니다.');
        setPendingUsers([]);
        return;
      }

      // 모든 사용자 문서 로그 출력
      allUsersSnapshot.forEach((doc) => {
        const data = doc.data();
        console.log('📄 사용자 문서:', {
          id: doc.id,
          email: data.email,
          isApproved: data.isApproved,
          status: data.status,
          data: data
        });
      });

      // Firebase Firestore에서 승인 대기 중인 사용자 가져오기
      const usersCollection = collection(db, 'users');
      const pendingQuery = query(usersCollection, where('isApproved', '==', false));
      const querySnapshot = await getDocs(pendingQuery);
      
      console.log('🔍 isApproved=false 쿼리 결과:', querySnapshot.size, '개');
      
      const pendingUsersList: PendingUser[] = [];
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        console.log('📋 승인 대기 사용자:', {
          id: doc.id,
          email: userData.email,
          displayName: userData.displayName || userData.name,
          isApproved: userData.isApproved,
          status: userData.status
        });
        
        pendingUsersList.push({
          id: doc.id,
          email: userData.email || '',
          displayName: userData.name || userData.displayName || '알 수 없음',
          requestDate: userData.createdAt ? new Date(userData.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          reason: userData.reason || '가입 요청',
          name: userData.name || userData.displayName
        });
      });
      
      setPendingUsers(pendingUsersList);
      console.log('✅ 승인 대기 사용자 로드 성공:', pendingUsersList.length, '명');
      
      // 디버깅: 로드된 사용자 목록 상세 출력
      pendingUsersList.forEach((user, index) => {
        console.log(`👤 사용자 ${index + 1}:`, {
          id: user.id,
          email: user.email,
          displayName: user.displayName,
          requestDate: user.requestDate
        });
      });
      
    } catch (error) {
      console.error('❌ 승인 대기 사용자 로드 실패:', error);
      setPendingUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (userId: string) => {
    if (!isAdmin) {
      alert('관리자 권한이 필요합니다.');
      return;
    }

    try {
      const userToApprove = pendingUsers.find(user => user.id === userId);
      if (!userToApprove) {
        alert('사용자를 찾을 수 없습니다.');
        return;
      }

      // Firebase Firestore에서 사용자 승인 처리
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        isApproved: true,
        status: 'active',
        approvedAt: new Date()
      });

      // 로컬 상태 업데이트
      setPendingUsers(prev => prev.filter(user => user.id !== userId));
      
      alert(`${userToApprove.displayName}님의 가입이 승인되었습니다.`);
      console.log('✅ 사용자 승인 완료:', userToApprove.email);
    } catch (error) {
      console.error('❌ 사용자 승인 실패:', error);
      alert('승인 처리 중 오류가 발생했습니다.');
    }
  };

  const handleReject = async (userId: string) => {
    if (!isAdmin) {
      alert('관리자 권한이 필요합니다.');
      return;
    }

    try {
      const userToReject = pendingUsers.find(user => user.id === userId);
      if (!userToReject) {
        alert('사용자를 찾을 수 없습니다.');
        return;
      }

      if (window.confirm(`${userToReject.displayName}님의 가입 요청을 거절하시겠습니까?\n거절된 사용자는 계정이 삭제됩니다.`)) {
        // Firebase Firestore에서 사용자 삭제
        const userRef = doc(db, 'users', userId);
        await deleteDoc(userRef);

        // 로컬 상태 업데이트
        setPendingUsers(prev => prev.filter(user => user.id !== userId));
        
        alert(`${userToReject.displayName}님의 가입 요청이 거절되었습니다.`);
        console.log('✅ 사용자 거절 완료:', userToReject.email);
      }
    } catch (error) {
      console.error('❌ 사용자 거절 실패:', error);
      alert('거절 처리 중 오류가 발생했습니다.');
    }
  };

  // 관리자가 아닌 경우 접근 차단
  if (!isAdmin) {
    return (
      <Container>
        <Header>
          <Title>접근 권한 없음</Title>
          <Subtitle>관리자만 접근할 수 있는 페이지입니다</Subtitle>
        </Header>
        <EmptyState>
          <EmptyIcon>🔒</EmptyIcon>
          <EmptyText>관리자 권한이 필요합니다</EmptyText>
          <EmptySubtext>관리자 계정으로 로그인해주세요</EmptySubtext>
        </EmptyState>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container>
        <Header>
          <Title>승인 요청</Title>
          <Subtitle>회원가입 요청을 검토하고 승인/거부할 수 있습니다</Subtitle>
        </Header>
        <div style={{ textAlign: 'center', padding: '60px' }}>
          <p>로딩 중...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>승인 요청</Title>
        <Subtitle>회원가입 요청을 검토하고 승인/거부할 수 있습니다</Subtitle>
      </Header>
      
      {pendingUsers.length === 0 ? (
        <EmptyState>
          <EmptyIcon>📋</EmptyIcon>
          <EmptyText>대기 중인 승인 요청이 없습니다</EmptyText>
          <EmptySubtext>새로운 회원가입 요청이 있으면 여기에 표시됩니다</EmptySubtext>
        </EmptyState>
      ) : (
        <RequestsList>
          {pendingUsers.map((request) => (
            <RequestCard key={request.id}>
              <RequestHeader>
                <UserInfo>
                  <UserAvatar>
                    {request.displayName.charAt(0)}
                  </UserAvatar>
                  <UserDetails>
                    <UserName>{request.displayName}</UserName>
                    <UserEmail>{request.email}</UserEmail>
                  </UserDetails>
                </UserInfo>
                <RequestDate>
                  {new Date(request.requestDate).toLocaleDateString('ko-KR', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </RequestDate>
              </RequestHeader>
              
              {request.reason && (
                <Reason>
                  <ReasonLabel>가입 사유</ReasonLabel>
                  <ReasonText>{request.reason}</ReasonText>
                </Reason>
              )}
              
              <Actions>
                <Button
                  variant="reject"
                  onClick={() => handleReject(request.id)}
                >
                  거절
                </Button>
                <Button
                  variant="approve"
                  onClick={() => handleApprove(request.id)}
                >
                  승인
                </Button>
              </Actions>
            </RequestCard>
          ))}
        </RequestsList>
      )}
    </Container>
  );
};

export default ApprovalRequests; 