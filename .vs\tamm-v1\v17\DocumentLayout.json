{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\App.vue||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\App.vue||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\migrateToFirebase.ts||{3B902123-F8A7-4915-9F01-361F908088D0}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\utils\\migrateToFirebase.ts||{3B902123-F8A7-4915-9F01-361F908088D0}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\authObserver.ts||{3B902123-F8A7-4915-9F01-361F908088D0}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\utils\\authObserver.ts||{3B902123-F8A7-4915-9F01-361F908088D0}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\firestore-rules.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\utils\\firestore-rules.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\main.ts||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\main.ts||{3B902123-F8A7-4915-9F01-361F908088D0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "main.ts", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\main.ts", "RelativeDocumentMoniker": "src\\main.ts", "ToolTip": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\main.ts", "RelativeToolTip": "src\\main.ts", "ViewState": "AgIAACAAAAAAAAAAAECwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T16:24:35.233Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "App.vue", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\App.vue", "RelativeDocumentMoniker": "src\\App.vue", "ToolTip": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\App.vue", "RelativeToolTip": "src\\App.vue", "ViewState": "AgIAAG4BAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-07T16:23:21.709Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "firestore-rules.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\firestore-rules.md", "RelativeDocumentMoniker": "src\\utils\\firestore-rules.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\firestore-rules.md", "RelativeToolTip": "src\\utils\\firestore-rules.md", "ViewState": "AgIAAAAAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-07T16:21:42.51Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "authObserver.ts", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\authObserver.ts", "RelativeDocumentMoniker": "src\\utils\\authObserver.ts", "ToolTip": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\authObserver.ts", "RelativeToolTip": "src\\utils\\authObserver.ts", "ViewState": "AgIAAAAAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T16:21:16.47Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "migrateToFirebase.ts", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\migrateToFirebase.ts", "RelativeDocumentMoniker": "src\\utils\\migrateToFirebase.ts", "ToolTip": "C:\\Users\\<USER>\\Desktop\\tamm-v1\\src\\utils\\migrateToFirebase.ts", "RelativeToolTip": "src\\utils\\migrateToFirebase.ts", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T16:20:41.192Z", "EditorCaption": ""}]}]}]}