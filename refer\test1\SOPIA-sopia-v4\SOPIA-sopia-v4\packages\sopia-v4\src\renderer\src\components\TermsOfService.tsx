import React from 'react'
import { useTranslation } from 'react-i18next'

export const TermsOfService: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
        {t('signup.terms.title')}
      </h1>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.terms.purpose.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          <li>{t('signup.terms.purpose.content')}</li>
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.terms.effect.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          {Object.values(t('signup.terms.effect.items', { returnObjects: true })).map(
            (item: string, index: number) => (
              <li key={index}>{item}</li>
            )
          )}
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.terms.agreement.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          <li>{t('signup.terms.agreement.content')}</li>
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.terms.provider.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          {Object.values(t('signup.terms.provider.items', { returnObjects: true })).map(
            (item: string, index: number) => (
              <li key={index}>{item}</li>
            )
          )}
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.terms.user.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          <li>
            {t('signup.terms.user.hacking')}{' '}
            <a
              href="https://github.com/sopia-bot/SOPIA"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:text-primary-light underline"
            >
              {t('signup.terms.user.opensource')}
            </a>
          </li>
          <li>{t('signup.terms.user.traffic')}</li>
          <li>{t('signup.terms.user.disclaimer')}</li>
        </ul>
      </section>

      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">
          {t('signup.terms.limitation.title')}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
          {Object.values(t('signup.terms.limitation.items', { returnObjects: true })).map(
            (item: string, index: number) => (
              <li key={index}>{item}</li>
            )
          )}
        </ul>
      </section>

      <p className="text-sm text-gray-500 dark:text-gray-400 mt-6">
        {t('signup.terms.effectiveDate')}
      </p>
    </div>
  )
}
