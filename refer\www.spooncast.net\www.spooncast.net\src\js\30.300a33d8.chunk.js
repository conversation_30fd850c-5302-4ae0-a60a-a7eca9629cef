(window["webpackJsonpspooncast-web-next"] = window["webpackJsonpspooncast-web-next"] || []).push([
    [30], {
        2401: function(e, t, n) {
            "use strict";
            n.r(t);
            var r, a, l, c, o, i, s, m, u, d, p, b, g, h, x, O, j, f, y, _ = n(0),
                E = n.n(_),
                S = n(63),
                w = n(6),
                v = n(8),
                k = n(17),
                C = n(545),
                R = n(5),
                M = n(45),
                $ = n(4);
            const I = 56,
                L = 40,
                P = 32,
                T = $.d.footer(r || (r = Object(R.a)(["\n  ", ";\n  background-color: ", ";\n  border-top: 1px solid ", ";\n  position: relative;\n\n  ", "\n\n  ", "\n\n  ", ";\n  ", ";\n\n  ", ";\n  ", ";\n  ", ";\n\n  /* ! 기존 코드 반응형 확인필요 */\n  @media screen and (max-width: 1365px) {\n    ", "\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("center", "stretch")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.white
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_10
                }, e => {
                    let {
                        $isDarkMode: t
                    } = e;
                    return t && Object($.c)(a || (a = Object(R.a)(["\n      *,\n      button,\n      p {\n        color: ", ";\n      }\n      & .divide {\n        background-color: ", ";\n      }\n      background-color: ", ";\n      border-top: 1px solid ", ";\n    "])), e => {
                        let {
                            theme: t
                        } = e;
                        return t.colors.fill.tertiary.default
                    }, e => {
                        let {
                            theme: t
                        } = e;
                        return t.colors.fill.secondary.default
                    }, e => {
                        let {
                            theme: t
                        } = e;
                        return t.colors.fill.primary.default
                    }, e => {
                        let {
                            theme: t
                        } = e;
                        return t.colors.fill.secondary.default
                    })
                }, e => {
                    let {
                        $isSideMenuOpen: t,
                        $isDetailView: n,
                        $isChannelPage: r
                    } = e;
                    return t && (!n || r) && Object($.c)(l || (l = Object(R.a)(["\n      ", ";\n    "])), e => {
                        let {
                            theme: t
                        } = e;
                        return t.marginSet("left", M.n)
                    })
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._screen.xl(Object($.c)(c || (c = Object(R.a)(["\n      padding: 56px;\n    "]))))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._screen.lg(Object($.c)(o || (o = Object(R.a)(["\n      padding: 56px 0;\n    "]))))
                }, e => {
                    let {
                        theme: t,
                        $isContentsListPage: n,
                        $isSpoonMembershipPage: r
                    } = e;
                    return t._screen.md(Object($.c)(i || (i = Object(R.a)(["\n      padding: 56px 0;\n      ", "\n    "])), !n && !r && Object($.c)(s || (s = Object(R.a)(["\n        display: none;\n      "])))))
                }, e => {
                    let {
                        theme: t,
                        $isContentsListPage: n,
                        $isSpoonMembershipPage: r
                    } = e;
                    return t._screen.sm(Object($.c)(m || (m = Object(R.a)(["\n      padding: 40px 0;\n      ", "\n    "])), !n && !r && Object($.c)(u || (u = Object(R.a)(["\n        display: none;\n      "])))))
                }, e => {
                    let {
                        theme: t,
                        $isContentsListPage: n,
                        $isSpoonMembershipPage: r
                    } = e;
                    return t._screen.xs(Object($.c)(d || (d = Object(R.a)(["\n      padding: 40px 16px;\n      ", "\n    "])), !n && !r && Object($.c)(p || (p = Object(R.a)(["\n        display: none;\n      "])))))
                }, e => {
                    let {
                        theme: t,
                        $isSideMenuOpen: n
                    } = e;
                    return n && Object($.c)(b || (b = Object(R.a)(["\n        ", ";\n      "])), t.marginSet("left", "0"))
                }),
                A = $.d.div(g || (g = Object(R.a)(["\n  width: 100%;\n  max-width: 1440px;\n  ", ";\n\n  ", ";\n  ", ";\n  ", ";\n  ", ";\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexColumnSet("center", "flex-start", "48px")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.xl(Object($.c)(h || (h = Object(R.a)(["\n      margin: 0 auto;\n    "]))))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.lg(Object($.c)(x || (x = Object(R.a)(["\n      margin: 0 56px;\n      width: calc(100% - ", "px);\n    "])), 2 * I))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.md(Object($.c)(O || (O = Object(R.a)(["\n      margin: 0 40px;\n      width: calc(100% - ", "px);\n    "])), 2 * L))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(j || (j = Object(R.a)(["\n      margin: 0 32px;\n      width: calc(100% - ", "px);\n    "])), 2 * P))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.xs(Object($.c)(f || (f = Object(R.a)(["\n      width: 100%;\n      ", ";\n      margin: 0;\n    "])), e => {
                        let {
                            theme: t
                        } = e;
                        return t.flexColumnSet("center", "flex-start", "16px")
                    }))
                });
            $.d.div(y || (y = Object(R.a)([""])));
            var H, U, N, z = n(132),
                D = n(20),
                F = n(155),
                J = n(75);
            const V = $.d.div(H || (H = Object(R.a)(["\n  width: ", ";\n  height: ", ";\n\n  ", "\n\n  & > img {\n    width: 100%;\n    height: 100%;\n  }\n"])), e => {
                    let {
                        $size: t
                    } = e;
                    return t
                }, e => {
                    let {
                        $size: t
                    } = e;
                    return t
                }, e => {
                    let {
                        $type: t,
                        theme: n
                    } = e;
                    return t.includes("ISO") && Object($.c)(U || (U = Object(R.a)(["\n      padding: 4px;\n      ", "\n    "])), n.screen.md(Object($.c)(N || (N = Object(R.a)(["\n        padding: 3px;\n      "])))))
                }),
                q = ["type", "size"];
            var B, X, G, K, Q, W, Y, Z, ee, te = e => {
                    let {
                        type: t,
                        size: n
                    } = e, r = Object(D.a)(e, q);
                    const a = Object(J.a)("down", "md"),
                        l = {
                            ISO_2022: {
                                src: F.d,
                                alt: "footer_isms_description",
                                md: "32px",
                                lg: "40px"
                            },
                            ISO_2019: {
                                src: F.e,
                                alt: "footer_isms_description",
                                md: "32px",
                                lg: "40px"
                            },
                            ISO_2015: {
                                src: F.f,
                                alt: "footer_isms_description",
                                md: "32px",
                                lg: "40px"
                            },
                            ISMS: {
                                src: F.c,
                                alt: "footer_isms_p_description",
                                md: "36px",
                                lg: "48px"
                            },
                            APEC: {
                                src: F.a,
                                alt: "footer_cbpr_description",
                                md: "36px",
                                lg: "48px"
                            },
                            NexTone: {
                                src: F.j,
                                alt: "nextone_license_number",
                                md: "36px",
                                lg: "48px"
                            },
                            JASRAC: {
                                src: F.h,
                                alt: "jasrac_license_number",
                                md: "36px",
                                lg: "48px"
                            }
                        }[t] || null;
                    return l ? E.a.createElement(V, Object.assign({
                        $size: l[n] || (a ? l.md : l.lg),
                        $type: t
                    }, r), E.a.createElement("img", {
                        src: l.src,
                        alt: l.alt
                    })) : null
                },
                ne = n(50);
            const re = $.d.div(B || (B = Object(R.a)(["\n  width: 100%;\n  ", ";\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexColumnSet("flex-start", "flex-start", "32px")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(X || (X = Object(R.a)(["\n      align-items: center;\n    "]))))
                }),
                ae = $.d.p(G || (G = Object(R.a)(["\n  color: ", ";\n  ", ";\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_90
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._textSet("body1", "regular")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(K || (K = Object(R.a)(["\n      text-align: center;\n      display: none;\n    "]))))
                }),
                le = $.d.div(Q || (Q = Object(R.a)(["\n  background-color: ", ";\n  width: 100%;\n  height: 1px;\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_5
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(W || (W = Object(R.a)(["\n      display: none;\n    "]))))
                }),
                ce = $.d.ul(Y || (Y = Object(R.a)(["\n  ", ";\n  flex-wrap: wrap;\n  row-gap: 16px;\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("flex-start", "center", "16px")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(Z || (Z = Object(R.a)(["\n      display: none;\n    "]))))
                }),
                oe = $.d.li(ee || (ee = Object(R.a)(["\n  ", "\n\n  &:last-child {\n    flex: 1 1 auto;\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("flex-start", "center", "8px")
                });
            var ie, se, me = () => {
                    const {
                        countryCode: e
                    } = Object(w.b)(e => ({
                        countryCode: e.data.get("countryCode")
                    })), {
                        string: t
                    } = Object(w.s)(), n = z.b.ISO.test(e), r = z.b.ISMS.test(e), a = z.b.CBPR.test(e), l = z.b.JASRAC.test(e), c = z.b.NEXTONE.test(e), o = n || r || a || l || c;
                    return E.a.createElement(re, null, E.a.createElement(ae, {
                        dangerouslySetInnerHTML: {
                            __html: t.get("footer_copyright")
                        }
                    }), o && E.a.createElement(le, {
                        className: "divide"
                    }), o && E.a.createElement(ce, null, n && E.a.createElement(E.a.Fragment, null, E.a.createElement(te, {
                        type: "ISO_2022"
                    }), E.a.createElement(te, {
                        type: "ISO_2019"
                    }), E.a.createElement(te, {
                        type: "ISO_2015"
                    })), r && E.a.createElement(oe, {
                        type: "iso"
                    }, E.a.createElement(te, {
                        type: "ISMS"
                    }), E.a.createElement(ne.a, {
                        dangerouslySetInnerHTML: {
                            __html: t.get("footer_isms_p_description")
                        },
                        variant: "xxs400",
                        color: "secondary"
                    })), a && E.a.createElement(oe, {
                        type: "certification"
                    }, E.a.createElement(te, {
                        type: "APEC"
                    }), E.a.createElement(ne.a, {
                        dangerouslySetInnerHTML: {
                            __html: t.get("footer_cbpr_description")
                        },
                        variant: "xxs400",
                        color: "secondary"
                    })), l && E.a.createElement(oe, {
                        type: "certification"
                    }, E.a.createElement(te, {
                        type: "JASRAC"
                    }), E.a.createElement(ne.a, {
                        dangerouslySetInnerHTML: {
                            __html: t.get("jasrac_license_number").replace(/<br\/>/g, " ")
                        },
                        variant: "xxs400",
                        color: "secondary"
                    })), c && E.a.createElement(oe, {
                        type: "certification"
                    }, E.a.createElement(te, {
                        type: "NexTone"
                    }), E.a.createElement(ne.a, {
                        dangerouslySetInnerHTML: {
                            __html: t.get("nextone_license_number")
                        },
                        variant: "xxs400",
                        color: "secondary"
                    }))))
                },
                ue = n(86);
            const de = Object($.d)(ue.a)(ie || (ie = Object(R.a)(["\n  justify-content: center;\n  flex-direction: row;\n  flex-wrap: wrap;\n\n  padding: ", " ", ";\n  border-radius: ", ";\n  background-color: ", ";\n\n  &::after {\n    content: '';\n    display: block;\n    width: 1px;\n    height: 10px;\n    background-color: ", ";\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.layout.padding.ms
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.layout.padding.m
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.layout.spacing.ms
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.colors.background[200]
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale[20]
                }),
                pe = $.d.div(se || (se = Object(R.a)(["\n  ", ";\n\n  &::after {\n    content: '';\n    display: block;\n    width: 1px;\n    height: 10px;\n    background-color: ", ";\n    margin: 0 ", ";\n  }\n\n  &:last-child {\n    &:after {\n      display: none;\n    }\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("center", "center")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_20
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.layout.spacing.xs
                }),
                be = [{
                    content: "footer_ceo"
                }, {
                    content: "footer_business_number"
                }, {
                    content: "footer_sale_number"
                }, {
                    content: "footer_company_address"
                }, {
                    content: "footer_company_telephone"
                }, {
                    content: "customer_inconvenience",
                    subContent: "common_cs_email"
                }];
            var ge = () => {
                    const {
                        string: e
                    } = Object(w.s)();
                    return E.a.createElement(de, null, be.map(t => E.a.createElement(pe, null, E.a.createElement(ne.a, {
                        asElement: "p",
                        variant: "xs400",
                        value: e.get(t.content),
                        color: "secondary"
                    }), t.subContent && E.a.createElement(ne.a, {
                        asElement: "p",
                        variant: "xs400",
                        value: ":".concat(e.get(t.subContent)),
                        color: "secondary"
                    }))))
                },
                he = n(16);
            var xe, Oe, je, fe = e => {
                    let {
                        toggled: t,
                        onClick: n
                    } = e;
                    return E.a.createElement(ue.a, {
                        gap: "xs",
                        direction: "row",
                        align: "center",
                        onClick: n
                    }, E.a.createElement(ne.a, {
                        value: "사업자 정보",
                        variant: "s400",
                        color: "primary"
                    }), E.a.createElement(he.e, {
                        icon: "ic_arrow_".concat(t ? "up" : "down"),
                        fill: "secondary",
                        width: 12,
                        height: 12
                    }))
                },
                ye = n(108);
            const _e = $.d.div(xe || (xe = Object(R.a)(["\n  color: ", ";\n  ", ";\n\n  a:hover,\n  button:hover {\n    text-decoration: underline;\n  }\n\n  /* @media screen and (max-width: 767px) {\n    a:hover {\n      text-decoration: none;\n    }\n    .recruit {\n      display: none;\n\n      &.on {\n        display: block;\n      }\n    }\n  } */\n"])), e => {
                let {
                    theme: t
                } = e;
                return t._colors.grayscale.gray_90
            }, e => {
                let {
                    theme: t
                } = e;
                return t.textSet("s400")
            });
            $.d.span(Oe || (Oe = Object(R.a)(["\n  color: ", ";\n  margin: 0 12px;\n\n  ", "\n"])), e => {
                let {
                    theme: t
                } = e;
                return t._colors.grayscale.gray_20
            }, e => {
                let {
                    isLastItem: t
                } = e;
                return t && Object($.c)(je || (je = Object(R.a)(["\n      display: none;\n    "])))
            });
            var Ee, Se, we, ve, ke, Ce, Re, Me, $e, Ie, Le, Pe = e => {
                let {
                    menuLinkItem: t
                } = e;
                if (!t.href) {
                    const e = t.title && t.title.length > 0;
                    return E.a.createElement(_e, null, e && E.a.createElement(ye.a, Object.assign({
                        title: t.title
                    }, t.handleClick && {
                        onClick: t.handleClick(t.name)
                    }), E.a.createElement("span", null, t.title)))
                }
                return E.a.createElement(_e, null, !t.isExternalLink && E.a.createElement(E.a.Fragment, null, E.a.createElement("a", Object.assign({
                    title: t.title,
                    href: t.href
                }, t.handleClick && {
                    onClick: t.handleClick(t.name)
                }), t.isStrong && E.a.createElement("strong", null, t.title), !t.isStrong && E.a.createElement(E.a.Fragment, null, t.title))), t.isExternalLink && E.a.createElement(E.a.Fragment, null, E.a.createElement("a", Object.assign({
                    title: t.title,
                    href: t.href,
                    target: "_blank",
                    rel: "noreferrer noopener"
                }, t.handleClick && {
                    onClick: t.handleClick(t.name)
                }), t.title)))
            };
            const Te = $.d.div(Ee || (Ee = Object(R.a)(["\n  width: 100%;\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexColumnSet("flex-start", "flex-start", "l")
                }),
                Ae = $.d.ul(Se || (Se = Object(R.a)(["\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexColumnSet("flex-start", "flex-start", "24px")
                }),
                He = $.d.li(we || (we = Object(R.a)(["\n  width: 100%;\n  ", ";\n"])), e => {
                    let {
                        theme: t,
                        type: n
                    } = e;
                    return "information" === n && t.screen.sm(Object($.c)(ve || (ve = Object(R.a)(["\n      display: none;\n    "]))))
                }),
                Ue = $.d.div(ke || (ke = Object(R.a)(["\n  color: ", ";\n  ", ";\n  ", ";\n  flex-wrap: wrap;\n  column-gap: 24px;\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_90
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("flex-start", "flex-start")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t._textSet("body1", "regular")
                }),
                Ne = $.d.span(Ce || (Ce = Object(R.a)(["\n  position: relative;\n  &:last-child {\n    &:after {\n      display: none;\n    }\n  }\n  &:after {\n    background-color: ", ";\n    content: '';\n    width: 1px;\n    height: 10px;\n    position: absolute;\n    top: 50%;\n    inset-inline-end: 0;\n    transform: translate(12px, -50%);\n\n    ", ";\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_20
                }, e => {
                    let {
                        isRtl: t
                    } = e;
                    return t && Object($.c)(Re || (Re = Object(R.a)(["\n        transform: translate(-12px, -50%);\n      "])))
                }),
                ze = $.d.ul(Me || (Me = Object(R.a)(["\n  width: 100%;\n  ", ";\n  flex-wrap: wrap;\n\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("flex-start", "center", "24px")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)($e || ($e = Object(R.a)(["\n      ", ";\n      column-gap: 24px;\n    "])), e => {
                        let {
                            theme: t
                        } = e;
                        return t.flexRowSet()
                    }))
                }),
                De = $.d.li(Ie || (Ie = Object(R.a)(["\n  position: relative;\n\n  &:last-child {\n    &:after {\n      display: none;\n    }\n  }\n  &:after {\n    background-color: ", ";\n    content: '';\n    width: 1px;\n    height: 10px;\n    position: absolute;\n    top: 50%;\n    inset-inline-end: 0;\n    transform: translate(12px, -50%);\n\n    ", ";\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_20
                }, e => {
                    let {
                        isRtl: t
                    } = e;
                    return t && Object($.c)(Le || (Le = Object(R.a)(["\n        transform: translate(-12px, -50%);\n      "])))
                });
            var Fe, Je, Ve = () => {
                    const {
                        countryCode: e,
                        isRtl: t
                    } = Object(w.b)(e => ({
                        countryCode: e.data.get("countryCode"),
                        isRtl: e.data.get("isRtl")
                    })), {
                        string: n
                    } = Object(w.s)(), r = Object(J.a)("down", "sm"), [a, l] = Object(_.useState)(!1), c = () => {
                        l(e => !e)
                    }, o = Object(_.useMemo)(() => "kr" === e ? "".concat(n.get("customer_inconvenience"), ": ").concat(n.get("common_cs_email")) : "", [n, e]), i = Object(w.i)();
                    return E.a.createElement(Te, null, E.a.createElement(Ae, null, E.a.createElement(He, {
                        type: "information"
                    }, "kr" === e && E.a.createElement(Ue, null, E.a.createElement(Ne, null, n.get("footer_ceo")), E.a.createElement(Ne, null, n.get("footer_business_number")), E.a.createElement(Ne, null, n.get("footer_sale_number"))), !/us|jp/.test(e) && E.a.createElement(Ue, {
                        type: "address"
                    }, E.a.createElement(E.a.Fragment, null, E.a.createElement(Ne, {
                        isRtl: t
                    }, n.get("footer_company_address")), E.a.createElement(Ne, {
                        isRtl: t,
                        dangerouslySetInnerHTML: {
                            __html: n.get("footer_company_telephone")
                        }
                    }), o && E.a.createElement(Ne, {
                        isRtl: t,
                        dangerouslySetInnerHTML: {
                            __html: o
                        }
                    })))), E.a.createElement(He, null, E.a.createElement(ze, null, i.map(e => E.a.createElement(De, {
                        key: e.name,
                        isRtl: t
                    }, E.a.createElement(Pe, {
                        menuLinkItem: e
                    }), r && "businessInformation" === e.name && E.a.createElement(fe, {
                        toggled: !!a,
                        onClick: c
                    })))))), r && !!a && E.a.createElement(ge, null))
                },
                qe = n(192),
                Be = n(255),
                Xe = n(290);
            const Ge = $.d.a(Fe || (Fe = Object(R.a)(["\n  ", "\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.inlineFlexRowSet("center", "center", "8px")
                }),
                Ke = $.d.span(Je || (Je = Object(R.a)(["\n  ", "\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._textSet("subTitle", "bold")
                });
            var Qe, We = e => {
                let {
                    title: t,
                    icon: n,
                    url: r
                } = e;
                return E.a.createElement(Ge, {
                    title: t,
                    href: r
                }, E.a.createElement(he.e, {
                    icon: n,
                    role: "img",
                    "aria-label": t,
                    width: 20,
                    height: 20,
                    fill: "primary"
                }), E.a.createElement(Ke, null, t))
            };
            const Ye = $.d.a(Qe || (Qe = Object(R.a)(["\n  background-color: ", ";\n  border-radius: 50%;\n  width: 40px;\n  height: 40px;\n\n  ", ";\n"])), e => {
                let {
                    theme: t,
                    $isDarkMode: n
                } = e;
                return n ? t.colors.fill.secondary.default : t._colors.grayscale.gray_20
            }, e => {
                let {
                    theme: t
                } = e;
                return t.flexRowSet()
            });
            var Ze, et, tt, nt, rt, at, lt, ct, ot, it, st, mt, ut, dt, pt, bt, gt, ht, xt = e => {
                let {
                    title: t,
                    imageUrl: n,
                    url: r
                } = e;
                const a = /creator/.test(window.location.pathname);
                return E.a.createElement(Ye, {
                    title: t,
                    href: r,
                    rel: "noopener noreferrer",
                    $isDarkMode: a
                }, E.a.createElement("img", {
                    src: n,
                    alt: t
                }))
            };
            const Ot = $.d.div(Ze || (Ze = Object(R.a)(["\n  width: 100%;\n"]))),
                jt = $.d.div(et || (et = Object(R.a)(["\n  ", ";\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexColumnSet("flex-start", "flex-start", "24px")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(tt || (tt = Object(R.a)(["\n      gap: 0;\n    "]))))
                }),
                ft = $.d.div(nt || (nt = Object(R.a)(["\n  ", ";\n  width: 100%;\n  ", ";\n\n  & > .footer-logo {\n    ", ";\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("space-between")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(rt || (rt = Object(R.a)(["\n      ", ";\n    "])), t.flexRowSet()))
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(at || (at = Object(R.a)(["\n        display: none;\n      "]))))
                }),
                yt = $.d.nav(lt || (lt = Object(R.a)([""]))),
                _t = $.d.ul(ct || (ct = Object(R.a)(["\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("flex-start", "center", "8px")
                }),
                Et = $.d.li(ot || (ot = Object(R.a)([""]))),
                St = $.d.ul(it || (it = Object(R.a)(["\n  ", ";\n  flex-wrap: wrap;\n  ", ";\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t.flexRowSet("flex-start", "center", "24px")
                }, e => {
                    let {
                        theme: t
                    } = e;
                    return t.screen.sm(Object($.c)(st || (st = Object(R.a)(["\n      display: none;\n    "]))))
                }),
                wt = $.d.li(mt || (mt = Object(R.a)(["\n  position: relative;\n\n  &:last-child {\n    &:after {\n      display: none;\n    }\n  }\n  &:after {\n    background-color: ", ";\n    content: '';\n    width: 1px;\n    height: 10px;\n    position: absolute;\n    top: 50%;\n    inset-inline-end: 0;\n    transform: translate(12px, -50%);\n\n    ", ";\n  }\n"])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._colors.grayscale.gray_20
                }, e => {
                    let {
                        isRtl: t
                    } = e;
                    return t && Object($.c)(ut || (ut = Object(R.a)(["\n        transform: translate(-12px, -50%);\n      "])))
                });
            $.d.ul(dt || (dt = Object(R.a)(["\n  ", ";\n  align-self: flex-start;\n"])), e => {
                let {
                    theme: t
                } = e;
                return t.flexRowSet("flex-start", "center", "24px")
            }), $.d.li(pt || (pt = Object(R.a)(["\n  ", ";\n  position: relative;\n\n  &:last-child {\n    &:after {\n      display: none;\n    }\n  }\n\n  &:after {\n    background-color: ", ";\n    content: '';\n    width: 1px;\n    height: 10px;\n    position: absolute;\n    top: 50%;\n    inset-inline-end: 0;\n    transform: translate(12px, -50%);\n\n    ", ";\n  }\n"])), e => {
                let {
                    theme: t
                } = e;
                return t.flexRowSet("flex-start")
            }, e => {
                let {
                    theme: t
                } = e;
                return t._colors.grayscale.gray_20
            }, e => {
                let {
                    isRtl: t
                } = e;
                return t && Object($.c)(bt || (bt = Object(R.a)(["\n        transform: translate(-12px, -50%);\n      "])))
            }), $.d.button(gt || (gt = Object(R.a)(["\n  color: ", ";\n  cursor: pointer;\n  ", ";\n\n  ", "\n"])), e => {
                let {
                    theme: t
                } = e;
                return t._colors.grayscale.black
            }, e => {
                let {
                    theme: t
                } = e;
                return t._textSet("body1", "regular")
            }, e => {
                let {
                    isActive: t
                } = e;
                return t && Object($.c)(ht || (ht = Object(R.a)(["\n      ", ";\n    "])), e => {
                    let {
                        theme: t
                    } = e;
                    return t._textSet("body1", "bold")
                })
            });
            var vt = () => {
                const {
                    indexUrl: e,
                    countryCode: t,
                    isRtl: n
                } = Object(w.b)(e => ({
                    indexUrl: e.data.get("indexUrl"),
                    countryCode: e.data.get("countryCode"),
                    isRtl: e.data.get("isRtl")
                })), {
                    string: r
                } = Object(w.s)(), {
                    disabledCast: a
                } = Object(qe.a)(), l = Object(Be.c)(t).sns, c = Object(_.useMemo)(() => a ? [{
                    title: r.get("common_live"),
                    url: "".concat(e, "live"),
                    icon: "ic_live"
                }, {
                    title: r.get("common_community"),
                    url: "".concat(e, "community"),
                    icon: "ic_community"
                }] : [{
                    title: r.get("common_live"),
                    url: "".concat(e, "live"),
                    icon: "ic_live"
                }, {
                    title: r.get("common_cast"),
                    url: "".concat(e, "cast"),
                    icon: "ic_cast"
                }, {
                    title: r.get("menu_cast_storage"),
                    url: "".concat(e, "playlist"),
                    icon: "ic_music"
                }, {
                    title: r.get("common_community"),
                    url: "".concat(e, "community"),
                    icon: "ic_community"
                }], [a, e, r]);
                return E.a.createElement(Ot, null, E.a.createElement(jt, null, E.a.createElement(ft, null, E.a.createElement(Xe.d, {
                    color: "gray",
                    size: "xl",
                    role: "img",
                    "aria-label": "logo",
                    className: "footer-logo"
                }), E.a.createElement(_t, null, l.map(e => E.a.createElement(Et, {
                    key: e.name
                }, E.a.createElement(xt, {
                    title: e.title,
                    imageUrl: e.img,
                    url: e.url,
                    label: e.label
                }))))), E.a.createElement(ft, null, E.a.createElement(yt, null, E.a.createElement(St, null, c.map(e => E.a.createElement(wt, {
                    key: e.title,
                    isRtl: n
                }, E.a.createElement(We, {
                    title: e.title,
                    icon: e.icon,
                    url: e.url
                }))))))))
            };
            var kt = Object(_.memo)(() => {
                const {
                    indexUrl: e,
                    isServerStatusLoading: t,
                    isServerOn: n,
                    isSideMenuOpen: r
                } = Object(w.b)(e => ({
                    indexUrl: e.data.get("indexUrl"),
                    isServerStatusLoading: e.commonConfig.isServerStatusLoading,
                    isServerOn: e.commonConfig.isServerOn,
                    isSideMenuOpen: e.sideMenu.isSideMenuOpen
                })), a = Object(S.h)(), {
                    page: l,
                    pathname: c
                } = Object(v.H)(e, a.pathname), o = Object(C.a)(e, a.pathname), i = !/notice|faq/.test(l), s = o && i, m = Object(_.useMemo)(() => /spoon-membership/.test(c), [c]), u = Object(_.useMemo)(() => "" === l || /live|cast|playlist|creator|console|my|notice/.test(l) && !s, [s, l]), d = /creator/.test(l), p = /discovery/.test(l) && c.includes("preview"), b = Object(_.useMemo)(() => p, [p]);
                return t || !n || b ? null : E.a.createElement(T, {
                    className: "footer-container",
                    $isSideMenuOpen: r,
                    $isDetailView: s,
                    $isChannelPage: l === k.v,
                    $isContentsListPage: u,
                    $isSpoonMembershipPage: m,
                    $isDarkMode: d,
                    role: "contentinfo"
                }, E.a.createElement(A, null, E.a.createElement(vt, null), E.a.createElement(Ve, null), E.a.createElement(me, null)))
            });
            n.d(t, "default", (function() {
                return kt
            }))
        }
    }
]);
//# sourceMappingURL=30.300a33d8.chunk.js.map