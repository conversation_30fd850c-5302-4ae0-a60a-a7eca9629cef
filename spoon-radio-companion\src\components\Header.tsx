import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuthContext } from '../contexts/AuthContext';
import { useSpoonAuth } from '../contexts/SpoonAuthContext';

interface HeaderProps {
  onSearch: (query: string) => void;
  onAuthClick: () => void;
  onSidebarToggle: () => void;
}

const HeaderContainer = styled.header`
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
`;

const HeaderContent = styled.div`
  width: 100%;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
`;

const HamburgerButton = styled.button`
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &:focus {
    outline: none;
  }
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
`;

const LogoText = styled.span`
  font-size: 24px;
  font-weight: 700;
  color: #8b5cf6;
  letter-spacing: -0.5px;
`;

const CenterSection = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
  margin: 0 20px;
`;

const SearchContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 480px;
`;

const SearchInput = styled.input`
  width: 100%;
  height: 40px;
  padding: 0 40px 0 16px;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  font-size: 14px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #8b5cf6;
    background-color: white;
  }
  
  &::placeholder {
    color: #999;
  }
`;

const SearchButton = styled.button`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #8b5cf6;
  }
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
`;

const AuthButtonContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const LoginButton = styled.button`
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f0f0f0;
    color: #8b5cf6;
  }
`;

const SignupButton = styled.button`
  background-color: #8b5cf6;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #7c3aed;
  }
`;

const SpoonConnectButton = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    
    &::before {
      display: none;
    }
  }
  
  &:focus {
    outline: none;
  }
`;

const SpoonUserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 20px;
  border: 1px solid #e9ecef;
`;

const SpoonUserAvatar = styled.img`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
`;

const SpoonUserName = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const LoadingSpinner = styled.div`
  width: 12px;
  height: 12px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ProfileContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const ProfileButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &:focus {
    outline: none;
  }
`;

const ProfileName = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const ProfileDropdown = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  min-width: 160px;
  z-index: 1000;
  margin-top: 4px;
`;

const DropdownItem = styled.button`
  width: 100%;
  padding: 8px 16px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  color: #1a1a1a;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  &.logout {
    color: #8b5cf6;
    border-top: 1px solid #e5e5e5;
    margin-top: 4px;
  }
`;

const Header: React.FC<HeaderProps> = ({ onSearch, onAuthClick, onSidebarToggle }) => {
  const { user, logout } = useAuthContext();
  const { spoonUser, isSpoonAuthenticated, authenticateWithSpoon, loading } = useSpoonAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch(searchQuery);
    }
  };

  const handleProfileClick = () => {
    setShowProfileDropdown(!showProfileDropdown);
  };

  const handleLogout = () => {
    logout();
    setShowProfileDropdown(false);
  };

  const handleSpoonConnect = async () => {
    try {
      await authenticateWithSpoon();
    } catch (error) {
      console.error('스푼 연동 실패:', error);
    }
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <LeftSection>
          <HamburgerButton onClick={onSidebarToggle}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
            </svg>
          </HamburgerButton>
          
          <LogoContainer onClick={() => window.location.href = '/'}>
            <LogoText>Tamm</LogoText>
          </LogoContainer>
        </LeftSection>

        <CenterSection>
          <SearchContainer>
            <SearchInput
              type="text"
              placeholder="검색어를 입력하세요"
              value={searchQuery}
              onChange={handleSearchInputChange}
              onKeyPress={handleSearchKeyPress}
            />
            <SearchButton type="button" onClick={() => onSearch(searchQuery)}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </SearchButton>
          </SearchContainer>
        </CenterSection>

        <RightSection>
          {user ? (
            <>
              {isSpoonAuthenticated ? (
                <SpoonUserInfo>
                  <SpoonUserAvatar 
                    src={spoonUser?.profile_url || '/default-avatar.png'} 
                    alt={spoonUser?.nickname}
                    onError={(e) => {
                      e.currentTarget.src = '/default-avatar.png';
                    }}
                  />
                  <SpoonUserName>{spoonUser?.nickname}</SpoonUserName>
                </SpoonUserInfo>
              ) : (
                <SpoonConnectButton onClick={handleSpoonConnect} disabled={loading}>
                  {loading ? (
                    <>
                      <LoadingSpinner />
                      연동 중...
                    </>
                  ) : (
                    <>
                      스푼 연동
                    </>
                  )}
                </SpoonConnectButton>
              )}

              <ProfileContainer>
                <ProfileButton onClick={handleProfileClick}>
                  <ProfileName>
                    {user.displayName || user.email?.split('@')[0] || '사용자'}
                  </ProfileName>
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 10l5 5 5-5z"/>
                  </svg>
                </ProfileButton>
                
                {showProfileDropdown && (
                  <ProfileDropdown>
                    <DropdownItem>내 프로필</DropdownItem>
                    <DropdownItem>내 방송</DropdownItem>
                    <DropdownItem>팔로우</DropdownItem>
                    <DropdownItem>설정</DropdownItem>
                    <DropdownItem className="logout" onClick={handleLogout}>
                      로그아웃
                    </DropdownItem>
                  </ProfileDropdown>
                )}
              </ProfileContainer>
            </>
          ) : (
            <AuthButtonContainer>
              <LoginButton onClick={onAuthClick}>
                로그인
              </LoginButton>
              <SignupButton onClick={onAuthClick}>
                회원가입
              </SignupButton>
            </AuthButtonContainer>
          )}
        </RightSection>
      </HeaderContent>
    </HeaderContainer>
  );
};

export default Header;