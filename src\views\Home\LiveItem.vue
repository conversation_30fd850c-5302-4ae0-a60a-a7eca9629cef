<!--
 * LiveItem.vue
 * Created on Tue Aug 25 2020
 *
 * Copyright (c) Tree Some. Licensed under the MIT License.
-->
<template>
	<v-hover>
		<template v-slot="{ hover }">
			<v-card
				outlined
				@click.stop="$evt.$emit('live-join', live, isMembership)"
				color="transparent"
				class="mb-3 mx-auto live-item-card"
				style="cursor: pointer; border-radius: 10px; overflow: hidden;"
				:elevation="hover ? 3 : 0"
				>
				<div class="position-relative">
					<!-- 이미지 -->
					<v-img
						height="190"
						:src="live.img_url"
						class="live-image"
						gradient="to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.4)"
						>
						<!-- 시청자 수와 좋아요 수 -->
						<div class="live-stats">
							<div class="d-flex align-center stat-item">
								<v-icon size="18" color="white">mdi-account</v-icon>
								<span class="ml-1 white--text">{{ live.member_count }}</span>
							</div>
							<div class="d-flex align-center stat-item">
								<v-icon size="18" color="white">mdi-heart</v-icon>
								<span class="ml-1 white--text">{{ live.like_count }}</span>
							</div>
						</div>
					</v-img>
				</div>

				<!-- 제목과 작성자 정보 -->
				<div class="pa-2">
					<div class="live-title text-truncate">
						{{ live.title }}
					</div>
					<div class="d-flex align-center mt-1">
						<span class="author-name text-truncate" @click.stop="$assign('/user/' + live.author.id)">
							{{ live.author.nickname }}
						</span>
					</div>
				</div>
			</v-card>
		</template>
	</v-hover>
</template>
<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { Live } from '@sopia-bot/core';

@Component
export default class LiveItem extends Mixins(GlobalMixins) {
	@Prop(Object) public live!: Live;
	@Prop(Boolean) public isMembership!: boolean;
}
</script>
<style scoped>
.live-item-card {
	width: 100%;
	max-width: 180px;
	margin: 0 auto;
	transition: transform 0.2s;
}

.live-item-card:hover {
	transform: translateY(-3px);
}

.live-image {
	border-radius: 10px;
}

.position-relative {
	position: relative;
}

.live-stats {
	position: absolute;
	bottom: 8px;
	left: 8px;
	right: 8px;
	display: flex;
	justify-content: space-between;
	z-index: 2;
}

.stat-item {
	background-color: rgba(0, 0, 0, 0.4);
	border-radius: 12px;
	padding: 2px 8px;
	font-size: 0.75rem;
	font-weight: 500;
}

.live-title {
	font-size: 0.9rem;
	font-weight: 600;
	line-height: 1.2;
	margin-top: 4px;
	color: #333;
}

.author-name {
	font-size: 0.75rem;
	color: #777;
	font-weight: 400;
}
</style>
